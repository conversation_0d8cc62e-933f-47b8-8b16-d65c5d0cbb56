// eslint.config.mjs
import { FlatCompat } from '@eslint/eslintrc';
import js from '@eslint/js';
import vuePlugin from 'eslint-plugin-vue';
import tsPlugin from '@typescript-eslint/eslint-plugin';
import tsParser from '@typescript-eslint/parser';
import vueParser from 'vue-eslint-parser';

const uniGlobals = {
  uni: 'readonly',
  plus: 'readonly',
  wx: 'readonly',
  getApp: 'readonly',
  getCurrentPages: 'readonly',
  App: 'readonly',
  Page: 'readonly',
  Component: 'readonly',
  window: 'readonly',
  document: 'readonly',
  setTimeout: 'readonly',
  clearTimeout: 'readonly',
  setInterval: 'readonly',
  clearInterval: 'readonly',
  localStorage: 'readonly',
  sessionStorage: 'readonly',
  console: 'readonly',
  URLSearchParams: 'readonly',
  MouseEvent: 'readonly',
  Event: 'readonly',
  HTMLElement: 'readonly',
  TouchEvent: 'readonly',
  Node: 'readonly',
  Promise: 'readonly',
};

export default [
  // JS/TS 基础规则
  js.configs.recommended,

  // Vue 文件规则
  {
    files: ['**/*.vue'],
    languageOptions: {
      parser: vueParser,
      parserOptions: {
        parser: tsParser,
        ecmaVersion: 2020,
        sourceType: 'module',
        extraFileExtensions: ['.vue'],
      },
      globals: uniGlobals,
    },
    plugins: { vue: vuePlugin },
    rules: {
      ...vuePlugin.configs['vue3-recommended'].rules,
      'vue/multi-word-component-names': 'off',
      'vue/no-v-html': 'error',
      'vue/require-default-prop': 'error',
      'vue/no-unused-vars': 'error',
      'vue/html-indent': ['error', 2],
      'vue/html-self-closing': ['error', {
        html: { void: 'always', normal: 'never', component: 'always' },
      }],
      // 优化Vue相关规则，防止自动修复导致UI问题
      'vue/no-static-inline-styles': 'off',
      'vue/v-on-style': 'off',
      'vue/attribute-hyphenation': 'off',
      'vue/v-bind-style': 'off',
      'vue/component-options-name-casing': 'off',
      'vue/no-deprecated-v-on-native-modifier': 'warn',
      'vue/require-explicit-emits': 'off',
      'vue/prefer-import-from-vue': 'off',
      'vue/html-button-has-type': 'off',
      'vue/no-ref-object-destructure': 'off',
      'vue/no-v-text': 'off',
      'vue/no-deprecated-slot-attribute': 'off',
      'vue/no-reserved-component-names': 'off',
      'vue/v-slot-style': 'off',
      'vue/valid-v-slot': 'off'
    },
  },
  // TS 文件规则
  {
    files: ['**/*.ts', '**/*.tsx'],
    languageOptions: {
      parser: tsParser,
      parserOptions: {
        ecmaVersion: 2020,
      sourceType: 'module',
      },
      globals: uniGlobals,
    },
    plugins: { '@typescript-eslint': tsPlugin },
    rules: {
      ...tsPlugin.configs.recommended.rules,
      'no-unused-vars': 'off',
      '@typescript-eslint/no-unused-vars': ['warn'],
      },
    },
  // 通用风格规则
  {
    files: ['**/*.{js,ts,vue}'],
    languageOptions: {
      globals: uniGlobals,
    },
    rules: {
      'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
      'no-debugger': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
      'no-alert': process.env.NODE_ENV === 'production' ? 'error' : 'warn',
      'semi': ['warn', 'always'],
      'quotes': ['warn', 'single'],
      'max-len': ['warn', { 
        code: 120, 
        ignoreUrls: true, 
        ignoreStrings: true, 
        ignoreComments: true,
        ignoreTemplateLiterals: true,
        ignoreRegExpLiterals: true
      }],
      'comma-dangle': ['warn', 'always-multiline'],
      'eqeqeq': ['warn', 'smart'],
      'no-var': 'warn',
      'prefer-const': 'warn',
      'prefer-template': 'warn',
      'no-multiple-empty-lines': ['warn', { max: 1, maxEOF: 1 }],
      'arrow-body-style': ['warn', 'as-needed'],
      'object-shorthand': ['warn', 'always'],
      'no-unused-expressions': ['warn', { allowShortCircuit: true, allowTernary: true }],
      'no-undef': 'error',
      // 禁用可能导致UI问题的规则
      'vue/first-attribute-linebreak': 'off',
      'vue/max-attributes-per-line': 'off',
      'vue/singleline-html-element-content-newline': 'off'
    },
  },
  // 特定目录/文件的例外规则
  {
    files: ['**/components/common/**/*.vue', '**/components/business/**/*.vue'],
    rules: {
      'vue/require-default-prop': 'off',
      'vue/attribute-hyphenation': 'off',
    },
  },
]; 