# 需求确认

您希望我为一个移动APP开发项目生成详细的接口文档样例，该项目是一个使用Vue3+uni-app+Vant技术栈的多端应用（微信小程序、iOS、Android），后端使用Sealos云平台，核心功能包括安全认证、语音记账和预算分析等。您需要我根据您提供的技术方案展示接口文档的详细规范，对吗？

# 接口文档样例

## 📑 目录导航

### 基础规范
1. [接口规范总览](#1-接口规范总览)
2. [认证与安全模块接口](#2-认证与安全模块接口)
6. [安全与系统接口](#6-安全与系统接口)

### 核心功能
3. [语音交互模块接口](#3-语音交互模块接口)
4. [数据分析模块接口](#4-数据分析模块接口)
5. [数据同步模块接口](#5-数据同步模块接口)
8. [票据识别功能接口](#8-票据识别功能接口)
9. [智能提醒功能接口](#9-智能提醒功能接口)
10. [AI个性化功能接口](#10-ai个性化功能接口)
11. [导出与备份功能接口](#11-导出与备份功能接口)

### 数据管理
13. [账本管理接口](#13-账本管理接口)
14. [资产管理接口](#14-资产管理接口)

### 用户互动
7. [社交共享功能接口](#7-社交共享功能接口)
12. [VIP会员功能接口](#12-vip会员功能接口)

### 个性化设置
15. [日历管理接口](#15-日历管理接口)
16. [皮肤与主题接口](#16-皮肤与主题接口)

### 增值服务
17. [财务顾问接口](#17-财务顾问接口)

### 附录
- [WebSocket接口规范](#a-websocket接口规范)
- [安全规范补充](#b-安全规范补充)
- [集成与环境](#b1-环境信息)
- [第三方集成接口](#b2-第三方集成接口)
- [SDK集成示例](#b3-sdk集成示例)

## 1. 接口规范总览

🔑 **基础规范**
```
基础URL: https://api.example.com
API版本: v1
认证方式: Bearer Token JWT
响应格式: JSON
状态码: 200(成功), 400(请求错误), 401(未授权), 403(禁止), 404(未找到), 429(请求过多), 500(服务器错误)
```

🔑 **请求/响应规范**
```
请求头: 
  - Content-Type: application/json 或 multipart/form-data (文件上传)
  - Authorization: Bearer <token>
  - X-Device-ID: <设备唯一标识>
  - X-App-Version: <应用版本>

响应体统一格式:
{
  "code": 0,               // 业务状态码，0表示成功
  "message": "success",    // 状态描述
  "data": { ... }          // 业务数据
}
```

🔑 **通用参数约定**

**1. 命名规范**
```
- 所有参数采用小驼峰命名法(如pageSize)
- 路径参数使用kebab-case(如user-profile)
- URI路径使用复数名词表示资源集合(如/users, /transactions)
- 枚举值使用全大写下划线命名法(如STATUS_ACTIVE)
```

**2. 分页参数** (适用于所有返回列表的接口)
```
pageSize=20                // 每页数量，默认20，最大100
pageNum=1                  // 页码，从1开始
total                      // 响应中包含总记录数
pages                      // 响应中包含总页数
```

**3. 排序参数** (适用于所有列表接口)
```
sortBy=createdAt           // 排序字段，默认为创建时间
sortOrder=DESC             // 排序方向：ASC(升序), DESC(降序)，默认DESC
```

**4. 时间与时区处理**
```
// 所有时间戳统一使用毫秒级Unix时间戳
// 所有日期格式统一使用ISO 8601标准：YYYY-MM-DD
// 所有日期时间格式统一使用ISO 8601标准：YYYY-MM-DDTHH:mm:ssZ
// 时间范围参数统一使用startTime和endTime

// 涉及日期范围查询的接口可指定时区
timeZone=Asia/Shanghai     // 时区参数，默认UTC
```

**5. 文件上传约定**
```
// 所有文件上传统一使用multipart/form-data格式
// 文件大小限制：图片10MB，音频20MB，视频50MB，其他类型20MB
// 大文件(>10MB)上传支持断点续传，详见文件上传接口说明
```

**6. API访问限制**
```
// 默认限制：
- 普通用户: 100次请求/分钟
- VIP用户: 300次请求/分钟
- 敏感操作(如支付、备份): 10次请求/分钟
- 文件上传: 50MB/天

// 超出限制后返回HTTP 429状态码
// Retry-After头指示需等待的秒数
```

🔑 **错误码规范**

| 错误码范围 | 模块 | 说明 |
|------------|------|------|
| 1000-1999 | 用户与认证 | 用户注册、登录、密码相关错误 |
| 2000-2999 | 交易与记账 | 记账、交易处理相关错误 |
| 3000-3999 | 安全与加密 | 加密、安全验证相关错误 |
| 4000-4999 | 数据同步 | 数据同步、冲突解决相关错误 |
| 5000-5999 | 第三方服务 | 第三方API调用相关错误 |
| 6000-6999 | 资源限制 | 配额限制、频率限制相关错误 |
| 9000-9999 | 系统错误 | 服务器内部、数据库相关错误 |

**HTTP状态码使用说明**:
- 200: 请求成功
- 400: 请求参数错误，详见业务错误码
- 401: 未认证或认证失效，需重新登录
- 403: 权限不足，如VIP功能非VIP用户访问
- 404: 资源不存在
- 429: 请求频率超限，需遵循响应头中的Retry-After值
- 500: 服务器内部错误

**统一错误响应格式**:
```json
{
  "code": 4001,
  "message": "数据同步冲突",
  "data": {
    "errorType": "SYNC_CONFLICT",
    "details": "本地数据版本过旧",
    "timestamp": 1693490000000,
    "requestId": "req_123456",
    "suggestions": [
      "请先更新本地数据再提交变更",
      "如持续出现请联系客服"
    ],
    "retryAfter": 5000,  // 建议多久后重试(ms)
    "helpUrl": "https://support.example.com/sync-errors" // 帮助文档URL
  }
}
```

🔑 **JWT令牌刷新机制**

![JWT令牌刷新流程](https://storage.example.com/docs/jwt-refresh-flow.png)

1. 客户端初始登录获取访问令牌(access_token)和刷新令牌(refresh_token)
2. 访问令牌过期后(HTTP 401状态码)，客户端使用刷新令牌获取新的访问令牌
3. 刷新令牌过期后，客户端需要重新完整登录
4. 令牌有效期设置：
   - 访问令牌: 30分钟
   - 刷新令牌: 30天
5. 客户端应在访问令牌过期前主动刷新，避免用户体验中断

🔑 **重试策略约定**

对于以下情况，客户端应实现适当的重试机制：
- 网络连接问题 (如超时)
- 服务器临时不可用 (HTTP 503)
- 请求过于频繁 (HTTP 429)

推荐的重试策略：
1. **指数退避算法**：
   - 初始等待时间: 1秒
   - 最大等待时间: 60秒
   - 最大重试次数: 3次
   - 退避因子: 2 (每次重试等待时间翻倍)

2. **HTTP 429处理**：
   ```javascript
   if (response.status === 429) {
     const retryAfter = response.headers.get('Retry-After') || 60;
     setTimeout(() => retry(request), retryAfter * 1000);
   }
   ```

3. **网络错误处理**：
   ```javascript
   fetch(url)
     .catch(error => {
       if (isNetworkError(error) && retryCount < maxRetries) {
         const delay = Math.min(1000 * Math.pow(2, retryCount), maxDelay);
         setTimeout(() => retry(request, retryCount + 1), delay);
       }
     });
```

## 2. 认证与安全模块接口

### 2.1 用户注册

**接口描述**: 创建新用户账号  
**请求方法**: `POST`  
**接口路径**: `/api/v1/users/register`
**预期响应时间**: < 500ms

**请求参数**:
```json
{
  "phone": "13800138000",     // 手机号码
  "smsCode": "123456",        // 短信验证码
  "nickname": "张三",         // 用户昵称
  "deviceInfo": {             // 设备信息
    "type": "iOS",            // 设备类型：iOS/Android/Web
    "model": "iPhone 14",     // 设备型号
    "systemVersion": "16.2",  // 系统版本
    "deviceId": "uuid-xxxxx"  // 设备唯一标识
  }
}
```

**请求传输加密**:
手机号和验证码等敏感信息使用RSA公钥加密传输：
```javascript
// 客户端加密示例
const publicKey = await getPublicKey(); // 获取服务端公钥
const encryptedData = RSA.encrypt(JSON.stringify({
  phone: phone,
  smsCode: smsCode
}), publicKey);

// 加密后数据放入请求
request.sensitiveData = encryptedData;
```

**响应数据**:
```json
{
  "code": 0,
  "message": "注册成功",
  "data": {
    "userId": "u_10001",            // 用户ID
    "token": "eyJhbGciOiJIUzI1...", // JWT令牌
    "expires": 1693526400000,       // 令牌过期时间戳
    "needSetupSecurity": true       // 是否需要设置安全认证
  }
}
```

**错误码说明**:
| 错误码 | 说明 | 处理建议 |
|--------|------|---------|
| 1001 | 手机号已注册 | 引导用户登录或找回密码，或使用其他手机号注册 |
| 1002 | 验证码错误或已过期 | 提示用户重新获取验证码，确认短信接收情况 |
| 1003 | 设备信息不完整 | 确保deviceId和必要设备信息已提供，检查应用权限 |
| 1004 | 手机号格式无效 | 提示用户检查输入，确保符合标准手机号格式 |
| 1005 | 昵称包含敏感词 | 提示用户修改昵称，避免使用敏感词汇 |
| 1006 | 同IP注册频率过高 | 提示用户稍后再试，或更换网络环境 |
| 6001 | 注册功能暂时不可用 | 系统维护或负载过高，建议稍后再试 |

**异常情况处理**:
1. 网络超时：自动重试最多2次，间隔3秒
2. 验证码频繁错误：第3次错误后提示用户可尝试重新获取验证码
3. 服务端返回500：记录traceid，提示用户系统繁忙并稍后再试

### 2.2 发送短信验证码

**接口描述**: 发送短信验证码  
**请求方法**: `POST`  
**接口路径**: `/api/v1/sms/send`

**请求参数**:
```json
{
  "phone": "13800138000",     // 手机号码
  "type": "register"          // 验证码类型：register(注册), login(登录), reset(重置密码)
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "发送成功",
  "data": {
    "expireIn": 300,          // 验证码有效期(秒)
    "requestId": "sms_req_10001" // 请求ID，用于问题排查
  }
}
```

❗ **注意事项**:
- 同一手机号60秒内只能发送一次验证码
- 同一IP地址24小时内最多发送20次验证码
- 验证码默认有效期5分钟

### 2.3 手势密码设置

**接口描述**: 设置手势密码  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/gesture`  
**需要认证**: 是

**请求参数**:
```json
{
  "pattern": "15973",             // 手势轨迹数字矩阵(1-9的数字组合)
  "securityLevel": "high",        // 安全级别：low(仅应用内), medium(敏感操作), high(每次启动)
  "patternHash": "sha256_hash"    // 客户端生成的手势密码哈希值
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "设置成功",
  "data": {
    "encryptedToken": "aes_encrypted_token",  // 加密后令牌
    "securityId": "sec_10001",                // 安全设置ID
    "updateTime": 1693440000000              // 更新时间戳
  }
}
```

**补充说明**：
❗ **客户端哈希生成规则**  
- 使用SHA-256(salt + pattern)生成patternHash  
- salt为设备唯一标识(deviceId)的前8位  
- 示例：deviceId=iphone14_abc123 → salt=iphone1  

❗ **加密Token说明**
- encryptedToken是用户JWT令牌的加密版本
- 使用从手势密码派生的密钥(PBKDF2)进行AES-256-GCM加密
- 客户端解锁时用手势密码解密令牌，无需每次向服务器请求新令牌
- 令牌实际过期前，客户端应使用刷新令牌获取新令牌并本地更新加密存储

🔑 **密钥轮换流程**  
1. 每月1日生成新MEK，旧密钥保留30天  
2. 用户登录时自动迁移DEK至新MEK  
3. 客户端检测到密钥变更时触发静默更新

### 2.4 锁屏验证

**接口描述**: 验证锁屏密码(手势或数字)  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/unlock`

**请求头**:
```
X-Device-ID: device_uuid_xxxxx
```

**请求参数**:
```json
{
  "input": "15973",                // 手势轨迹或6位数字密码
  "inputType": "gesture",          // 输入类型：gesture(手势), pin(数字), biometric(生物识别)
  "verifyToken": "verify_token"    // 验证令牌(生物识别场景使用)
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "验证成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1...",  // 有效JWT令牌
    "expires": 1693526400000,         // 令牌过期时间戳
    "userStatus": "normal"            // 用户状态：normal(正常), warning(异常登录)
  }
}
```

## 3. 语音交互模块接口

### 3.1 语音记账

**接口描述**: 上传语音进行记账识别  
**请求方法**: `POST`  
**接口路径**: `/api/v1/transaction/voice`  
**需要认证**: 是
**请求类型**: `multipart/form-data`

**请求参数**:
```
audio                      // 语音文件
format=wav                  // 音频格式：wav, mp3, m4a
duration=5                  // 音频时长(秒)
location={"latitude":39.9042,"longitude":116.4074,"address":"北京市朝阳区"} // 可选，位置信息JSON字符串
options={"preferredService":"azure","acceptLowerAccuracy":false,"maxResponseTime":1500} // 选项JSON字符串
```

**响应数据**:
```json
{
  "code": 0,
  "message": "识别成功",
  "data": {
    "transactionId": "t_10001",       // 交易ID
    "amount": 50.00,                  // 金额
    "category": "餐饮",               // 消费类别
    "description": "午餐",            // 消费描述
    "date": "2024-08-01",             // 交易日期
    "time": "12:30:00",               // 交易时间
    "location": "北京市朝阳区",        // 地点信息
    "budgetWarning": true,            // 预算警告
    "recognizedText": "午餐花了50元",   // 识别出的原始文本
    "serviceInfo": {
      "serviceUsed": "azure",          // 实际使用的服务
      "confidenceScore": 0.92,         // 识别置信度
      "processingTime": 650,           // 处理时间(ms)
      "isDowngraded": false            // 是否使用了降级服务
    }
  }
}
```

💡 **优化建议**:
- 较大音频文件(>1MB)应使用multipart/form-data格式上传，避免Base64编码造成的额外开销
- 语音录制最佳长度5-10秒，超过15秒会被截断

💡 **容灾方案**
- 当Azure语音服务不可用时，自动切换至备用DeepGram API或本地TensorFlow Lite模型
- 降级模式响应时间延长至1.5秒内，准确率可能有所降低

**语音服务切换逻辑**：
1. 客户端发起请求时，首先检测网络延迟
   - 如果网络延迟 < 500ms，优先使用Azure服务
   - 如果网络延迟 ≥ 500ms，检查DeepGram可用性
2. DeepGram服务检查
   - 如果DeepGram可用，切换使用DeepGram服务
   - 如果DeepGram不可用，启用本地TensorFlow Lite模型进行离线识别

### 3.2 语音识别文本确认

**接口描述**: 修正或确认语音识别结果  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/transaction/voice/{transactionId}/confirm`  
**需要认证**: 是

**请求参数**:
```json
{
  "confirmed": true,               // 是否确认识别结果
  "corrections": {                 // 修正信息(confirmed=false时必填)
    "amount": 55.00,               // 修正后的金额
    "category": "外卖",            // 修正后的类别
    "description": "外卖午餐"      // 修正后的描述
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "确认成功",
  "data": {
    "transactionId": "t_10001",
    "status": "confirmed",         // 交易状态：pending(待确认), confirmed(已确认), canceled(已取消)
    "learnStatus": true            // AI是否已学习此次修正
  }
}
```
### 3.3 自然语言查询
**接口描述**: 通过语音或文本查询消费情况和生成报表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/query/natural`  
**需要认证**: 是

**请求参数**:
```json
{
  "input": "上个月家庭生活开支多少",  // 用户输入的语音或文本
  "inputType": "voice",            // 输入类型：voice(语音)/text(文本)
  "options": {
    "responseType": "both",        // 响应类型：voice(语音)/text(文本)/both(两者)
    "timezone": "Asia/Shanghai"    // 时区设置
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "type": "statistical",          // 响应类型：statistical(统计)/list(明细)
    "timeRange": "2024-07-01~2024-07-31", 
    "totalAmount": 8560.00,
    "currency": "CNY",
    "details": [
      {
        "category": "食品采购",
        "amount": 3200.00,
        "percentage": 37.38,
        "trend": "下降2%"
      },
      {
        "category": "水电煤",
        "amount": 1500.00,
        "percentage": 17.52,
        "trend": "持平"
      }
    ],
    "voiceResponse": "base64_encoded_audio", // 语音回复的base64编码
    "suggestions": [                // 智能建议
      "您本月家庭开支已超预算10%",
      "同比上月增加5%"
    ]
  }
}
```

**技术实现说明**:
1. 语义解析：使用DeepSeek的`/v1/chat/completions`接口，添加定制化提示词
```javascript
const prompt = `
你是一个专业记账助手，需从文本中提取：
1. 时间范围(近7天、本月、上月、自定义日期)
2. 消费分类(餐饮、交通、家庭生活等)
3. 统计维度(总额、分类占比、趋势变化)
示例输入："查询上个月家庭生活开支" 
输出JSON：
{
  "time_range": "last_month",
  "category": ["家庭生活"],
  "metric": "total"
}
`;
```

2. 语音合成：集成Azure Text-to-Speech，支持情感化回复（惊喜/提醒/平静三种语调）

**错误码说明**:

| 错误码 | 说明 | 解决方案 |
|--------|------|---------|
| 2001 | 输入内容无法解析 | 提供更明确的查询语句 |
| 2002 | 时间范围无效 | 检查时间格式是否正确 |

## 4. 数据分析模块接口

### 4.1 预算分析

**接口描述**: 获取月度预算分析  
**请求方法**: `GET`  
**接口路径**: `/api/v1/analysis/budget`  
**需要认证**: 是

**请求参数**:
```
month=2024-08         // 查询月份，格式: YYYY-MM
timeZone=Asia/Shanghai // 时区
```

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "month": "2024-08",
    "totalBudget": 5000.00,        // 总预算
    "totalSpent": 4200.00,         // 总支出
    "remaining": 800.00,           // 剩余预算
    "progress": 84,                // 预算使用进度(%)
    "daysRemaining": 10,           // 本月剩余天数
    "dailyRemaining": 80.00,       // 日均可用预算
    "riskLevel": "medium",         // 超支风险：low, medium, high
    "categoryBreakdown": [         // 分类明细
      {
        "category": "餐饮",
        "spent": 1500.00,
        "budget": 2000.00,
        "progress": 75,
        "trend": "stable"          // 消费趋势：increasing, decreasing, stable
      },
      {
        "category": "交通",
        "spent": 800.00,
        "budget": 600.00,
        "progress": 133,
        "trend": "increasing"
      },
      {
        "category": "购物",
        "spent": 1900.00,
        "budget": 2400.00,
        "progress": 79,
        "trend": "decreasing"
      }
    ]
  }
}
```

### 4.2 消费趋势

**接口描述**: 获取消费趋势分析  
**请求方法**: `GET`  
**接口路径**: `/api/v1/analysis/trends`  
**需要认证**: 是

**请求参数**:
```
period=month          // 周期类型：week, month, quarter, year
count=6               // 周期数量
endDate=2024-08-31    // 结束日期，默认当前日期
category=餐饮         // 可选，指定分类
```

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "period": "month",
    "periodCount": 6,
    "currency": "CNY",
    "trends": [
      {
        "label": "2024-03",
        "amount": 4500.00,
        "change": 0                // 环比变化(%)
      },
      {
        "label": "2024-04",
        "amount": 4800.00,
        "change": 6.67
      },
      {
        "label": "2024-05",
        "amount": 4200.00,
        "change": -12.5
      },
      {
        "label": "2024-06",
        "amount": 4600.00,
        "change": 9.52
      },
      {
        "label": "2024-07",
        "amount": 5100.00,
        "change": 10.87
      },
      {
        "label": "2024-08",
        "amount": 4200.00,
        "change": -17.65
      }
    ],
    "statistics": {
      "average": 4566.67,          // 平均值
      "median": 4550.00,           // 中位数
      "max": {                     // 最大值
        "period": "2024-07",
        "amount": 5100.00
      },
      "min": {                     // 最小值
        "period": "2024-05",
        "amount": 4200.00
      },
      "trend": "fluctuating"       // 总体趋势：increasing, decreasing, stable, fluctuating
    }
  }
}
```

## 5. 数据同步模块接口

### 5.1 账单数据同步

**接口描述**: 同步本地与云端账单数据  
**请求方法**: `POST`  
**接口路径**: `/api/v1/sync/transactions`  
**需要认证**: 是

**请求参数**:
```json
{
  "lastSyncTime": 1690000000000,     // 上次同步时间戳(毫秒)
  "deviceId": "uuid-xxxxx",          // 设备ID
  "changes": [                       // 本地变更数据
    {
      "id": "local_t_1001",          // 本地ID
      "serverId": null,              // 服务器ID(新增为null)
      "action": "create",            // 操作类型：create, update, delete
      "data": {                      // 数据内容(delete时可为null)
        "amount": 35.50,
        "category": "餐饮",
        "description": "早餐",
        "date": "2024-08-01",
        "time": "08:30:00",
        "updateTime": 1690000000000
      }
    },
    {
      "id": "local_t_1002",
      "serverId": "t_10002",
      "action": "update",
      "data": {
        "amount": 120.00,           // 只传更新字段
        "updateTime": 1690010000000
      }
    },
    {
      "id": "local_t_1003",
      "serverId": "t_10003",
      "action": "delete",
      "data": null
    }
  ]
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "同步成功",
  "data": {
    "syncTime": 1693500000000,      // 当前同步时间戳
    "serverChanges": [              // 服务器变更数据
      {
        "id": "t_10004",
        "clientId": null,
        "action": "create",
        "data": {
          "amount": 88.00,
          "category": "娱乐",
          "description": "电影票",
          "date": "2024-08-02",
          "time": "19:30:00",
          "updateTime": 1693400000000
        }
      },
      {
        "id": "t_10002",
        "clientId": "local_t_1002",
        "action": "update",
        "data": {
          "updateTime": 1693450000000
        }
      }
    ],
    "mapping": [                   // ID映射关系(本地ID->服务器ID)
      {
        "clientId": "local_t_1001",
        "serverId": "t_10005"
      }
    ],
    "conflicts": [                // 数据冲突(需客户端解决)
      {
        "serverId": "t_10002",
        "clientId": "local_t_1002",
        "serverData": {
          "amount": 150.00,
          "updateTime": 1693450000000
        },
        "clientData": {
          "amount": 120.00,
          "updateTime": 1690010000000
        },
        "resolution": "server_win" // 冲突解决策略：server_win, client_win
      }
    ]
  }
}
```

❗ **注意事项**:
- 同步冲突按"后者胜出"原则处理，以updateTime为判断依据
- 客户端应保留每次同步时间戳，用于增量同步
- 网络不稳定情况下建议5分钟后重试同步

🔑 **数据同步冲突解决增强策略**
- 网络不稳定情况下，本地保存完整请求日志
- 实现基于CRDTs(无冲突复制数据类型)的数据模型
- 提供手动合并选项，让用户决策复杂冲突

🔧 **时间同步机制**  
- 客户端每次同步时获取服务端时间戳  
- 计算设备时间偏移量并本地存储  
- 冲突判断使用服务端时间为主时钟

## 6. 安全与系统接口

### 6.1 token刷新

**接口描述**: 刷新JWT令牌  
**请求方法**: `POST`  
**接口路径**: `/api/v1/auth/refresh`  
**需要认证**: 是（使用refresh_token）

**请求参数**:
```json
{
  "refreshToken": "eyJhbGciOiJIUzI1...",  // 刷新令牌
  "deviceId": "uuid-xxxxx"                // 设备ID
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "刷新成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1...",      // 新的访问令牌
    "refreshToken": "eyJhbGciOiJIUzI1...", // 新的刷新令牌
    "expires": 1693526400000,             // 访问令牌过期时间
    "refreshExpires": 1696118400000       // 刷新令牌过期时间
  }
}
```

### 6.2 系统配置获取

**接口描述**: 获取系统配置信息  
**请求方法**: `GET`  
**接口路径**: `/api/v1/system/config`  
**需要认证**: 否

**请求参数**:
```
version=1.0.0         // APP版本号
platform=ios          // 平台：ios, android, web
deviceId=uuid-xxxxx   // 设备ID
```

**响应数据**:
```json
{
  "code": 0,
  "message": "获取成功",
  "data": {
    "apiVersion": "1.0.0",
    "minClientVersion": "1.0.0",    // 最低客户端版本
    "forceUpdate": false,           // 是否强制更新
    "updateUrl": "https://example.com/download", // 更新地址
    "features": {                   // 功能开关
      "voiceRecognition": true,
      "biometricLogin": true,
      "aiAnalysis": true
    },
    "limits": {                     // 使用限制
      "maxVoiceDuration": 15,       // 最大语音时长(秒)
      "maxUploadSize": 5242880,     // 最大上传大小(字节)
      "dailyRequestLimit": 1000     // 日请求限制
    },
    "endpoints": {                  // 服务端点
      "main": "https://api.example.com",
      "backup": "https://api2.example.com"
    },
    "maintenance": {               // 维护信息
      "inMaintenance": false,
      "startTime": null,
      "endTime": null,
      "message": ""
    }
  }
}
```

### 6.3 安全日志查询

**接口描述**: 查询账户安全相关操作记录  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/logs`  
**需要认证**: 是
**预期响应时间**: < 800ms (< 100条记录时)

**请求参数**:
```
startTime=1693300000000     // 开始时间戳(毫秒)
endTime=1693400000000       // 结束时间戳(毫秒)
type=ALL                    // 日志类型：ALL(全部), LOGIN(登录), PASSWORD(密码), SECURITY(安全设置), AUTHORIZATION(授权)
severity=ALL                // 严重程度：ALL(全部), INFO(信息), WARNING(警告), CRITICAL(严重)
deviceId=uuid-xxxxx         // 可选，设备ID，不传表示所有设备
pageSize=20                 // 每页数量，默认20
pageNum=1                   // 页码，从1开始
sortBy=timestamp            // 排序字段：timestamp(时间), severity(严重程度), type(类型)
sortOrder=DESC              // 排序方向：ASC(升序), DESC(降序)
```

**分页示例**:
```javascript
// 客户端分页获取示例
async function fetchAllSecurityLogs(startTime, endTime) {
  let allLogs = [];
  let pageNum = 1;
  const pageSize = 50;
  
  while (true) {
    const response = await api.get('/security/logs', {
      startTime, 
      endTime, 
      pageSize, 
      pageNum
    });
    
    allLogs = allLogs.concat(response.data.logs);
    
    if (pageNum >= response.data.pages) {
      break;
    }
    
    pageNum++;
  }
  
  return allLogs;
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "total": 258,                    // 总记录数
    "pages": 13,                     // 总页数
    "pageSize": 20,                  // 每页大小
    "pageNum": 1,                    // 当前页码
    "logs": [
      {
        "logId": "log_10001",
        "timestamp": 1693390000000,  // 时间戳
        "type": "LOGIN",             // 日志类型
        "subType": "SUCCESS",        // 子类型
        "severity": "INFO",          // 严重程度
        "message": "成功登录",
        "deviceInfo": {              // 设备信息
          "deviceId": "uuid-xxxxx",
          "deviceModel": "iPhone 14",
          "ipAddress": "***********",
          "location": "北京市",
          "userAgent": "App/1.0"
        },
        "details": {                 // 详细信息
          "loginMethod": "password", // 登录方式
          "authFactors": ["password", "sms"] // 认证因素
        }
      },
      {
        "logId": "log_10002",
        "timestamp": 1693380000000,
        "type": "SECURITY",
        "subType": "GESTURE_UPDATED",
        "severity": "WARNING",
        "message": "手势密码已更新",
        "deviceInfo": {
          "deviceId": "uuid-yyyyy",
          "deviceModel": "OPPO R11",
          "ipAddress": "***********",
          "location": "上海市",
          "userAgent": "App/1.0"
        }
      }
    ],
    "summary": {                     // 汇总数据
      "criticalCount": 0,            // 严重问题数
      "warningCount": 5,             // 警告数
      "infoCount": 253,              // 信息数
      "latestCritical": null,        // 最近严重问题
      "latestLogin": {               // 最近登录
        "timestamp": 1693390000000,
        "deviceInfo": {
          "deviceModel": "iPhone 14",
          "location": "北京市"
        }
      }
    },
    "queryPerformance": {           // 查询性能信息
      "queryTime": 285,             // 查询耗时(毫秒)
      "resultCount": 20,            // 返回结果数
      "appliedFilters": ["type", "startTime", "endTime"]  // 应用的过滤条件
    }
  }
}
```

**日志类型枚举**:
| 类型(type) | 子类型(subType) | 描述 |
|------------|----------------|------|
| LOGIN | SUCCESS | 登录成功 |
| LOGIN | FAILED | 登录失败 |
| LOGIN | LOCKED | 账户锁定 |
| PASSWORD | RESET | 密码重置 |
| PASSWORD | CHANGED | 密码修改 |
| SECURITY | GESTURE_UPDATED | 手势密码更新 |
| SECURITY | BIOMETRIC_ENABLED | 生物识别启用 |
| SECURITY | RECOVERY_UPDATED | 恢复选项更新 |
| AUTHORIZATION | APP_AUTHORIZED | 应用授权 |
| AUTHORIZATION | PERMISSION_GRANTED | 权限授予 |
| AUTHORIZATION | PERMISSION_REVOKED | 权限撤销 |

**严重程度枚举**:
| 严重程度(severity) | 描述 |
|-------------------|------|
| INFO | 普通操作，无安全风险 |
| WARNING | 需要注意的操作，可能存在安全风险 |
| CRITICAL | 严重操作，可能表明账户被盗用 |

**错误码说明**:
| 错误码 | 说明 | 处理建议 |
|--------|------|---------|
| 1201 | 查询时间范围过大 | 缩短查询时间范围，不超过90天 |
| 1202 | 无权限查看日志 | 检查用户角色权限配置 |
| 6101 | 查询请求过于频繁 | 降低查询频率，建议每分钟不超过10次 |

**性能优化**:
- 建议使用较小的日期范围进行查询，过大的范围会导致查询变慢
- 如需导出大量日志，建议使用导出接口而非分页查询
- 重复查询使用客户端缓存，相同参数的查询在5分钟内使用缓存结果

### 6.4 生物识别设置

**接口描述**: 设置生物识别选项  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/biometric`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometricType": "face",          // 生物识别类型：face(面部), fingerprint(指纹), iris(虹膜)
  "enabled": true,                  // 是否启用
  "useForLogin": true,             // 是否用于登录
  "useForPayment": true            // 是否用于支付
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "设置成功",
  "data": {
    "biometricId": "biometric_10001",    // 生物识别ID
    "biometricType": "face",             // 生物识别类型
    "useForLogin": true,                // 是否用于登录
    "useForPayment": true,             // 是否用于支付
    "status": "enabled"                 // 状态：enabled(已启用), disabled(已禁用)
  }
}
```

### 6.5 安全问题设置

**接口描述**: 设置安全问题  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/questions`  
**需要认证**: 是

**请求参数**:
```json
{
  "question1": "你最喜欢的颜色是什么？",
  "answer1": "蓝色",
  "question2": "你最喜欢的动物是什么？",
  "answer2": "猫"
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "设置成功",
  "data": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

### 6.6 安全问题验证

**接口描述**: 验证安全问题答案  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/questions/verify`  
**需要认证**: 是

**请求参数**:
```json
{
  "question1": "你最喜欢的颜色是什么？",
  "answer1": "蓝色",
  "question2": "你最喜欢的动物是什么？",
  "answer2": "猫"
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "验证成功",
  "data": {
    "isCorrect": true
  }
}
```

### 6.7 安全设置备份

**接口描述**: 备份安全设置  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/backup`  
**需要认证**: 是

**请求参数**:
```json
{
  "backupType": "all",              // 备份类型：all(全部), selected(选择项)
  "selectedItems": ["biometric", "questions"] // 选择备份的项目
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "备份成功",
  "data": {
    "backupId": "backup_10001",      // 备份ID
    "backupType": "all",             // 备份类型
    "backupTime": 1693440000000,     // 备份时间戳
    "backupData": {                 // 备份数据
      "biometric": {
        "biometricId": "biometric_10001",
        "biometricType": "face",
        "useForLogin": true,
        "useForPayment": true,
        "status": "enabled"
      },
      "questions": {
        "question1": "你最喜欢的颜色是什么？",
        "answer1": "蓝色",
        "question2": "你最喜欢的动物是什么？",
        "answer2": "猫"
      }
    }
  }
}
```

### 6.8 安全设置恢复

**接口描述**: 恢复安全设置  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/restore`  
**需要认证**: 是

**请求参数**:
```json
{
  "backupId": "backup_10001",      // 备份ID
  "backupType": "all"              // 备份类型
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "恢复成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.9 安全设置重置

**接口描述**: 重置安全设置  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/reset`  
**需要认证**: 是

**请求参数**:
```json
{
  "resetType": "all",              // 重置类型：all(全部), selected(选择项)
  "selectedItems": ["biometric", "questions"] // 选择重置的项目
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "重置成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.10 安全设置查看

**接口描述**: 查看安全设置  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.11 安全设置修改

**接口描述**: 修改安全设置  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "修改成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.12 安全设置删除

**接口描述**: 删除安全设置  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.13 安全设置添加

**接口描述**: 添加安全设置  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.14 安全设置更新

**接口描述**: 更新安全设置  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.15 安全设置查看详情

**接口描述**: 查看安全设置详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.16 安全设置删除详情

**接口描述**: 删除安全设置详情  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.17 安全设置添加详情

**接口描述**: 添加安全设置详情  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.18 安全设置更新详情

**接口描述**: 更新安全设置详情  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.19 安全设置查看列表

**接口描述**: 查看安全设置列表  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.20 安全设置删除列表

**接口描述**: 删除安全设置列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.21 安全设置添加列表

**接口描述**: 添加安全设置列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.22 安全设置更新列表

**接口描述**: 更新安全设置列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.23 安全设置查看详情列表

**接口描述**: 查看安全设置详情列表  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.24 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.25 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.26 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.27 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.28 安全设置删除列表详情

**接口描述**: 删除安全设置列表详情  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.29 安全设置添加列表详情

**接口描述**: 添加安全设置列表详情  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.30 安全设置更新列表详情

**接口描述**: 更新安全设置列表详情  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.31 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.32 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
        "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.33 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.34 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.35 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.36 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.37 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.38 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.39 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
        "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.40 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
        "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.41 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.42 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.43 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.44 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.45 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.46 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.47 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.48 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.49 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.50 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.51 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.52 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.53 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.54 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.55 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.56 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.57 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.58 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "更新成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.59 安全设置查看列表详情

**接口描述**: 查看安全设置列表详情  
**请求方法**: `GET`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "查询成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.60 安全设置删除详情列表

**接口描述**: 删除安全设置详情列表  
**请求方法**: `DELETE`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**响应数据**:
```json
{
  "code": 0,
  "message": "删除成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.61 安全设置添加详情列表

**接口描述**: 添加安全设置详情列表  
**请求方法**: `POST`  
**接口路径**: `/api/v1/security/settings`  
**需要认证**: 是

**请求参数**:
```json
{
  "biometric": {
    "biometricType": "face",
    "enabled": true,
    "useForLogin": true,
    "useForPayment": true
  },
  "questions": {
    "question1": "你最喜欢的颜色是什么？",
    "answer1": "蓝色",
    "question2": "你最喜欢的动物是什么？",
    "answer2": "猫"
  }
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "添加成功",
  "data": {
    "biometric": {
      "biometricId": "biometric_10001",
      "biometricType": "face",
      "useForLogin": true,
      "useForPayment": true,
      "status": "enabled"
    },
    "questions": {
      "question1": "你最喜欢的颜色是什么？",
      "answer1": "蓝色",
      "question2": "你最喜欢的动物是什么？",
      "answer2": "猫"
    }
  }
}
```

### 6.62 安全设置更新详情列表

**接口描述**: 更新安全设置详情列表  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/security/settings/{settingId}`  
**需要认证**: 是

**请求参数**:
```json
    "planId": "plan_10001",          // 计划ID
    "targetAmount": 50000.00,        // 目标金额
    "targetDate": "2025-12-31",      // 目标日期
    "timeFrame": {                   // 时间框架
      "months": 16,                  // 总月数
      "startDate": "2024-08-15",     // 开始日期
      "endDate": "2025-12-31"        // 结束日期
    },
```

## B. 安全规范补充

### B.1 密钥体系说明

本应用采用多层密钥结构保障用户数据安全：

1. **MEK (主加密密钥)**
   - 服务端托管，使用HSM保护
   - 用于加密用户的DEK
   - 定期轮换(每月)

2. **DEK (数据加密密钥)**
   - 用户特定密钥，由MEK加密存储
   - 用于加密用户敏感数据
   - 每个用户拥有唯一DEK

3. **KEK (密钥加密密钥)**
   - 基于用户口令/手势密码派生
   - 用于本地加密认证令牌
   - 仅存在于客户端内存中

### B.2 密钥备份与恢复

**备份机制**:

1. **安全恢复码**
   - 系统生成16位随机字母数字组合作为主恢复码
   - 采用PBKDF2算法从恢复码生成备份密钥
   - 使用备份密钥加密DEK
   - 恢复码明文仅展示给用户一次，不在服务器存储明文

2. **云端备份**
   - 加密后的DEK存储在服务端
   - 用户可通过多重身份认证+恢复码恢复

3. **混合恢复**
   - 支持部分恢复码(8位)+OTP验证的组合认证
   - 恢复码尝试错误5次将锁定24小时
   - 恢复尝试会生成安全审计日志并通知用户

**恢复流程**:
```
1. 用户输入恢复码
2. 系统验证用户身份(通过手机号+短信验证码)
3. 客户端使用恢复码重新生成备份密钥
4. 使用备份密钥解密DEK
5. 重新配置用户设备安全设置
```

### B.3 PIPL合规要求

作为面向中国市场的应用，我们严格遵守《个人信息保护法》(PIPL)要求：

1. **最小化原则**
   - 仅收集必要的个人信息
   - 提供明确的隐私政策和权限说明

2. **敏感信息处理**
   - 涉及交易、位置等敏感信息时单独授权
   - 提供撤回同意的简单机制

3. **本地处理优先**
   - 优先在用户设备上处理信息
   - 跨境传输数据时获得明确授权

4. **访问控制**
   - 实施严格访问控制策略
   - 对用户敏感数据进行分级处理

开发者应确保API调用时遵循这些规范，特别是在处理下列敏感API时：
- 通讯录访问(用于邀请功能)
- 位置信息(用于记账位置标记)
- 短信读取(用于账单检测)
- 照片库访问(用于票据识别)

### 8.1 票据扫描识别

**接口描述**: 上传票据图片进行OCR识别  
**请求方法**: `POST`  
**接口路径**: `/api/v1/receipt/scan`  
**需要认证**: 是  
**请求类型**: `multipart/form-data`  
**预期响应时间**: 1-3秒 (取决于图片复杂度)

**请求参数**:
```
image                      // 票据图片文件
imageType=jpeg             // 图片类型：jpeg, png, heic
receiptType=AUTO           // 票据类型：AUTO(自动识别), INVOICE(发票), RECEIPT(小票)
options={"enhanceImage":true,"correctAngle":true,"extractItems":true} // 选项JSON字符串
location={"latitude":39.9042,"longitude":116.4074,"address":"北京市朝阳区"} // 可选，位置信息JSON字符串
```

**大文件断点续传**:
对于超过5MB的图片文件，支持分块上传：
1. 初始化上传:
```
POST /api/v1/receipt/scan/init
Content-Type: application/json

{
  "fileName": "receipt.jpg",
  "fileSize": 8388608,     // 文件总大小(字节)
  "imageType": "jpeg",
  "chunkSize": 1048576     // 每块大小(1MB)
}

响应:
{
  "code": 0,
  "message": "初始化成功",
  "data": {
    "uploadId": "upload_10001",     // 上传ID
    "chunkSize": 1048576,           // 服务器确认的块大小
    "expires": 1693526400000        // 上传链接过期时间
  }
}
```

2. 上传分块:
```
POST /api/v1/receipt/scan/chunk
Content-Type: multipart/form-data

uploadId=upload_10001       // 上传ID
chunkIndex=0                // 块索引(从0开始)
chunk                       // 文件块
totalChunks=8               // 总块数

响应:
{
  "code": 0,
  "message": "块上传成功",
  "data": {
    "uploadId": "upload_10001",
    "chunkIndex": 0,
    "receivedChunks": [0],          // 已接收的块
    "nextExpectedChunk": 1           // 下一个期望的块
  }
}
```

3. 完成上传:
```
POST /api/v1/receipt/scan/complete
Content-Type: application/json

{
  "uploadId": "upload_10001",
  "receiptType": "AUTO",
  "options": {"enhanceImage":true,"correctAngle":true},
  "location": {"latitude":39.9042,"longitude":116.4074}
}
```

**响应数据**:
```json
{
  "code": 0,
  "message": "识别成功",
  "data": {
    "receiptId": "receipt_10001",      // 票据ID
    "recognizedType": "VAT_INVOICE",   // 识别出的票据类型
    "merchant": "北京XXX超市",          // 商家名称
    "totalAmount": 235.50,             // 总金额
    "taxAmount": 30.50,                // 税额
    "currency": "CNY",                 // 货币单位
    "issueDate": "2024-08-15",         // 开票/交易日期
    "receiptNumber": "*********",      // 票据号码
    "items": [                         // 商品明细
      {
        "name": "矿泉水",              // 商品名称
        "quantity": 2,                 // 数量
        "unitPrice": 3.00,             // 单价
        "amount": 6.00                 // 小计
      },
      {
        "name": "面包",
        "quantity": 3,
        "unitPrice": 15.00,
        "amount": 45.00
      }
    ],
    "categories": [                   // 推荐分类
      {
        "name": "食品",
        "confidence": 0.95
      },
      {
        "name": "日用品",
        "confidence": 0.36
      }
    ],
    "imageUrl": "https://storage.example.com/receipts/123.jpg",  // 存储的图片URL
    "confidence": 0.92,               // 整体识别置信度
    "extracted": {                    // 提取的关键字段
      "merchantTaxId": "91110105XXXXXXXXXX", // 税号
      "paymentMethod": "现金",         // 支付方式
      "qrCodeContent": "https://fapiao.com/v/xxxxx" // 二维码内容
    },
    "processingTime": 1580            // 处理时间(毫秒)
  }
}
```

**错误码说明**:
| 错误码 | 说明 | 处理建议 |
|--------|------|---------|
| 2101 | 图片无法识别 | 检查图片清晰度，尝试重新拍摄或调整光线 |
| 2102 | 非票据图片 | 确认上传的是票据图片，不是其他类型文档 |
| 2103 | 图片分辨率过低 | 使用更高分辨率拍摄，确保票据边缘清晰可见 |
| 2104 | 票据类型不支持 | 当前仅支持增值税发票、普通发票和购物小票 |
| 2105 | 图片已被处理过 | 使用原始票据图片，避免使用截图或处理过的图片 |
| 5101 | OCR服务暂时不可用 | 服务器繁忙，请稍后重试 |
| 5102 | 图片处理超时 | 尝试压缩图片或提高网络质量后重试 |
| 6201 | 上传分块缺失 | 检查分块上传过程，确保所有分块正确上传 |
| 6202 | 上传ID已过期 | 重新初始化上传流程 |

**断点续传异常处理**:
1. 上传中断：客户端记录已上传的块索引，恢复时调用查询已上传块接口
```
GET /api/v1/receipt/scan/status?uploadId=upload_10001

响应:
{
  "code": 0,
  "data": {
    "uploadId": "upload_10001",
    "receivedChunks": [0, 1, 2],     // 已接收的块
    "missingChunks": [3, 4, 5, 6, 7], // 缺失的块
    "expires": 1693526400000          // 上传过期时间
  }
}
```

2. 网络波动：实现块上传重试机制，每块最多尝试3次，间隔递增
3. 上传过期：检测uploadId过期时自动重新初始化上传

❗ **注意事项**:
- 支持的图片格式：JPEG、PNG、HEIC
- 图片大小不超过10MB
- 图片分辨率建议不低于1000×800像素
- 拍摄时避免强光反射和模糊
- 大文件上传建议使用WiFi环境

### 16.2 皮肤应用

**接口描述**: 更换应用皮肤  
**请求方法**: `PUT`  
**接口路径**: `/api/v1/themes/current`  
**需要认证**: 是  
**预期响应时间**: < 300ms

**请求参数**:
```json
{
  "themeId": "theme_gold"            // 要应用的主题ID
}
```

**主题应用流程**:
```
┌─────────────┐     ┌─────────────┐     ┌─────────────┐     ┌─────────────┐
│  用户选择   │     │ 获取主题    │     │ 应用主题    │     │ 预加载      │
│  主题       │────►│ 资源信息    │────►│ 到应用      │────►│ 主题资源    │
└─────────────┘     └─────────────┘     └─────────────┘     └─────────────┘
                           │                   │                   │
                           ▼                   │                   ▼
                    ┌─────────────┐           │            ┌─────────────┐
                    │ 检查是否    │◄──────────┘            │ 保存用户    │
                    │ 需要下载    │                        │ 主题偏好    │
                    └─────────────┘                        └─────────────┘
                           │                                      │
                           ▼                                      │
                    ┌─────────────┐                               │
                    │ 下载主题    │                               │
                    │ 资源包      │◄──────────────────────────────┘
                    └─────────────┘
```

**响应数据**:
```json
{
  "code": 0,
  "message": "应用成功",
  "data": {
    "themeId": "theme_gold",
    "name": "金色财富",
    "appliedAt": 1693440000000,     // 应用时间
    "themeResources": {             // 主题资源
      "styleUrl": "https://storage.example.com/themes/gold_style.css",
      "assetsUrl": "https://storage.example.com/themes/gold_assets.zip",
      "fontsUrl": "https://storage.example.com/themes/gold_fonts.zip"
    },
    "systemSettings": {             // 系统设置
      "useDarkStatusBar": false,    // 是否使用深色状态栏
      "useAmoledBlack": false,      // 是否使用AMOLED黑
      "fontScale": 1.0              // 字体缩放
    },
    "resourceInfo": {               // 资源信息
      "totalSize": 2458624,         // 总大小(字节)
      "requiredStorage": 3145728,   // 所需存储空间(字节)
      "downloadEstimate": 5,        // 预计下载时间(秒)
      "alreadyCached": false        // 是否已缓存
    }
  }
}
```

**客户端实现示例**:
```javascript
// 主题应用过程
async function applyTheme(themeId) {
  try {
    // 1. 应用主题
    const response = await api.put('/themes/current', { themeId });
    
    // 2. 检查是否需要下载资源
    if (!response.data.resourceInfo.alreadyCached) {
      // 3. 下载主题资源
      await Promise.all([
        downloadResource(response.data.themeResources.styleUrl, 'styles'),
        downloadResource(response.data.themeResources.assetsUrl, 'assets'),
        downloadResource(response.data.themeResources.fontsUrl, 'fonts')
      ]);
    }
    
    // 4. 应用主题到UI
    applyThemeToUI(response.data);
    
    // 5. 保存用户偏好
    saveUserPreference('theme', themeId);
    
    return true;
  } catch (error) {
    // 处理错误情况
    handleThemeApplyError(error);
    return false;
  }
}

// 错误处理
function handleThemeApplyError(error) {
  if (error.code === 1301) {
    // VIP限制
    showVipUpgradeDialog('使用高级主题需要升级为VIP会员');
  } else if (error.code === 6301) {
    // 存储空间不足
    showStorageWarning(error.data.requiredStorage);
  } else if (error.code === 5201) {
    // 主题资源暂时不可用
    showToast('主题资源暂时不可用，请稍后再试');
  } else {
    // 其他错误
    showToast('应用主题失败，请重试');
  }
}
```

**错误码说明**:
| 错误码 | 说明 | 处理建议 |
|--------|------|---------|
| 1301 | 非VIP用户使用VIP主题 | 提示用户升级为VIP会员 |
| 1302 | 主题不存在 | 检查themeId是否正确，或刷新主题列表 |
| 1303 | 主题已下架 | 提示用户选择其他主题 |
| 5201 | 主题资源暂时不可用 | 服务器资源暂时不可用，稍后重试 |
| 6301 | 设备存储空间不足 | 提示用户清理空间后重试 |
| 6302 | 主题下载频率限制 | 24小时内下载主题不超过10个，建议稍后重试 |

**性能优化建议**:
1. 首次应用主题后缓存主题资源，避免重复下载
2. 使用WebP格式图片资源，减少下载大小
3. 预加载用户最近使用过的几个主题，提升切换速度
4. 如设备存储紧张，可启用"轻量模式"，仅下载必要资源

### 11.1 数据导出任务创建

**接口描述**: 创建数据导出任务  
**请求方法**: `POST`  
**接口路径**: `/api/v1/export/create`  
**需要认证**: 是  
**预期响应时间**: < 500ms (任务创建，不含导出处理时间)

**请求参数**:
```json
{
  "exportType": "TRANSACTIONS",      // 导出类型：TRANSACTIONS(交易), ACCOUNTS(账户), BUDGET(预算), ALL(全部)
  "format": "EXCEL",                 // 导出格式：CSV, EXCEL, JSON, PDF
  "dateRange": {                     // 日期范围
    "startTime": *************,      // 开始时间戳(毫秒) - 2024-01-01
    "endTime": *************         // 结束时间戳(毫秒) - 2024-08-31
  },
  "filters": {                       // 筛选条件
    "categories": ["餐饮", "交通"],   // 分类
    "minAmount": 100,                // 最小金额
    "maxAmount": 1000,               // 最大金额
    "accounts": ["account_10001"]    // 账户ID
  },
  "options": {                      // 导出选项
    "includeAttachments": false,    // 是否包含附件
    "includeTags": true,            // 是否包含标签
    "includeNotes": true,           // 是否包含备注
    "groupBy": "MONTH",             // 分组方式：NONE, DAY, WEEK, MONTH, CATEGORY
    "sortBy": "DATE",               // 排序字段：DATE(日期), AMOUNT(金额), CATEGORY(分类)
    "sortOrder": "DESC",            // 排序方向：ASC(升序), DESC(降序)
    "locale": "zh-CN"               // 语言区域
  },
  "notification": {                 // 通知设置
    "notifyWhenComplete": true,     // 完成时通知
    "notifyMethod": "APP"           // 通知方式：APP(应用内), EMAIL(邮件)
  }
}
```

**接口调用时序示例**:
```
┌─────────┐          ┌────────────┐          ┌─────────────┐          ┌───────────┐
│ 客户端  │          │ API服务器  │          │ 任务处理服务 │          │ 存储服务  │
└────┬────┘          └──────┬─────┘          └──────┬──────┘          └─────┬─────┘
     │                      │                       │                        │
     │  1. 创建导出任务     │                       │                        │
     │ ──────────────────► │                       │                        │
     │                      │                       │                        │
     │                      │ 2. 创建任务并排队     │                        │
     │                      │ ──────────────────► │                        │
     │                      │                       │                        │
     │ 3. 返回taskId        │                       │                        │
     │ ◄────────────────── │                       │                        │
     │                      │                       │                        │
     │ 4. 查询任务状态      │                       │                        │
     │ ──────────────────► │                       │                        │
     │                      │                       │                        │
     │                      │ 5. 获取任务状态       │                        │
     │                      │ ──────────────────► │                        │
     │                      │                       │                        │
     │ 6. 返回processing    │                       │                        │
     │ ◄────────────────── │ ◄────────────────── │                        │
     │                      │                       │                        │
     │                      │                       │ 7. 处理数据导出        │
     │                      │                       │ ───────────────────► │
     │                      │                       │                        │
     │                      │                       │ 8. 文件就绪通知        │
     │                      │                       │ ◄─────────────────── │
     │                      │                       │                        │
     │ 9. 查询任务状态      │                       │                        │
     │ ──────────────────► │                       │                        │
     │                      │                       │                        │
     │ 10. 返回completed    │                       │                        │
     │ ◄────────────────── │                       │                        │
     │                      │                       │                        │
     │ 11. 下载导出文件     │                       │                        │
     │ ───────────────────────────────────────────────────────────────────► │
     │                      │                       │                        │
     │ 12. 返回文件内容     │                       │                        │
     │ ◄─────────────────────────────────────────────────────────────────── │
     │                      │                       │                        │
```

**响应数据**:
```json
{
  "code": 0,
  "message": "创建成功",
  "data": {
    "taskId": "export_10001",       // 导出任务ID
    "status": "PENDING",            // 任务状态：PENDING(等待中), PROCESSING(处理中), COMPLETED(已完成), FAILED(失败)
    "createdAt": 1693440000000,     // 创建时间
    "estimatedSize": "2.5MB",       // 预估文件大小
    "estimatedTime": 30,            // 预估耗时(秒)
    "recordCount": 158,             // 符合条件的记录数
    "queuePosition": 2,             // 队列位置
    "priority": "NORMAL",           // 任务优先级：HIGH(高), NORMAL(普通), LOW(低)
    "expiresAt": 1693612800000      // 导出文件过期时间(48小时后)
  }
}
```

**错误码说明**:
| 错误码 | 说明 | 处理建议 |
|--------|------|---------|
| 2501 | 日期范围过大 | 缩短日期范围，单次导出不超过1年数据 |
| 2502 | 无符合条件的数据 | 检查筛选条件是否过于严格 |
| 6401 | 导出请求过于频繁 | 普通用户24小时内最多10次导出，VIP用户30次 |
| 6402 | 已达队列上限 | 系统导出队列已满，建议稍后再试 |
| 6403 | 导出文件超过大小限制 | 缩小导出范围或减少导出内容(如不包含附件) |

**客户端实现建议**:
```javascript
// 导出流程示例
async function exportData(exportOptions) {
  try {
    // 1. 创建导出任务
    const createResponse = await api.post('/export/create', exportOptions);
    const taskId = createResponse.data.taskId;
    
    // 2. 轮询任务状态
    let status = 'PENDING';
    while (status !== 'COMPLETED' && status !== 'FAILED') {
      // 等待适当时间再查询
      await sleep(createResponse.data.estimatedTime > 60 ? 10000 : 3000);
      
      // 查询任务状态
      const statusResponse = await api.get(`/export/${taskId}`);
      status = statusResponse.data.status;
      
      // 更新进度UI
      updateExportProgressUI(statusResponse.data);
      
      if (status === 'FAILED') {
        throw new Error(statusResponse.data.error || '导出失败');
      }
    }
    
    // 3. 下载导出文件
    const downloadResponse = await api.get(`/export/${taskId}/download`, {
      responseType: 'blob'
    });
    
    // 4. 处理文件
    saveFile(downloadResponse.data, createResponse.data.fileName);
    
    return true;
  } catch (error) {
    handleExportError(error);
    return false;
  }
}

// 进度更新函数
function updateExportProgressUI(data) {
  if (data.status === 'PENDING') {
    showQueuePosition(data.queuePosition);
  } else if (data.status === 'PROCESSING') {
    showProgress(data.progress || 0);
    showEstimatedTimeRemaining(data.estimatedTimeRemaining);
  }
}
```

❗ **注意事项**:
- 导出文件仅保留48小时，需及时下载
- 大型导出(超过10MB)建议在WiFi环境下进行
- 应用后台自动重试导出下载，即使用户切换应用
- VIP用户享有更高导出优先级和更大导出文件大小限制(500MB vs 50MB)