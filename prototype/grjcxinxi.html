<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>完善个人资料 - AI记账</title>
    <!-- Font Awesome CDN (在实际项目中应使用 <FaIcon> 组件) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* --- Core Variables (Simulating variables.scss from home.html & aigexing.html) --- */
        :root {
            --color-primary: #FF6B35;          /* 主橙色 */
            --color-primary-light: #FFF5F2;     /* 浅橙色背景 */
            --color-text-primary: #333333;     /* 主要文字 */
            --color-text-secondary: #666666;    /* 次要文字 */
            --color-text-hint: #999999;        /* 提示文字 */
            --color-text-inverse: #FFFFFF;     /* 反色文字 */
            --color-bg-primary: #FFFFFF;      /* 主背景 */
            --color-bg-secondary: #F8F9FA;    /* 次要背景 (页面背景) */
            --color-border: rgba(0, 0, 0, 0.08); /* 边框颜色，稍深一点 */
            --color-shadow: rgba(0, 0, 0, 0.06); /* 阴影颜色，稍浅 */
            --color-success: #4CAF50;         /* 成功色 */
            --color-error: #F44336;          /* 错误色 */
            --radius-card: 18px;             /* 卡片圆角 */
            --radius-button: 24px;            /* 按钮圆角 */
            --radius-input: 10px;             /* 输入框圆角 */
            --radius-avatar: 50%;           /* 头像圆角 */
            --radius-icon-bg: 12px;          /* 图标背景圆角 */
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            --transition-speed: 0.2s;
        }

        /* --- Basic Reset & Body --- */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        html {
             -webkit-text-size-adjust: 100%;
        }
        body {
            font-family: var(--font-family);
            color: var(--color-text-primary);
            background-color: var(--color-bg-secondary);
            line-height: 1.6;
            font-size: 16px;
        }

        /* --- Phone Container --- */
        .phone-container {
             max-width: 414px;
             margin: 0 auto;
             background-color: var(--color-bg-secondary);
             box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
             min-height: 100vh;
             position: relative; /* Needed for fixed button positioning */
             padding-bottom: 90px; /* Space for the save button */
        }

        /* --- Status Bar --- */
        .status-bar {
            display: flex;
            justify-content: space-between;
            padding: 10px 20px;
            background-color: var(--color-bg-primary); /* Use primary background */
            color: var(--color-text-primary);
            font-size: 13px;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .status-bar-icon { margin-left: 6px; }

        /* --- Navigation Bar --- */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: center; /* Center title */
            height: 56px;
            padding: 0 15px;
            background-color: var(--color-bg-primary);
            border-bottom: 1px solid var(--color-border);
            position: sticky;
            top: 37px; /* Adjust based on status bar height */
            z-index: 99;
        }
        .nav-title {
            font-size: 17px;
            font-weight: 600;
        }
        /* No back button for initial setup, maybe a skip button */
        .nav-right {
            position: absolute;
            right: 15px;
        }
        .skip-button {
            font-size: 15px;
            color: var(--color-text-secondary);
            background: none;
            border: none;
            padding: 5px;
            cursor: pointer;
        }

        /* --- Content Area --- */
        .content {
            padding: 20px 15px;
        }

        /* --- Card Style --- */
        .card {
            background-color: var(--color-bg-primary);
            border-radius: var(--radius-card);
            margin-bottom: 20px;
            box-shadow: 0 6px 16px var(--color-shadow);
            overflow: hidden;
        }

        /* --- Setting Item (Adapted from home.html list-item and aigexing.html setting-item) --- */
        .setting-item {
            display: flex;
            align-items: center;
            padding: 15px; /* Consistent padding */
            border-bottom: 1px solid var(--color-border);
            position: relative;
            cursor: pointer;
            min-height: 60px; /* Ensure consistent height */
            transition: background-color var(--transition-speed);
        }
        .setting-item:last-child { border-bottom: none; }
        .setting-item:active { background-color: #f9f9f9; }

        .setting-icon-wrapper { /* Wrapper for icon background */
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-icon-bg);
            margin-right: 15px;
            flex-shrink: 0;
        }
        .setting-icon { /* The actual icon */
            font-size: 18px;
            color: white;
        }
        /* Icon background colors */
        .icon-bg-avatar { background-color: #4CAF50; } /* Green */
        .icon-bg-nickname { background-color: #2196F3; } /* Blue */
        .icon-bg-gender { background-color: #E91E63; } /* Pink */
        .icon-bg-birthday { background-color: #FF9800; } /* Orange */
        .icon-bg-phone { background-color: #673AB7; } /* Deep Purple */
        .icon-bg-email { background-color: #00BCD4; } /* Cyan */
        .icon-bg-location { background-color: #795548; } /* Brown */
        .icon-bg-interests { background-color: #FF5722; } /* Deep Orange */

        .setting-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        .setting-label {
            font-size: 16px;
            font-weight: 500;
            color: var(--color-text-primary);
            /* margin-bottom: 2px; */ /* Remove if only one line needed */
        }
        .setting-action {
            margin-left: 15px;
            color: var(--color-text-hint);
            display: flex;
            align-items: center;
            gap: 8px;
            text-align: right; /* Align action content to right */
        }
        .setting-value { /* To display current selection or input */
             font-size: 16px; /* Match label size */
             color: var(--color-text-secondary);
             /* font-weight: 500; */
             flex: 1; /* Allow input to take space */
             text-align: right; /* Align input text to right */
             border: none;
             background: transparent;
        }
        .setting-value::placeholder {
            color: var(--color-text-hint);
        }
        .setting-value:focus { outline: none; }
        .setting-chevron {
            font-size: 14px; /* Smaller chevron */
        }

        /* Specific Item Styles */
        .avatar-setting {
            align-items: center; /* Vertically center avatar stuff */
        }
        .avatar-preview {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-avatar);
            object-fit: cover;
            border: 2px solid var(--color-border);
        }
        .interest-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 5px;
            justify-content: flex-end; /* Align tags to right */
            max-width: 150px; /* Limit width to prevent overflow */
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .interest-tag-preview {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
            padding: 2px 6px;
            border-radius: 4px;
            font-size: 12px;
        }

        /* --- Save Button --- */
        .save-button-container {
             position: fixed;
             bottom: 0;
             left: 0;
             right: 0;
             padding: 15px 20px calc(15px + env(safe-area-inset-bottom)); /* Add safe area padding */
             background: var(--color-bg-primary);
             border-top: 1px solid var(--color-border);
             max-width: 414px; /* Match phone container */
             margin: 0 auto;
             z-index: 50;
        }
        .save-button {
            width: 100%;
            height: 48px;
            background-color: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-button);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            /* box-shadow: 0 6px 16px var(--color-shadow-orange); */ /* Removing shadow for cleaner look */
            transition: all var(--transition-speed) ease;
            cursor: pointer;
        }
        .save-button i { margin-right: 8px; }
        .save-button:active {
            transform: scale(0.98);
            opacity: 0.9;
        }

    </style>
</head>
<body>

    <div class="phone-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-bar-left">9:41</div>
            <div class="status-bar-right">
                <i class="fas fa-signal status-bar-icon"></i>
                <i class="fas fa-wifi status-bar-icon"></i>
                <i class="fas fa-battery-full status-bar-icon"></i>
            </div>
        </div>

        <!-- Navigation Bar -->
        <div class="nav-bar">
            <!-- No back button needed for initial setup -->
            <div class="nav-title">完善个人资料</div>
            <div class="nav-right">
                <button class="skip-button" onclick="skipSetup()">跳过</button>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content">
            <p style="text-align: center; color: var(--color-text-secondary); font-size: 14px; margin-bottom: 25px;">
                补充信息，让 AI 更好地为你服务 ✨
            </p>

            <div class="card">
                <!-- 1. Avatar Setting -->
                <div class="setting-item avatar-setting" onclick="changeAvatar()">
                    <div class="setting-icon-wrapper icon-bg-avatar">
                        <i class="fas fa-user setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">头像</div>
                    </div>
                    <div class="setting-action">
                        <img src="https://img.zcool.cn/community/01f09e5930bc44a8012193a314d745.png@1280w_1l_2o_100sh.png" alt="默认头像" class="avatar-preview" id="avatarPreview">
                        <i class="fas fa-chevron-right setting-chevron"></i>
                    </div>
                </div>

                <!-- 2. Nickname Setting -->
                <div class="setting-item">
                    <div class="setting-icon-wrapper icon-bg-nickname">
                        <i class="fas fa-pencil-alt setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">昵称</div>
                    </div>
                    <div class="setting-action">
                        <input type="text" id="nicknameInput" class="setting-value" placeholder="请输入昵称">
                    </div>
                </div>

                <!-- 3. Gender Setting -->
                <div class="setting-item" onclick="selectGender()">
                    <div class="setting-icon-wrapper icon-bg-gender">
                        <i class="fas fa-venus-mars setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">性别</div>
                    </div>
                    <div class="setting-action">
                        <span class="setting-value" id="genderValue">请选择</span>
                        <i class="fas fa-chevron-right setting-chevron"></i>
                    </div>
                </div>

                <!-- 4. Birthdate Setting -->
                <div class="setting-item" onclick="selectBirthdate()">
                    <div class="setting-icon-wrapper icon-bg-birthday">
                        <i class="fas fa-birthday-cake setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">出生日期</div>
                    </div>
                    <div class="setting-action">
                        <span class="setting-value" id="birthdateValue">请选择</span>
                        <i class="fas fa-chevron-right setting-chevron"></i>
                    </div>
                </div>
            </div>

            <div class="card">
                <!-- 5. Phone Setting -->
                <div class="setting-item">
                    <div class="setting-icon-wrapper icon-bg-phone">
                        <i class="fas fa-mobile-alt setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">手机号码</div>
                    </div>
                    <div class="setting-action">
                        <input type="tel" id="phoneInput" class="setting-value" placeholder="请输入手机号">
                        <!-- Verification button could be added here in real app -->
                    </div>
                </div>

                <!-- 6. Email Setting -->
                <div class="setting-item">
                    <div class="setting-icon-wrapper icon-bg-email">
                        <i class="fas fa-envelope setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">电子邮箱</div>
                    </div>
                    <div class="setting-action">
                        <input type="email" id="emailInput" class="setting-value" placeholder="选填，用于找回密码">
                    </div>
                </div>
            </div>

            <div class="card">
                <!-- 7. Location Setting -->
                <div class="setting-item" onclick="selectLocation()">
                    <div class="setting-icon-wrapper icon-bg-location">
                        <i class="fas fa-map-marker-alt setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">所在地区</div>
                    </div>
                    <div class="setting-action">
                        <span class="setting-value" id="locationValue">请选择</span>
                        <i class="fas fa-chevron-right setting-chevron"></i>
                    </div>
                </div>

                <!-- 8. Interests Setting -->
                <div class="setting-item" onclick="selectInterests()">
                    <div class="setting-icon-wrapper icon-bg-interests">
                        <i class="fas fa-heart setting-icon"></i>
                    </div>
                    <div class="setting-content">
                        <div class="setting-label">兴趣爱好</div>
                    </div>
                    <div class="setting-action">
                         <div class="interest-tags" id="interestPreview">
                            <!-- Selected tags will appear here -->
                            <span class="setting-value" id="interestPlaceholder">请选择 (可选)</span>
                         </div>
                        <i class="fas fa-chevron-right setting-chevron"></i>
                    </div>
                </div>
            </div>

        </div>

        <!-- Save Button Area -->
        <div class="save-button-container">
            <button class="save-button" onclick="saveProfile()">
                <i class="fas fa-check"></i>完成
            </button>
        </div>
    </div>

    <script>
        // --- Basic Interaction Logic (for prototyping) ---
        function skipSetup() {
            console.log('跳过设置');
            alert('跳过设置（实际应用中跳转到首页）');
            // window.location.href = 'home.html'; // Uncomment for actual navigation
        }

        function changeAvatar() {
            console.log('更换头像');
            alert('触发更换头像操作（拍照/相册）');
            // Logic to open camera/gallery and update #avatarPreview src
        }

        function selectGender() {
            console.log('选择性别');
            alert('弹出性别选择器（男/女/保密）');
            // Update #genderValue based on selection
             // Example: document.getElementById('genderValue').textContent = '男';
        }

        function selectBirthdate() {
            console.log('选择出生日期');
            alert('弹出日期选择器');
            // Update #birthdateValue based on selection
            // Example: document.getElementById('birthdateValue').textContent = '1995-08-10';
        }

        function selectLocation() {
            console.log('选择地区');
            alert('弹出地区选择器');
            // Update #locationValue based on selection
            // Example: document.getElementById('locationValue').textContent = '广东省 深圳市';
        }

        function selectInterests() {
            console.log('选择兴趣爱好');
            alert('弹出兴趣标签选择器');
            // Update #interestPreview based on selection
            // Example: updateInterestPreview(['阅读', '旅行']);
        }
        // Helper function to update interest preview tags
        function updateInterestPreview(interests = []) {
            const previewContainer = document.getElementById('interestPreview');
            const placeholder = document.getElementById('interestPlaceholder');
            previewContainer.innerHTML = ''; // Clear previous
            if (interests.length > 0) {
                if(placeholder) placeholder.style.display = 'none';
                interests.slice(0, 3).forEach(interest => { // Show max 3 tags in preview
                    const tag = document.createElement('span');
                    tag.className = 'interest-tag-preview';
                    tag.textContent = interest;
                    previewContainer.appendChild(tag);
                });
                if (interests.length > 3) {
                     const more = document.createElement('span');
                     more.className = 'interest-tag-preview';
                     more.textContent = '...';
                     previewContainer.appendChild(more);
                }
            } else {
                if(placeholder) placeholder.style.display = 'inline';
            }
        }


        function saveProfile() {
            const nickname = document.getElementById('nicknameInput').value;
            const gender = document.getElementById('genderValue').textContent;
            const birthdate = document.getElementById('birthdateValue').textContent;
            const phone = document.getElementById('phoneInput').value;
            const email = document.getElementById('emailInput').value;
            const location = document.getElementById('locationValue').textContent;
            // Get selected interests (needs actual selection logic)
            const interests = []; // Placeholder

            // Basic validation (example)
            if (!nickname) {
                alert('请输入昵称');
                return;
            }
            if (gender === '请选择') {
                alert('请选择性别');
                return;
            }
             if (birthdate === '请选择') {
                alert('请选择出生日期');
                return;
            }
             if (!phone) {
                 alert('请输入手机号码');
                 return;
             }
             // Add more validation as needed (phone format, email format etc.)

            console.log('保存个人资料:', {
                avatar: document.getElementById('avatarPreview').src, // Placeholder, get actual path/file
                nickname,
                gender,
                birthdate,
                phone,
                email,
                location,
                interests
            });
            alert('资料已保存！（实际应用中将发送API请求并跳转）');
            // window.location.href = 'home.html'; // Navigate after successful save
        }

        // Initial setup example for interests
         // updateInterestPreview(['阅读', '旅行', '编程', '音乐']);
         updateInterestPreview([]); // Show placeholder initially
    </script>
</body>
</html>