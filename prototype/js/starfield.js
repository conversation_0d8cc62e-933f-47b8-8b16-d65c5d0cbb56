// 星空背景动画
document.addEventListener('DOMContentLoaded', function() {
  const canvas = document.getElementById('starfield');
  if (!canvas) return;
  
  const ctx = canvas.getContext('2d');
  let width = canvas.width = window.innerWidth;
  let height = canvas.height = window.innerHeight;
  
  // 星星粒子数组
  let stars = [];
  const maxStars = 200;
  
  // 窗口大小变化时重置画布尺寸
  window.addEventListener('resize', function() {
    width = canvas.width = window.innerWidth;
    height = canvas.height = window.innerHeight;
    initStars();
  });
  
  // 初始化星星
  function initStars() {
    stars = [];
    for (let i = 0; i < maxStars; i++) {
      stars.push({
        x: Math.random() * width,
        y: Math.random() * height,
        radius: Math.random() * 1.5 + 0.5,
        speed: Math.random() * 0.05 + 0.02,
        brightness: Math.random() * 0.2 + 0.4,
        color: generateStarColor()
      });
    }
  }
  
  // 生成星星颜色，与LOGO色调相似
  function generateStarColor() {
    const colors = [
      'rgba(255, 215, 0, alpha)', // 金色
      'rgba(255, 230, 130, alpha)', // 淡金色
      'rgba(250, 250, 250, alpha)'  // 白色
    ];
    const color = colors[Math.floor(Math.random() * colors.length)];
    const alpha = Math.random() * 0.7 + 0.3;
    return color.replace('alpha', alpha);
  }
  
  // 绘制星星
  function drawStars() {
    ctx.clearRect(0, 0, width, height);
    ctx.globalCompositeOperation = 'lighter';
    
    for (let i = 0; i < stars.length; i++) {
      const star = stars[i];
      
      ctx.beginPath();
      ctx.arc(star.x, star.y, star.radius, 0, Math.PI * 2);
      ctx.fillStyle = star.color;
      ctx.globalAlpha = star.brightness;
      ctx.fill();
      
      // 星星闪烁效果
      star.brightness += (Math.random() - 0.5) * 0.01;
      star.brightness = Math.max(0.3, Math.min(0.7, star.brightness));
      
      // 星星移动
      star.y -= star.speed;
      
      // 如果星星超出屏幕顶部，重置到底部
      if (star.y < -10) {
        star.y = height + 10;
        star.x = Math.random() * width;
      }
    }
  }
  
  // 创建流星
  function createShootingStar() {
    if (Math.random() > 0.97) {
      const shootingStar = {
        x: Math.random() * width,
        y: 0,
        length: Math.random() * 80 + 20,
        speed: Math.random() * 8 + 4,
        angle: Math.PI / 4 + (Math.random() * Math.PI / 8),
        life: 0,
        maxLife: Math.random() * 100 + 50
      };
      
      function drawShootingStar() {
        if (shootingStar.life >= shootingStar.maxLife) return;
        
        shootingStar.life++;
        
        ctx.beginPath();
        ctx.moveTo(shootingStar.x, shootingStar.y);
        
        const tailX = shootingStar.x - Math.cos(shootingStar.angle) * shootingStar.length;
        const tailY = shootingStar.y + Math.sin(shootingStar.angle) * shootingStar.length;
        
        ctx.lineTo(tailX, tailY);
        
        const gradient = ctx.createLinearGradient(
          shootingStar.x, shootingStar.y, 
          tailX, tailY
        );
        
        gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
        gradient.addColorStop(1, 'rgba(255, 215, 0, 0)');
        
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 2;
        ctx.stroke();
        
        // 更新位置
        shootingStar.x += Math.cos(shootingStar.angle) * shootingStar.speed;
        shootingStar.y -= Math.sin(shootingStar.angle) * shootingStar.speed;
        
        // 下一帧绘制
        if (shootingStar.life < shootingStar.maxLife) {
          requestAnimationFrame(drawShootingStar);
        }
      }
      
      drawShootingStar();
    }
  }
  
  // 初始化并开始动画
  initStars();
  
  function animate() {
    drawStars();
    createShootingStar();
    requestAnimationFrame(animate);
  }
  
  animate();
}); 