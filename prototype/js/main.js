document.addEventListener('DOMContentLoaded', function() {
  // 检查当前页面是否为主页
  if (window.location.pathname.includes('index.html') || window.location.pathname.endsWith('/prototype/')) {
    // 强制隐藏所有滚动条函数 - 只针对iframe内容，不影响主页面
    const hideAllScrollbars = function(doc) {
      // 创建并添加隐藏所有滚动条的样式 - 只针对iframe内容
      const noScrollbarsStyle = doc.createElement('style');
      noScrollbarsStyle.textContent = `
        * {
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
        }
        *::-webkit-scrollbar {
          width: 0px !important;
          height: 0px !important;
          background: transparent !important;
          display: none !important;
        }
        body, .phone-container {
          overflow: hidden !important;
        }
        .content {
          overflow-y: auto !important;
          overflow-x: hidden !important;
          -webkit-overflow-scrolling: touch;
        }
      `;
      
      // 添加到文档头部
      doc.head.appendChild(noScrollbarsStyle);
      
      // 直接应用到所有可能有滚动条的元素
      const scrollableElements = doc.querySelectorAll('div, section, main, article, aside, nav');
      scrollableElements.forEach(el => {
        if (el.classList.contains('content')) {
          el.style.overflowY = 'auto';
          el.style.overflowX = 'hidden';
        } else {
          el.style.overflow = 'hidden';
        }
      });
    };
    
    // 不应用到主页，只应用到iframe内部
    // hideAllScrollbars(document);
    
    // 为iframe添加加载完成事件，调整iframe内容
    const iframes = document.querySelectorAll('iframe');
    iframes.forEach(iframe => {
      iframe.onload = function() {
        // 尝试访问iframe内容并调整样式
        try {
          const iframeDocument = this.contentDocument;
          const iframeBody = iframeDocument.body;
          
          // 为iframe内容强制隐藏所有滚动条
          hideAllScrollbars(iframeDocument);
          
          // 为iframe内的body添加标识类
          iframeBody.classList.add('phone-page');
          
          // 移除iframe内容的滚动条
          iframeBody.style.overflow = 'hidden';
          
          // 确保iframe内部的phone-container正确显示
          const phoneContainer = iframeBody.querySelector('.phone-container');
          if (phoneContainer) {
            // 检查当前页面类型
            const isProfilePage = window.location.pathname.includes('profile.html');
            const isLoginPage = window.location.pathname.includes('login.html');
            const isWelcomePage = window.location.pathname.includes('welcome.html');
            const isTransactionPage = window.location.pathname.includes('transaction');
            const isAnalysisPage = window.location.pathname.includes('analysis.html');
            const isTransactionsListPage = window.location.pathname.includes('transactions.html');
            
            // 添加 has-tab-bar 类来控制高度，根据页面类型调整
            if (phoneContainer.classList.contains('has-tab-bar')) {
              phoneContainer.classList.add('has-tab-bar');
              // 账单列表和数据分析页面需要显示更多内容，减少底部空白
              if (isTransactionsListPage || isAnalysisPage) {
                phoneContainer.style.height = 'calc(100% - 44px - 50px - 70px)';
                phoneContainer.style.paddingBottom = '20px'; // 增加底部填充确保内容完全显示
              } else {
                // 适应iPhone 15 Pro Max的高度计算
                phoneContainer.style.height = 'calc(100% - 44px - 50px - 90px)';
              }
            } else {
              // 没有标签栏的页面
              // 登录页和欢迎页减少内容区域高度，避免底部空白
              if (isLoginPage || isWelcomePage) {
                phoneContainer.style.height = 'calc(100% - 44px - 50px - 40px)';
                phoneContainer.style.paddingBottom = '0';
              } else if (isProfilePage) {
                // 个人信息页需要为底部按钮留出统一空间
                phoneContainer.style.height = 'calc(100% - 44px - 50px - 60px)';
                phoneContainer.style.paddingBottom = '80px'; // 统一底部空间
              } else if (isTransactionPage) {
                // 交易确认页面减少底部空白
                phoneContainer.style.height = 'calc(100% - 44px - 50px - 20px)';
                phoneContainer.style.paddingBottom = '5px';
              } else {
                phoneContainer.style.height = 'calc(100% - 44px - 50px)';
              }
            }
            
            // 调整手机容器样式
            phoneContainer.style.margin = '0';
            phoneContainer.style.width = '430px'; // iPhone 15 Pro Max宽度
            phoneContainer.style.height = phoneContainer.style.height;
            phoneContainer.style.border = '0'; // 移除边框以更好显示在iframe中
            phoneContainer.style.borderRadius = '0';
            phoneContainer.style.boxShadow = 'none';
            phoneContainer.style.transform = 'none';
            phoneContainer.style.position = 'absolute';
            phoneContainer.style.top = '0';
            phoneContainer.style.left = '0';
            
            // 添加样式元素以确保正确显示
            const style = iframeDocument.createElement('style');
            style.textContent = `
              body {
                margin: 0;
                padding: 0;
                overflow: hidden;
                display: flex;
                justify-content: flex-start;
                align-items: flex-start;
                background-color: white;
                width: 430px;
                height: 932px;
              }
              .phone-container {
                width: 430px;
                height: 932px;
                overflow: hidden;
                margin: 0;
                padding: 0;
                border: none;
                border-radius: 0;
                box-shadow: none;
                transform: none;
                background-color: white;
                position: absolute;
                top: 0;
                left: 0;
              }
              /* 确保iframe内部所有元素位置正确 */
              .status-bar, .nav-bar, .tab-bar {
                width: 100%;
                left: 0;
                right: 0;
              }
              .content {
                padding: 20px;
                overflow-x: hidden !important;
                overflow-y: auto !important;
                -webkit-overflow-scrolling: touch;
                position: relative;
              }
              /* 微型滚动条 - 只在需要滚动时可见 */
              .content::-webkit-scrollbar {
                width: 3px;
                background: transparent;
              }
              .content::-webkit-scrollbar-thumb {
                background-color: rgba(0,0,0,0.15);
                border-radius: 3px;
              }
              .content::-webkit-scrollbar-thumb:hover {
                background-color: rgba(0,0,0,0.25);
              }
              /* 隐藏其他区域的滚动条 */
              body::-webkit-scrollbar,
              .phone-container::-webkit-scrollbar,
              .phone-container > *:not(.content)::-webkit-scrollbar {
                width: 0px !important;
                display: none !important;
              }
              /* 处理所有可能的滚动容器 */
              .phone-container > *:not(.content) {
                overflow: hidden !important;
              }
            `;
            iframeDocument.head.appendChild(style);
            
            // 检测是否有底部标签栏
            const tabBar = phoneContainer.querySelector('.tab-bar');
            if (tabBar) {
              // 有底部标签栏时，添加标识类
              phoneContainer.classList.add('has-tab-bar');
              // 调整底部标签栏样式
              tabBar.style.height = '90px';
              tabBar.style.paddingBottom = '10px';
            }
            
            // 调整导航栏高度
            const navBar = phoneContainer.querySelector('.nav-bar');
            if (navBar) {
              navBar.style.height = '50px';
              navBar.style.padding = '0 20px';
            }
            
            // 调整状态栏
            const statusBar = phoneContainer.querySelector('.status-bar');
            if (statusBar) {
              statusBar.style.padding = '0 20px';
            }
            
            // 确保content区域内可以滚动，但不会影响到外部
            const contentDiv = phoneContainer.querySelector('.content');
            if (contentDiv) {
              contentDiv.style.padding = '20px';
              contentDiv.style.overflowX = 'hidden';
              contentDiv.style.overflowY = 'auto';
              contentDiv.style.WebkitOverflowScrolling = 'touch';
              
              // 使用优化后的滚动条样式而不是完全隐藏
              const contentStyle = document.createElement('style');
              contentStyle.textContent = `
                /* 微型滚动条 - 只在需要滚动时可见 */
                .content::-webkit-scrollbar {
                  width: 3px;
                  background: transparent;
                }
                .content::-webkit-scrollbar-thumb {
                  background-color: rgba(0,0,0,0.15);
                  border-radius: 3px;
                }
                .content::-webkit-scrollbar-thumb:hover {
                  background-color: rgba(0,0,0,0.25);
                }
              `;
              iframeDocument.head.appendChild(contentStyle);
              
              // 处理内容滚动区域
              contentDiv.addEventListener('scroll', function() {
                // 检查是否滚动到底部，确保底部内容显示完整
                const isNearBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 20;
                if (isNearBottom) {
                  // 接近底部，确保有足够的底部填充让用户能看到所有内容
                  this.style.paddingBottom = '20px';
                }
              });
              
              // 确保内容区域底部不留空白
              const adjustContentHeight = function() {
                // 检查当前页面类型
                const isProfilePage = window.location.pathname.includes('profile.html');
                const isLoginPage = window.location.pathname.includes('login.html');
                const isWelcomePage = window.location.pathname.includes('welcome.html');
                const isTransactionPage = window.location.pathname.includes('transaction');
                
                // 添加 has-tab-bar 类来控制高度，根据页面类型调整
                if (tabBar) {
                  contentDiv.parentElement.classList.add('has-tab-bar');
                  // 账单列表等页面需要显示更多内容，减少底部空白
                  if (window.location.pathname.includes('transactions.html')) {
                    contentDiv.style.height = 'calc(100% - 44px - 50px - 80px)';
                    contentDiv.style.paddingBottom = '5px';
                  } else {
                    // 适应iPhone 15 Pro Max的高度计算
                    contentDiv.style.height = 'calc(100% - 44px - 50px - 90px)';
                  }
                } else {
                  // 没有标签栏的页面
                  // 登录页和欢迎页减少内容区域高度，避免底部空白
                  if (isLoginPage || isWelcomePage) {
                    contentDiv.style.height = 'calc(100% - 44px - 50px - 40px)';
                    contentDiv.style.paddingBottom = '0';
                  } else if (isProfilePage) {
                    // 个人信息页需要更多空间显示底部按钮
                    contentDiv.style.height = 'calc(100% - 44px - 50px + 40px)';
                    contentDiv.style.paddingBottom = '80px';
                  } else if (isTransactionPage) {
                    // 交易确认页面减少底部空白
                    contentDiv.style.height = 'calc(100% - 44px - 50px - 20px)';
                    contentDiv.style.paddingBottom = '5px';
                  } else {
                    contentDiv.style.height = 'calc(100% - 44px - 50px)';
                  }
                }
                
                // 检查是否有退出登录等底部固定按钮
                const bottomButtons = phoneContainer.querySelectorAll('.btn:last-child, .btn-primary:last-child, button:last-child');
                bottomButtons.forEach(button => {
                  const rect = button.getBoundingClientRect();
                  const containerRect = contentDiv.getBoundingClientRect();
                  
                  // 如果按钮位于内容区域底部附近，确保其可见
                  if (rect.bottom > containerRect.bottom - 20) {
                    button.style.marginBottom = '0';
                    // 如果是设置页面的退出登录，给予特殊处理
                    if (button.textContent.includes('退出登录') || button.textContent.includes('注销账号')) {
                      button.style.position = 'absolute';
                      button.style.bottom = '25px';
                      button.style.left = '50%';
                      button.style.transform = 'translateX(-50%)';
                      button.style.width = '90%';
                      button.style.padding = '15px 0';
                    }
                  }
                });
              };
              
              // 处理设置页面的特殊情况
              if (window.location.pathname.includes('settings.html')) {
                // 在设置页面，确保底部区域不会被错误截断
                contentDiv.style.paddingBottom = '80px';
              } else if (window.location.pathname.includes('profile.html')) {
                // 个人信息页面需要显示退出登录按钮
                contentDiv.style.paddingBottom = '80px';
              }
              
              // 调整高度
              adjustContentHeight();
              // 窗口大小改变时重新调整
              window.addEventListener('resize', adjustContentHeight);
            }
          }
        } catch(e) {
          console.log('无法访问iframe内容:', e);
        }
      };
    });
  } else {
    // 强制隐藏所有滚动条函数 - 只针对非主页面的移动端容器内部
    const hideAllScrollbars = function(doc) {
      // 创建并添加针对性隐藏滚动条的样式
      const noScrollbarsStyle = doc.createElement('style');
      noScrollbarsStyle.textContent = `
        /* 只针对移动端容器内部 */
        .phone-container > *:not(.content) {
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
          overflow: hidden !important;
        }
        .phone-container > *:not(.content)::-webkit-scrollbar {
          width: 0px !important;
          height: 0px !important;
          background: transparent !important;
          display: none !important;
        }
        /* 内容区域使用美观的小型滚动条 */
        .content {
          overflow-y: auto !important;
          overflow-x: hidden !important;
          -webkit-overflow-scrolling: touch;
        }
        /* 微型滚动条 - 只在需要滚动时可见 */
        .content::-webkit-scrollbar {
          width: 3px;
          background: transparent;
        }
        .content::-webkit-scrollbar-thumb {
          background-color: rgba(0,0,0,0.15);
          border-radius: 3px;
        }
        .content::-webkit-scrollbar-thumb:hover {
          background-color: rgba(0,0,0,0.25);
        }
      `;
      
      // 添加到文档头部
      doc.head.appendChild(noScrollbarsStyle);
    };
    
    // 为当前页面应用隐藏滚动条
    hideAllScrollbars(document);
    
    // 为当前页面body添加类
    document.body.classList.add('phone-page');
    
    // 设置根变量以适应iPhone 15 Pro Max
    document.documentElement.style.setProperty('--device-width', '430px');
    document.documentElement.style.setProperty('--device-height', '932px');
    
    // 检测底部标签栏
    const tabBar = document.querySelector('.tab-bar');
    if (tabBar) {
      document.body.classList.add('has-tab-bar');
      // 调整底部标签栏高度
      tabBar.style.height = '90px';
    }
    
    // 调整导航栏
    const navBar = document.querySelector('.nav-bar');
    if (navBar) {
      navBar.style.height = '50px';
    }
    
    // 为所有可点击元素添加点击效果
    const clickableElements = document.querySelectorAll('button, .tab-item, .list-item, .card a, .nav-left, .nav-right');
    
    clickableElements.forEach(element => {
      element.addEventListener('click', function(e) {
        // 添加点击效果
        this.style.opacity = '0.7';
        setTimeout(() => {
          this.style.opacity = '1';
        }, 200);
        
        // 阻止默认行为
        e.preventDefault();
      });
    });
    
    // 底部标签栏切换
    const tabItems = document.querySelectorAll('.tab-item');
    tabItems.forEach(tab => {
      tab.addEventListener('click', function() {
        // 移除所有激活状态
        tabItems.forEach(t => {
          t.querySelector('.tab-icon').classList.remove('active');
          t.querySelector('.tab-label').classList.remove('active');
        });
        
        // 添加当前激活状态
        this.querySelector('.tab-icon').classList.add('active');
        this.querySelector('.tab-label').classList.add('active');
      });
    });
    
    // 浮动按钮点击效果
    const fab = document.querySelector('.fab');
    if (fab) {
      fab.addEventListener('click', function() {
        this.style.transform = 'scale(0.9)';
        setTimeout(() => {
          this.style.transform = 'scale(1)';
        }, 200);
      });
    }
    
    // 语音录制动画
    const voiceButton = document.querySelector('.voice-button');
    if (voiceButton) {
      voiceButton.addEventListener('click', function() {
        const recordingWaves = document.querySelector('.recording-waves');
        if (recordingWaves) {
        if (recordingWaves.classList.contains('animated-wave')) {
          recordingWaves.classList.remove('animated-wave');
        } else {
          recordingWaves.classList.add('animated-wave');
          }
        }
      });
    }
    
    // 确保内容区域正确滚动和显示
    const contentDiv = document.querySelector('.content');
    if (contentDiv) {
      // 设置自动滚动支持
      contentDiv.style.overflowX = 'hidden';
      contentDiv.style.overflowY = 'auto';
      contentDiv.style.WebkitOverflowScrolling = 'touch';
      contentDiv.style.padding = '20px';
      
      // 使用优化后的滚动条样式而不是完全隐藏
      const contentStyle = document.createElement('style');
      contentStyle.textContent = `
        /* 微型滚动条 - 只在需要滚动时可见 */
        .content::-webkit-scrollbar {
          width: 3px;
          background: transparent;
        }
        .content::-webkit-scrollbar-thumb {
          background-color: rgba(0,0,0,0.15);
          border-radius: 3px;
        }
        .content::-webkit-scrollbar-thumb:hover {
          background-color: rgba(0,0,0,0.25);
        }
      `;
      document.head.appendChild(contentStyle);
      
      // 处理内容滚动区域
      contentDiv.addEventListener('scroll', function() {
        // 检查是否滚动到底部，确保底部内容显示完整
        const isNearBottom = this.scrollHeight - this.scrollTop - this.clientHeight < 20;
        if (isNearBottom) {
          // 接近底部，确保有足够的底部填充让用户能看到所有内容
          this.style.paddingBottom = '20px';
        }
      });
      
      // 调整内容区域高度
      const phoneContainer = document.querySelector('.phone-container');
      if (phoneContainer) {
        // 检查当前页面类型
        const isProfilePage = window.location.pathname.includes('profile.html');
        const isSettingsPage = window.location.pathname.includes('settings.html');
        const isLoginPage = window.location.pathname.includes('login.html');
        const isWelcomePage = window.location.pathname.includes('welcome.html');
        const isTransactionPage = window.location.pathname.includes('transaction');
        const isTransactionsListPage = window.location.pathname.includes('transactions.html');
        const isAnalysisPage = window.location.pathname.includes('analysis.html');
        
        if (tabBar) {
          // 有底部标签栏的页面
          if (isTransactionsListPage || isAnalysisPage) {
            // 账单列表和数据分析页面需要更多内容区域
            contentDiv.style.height = 'calc(100% - 44px - 50px - 70px)';
            contentDiv.style.paddingBottom = '20px'; // 确保内容完全显示
          } else {
            // 有标签栏的其他页面
            contentDiv.style.height = 'calc(100% - 44px - 50px - 90px)';
          }
        } else {
          // 没有标签栏的页面
          if (isLoginPage || isWelcomePage) {
            // 登录页和欢迎页减少内容区域高度，避免底部空白
            contentDiv.style.height = 'calc(100% - 44px - 50px - 40px)';
            contentDiv.style.paddingBottom = '0';
          } else if (isProfilePage || isSettingsPage) {
            // 个人信息页和设置页面统一底部空间标准
            contentDiv.style.height = 'calc(100% - 44px - 50px - 60px)';
            contentDiv.style.paddingBottom = '80px'; // 统一底部空间
          } else if (isTransactionPage) {
            // 交易确认页面减少底部空白
            contentDiv.style.height = 'calc(100% - 44px - 50px - 10px)';
            contentDiv.style.paddingBottom = '0';
          } else {
            // 其他页面默认高度
            contentDiv.style.height = 'calc(100% - 44px - 50px)';
          }
        }
      }
      
      // 特殊处理设置、个人信息页面和有底部按钮的页面
      if (window.location.pathname.includes('settings.html')) {
        // 设置页面底部区域处理
        contentDiv.style.paddingBottom = '80px'; // 统一与个人信息页面一致
        
        // 查找并调整退出登录按钮位置
        const logoutButton = document.querySelector('.btn-danger, .btn-outline');
        if (logoutButton) {
          logoutButton.className = 'btn btn-danger';
          logoutButton.style.marginBottom = '30px'; // 增加间距
          logoutButton.style.width = '100%';
          logoutButton.style.padding = '12px';
        }
      } else if (window.location.pathname.includes('profile.html')) {
        // 个人信息页面需要显示退出登录按钮
        contentDiv.style.paddingBottom = '80px'; // 与设置页面保持一致
        
        // 查找并调整退出登录按钮位置
        const logoutButtonContainer = document.querySelector('.btn-danger').parentElement;
        if (logoutButtonContainer) {
          logoutButtonContainer.style.position = 'sticky';
          logoutButtonContainer.style.bottom = '15px';
          logoutButtonContainer.style.backgroundColor = 'white';
          logoutButtonContainer.style.padding = '10px 0';
          logoutButtonContainer.style.marginTop = '10px';
          logoutButtonContainer.style.marginBottom = '30px'; // 增加间距
          
          const logoutButton = logoutButtonContainer.querySelector('.btn-danger');
          logoutButton.style.width = '100%';
          logoutButton.style.padding = '12px';
        }
      } else if (window.location.pathname.includes('transactions.html')) {
        // 账单列表页面特殊处理，确保底部内容可见
        contentDiv.style.paddingBottom = '20px'; // 增加底部填充确保内容完全显示
        
        // 特殊处理fab按钮位置，确保不遮挡内容
        const fab = document.querySelector('.fab');
        if (fab) {
          fab.style.bottom = '105px';
        }
      } else if (window.location.pathname.includes('analysis.html')) {
        // 数据分析页面特殊处理，确保底部数据完全显示
        contentDiv.style.paddingBottom = '20px'; // 增加底部填充确保内容完全显示
        
        // 确保底部预算执行情况完全显示
        const budgetItems = document.querySelectorAll('.card:last-child');
        budgetItems.forEach(item => {
          item.style.marginBottom = '20px'; // 增加底部预算卡片的底部间距
        });
      }
    }
  }

  // 给特定页面添加自定义处理脚本
  const isTransactionsPage = window.location.pathname.includes('transactions.html');
  const isAnalysisPage = window.location.pathname.includes('analysis.html');
  
  if (isTransactionsPage || isAnalysisPage) {
    // 获取内容区域
    const contentDiv = document.querySelector('.content');
    
    if (contentDiv) {
      // 调整内容区域为可滚动
      contentDiv.style.height = 'calc(100% - 44px - 50px - 60px)'; // 减少高度给底部留出空间
      contentDiv.style.overflowY = 'auto';
      contentDiv.style.paddingBottom = '60px'; // 增加底部内容填充
      
      // 修改最后一个卡片的底部边距
      const lastCard = document.querySelector('.card:last-of-type');
      if (lastCard) {
        lastCard.style.marginBottom = '30px';
      }
      
      // 调整浮动按钮位置，以避免遮挡内容
      if (isTransactionsPage) {
        const fab = document.querySelector('.fab');
        if (fab) {
          fab.style.bottom = '100px'; // 只调整账单列表页面的浮动按钮位置
        }
      }
    }
  }
});