// 功能引导动画
document.addEventListener('DOMContentLoaded', function() {
  // 头像挥手动画
  const avatar = document.querySelector('.avatar-container img');
  if (avatar) {
    setInterval(function() {
      avatar.classList.add('wave-animation');
      
      setTimeout(function() {
        avatar.classList.remove('wave-animation');
      }, 1000);
    }, 5000);
  }
  
  // 功能球动画
  const functionBalls = document.querySelectorAll('.function-ball');
  
  functionBalls.forEach((ball, index) => {
    // 错开时间依次显示
    setTimeout(() => {
      ball.classList.add('show');
      
      // 添加悬浮动画
      setInterval(() => {
        // 交替上下浮动效果
        ball.classList.add('float');
        
        setTimeout(() => {
          ball.classList.remove('float');
        }, 2000);
      }, 4000);
      
      // 为每个功能球添加点击动画效果
      ball.addEventListener('click', function() {
        this.classList.add('pulse');
        
        setTimeout(() => {
          this.classList.remove('pulse');
        }, 500);
      });
      
      // 添加功能特有的动画
      const icon = ball.querySelector('.function-icon');
      
      if (icon) {
        // 针对不同功能添加特定动画
        if (index === 0) { // 拍照记账
          setInterval(() => {
            icon.classList.add('scan-animation');
            
            setTimeout(() => {
              icon.classList.remove('scan-animation');
            }, 1500);
          }, 7000);
        } 
        else if (index === 1) { // 截图记账
          setInterval(() => {
            icon.classList.add('frame-animation');
            
            setTimeout(() => {
              icon.classList.remove('frame-animation');
            }, 1500);
          }, 8000);
        }
        else if (index === 2) { // 智能问答
          setInterval(() => {
            icon.classList.add('bubble-animation');
            
            setTimeout(() => {
              icon.classList.remove('bubble-animation');
            }, 1500);
          }, 9000);
        }
      }
    }, index * 300);
  });
}); 