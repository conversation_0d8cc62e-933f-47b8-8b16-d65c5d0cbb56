<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>账单提醒 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden; /* 防止页面整体滚动 */
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      overflow-x: hidden; /* 防止水平滚动 */
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box; /* 确保padding不会增加宽度 */
    }
    
    /* 选项卡样式 */
    .tabs {
      display: flex;
      background-color: #fff;
      border-radius: 16px;
      overflow: hidden;
      margin-bottom: 15px;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
      position: relative;
    }
    
    .tab {
      flex: 1;
      text-align: center;
      padding: 12px 8px;
      font-size: 14px;
      font-weight: 500;
      color: #666;
      cursor: pointer;
      position: relative;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    .tab.active {
      color: #FF6B35;
    }
    
    .tab.active::after {
      content: "";
      display: block;
      width: 20px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 16px;
      padding: 18px;
      margin-bottom: 15px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    /* 提醒项目列表样式 */
    .reminder-list {
      margin-bottom: 15px;
    }
    
    .reminder-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.06);
    }
    
    .reminder-item:last-child {
      border-bottom: none;
    }
    
    .reminder-icon {
      width: 44px;
      height: 44px;
      border-radius: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 14px;
    }
    
    .reminder-details {
      flex: 1;
    }
    
    .reminder-title {
      font-weight: 500;
      font-size: 15px;
      margin-bottom: 4px;
      color: #333;
    }
    
    .reminder-subtitle {
      font-size: 13px;
      color: #999;
    }
    
    .reminder-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;
    }
    
    .reminder-amount {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 4px;
    }
    
    .reminder-status {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 10px;
      font-weight: 500;
    }
    
    .status-urgent {
      color: #E11D48;
      background-color: #FFE4E6;
    }
    
    .status-pending {
      color: #F59E0B;
      background-color: #FEF3C7;
    }
    
    .status-paid {
      color: #10B981;
      background-color: #ECFDF5;
    }
    
    /* 底部添加按钮 */
    .add-button {
      position: absolute;
      bottom: 80px;
      right: 25px;
      width: 56px;
      height: 56px;
      border-radius: 28px;
      background-color: #FF6B35;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
      z-index: 100;
    }
    
    /* 空状态样式 */
    .empty-state {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 40px 0;
      color: #999;
    }
    
    .empty-state i {
      font-size: 40px;
      margin-bottom: 16px;
      color: #ddd;
    }
    
    .empty-state-text {
      font-size: 14px;
      text-align: center;
    }
    
    /* 底部标签栏 */
    .tab-bar {
      height: 64px;
      background-color: white;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      flex-shrink: 0;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
    }
    
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 100%;
    }
    
    .tab-icon {
      font-size: 20px;
      color: #999;
      margin-bottom: 4px;
    }
    
    .tab-label {
      font-size: 12px;
      color: #999;
    }
    
    .tab-icon.active {
      color: #FF6B35;
    }
    
    .tab-label.active {
      color: #FF6B35;
      font-weight: 500;
    }
    
    /* 标签内容区域 */
    .tab-content {
      display: none;
    }
    
    .tab-content.active {
      display: block;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left" style="color: #666;" id="back-button"></i>
      </div>
      <div class="nav-title">账单提醒</div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 选项卡 -->
      <div class="tabs">
        <div class="tab active" data-tab="due-reminders">到期提醒</div>
        <div class="tab" data-tab="installments">分期付款</div>
        <div class="tab" data-tab="recurring">定期账单</div>
        <div class="tab" data-tab="custom">自定义提醒</div>
      </div>
      
      <!-- 到期提醒内容 -->
      <div class="tab-content active" id="due-reminders">
        <div class="card reminder-list">
          <!-- 提醒项目 -->
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #FFF5F2;">
                <i class="fas fa-credit-card" style="color: #FF6B35; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">信用卡还款</div>
                <div class="reminder-subtitle">工商银行信用卡</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥2,580.00</div>
              <div class="reminder-status status-urgent">2天后到期</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #E0F2FE;">
                <i class="fas fa-home" style="color: #0EA5E9; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">房租</div>
                <div class="reminder-subtitle">本月房租</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥3,500.00</div>
              <div class="reminder-status status-pending">7天后到期</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #ECFDF5;">
                <i class="fas fa-bolt" style="color: #10B981; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">水电费</div>
                <div class="reminder-subtitle">6月水电费</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥240.50</div>
              <div class="reminder-status status-pending">10天后到期</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 分期付款内容 -->
      <div class="tab-content" id="installments">
        <div class="card reminder-list">
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #FFF5F2;">
                <i class="fas fa-mobile-alt" style="color: #FF6B35; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">iPhone 14 Pro</div>
                <div class="reminder-subtitle">12期 · 第6期/12期</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥830.00</div>
              <div class="reminder-status status-pending">5天后到期</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #F3E8FF;">
                <i class="fas fa-laptop" style="color: #8B5CF6; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">MacBook Air</div>
                <div class="reminder-subtitle">24期 · 第9期/24期</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥550.00</div>
              <div class="reminder-status status-paid">已支付</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #FFF6E9;">
                <i class="fas fa-couch" style="color: #FFA53D; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">家具套装</div>
                <div class="reminder-subtitle">6期 · 第2期/6期</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥1,200.00</div>
              <div class="reminder-status status-pending">15天后到期</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 定期账单内容 -->
      <div class="tab-content" id="recurring">
        <div class="card reminder-list">
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #FEF3C7;">
                <i class="fas fa-wifi" style="color: #F59E0B; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">宽带费</div>
                <div class="reminder-subtitle">每月20日 · 自动扣款</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥99.00</div>
              <div class="reminder-status status-pending">8天后到期</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #F0F9FF;">
                <i class="fas fa-video" style="color: #2878FF; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">视频会员</div>
                <div class="reminder-subtitle">每月1日 · 自动续费</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥45.00</div>
              <div class="reminder-status status-paid">已支付</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #FFF5F2;">
                <i class="fas fa-music" style="color: #FF6B35; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">音乐会员</div>
                <div class="reminder-subtitle">每月15日 · 手动支付</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥30.00</div>
              <div class="reminder-status status-pending">3天后到期</div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 自定义提醒内容 -->
      <div class="tab-content" id="custom">
        <div class="card reminder-list">
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #E0F2FE;">
                <i class="fas fa-graduation-cap" style="color: #0EA5E9; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">学费</div>
                <div class="reminder-subtitle">每学期</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥12,000.00</div>
              <div class="reminder-status status-pending">30天后到期</div>
            </div>
          </div>
          
          <div class="reminder-item">
            <div style="display: flex; align-items: center;">
              <div class="reminder-icon" style="background-color: #ECFDF5;">
                <i class="fas fa-car" style="color: #10B981; font-size: 18px;"></i>
              </div>
              <div class="reminder-details">
                <div class="reminder-title">汽车保险</div>
                <div class="reminder-subtitle">每年一次</div>
              </div>
            </div>
            <div class="reminder-right">
              <div class="reminder-amount">￥4,800.00</div>
              <div class="reminder-status status-pending">45天后到期</div>
            </div>
          </div>
        </div>
        
        <div class="empty-state">
          <i class="fas fa-plus-circle"></i>
          <div class="empty-state-text">点击下方添加按钮创建更多自定义提醒</div>
        </div>
      </div>
    </div>
    
    <!-- 添加提醒按钮 -->
    <div class="add-button">
      <i class="fas fa-plus"></i>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <div class="tab-label">首页</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-list-ul tab-icon active"></i>
        <div class="tab-label active">账单</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-wallet tab-icon"></i>
        <div class="tab-label">资产</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-chart-pie tab-icon"></i>
        <div class="tab-label">分析</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <div class="tab-label">我的</div>
      </div>
    </div>
  </div>

  <script>
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 选项卡切换功能
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          // 移除所有选项卡的active类
          tabs.forEach(t => t.classList.remove('active'));
          // 给当前点击的选项卡添加active类
          this.classList.add('active');
          
          // 隐藏所有内容
          const tabContents = document.querySelectorAll('.tab-content');
          tabContents.forEach(content => content.classList.remove('active'));
          
          // 显示对应的内容
          const tabId = this.getAttribute('data-tab');
          document.getElementById(tabId).classList.add('active');
        });
      });
      
      // 返回按钮点击事件
      const backButton = document.getElementById('back-button');
      backButton.addEventListener('click', function() {
        window.location.href = 'transactions.html';
      });
      
      // 添加按钮点击事件
      const addButton = document.querySelector('.add-button');
      addButton.addEventListener('click', function() {
        alert('创建新的账单提醒');
        // 实际项目中可以弹出创建提醒的表单或跳转到创建页面
      });
      
      // 底部标签栏点击事件
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach((item, index) => {
        item.addEventListener('click', function() {
          switch(index) {
            case 0: // 首页
              window.location.href = 'home.html';
              break;
            case 1: // 账单
              window.location.href = 'transactions.html';
              break;
            case 2: // 资产
              window.location.href = 'assets.html';
              break;
            case 3: // 分析
              window.location.href = 'analysis.html';
              break;
            case 4: // 我的
              window.location.href = 'profile-new.html';
              break;
          }
        });
      });
    });
  </script>
</body>
</html>