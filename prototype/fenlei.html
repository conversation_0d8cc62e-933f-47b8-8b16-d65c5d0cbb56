<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>分类设置</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            /* Variables */
            --color-primary: #FF6B35;
            --color-primary-darker: #e65a20;
            --text-primary: #333;
            --text-secondary: #666;
            --text-light: #999;
            --text-on-primary: #fff;
            --bg-white: #fff;
            --bg-light-gray: #f9f9f9;
            --bg-medium-gray: #f5f5f5;
            --bg-dark-gray: #eee;
            --border-color: #f0f0f0;
            --border-color-medium: #e0e0e0;
            --delete-red: #F44336;
            --placeholder-icon-color: #ccc;
            --radius-pill: 50px;
            --radius-large: 16px;
            --radius-medium: 12px;
            --radius-small: 8px;
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
             /* FIX: Increased default status bar height again for more clearance */
             --status-bar-height: env(safe-area-inset-top, 48px);
            --safe-area-bottom: env(safe-area-inset-bottom, 0px);
            --tab-height: 44px;
            --nav-bar-height: 44px;
            --add-button-height: 50px;
            --icon-preview-size: 40px;
        }

        html,
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-primary); background-color: #ccc; margin: 0; padding: 0;
            height: 100%; display: flex; justify-content: center; overflow: hidden;
        }

        .phone-container {
            width: 375px; height: 812px; background-color: var(--bg-white);
            border-radius: 40px; overflow: hidden; position: relative;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            display: flex; flex-direction: column; border: 12px solid #111;
             /* Removed container padding-top, handle spacing within nav-bar */
        }

        .status-bar {
            height: var(--status-bar-height); display: flex; justify-content: space-between;
            align-items: center; padding: 0 15px; box-sizing: border-box;
            font-size: 14px; color: var(--text-primary);
            /* Position absolute relative to phone container */
            position: absolute; top: 12px; left: 12px; right: 12px;
            z-index: 10; pointer-events: none;
             /* background: rgba(0, 255, 0, 0.1); /* Debugging */
        }
        .status-bar-left { font-weight: 600; }
        .status-bar-right { display: flex; align-items: center; gap: 6px;}

        .nav-bar {
            background-color: var(--bg-white);
            /* FIX: Ensure padding-top is sufficient */
            padding-top: var(--status-bar-height);
            display: flex; align-items: center; justify-content: center;
            position: relative; flex-shrink: 0; border-bottom: 1px solid var(--border-color);
            z-index: 5; height: var(--nav-bar-height); box-sizing: content-box;
            /* background: rgba(255, 0, 0, 0.1); /* Debugging */
        }
        .nav-back-btn {
            position: absolute; left: 10px;
            /* FIX: Center vertically within the 44px nav bar area, accounting for status bar padding */
            top: calc(var(--status-bar-height) + (var(--nav-bar-height) / 2));
            transform: translateY(-50%);
            font-size: 22px; color: var(--text-secondary); cursor: pointer;
            z-index: 6; padding: 8px;
        }
        .nav-title { font-size: 17px; font-weight: 600; color: var(--text-primary); }

        .tabs-container {
            display: flex; justify-content: center; padding: 10px 15px;
            background-color: var(--bg-white); border-bottom: 1px solid var(--border-color);
            flex-shrink: 0; position: sticky;
            /* FIX: Correct sticky top position below status + nav */
            top: calc(var(--status-bar-height) + var(--nav-bar-height));
            z-index: 4;
        }
        /* Tab option styles remain the same */
        .tab-option { padding: 8px 35px; font-size: 15px; font-weight: 500; color: var(--text-primary); background-color: var(--bg-medium-gray); border: 1px solid var(--border-color-medium); cursor: pointer; transition: all 0.2s ease; text-align: center; }
        .tab-option:first-child { border-top-left-radius: var(--radius-small); border-bottom-left-radius: var(--radius-small); border-right: none; }
        .tab-option:last-child { border-top-right-radius: var(--radius-small); border-bottom-right-radius: var(--radius-small); }
        .tab-option.active { color: var(--text-on-primary); background-color: var(--color-primary); border-color: var(--color-primary); z-index: 1; }

        /* Content and List styles remain the same */
        .content { flex: 1; overflow-y: auto; background-color: var(--bg-white); position: relative; padding: 0 0 calc(var(--add-button-height) + 20px + var(--safe-area-bottom)); }
        .category-list { list-style: none; padding: 0; margin: 0; }
        .category-list-item { display: flex; align-items: center; padding: 12px 15px; border-bottom: 1px solid var(--border-color); background-color: var(--bg-white); }
        .category-list-item:last-child { border-bottom: none; }
        .delete-btn { background: none; border: none; padding: 5px; margin-right: 10px; cursor: pointer; color: var(--delete-red); font-size: 20px; line-height: 1; }
        .delete-btn i { display: block; }
        .category-icon-bg { width: 36px; height: 36px; border-radius: var(--radius-small); display: flex; justify-content: center; align-items: center; margin-right: 12px; flex-shrink: 0; }
        .category-icon-bg i { font-size: 18px; color: white; } /* Default white */
        .category-icon-bg[style*="background-color: #FFEB3B"] i, /* Yellow */
        .category-icon-bg[style*="background-color: #FFCDD2"] i { /* Pink */
             color: #827717; /* Darker color for light backgrounds */
         }
        .category-name { flex-grow: 1; font-size: 15px; color: var(--text-primary); margin-right: 10px; overflow: hidden; white-space: nowrap; text-overflow: ellipsis; }
        .drag-handle { background: none; border: none; padding: 5px; cursor: grab; color: var(--text-light); font-size: 18px; }
        .drag-handle:active { cursor: grabbing; }
        .category-list[hidden] { display: none; }

        /* Add Button Container styles remain the same */
        .add-category-container { position: absolute; bottom: 0; left: 0; right: 0; padding: 15px 15px calc(15px + var(--safe-area-bottom)); background-color: var(--bg-white); border-top: 1px solid var(--border-color); z-index: 3; }
        .add-category-btn { display: block; width: 100%; padding: 12px; background-color: var(--color-primary); color: var(--text-on-primary); border: none; border-radius: var(--radius-medium); font-size: 16px; font-weight: 500; text-align: center; cursor: pointer; transition: background-color 0.2s ease; }
        .add-category-btn:active { background-color: var(--color-primary-darker); }
        .add-category-btn i { margin-right: 6px; }

        /* --- Modal Styles remain the same --- */
        .modal-overlay { position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-color: rgba(0, 0, 0, 0.4); z-index: 50; display: none; opacity: 0; transition: opacity 0.3s ease; }
        .modal-overlay.visible { display: block; opacity: 1; }
        .modal-content { position: absolute; bottom: 0; left: 0; right: 0; max-height: 85%; background-color: var(--bg-white); border-top-left-radius: var(--radius-large); border-top-right-radius: var(--radius-large); box-shadow: 0 -5px 20px rgba(0, 0, 0, 0.15); display: flex; flex-direction: column; transform: translateY(100%); transition: transform 0.3s ease; }
        .modal-overlay.visible .modal-content { transform: translateY(0); }
        .modal-header { display: flex; align-items: center; justify-content: space-between; padding: 10px 15px; border-bottom: 1px solid var(--border-color); flex-shrink: 0; min-height: 48px; }
        .modal-close-btn, .modal-save-btn { background: none; border: none; font-size: 16px; cursor: pointer; padding: 8px; }
        .modal-close-btn { color: var(--text-secondary); }
        .modal-save-btn { color: var(--color-primary); font-weight: 500; }
        .modal-save-btn:disabled { color: var(--text-light); cursor: not-allowed; }
        .modal-title { font-size: 17px; font-weight: 600; color: var(--text-primary); }
        .modal-body { padding: 25px 15px 25px; overflow-y: auto; flex-grow: 1; }
        .modal-body .input-group { margin-bottom: 30px; display: flex; align-items: center; position: relative; }
        .input-with-icon { display: flex; align-items: center; border: 1px solid var(--border-color-medium); border-radius: var(--radius-medium); background-color: var(--bg-white); padding: 5px 5px 5px 10px; width: 100%; box-sizing: border-box; transition: border-color 0.2s ease, box-shadow 0.2s ease; }
        .input-with-icon:focus-within { border-color: var(--color-primary); box-shadow: 0 0 0 2px rgba(255, 107, 53, 0.15); }
        .selected-icon-preview { width: var(--icon-preview-size); height: var(--icon-preview-size); border-radius: var(--radius-small); margin-right: 10px; display: flex; justify-content: center; align-items: center; background-color: var(--bg-medium-gray); flex-shrink: 0; transition: background-color 0.2s ease; overflow: hidden; }
        .selected-icon-preview i { font-size: 20px; color: var(--placeholder-icon-color); }
        .selected-icon-preview.has-icon { background-color: transparent; }
        .selected-icon-preview .icon-display-bg { width: 100%; height: 100%; border-radius: var(--radius-small); display: flex; justify-content: center; align-items: center; }
        .selected-icon-preview .icon-display-bg i { /* Color set by JS */ }
        .modal-body .category-name-input { font-size: 16px; padding: 0 8px; border: none; background-color: transparent; flex-grow: 1; height: 30px; line-height: 30px; box-shadow: none; outline: none; width: auto; }
        .modal-body .category-name-input::placeholder { color: var(--text-light); }
        .modal-body .category-name-input:focus { box-shadow: none; border-color: transparent; }
        .modal-body .icon-selection-area { margin-top: 0; }
        .modal-body .icon-grid { display: grid; grid-template-columns: repeat(5, 1fr); gap: 12px 10px; }
        .modal-body .icon-section-title { grid-column: 1 / -1; font-size: 13px; color: var(--text-secondary); margin-bottom: 8px; margin-top: 10px; font-weight: 400; text-align: center; }
        .modal-body .icon-section-title:first-child { margin-top: 0; }
        .modal-body .icon-item { padding: 0; display: flex; justify-content: center; }
        .modal-body .icon-bg { width: 48px; height: 48px; border-radius: var(--radius-medium); border: 2px solid transparent; transition: all 0.15s ease-in-out; box-shadow: var(--shadow-light); display: flex; justify-content: center; align-items: center; cursor: pointer; background-color: var(--bg-medium-gray); }
        .modal-body .icon-bg i { font-size: 22px; transition: color 0.15s ease-in-out; color: var(--text-secondary); }
        .modal-body .icon-bg[style*="background-color"] i { color: white; } /* Default */
        .modal-body .icon-bg[style*="background-color: #FFEB3B"] i, /* Yellow */
        .modal-body .icon-bg[style*="background-color: #FFCDD2"] i { /* Pink */
            color: #827717; /* Darker color */
         }
        .modal-body .icon-item.active .icon-bg { border-color: var(--color-primary); box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3); }

    </style>
</head>
<body>
    <div class="phone-container" id="phoneContainer">
        <!-- Status Bar -->
        <div class="status-bar">
             <div class="status-bar-left">9:42</div>
             <div class="status-bar-right">
                 <i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-three-quarters"></i>
             </div>
        </div>

        <!-- Navigation Bar -->
        <div class="nav-bar">
             <div class="nav-back-btn" id="backBtn"><i class="fas fa-chevron-left"></i></div>
             <div class="nav-title">分类设置</div>
        </div>

        <!-- Tabs -->
        <div class="tabs-container">
             <div class="tab-option active" data-type="expense">支出</div>
             <div class="tab-option" data-type="income">收入</div>
        </div>

        <!-- Content Area -->
        <div class="content" id="mainContent">
             <!-- FIX: Restored full expense list -->
             <ul class="category-list" id="expenseList">
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FF6B35;"><i class="fas fa-utensils"></i></div><span class="category-name">餐饮</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #4CAF50;"><i class="fas fa-shopping-cart"></i></div><span class="category-name">购物</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #2196F3;"><i class="fas fa-shopping-basket"></i></div><span class="category-name">日用</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #9C27B0;"><i class="fas fa-bus"></i></div><span class="category-name">交通</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #8BC34A;"><i class="fas fa-carrot"></i></div><span class="category-name">蔬菜</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FFEB3B;"><i class="fas fa-apple-alt"></i></div><span class="category-name">水果</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FF9800;"><i class="fas fa-cookie-bite"></i></div><span class="category-name">零食</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #00BCD4;"><i class="fas fa-dumbbell"></i></div><span class="category-name">运动</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #E91E63;"><i class="fas fa-gamepad"></i></div><span class="category-name">娱乐</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #3F51B5;"><i class="fas fa-phone"></i></div><span class="category-name">通讯</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #673AB7;"><i class="fas fa-tshirt"></i></div><span class="category-name">服饰</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #F06292;"><i class="fas fa-spa"></i></div><span class="category-name">美容</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #795548;"><i class="fas fa-home"></i></div><span class="category-name">住房</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #03A9F4;"><i class="fas fa-child"></i></div><span class="category-name">孩子</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FFC107;"><i class="fas fa-user-friends"></i></div><span class="category-name">长辈</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FF5722;"><i class="fas fa-users"></i></div><span class="category-name">社交</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #009688;"><i class="fas fa-plane"></i></div><span class="category-name">旅行</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #607D8B;"><i class="fas fa-wine-glass-alt"></i></div><span class="category-name">烟酒</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #F44336;"><i class="fas fa-briefcase-medical"></i></div><span class="category-name">医疗</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #CDDC39;"><i class="fas fa-book"></i></div><span class="category-name">书籍</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #9E9E9E;"><i class="fas fa-graduation-cap"></i></div><span class="category-name">学习</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FFCDD2;"><i class="fas fa-paw"></i></div><span class="category-name">宠物</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #D32F2F;"><i class="fas fa-gift"></i></div><span class="category-name">礼金</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                 <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #546E7A;"><i class="fas fa-tools"></i></div><span class="category-name">维修</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
             </ul>
             <!-- FIX: Restored full income list -->
             <ul class="category-list" id="incomeList" hidden>
                  <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #4CAF50;"><i class="fas fa-wallet"></i></div><span class="category-name">工资</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                  <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #8BC34A;"><i class="fas fa-hand-holding-usd"></i></div><span class="category-name">兼职</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                  <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #FFC107;"><i class="fas fa-coins"></i></div><span class="category-name">理财</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                  <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #03A9F4;"><i class="fas fa-gifts"></i></div><span class="category-name">礼金</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
                  <li class="category-list-item"><button class="delete-btn"><i class="fas fa-minus-circle"></i></button><div class="category-icon-bg" style="background-color: #9E9E9E;"><i class="fas fa-ellipsis-h"></i></div><span class="category-name">其他</span><button class="drag-handle"><i class="fas fa-bars"></i></button></li>
             </ul>
        </div>

        <!-- Add Button -->
        <div class="add-category-container">
             <button class="add-category-btn" id="addCategoryBtn"><i class="fas fa-plus"></i>添加类别</button>
        </div>
    </div>

    <!-- Modal Popup -->
    <div class="modal-overlay" id="customCategoryModal">
        <div class="modal-content">
            <div class="modal-header">
                 <button class="modal-close-btn" id="modalCloseBtn"><i class="fas fa-times"></i></button>
                 <span class="modal-title">自定义分类</span>
                 <button class="modal-save-btn" id="modalSaveBtn" disabled>保存</button>
            </div>
            <div class="modal-body">
                 <!-- Input Group with Icon Preview -->
                 <div class="input-group">
                      <div class="input-with-icon">
                           <div class="selected-icon-preview" id="modalSelectedIconPreview">
                                <!-- Default Placeholder -->
                                <i class="fas fa-icons" style="color: var(--placeholder-icon-color);"></i>
                           </div>
                           <input type="text" id="modalCategoryName" class="category-name-input" placeholder="请输入分类名称" maxlength="10">
                      </div>
                 </div>

                 <!-- Icon Selection -->
                 <div class="icon-selection-area">
                      <!-- Removed Title Span -->
                      <div class="icon-grid" id="modalIconGrid">
                           <!-- Icons with inline styles -->
                            <span class="icon-section-title">日常</span>
                            <div class="icon-item" data-icon="fas fa-utensils" data-color="#FF6B35"><div class="icon-bg" style="background-color: #FF6B35;"><i class="fas fa-utensils" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-shopping-cart" data-color="#4CAF50"><div class="icon-bg" style="background-color: #4CAF50;"><i class="fas fa-shopping-cart" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-bus" data-color="#9C27B0"><div class="icon-bg" style="background-color: #9C27B0;"><i class="fas fa-bus" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-home" data-color="#795548"><div class="icon-bg" style="background-color: #795548;"><i class="fas fa-home" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-phone" data-color="#3F51B5"><div class="icon-bg" style="background-color: #3F51B5;"><i class="fas fa-phone" style="color: white;"></i></div></div>

                            <span class="icon-section-title">娱乐</span>
                            <div class="icon-item" data-icon="fas fa-film" data-color="#E91E63"><div class="icon-bg" style="background-color: #E91E63;"><i class="fas fa-film" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-gamepad" data-color="#E91E63"><div class="icon-bg" style="background-color: #E91E63;"><i class="fas fa-gamepad" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-music" data-color="#009688"><div class="icon-bg" style="background-color: #009688;"><i class="fas fa-music" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-plane" data-color="#009688"><div class="icon-bg" style="background-color: #009688;"><i class="fas fa-plane" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-grin-stars" data-color="#FFEB3B"><div class="icon-bg" style="background-color: #FFEB3B;"><i class="fas fa-grin-stars" style="color:#827717;"></i></div></div>

                            <span class="icon-section-title">健康与学习</span>
                            <div class="icon-item" data-icon="fas fa-futbol" data-color="#00BCD4"><div class="icon-bg" style="background-color: #00BCD4;"><i class="fas fa-futbol" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-dumbbell" data-color="#00BCD4"><div class="icon-bg" style="background-color: #00BCD4;"><i class="fas fa-dumbbell" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-briefcase-medical" data-color="#F44336"><div class="icon-bg" style="background-color: #F44336;"><i class="fas fa-briefcase-medical" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-book" data-color="#CDDC39"><div class="icon-bg" style="background-color: #CDDC39;"><i class="fas fa-book" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-graduation-cap" data-color="#9E9E9E"><div class="icon-bg" style="background-color: #9E9E9E;"><i class="fas fa-graduation-cap" style="color: white;"></i></div></div>

                            <span class="icon-section-title">其他</span>
                            <div class="icon-item" data-icon="fas fa-gift" data-color="#D32F2F"><div class="icon-bg" style="background-color: #D32F2F;"><i class="fas fa-gift" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-paw" data-color="#FFCDD2"><div class="icon-bg" style="background-color: #FFCDD2;"><i class="fas fa-paw" style="color:#D32F2F;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-wrench" data-color="#546E7A"><div class="icon-bg" style="background-color: #546E7A;"><i class="fas fa-wrench" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-car" data-color="#607D8B"><div class="icon-bg" style="background-color: #607D8B;"><i class="fas fa-car" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-lightbulb" data-color="#FFC107"><div class="icon-bg" style="background-color: #FFC107;"><i class="fas fa-lightbulb" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-heart" data-color="#F06292"><div class="icon-bg" style="background-color: #F06292;"><i class="fas fa-heart" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-credit-card" data-color="#2196F3"><div class="icon-bg" style="background-color: #2196F3;"><i class="fas fa-credit-card" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-file-invoice-dollar" data-color="#4CAF50"><div class="icon-bg" style="background-color: #4CAF50;"><i class="fas fa-file-invoice-dollar" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-users" data-color="#FF5722"><div class="icon-bg" style="background-color: #FF5722;"><i class="fas fa-users" style="color: white;"></i></div></div>
                            <div class="icon-item" data-icon="fas fa-question" data-color="#BDBDBD"><div class="icon-bg" style="background-color: #BDBDBD;"><i class="fas fa-question" style="color: white;"></i></div></div>
                      </div>
                 </div>
            </div>
        </div>
    </div>

    <script>
        // --- JavaScript (No Changes Needed from Previous Version) ---
        document.addEventListener('DOMContentLoaded', function() {
             const tabs = document.querySelectorAll('.tab-option');
             const expenseList = document.getElementById('expenseList');
             const incomeList = document.getElementById('incomeList');
             const addCategoryBtn = document.getElementById('addCategoryBtn');
             const backBtn = document.getElementById('backBtn');
             const modalOverlay = document.getElementById('customCategoryModal');
             const modalIconGrid = document.getElementById('modalIconGrid');
             const modalCategoryNameInput = document.getElementById('modalCategoryName');
             const modalSaveBtn = document.getElementById('modalSaveBtn');
             const modalCloseBtn = document.getElementById('modalCloseBtn');
             const modalSelectedIconPreview = document.getElementById('modalSelectedIconPreview');

             let modalSelectedIconClass = null;
             let modalSelectedIconColor = null;
             let currentActiveList = expenseList;

             tabs.forEach(tab => {
                 tab.addEventListener('click', function() {
                     tabs.forEach(t => t.classList.remove('active'));
                     this.classList.add('active');
                     const type = this.dataset.type;
                     currentActiveList = (type === 'expense') ? expenseList : incomeList;
                     expenseList.hidden = (type !== 'expense');
                     incomeList.hidden = (type === 'expense');
                 });
             });

             function resetIconPreview() {
                  modalSelectedIconPreview.innerHTML = '<i class="fas fa-icons" style="color: var(--placeholder-icon-color);"></i>'; // Default placeholder icon
                  modalSelectedIconPreview.style.backgroundColor = 'var(--bg-medium-gray)';
                  modalSelectedIconPreview.classList.remove('has-icon');
                  const iconTag = modalSelectedIconPreview.querySelector('i');
                  if(iconTag) iconTag.className = 'fas fa-icons';
             }


             function openModal() {
                  modalCategoryNameInput.value = '';
                  const currentActiveIcon = modalIconGrid.querySelector('.icon-item.active');
                  if (currentActiveIcon) {
                      currentActiveIcon.classList.remove('active');
                  }
                  modalSelectedIconClass = null;
                  modalSelectedIconColor = null;
                  modalSaveBtn.disabled = true;
                  resetIconPreview();
                  modalOverlay.classList.add('visible');
             }

             function closeModal() { modalOverlay.classList.remove('visible'); }

             addCategoryBtn.addEventListener('click', openModal);
             modalCloseBtn.addEventListener('click', closeModal);

             function validateModalForm() {
                 const name = modalCategoryNameInput.value.trim();
                 const iconSelected = !!modalSelectedIconClass;
                 modalSaveBtn.disabled = !(name.length > 0 && iconSelected);
             }

             modalCategoryNameInput.addEventListener('input', validateModalForm);

             modalIconGrid.addEventListener('click', function(event) {
                 const iconItem = event.target.closest('.icon-item');
                 if (!iconItem) return;

                 const currentActive = modalIconGrid.querySelector('.icon-item.active');
                 if (currentActive) {
                     currentActive.classList.remove('active');
                 }

                 iconItem.classList.add('active');
                 modalSelectedIconClass = iconItem.dataset.icon;
                 modalSelectedIconColor = iconItem.dataset.color;

                 // Update Icon Preview
                 const iconBgDiv = document.createElement('div');
                 iconBgDiv.className = 'icon-display-bg';
                 iconBgDiv.style.backgroundColor = modalSelectedIconColor || 'var(--bg-medium-gray)';

                 const iconTag = document.createElement('i');
                 iconTag.className = modalSelectedIconClass || '';

                 if (modalSelectedIconColor === '#FFEB3B' || modalSelectedIconColor === '#FFCDD2') {
                    iconTag.style.color = '#827717';
                 } else if (modalSelectedIconColor) {
                     iconTag.style.color = 'white';
                 } else {
                     iconTag.style.color = 'var(--placeholder-icon-color)';
                 }

                 iconBgDiv.appendChild(iconTag);
                 modalSelectedIconPreview.innerHTML = '';
                 modalSelectedIconPreview.appendChild(iconBgDiv);
                 modalSelectedIconPreview.classList.add('has-icon');
                 modalSelectedIconPreview.style.backgroundColor = 'transparent';

                 validateModalForm();
             });


             modalSaveBtn.addEventListener('click', function() {
                  if (modalSaveBtn.disabled) return;
                  const categoryName = modalCategoryNameInput.value.trim();
                  const activeTabType = document.querySelector('.tab-option.active').dataset.type;

                  const newListElement = document.createElement('li');
                  newListElement.className = 'category-list-item';
                  const iconColor = modalSelectedIconColor || '#BDBDBD';
                  const iconClass = modalSelectedIconClass || 'fas fa-question';
                  // Create the list item using the selected icon and color
                  newListElement.innerHTML = `
                      <button class="delete-btn"><i class="fas fa-minus-circle"></i></button>
                      <div class="category-icon-bg" style="background-color: ${iconColor};"><i class="${iconClass}"></i></div>
                      <span class="category-name">${categoryName}</span>
                      <button class="drag-handle"><i class="fas fa-bars"></i></button>
                  `;
                   // Set icon color based on background for the added list item
                   const iconElement = newListElement.querySelector('.category-icon-bg i');
                   if (iconColor === '#FFEB3B' || iconColor === '#FFCDD2') {
                      iconElement.style.color = '#827717';
                   } else {
                      iconElement.style.color = 'white';
                   }
                  currentActiveList.appendChild(newListElement); // Add to the correct list (expense/income)
                  closeModal();
             });

              backBtn.addEventListener('click', () => {
                  console.log('Back button clicked');
                  alert('返回上一页');
              });

              // Initial setup
              const initialActiveType = document.querySelector('.tab-option.active').dataset.type;
              if (initialActiveType === 'income') {
                  expenseList.hidden = true; incomeList.hidden = false; currentActiveList = incomeList;
              }
              resetIconPreview(); // Ensure placeholder is set on load
         });
    </script>
</body>
</html>