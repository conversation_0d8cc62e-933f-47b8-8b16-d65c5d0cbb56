<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>定制你的助手 - AI智能记账</title>
    <!-- Font Awesome CDN (replace with <FaIcon> in production) -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* --- Core Variables (Simulating variables.scss) --- */
        :root {
            --color-primary: #FF6B35;
            --color-primary-light: #FFF5F2;
            --color-primary-lighter: #FFF9F7;
            --color-primary-border: #FFCDB7;
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-hint: #999999;
            --color-text-inverse: #FFFFFF;
            --color-bg-primary: #FFFFFF;
            --color-bg-secondary: #F8F9FA; /* Light gray background */
            --color-bg-icon-blue: #1E88E5;
            --color-bg-icon-purple: #7B61FF;
            --color-bg-icon-pink: #E91E63;
            --color-bg-icon-green: #43A047;
            --color-bg-icon-yellow: #FFB300;
            --color-border: rgba(0, 0, 0, 0.05);
            --color-shadow: rgba(0, 0, 0, 0.04);
            --color-shadow-orange: rgba(255, 107, 53, 0.2);
            --radius-card: 18px;
            --radius-button: 24px;
            --radius-tag: 50px;
            --radius-icon: 8px;
            --font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            --transition-speed: 0.3s;
        }

        /* --- Basic Reset & Body --- */
        *, *::before, *::after {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }
        html {
             -webkit-text-size-adjust: 100%; /* Prevent font scaling on orientation change */
        }
        body {
            font-family: var(--font-family);
            color: var(--color-text-primary);
            background-color: var(--color-bg-secondary);
            line-height: 1.6;
            font-size: 16px; /* Base font size */
        }

        /* --- Phone Container (for preview) --- */
        .phone-container {
             max-width: 414px; /* Simulates iPhone width */
             margin: 0 auto;
             background-color: var(--color-bg-secondary);
             box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
             min-height: 100vh;
        }

        /* --- Status Bar --- */
        .status-bar {
            display: flex;
            justify-content: space-between;
            padding: 10px 20px;
            background-color: var(--color-bg-primary);
            color: var(--color-text-primary);
            font-size: 13px;
            font-weight: 500;
            position: sticky;
            top: 0;
            z-index: 100;
        }
        .status-bar-icon { margin-left: 6px; }

        /* --- Navigation Bar --- */
        .nav-bar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 56px;
            padding: 0 15px;
            background-color: var(--color-bg-primary);
            border-bottom: 1px solid var(--color-border);
            position: sticky;
            top: 37px; /* Adjust based on status bar height */
            z-index: 99;
        }
        .nav-left, .nav-right { min-width: 40px; }
        .nav-title {
            font-size: 17px;
            font-weight: 600;
            position: relative;
            display: inline-block;
        }
        .nav-title::after {
            content: "";
            display: block;
            width: 30px;
            height: 3px;
            background: var(--color-primary);
            position: absolute;
            bottom: -7px; /* Adjusted position */
            left: 50%;
            transform: translateX(-50%);
            border-radius: 2px;
        }
        .back-button {
            color: var(--color-text-primary);
            font-size: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            transition: background-color 0.2s;
            cursor: pointer;
        }
        .back-button:active {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* --- Content Area --- */
        .content {
            padding: 15px 15px 80px 15px; /* Added bottom padding for save button */
        }

        /* --- Card Base Style --- */
        .card {
            background-color: var(--color-bg-primary);
            border-radius: var(--radius-card);
            margin-bottom: 20px;
            box-shadow: 0 6px 16px var(--color-shadow);
            overflow: hidden;
            transition: all var(--transition-speed) ease;
        }

        /* --- AI Profile Card --- */
        .ai-profile {
            position: relative;
            padding: 25px 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary-lighter));
            text-align: center;
        }
        .ai-avatar {
            width: 90px; /* Slightly smaller avatar */
            height: 90px;
            border-radius: 30%; /* Adjusted rounding */
            object-fit: cover;
            box-shadow: 0 8px 16px var(--color-shadow-orange);
            border: 3px solid var(--color-bg-primary);
            margin-bottom: 15px;
        }
        .ai-name {
            font-size: 22px;
            font-weight: 700;
            color: var(--color-primary);
            margin: 0;
            text-shadow: 0 1px 0px var(--color-bg-primary);
        }
        .ai-status {
            margin-top: 8px;
            display: inline-flex;
            align-items: center;
            background-color: rgba(255, 255, 255, 0.7);
            padding: 4px 12px;
            border-radius: var(--radius-tag);
            font-size: 12px;
            color: var(--color-text-secondary);
        }
        .ai-status i {
            color: #4CAF50; /* Green dot for online */
            margin-right: 5px;
            font-size: 8px; /* Smaller dot */
        }
        .relation-badge {
            position: absolute;
            top: 20px;
            right: 20px;
            background-color: var(--color-primary);
            color: white;
            padding: 6px 12px;
            border-radius: var(--radius-tag);
            font-size: 12px;
            font-weight: 500;
            box-shadow: 0 2px 8px var(--color-shadow-orange);
        }
        /* Trust Meter (Kept for visual appeal) */
        .trust-meter { width: 85%; margin: 15px auto 0; }
        .trust-bar { height: 6px; background-color: rgba(255, 107, 53, 0.2); border-radius: 3px; overflow: hidden; margin-bottom: 5px; }
        .trust-level { height: 100%; background-color: var(--color-primary); border-radius: 3px; transition: width var(--transition-speed) ease; }
        .trust-label { display: flex; justify-content: space-between; font-size: 11px; color: var(--color-text-hint); }

        /* --- Settings Tabs --- */
        .settings-tabs {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 6px; /* Reduced padding */
            background-color: var(--color-bg-primary);
            border-radius: 15px;
            box-shadow: 0 2px 8px var(--color-shadow);
        }
        .tab-item {
            flex: 1;
            padding: 8px 0;
            text-align: center;
            font-size: 14px;
            color: var(--color-text-secondary);
            border-radius: 10px;
            transition: all var(--transition-speed) ease;
            cursor: pointer;
            font-weight: 500;
        }
        .tab-item.active {
            color: var(--color-primary);
            background-color: var(--color-primary-light);
            font-weight: 600;
        }

        /* --- Settings Sections (Hidden by default) --- */
        .settings-section {
             display: none;
        }
        .settings-section.active {
             display: block;
        }

        /* Setting Group Title (Optional) */
        .setting-group-title {
            font-size: 14px;
            color: var(--color-text-hint);
            margin-bottom: 8px;
            padding-left: 15px;
            margin-top: 15px;
            font-weight: 500;
        }

        /* Settings Item */
        .setting-item {
            display: flex;
            align-items: center;
            padding: 16px 15px; /* Adjusted padding */
            border-bottom: 1px solid var(--color-border);
            position: relative;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .setting-item:last-child { border-bottom: none; }
        .setting-item:active { background-color: #f9f9f9; }
        .setting-icon {
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius-icon);
            margin-right: 15px;
            font-size: 16px;
            color: white;
        }
        /* Icon colors */
        .icon-nickname { background-color: var(--color-bg-icon-yellow); }
        .icon-relationship { background-color: var(--color-bg-icon-blue); }
        .icon-tone { background-color: var(--color-bg-icon-purple); }
        .icon-emotion { background-color: var(--color-bg-icon-pink); }
        .icon-emoji { background-color: var(--color-bg-icon-green); }
        .icon-confirm { background-color: var(--color-bg-icon-blue); }
        .icon-reminders { background-color: var(--color-bg-icon-green); }
        .icon-analysis { background-color: var(--color-bg-icon-purple); }

        .setting-content { flex: 1; }
        .setting-label {
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 2px;
            color: var(--color-text-primary);
        }
        .setting-desc {
            font-size: 13px;
            color: var(--color-text-secondary);
            line-height: 1.4;
        }
        .setting-action {
            margin-left: 15px;
            color: var(--color-text-hint);
            display: flex;
            align-items: center;
            gap: 8px;
        }
        .setting-value { /* To display current selection */
             font-size: 14px;
             color: var(--color-text-secondary);
             font-weight: 500;
        }

        /* Input Field inside Item */
        .setting-input {
             width: 100%;
             border: none;
             background: transparent;
             font-size: 16px;
             color: var(--color-text-primary);
             padding: 5px 0;
        }
        .setting-input::placeholder {
             color: var(--color-text-hint);
        }
        .setting-input:focus { outline: none; }

        /* Tag Container for selection */
        .tag-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            padding: 15px 15px 20px;
        }
        .tag {
            padding: 8px 16px; /* Slightly smaller padding */
            border-radius: var(--radius-tag);
            background-color: var(--color-bg-secondary);
            color: var(--color-text-secondary);
            font-size: 14px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
            user-select: none;
            cursor: pointer;
            border: 1px solid transparent;
        }
        .tag.selected {
            background-color: var(--color-primary-light);
            color: var(--color-primary);
            border: 1px solid var(--color-primary-border);
            font-weight: 600;
        }
        .tag:active { transform: scale(0.95); }

        /* Toggle Switch */
        .toggle-switch {
            position: relative;
            width: 48px;
            height: 26px;
        }
        .toggle-checkbox { opacity: 0; width: 0; height: 0; }
        .toggle-slider {
            position: absolute;
            cursor: pointer;
            top: 0; left: 0; right: 0; bottom: 0;
            background-color: #ccc;
            border-radius: 34px;
            transition: var(--transition-speed);
        }
        .toggle-slider:before {
            position: absolute;
            content: "";
            height: 20px; width: 20px;
            left: 3px; bottom: 3px;
            background-color: white;
            border-radius: 50%;
            transition: var(--transition-speed);
        }
        .toggle-checkbox:checked + .toggle-slider {
            background-color: var(--color-primary);
        }
        .toggle-checkbox:checked + .toggle-slider:before {
            transform: translateX(22px);
        }

        /* --- Save Button --- */
        .save-button-container {
             position: fixed;
             bottom: 0;
             left: 0;
             right: 0;
             padding: 15px 25px 25px; /* Adjust padding for safe area */
             background: linear-gradient(to top, var(--color-bg-primary), rgba(255, 255, 255, 0)); /* Gradient background */
             max-width: 414px; /* Match phone container */
             margin: 0 auto;
             z-index: 50;
        }
        .save-button {
            width: 100%;
            height: 48px; /* Increased height */
            background-color: var(--color-primary);
            color: white;
            border: none;
            border-radius: var(--radius-button);
            font-size: 16px;
            font-weight: 600;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 6px 16px var(--color-shadow-orange);
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .save-button i { margin-right: 8px; }
        .save-button:active {
            transform: translateY(2px);
            box-shadow: 0 3px 8px var(--color-shadow-orange);
        }

        /* --- Modal --- */
        .modal {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: none; justify-content: center; align-items: center;
            z-index: 1000;
        }
        .modal.active { display: flex; }
        .modal-content {
            width: 88%; /* Adjusted width */
            max-height: 75%;
            background-color: var(--color-bg-primary);
            border-radius: 20px;
            overflow: hidden;
            animation: modalFadeIn 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
        }
        .modal-header {
            padding: 16px 20px;
            border-bottom: 1px solid var(--color-border);
            display: flex; justify-content: space-between; align-items: center;
            flex-shrink: 0;
        }
        .modal-title { font-size: 18px; font-weight: 600; color: var(--color-text-primary); }
        .modal-close {
            font-size: 20px; color: var(--color-text-hint); width: 36px; height: 36px;
            display: flex; align-items: center; justify-content: center; border-radius: 50%;
            cursor: pointer; transition: background-color 0.2s;
        }
        .modal-close:active { background-color: rgba(0, 0, 0, 0.05); }
        .modal-body {
            padding: 10px 5px 10px 15px; /* Adjusted padding */
            overflow-y: auto;
            flex-grow: 1;
        }
        /* Custom scrollbar for modal body */
        .modal-body::-webkit-scrollbar { width: 6px; }
        .modal-body::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 3px;}
        .modal-body::-webkit-scrollbar-thumb { background: #ccc; border-radius: 3px;}
        .modal-body::-webkit-scrollbar-thumb:hover { background: #aaa; }

        @keyframes modalFadeIn {
            from { opacity: 0; transform: scale(0.95); }
            to { opacity: 1; transform: scale(1); }
        }

    </style>
</head>
<body>

    <div class="phone-container">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-bar-left">10:31</div>
            <div class="status-bar-right">
                <span>5.20 KB/s</span>
                <i class="fas fa-signal status-bar-icon"></i>
                <i class="fas fa-wifi status-bar-icon"></i>
                <i class="fas fa-battery-full status-bar-icon"></i>
            </div>
        </div>

        <!-- Navigation Bar -->
        <div class="nav-bar">
            <div class="nav-left">
                <div class="back-button" onclick="goBack()">
                    <i class="fas fa-chevron-left"></i>
                </div>
            </div>
            <div class="nav-title">定制你的助手</div>
            <div class="nav-right"></div>
        </div>

        <!-- Content Area -->
        <div class="content">
            <!-- AI Profile Card -->
            <div class="card ai-profile">
                <!-- 显示当前与AI助手的关系类型标签 -->
                <div class="relation-badge" id="relationDisplay">理财助手</div>
                
                <!-- AI助手头像 -->
                <img src="https://img.zcool.cn/community/015a535930bb19a8012193a3329729.jpg@1280w_1l_2o_100sh.jpg" alt="账无忌 Avatar" class="ai-avatar">
                
                <!-- AI助手名称 -->
                <h2 class="ai-name">账无忌</h2>
                <div class="ai-status"><i class="fas fa-circle"></i> 在线・持续进化中</div>
                <div class="trust-meter">
                    <div class="trust-bar">
                        <div class="trust-level" id="trustLevelBar" style="width: 35%;"></div>
                    </div>
                    <div class="trust-label">
                        <span>初识</span>
                        <span>熟悉</span>
                        <span>默契</span>
                    </div>
                </div>
            </div>

            <!-- Settings Tabs -->
            <div class="settings-tabs">
                <div class="tab-item active" onclick="switchTab(this, 'tab-relationship')">称呼与关系</div>
                <div class="tab-item" onclick="switchTab(this, 'tab-communication')">沟通风格</div>
                <div class="tab-item" onclick="switchTab(this, 'tab-interaction')">交互偏好</div>
            </div>

            <!-- Settings Sections -->
            <div id="tab-relationship" class="settings-section active">
                <div class="card">
                    <div class="setting-item">
                        <div class="setting-icon icon-nickname">
                            <i class="fas fa-pencil-alt"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">AI 如何称呼您？</div>
                            <input type="text" id="aiNickname" class="setting-input" placeholder="输入昵称，如“主人”">
                        </div>
                    </div>
                     <div class="setting-item" onclick="openModal('relationshipModal')">
                        <div class="setting-icon icon-relationship">
                            <i class="fas fa-users"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">与 AI 的关系</div>
                            <div class="setting-desc">定义你们的互动模式</div>
                        </div>
                        <div class="setting-action">
                             <span class="setting-value" id="relationshipValue">理财助手</span>
                             <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>

            <div id="tab-communication" class="settings-section">
                <div class="card">
                     <div class="setting-item" onclick="openModal('toneModal')">
                        <div class="setting-icon icon-tone">
                            <i class="fas fa-comment-dots"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">对话语气</div>
                            <div class="setting-desc">影响 AI 回答的风格</div>
                        </div>
                        <div class="setting-action">
                             <span class="setting-value" id="toneValue">亲切友好</span>
                             <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                     <div class="setting-item">
                        <div class="setting-icon icon-emoji">
                             <i class="far fa-smile-beam"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">使用 Emoji 表情</div>
                            <div class="setting-desc">让对话更生动</div>
                        </div>
                        <div class="setting-action">
                            <label class="toggle-switch">
                                <input type="checkbox" class="toggle-checkbox" id="useEmojis" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                     <!-- Optional: Emotion Slider can be added here if desired -->
                </div>
            </div>

            <div id="tab-interaction" class="settings-section">
                <div class="card">
                    <div class="setting-item" onclick="openModal('confirmModal')">
                        <div class="setting-icon icon-confirm">
                             <i class="fas fa-check-double"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">记账确认方式</div>
                            <div class="setting-desc">平衡效率与准确性</div>
                        </div>
                         <div class="setting-action">
                             <span class="setting-value" id="confirmValue">仅复杂时</span>
                             <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                    <div class="setting-group-title" style="padding-top: 10px;">主动提醒设置</div>
                    <div class="setting-item">
                        <div class="setting-icon icon-reminders" style="background-color: var(--color-bg-icon-yellow);">
                             <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">预算超支/临近提醒</div>
                            <div class="setting-desc">及时了解预算状况</div>
                        </div>
                        <div class="setting-action">
                             <label class="toggle-switch">
                                <input type="checkbox" class="toggle-checkbox" id="notifyBudget" checked>
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                     <div class="setting-item">
                        <div class="setting-icon icon-reminders">
                             <i class="fas fa-calendar-alt"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">每日/每周收支小结</div>
                            <div class="setting-desc">定期回顾财务状况</div>
                        </div>
                        <div class="setting-action">
                             <label class="toggle-switch">
                                <input type="checkbox" class="toggle-checkbox" id="notifySummary">
                                <span class="toggle-slider"></span>
                            </label>
                        </div>
                    </div>
                     <div class="setting-item" onclick="openModal('analysisModal')">
                        <div class="setting-icon icon-analysis">
                             <i class="fas fa-chart-pie"></i>
                        </div>
                        <div class="setting-content">
                            <div class="setting-label">分析侧重点</div>
                            <div class="setting-desc">AI 分析时关注的方面</div>
                        </div>
                         <div class="setting-action">
                             <span class="setting-value" id="analysisValue">消费模式</span>
                             <i class="fas fa-chevron-right"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Save Button Area -->
        <div class="save-button-container">
            <button class="save-button" onclick="saveSettings()">
                <i class="fas fa-check-circle"></i>应用设置
            </button>
        </div>

        <!-- Modals -->
        <!-- Relationship Modal -->
        <div class="modal" id="relationshipModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">选择与 AI 的关系</div>
                    <div class="modal-close" onclick="closeModal('relationshipModal')"><i class="fas fa-times"></i></div>
                </div>
                <div class="modal-body">
                    <div class="tag-container">
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">财务顾问</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">理财教练</div>
                        <div class="tag selected" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">理财助手</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">记账伙伴</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">金融导师</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">预算管家</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">财富管理师</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">省钱小能手</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">投资顾问</div>
                        <div class="tag" onclick="selectOption(this, 'relationshipModal', 'relationDisplay', 'relationshipValue')">财务规划师</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tone Modal -->
        <div class="modal" id="toneModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">选择对话语气</div>
                    <div class="modal-close" onclick="closeModal('toneModal')"><i class="fas fa-times"></i></div>
                </div>
                <div class="modal-body">
                     <div class="tag-container">
                        <div class="tag selected" onclick="selectOption(this, 'toneModal', null, 'toneValue')">亲切友好</div>
                        <div class="tag" onclick="selectOption(this, 'toneModal', null, 'toneValue')">专业正式</div>
                        <div class="tag" onclick="selectOption(this, 'toneModal', null, 'toneValue')">简洁明了</div>
                        <div class="tag" onclick="selectOption(this, 'toneModal', null, 'toneValue')">幽默风趣</div>
                        <div class="tag" onclick="selectOption(this, 'toneModal', null, 'toneValue')">耐心细致</div>
                        <div class="tag" onclick="selectOption(this, 'toneModal', null, 'toneValue')">高冷效率</div>
                    </div>
                </div>
            </div>
        </div>

         <!-- Confirmation Modal -->
        <div class="modal" id="confirmModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">选择记账确认方式</div>
                    <div class="modal-close" onclick="closeModal('confirmModal')"><i class="fas fa-times"></i></div>
                </div>
                <div class="modal-body">
                     <div class="tag-container">
                        <div class="tag" onclick="selectOption(this, 'confirmModal', null, 'confirmValue')">总是确认</div>
                        <div class="tag selected" onclick="selectOption(this, 'confirmModal', null, 'confirmValue')">仅复杂时确认</div>
                        <div class="tag" onclick="selectOption(this, 'confirmModal', null, 'confirmValue')">尽量直接记录</div>
                    </div>
                </div>
            </div>
        </div>

         <!-- Analysis Focus Modal -->
        <div class="modal" id="analysisModal">
            <div class="modal-content">
                <div class="modal-header">
                    <div class="modal-title">选择分析侧重点</div>
                    <div class="modal-close" onclick="closeModal('analysisModal')"><i class="fas fa-times"></i></div>
                </div>
                <div class="modal-body">
                     <div class="tag-container">
                        <div class="tag selected" onclick="selectOption(this, 'analysisModal', null, 'analysisValue')">消费模式分析</div>
                        <div class="tag" onclick="selectOption(this, 'analysisModal', null, 'analysisValue')">省钱建议</div>
                        <div class="tag" onclick="selectOption(this, 'analysisModal', null, 'analysisValue')">预算执行情况</div>
                         <div class="tag" onclick="selectOption(this, 'analysisModal', null, 'analysisValue')">收入来源分析</div>
                         <div class="tag" onclick="selectOption(this, 'analysisModal', null, 'analysisValue')">投资回报分析</div>
                    </div>
                </div>
            </div>
        </div>

    </div>

    <script>
        // --- Basic Interaction Logic ---
        function goBack() {
            // In a real app, use uni.navigateBack() or router.back()
            alert('返回上一页');
            // window.history.back();
        }

        function saveSettings() {
            const nickname = document.getElementById('aiNickname').value;
            const relationship = document.getElementById('relationshipValue').textContent;
            const tone = document.getElementById('toneValue').textContent;
            const useEmojis = document.getElementById('useEmojis').checked;
            const confirmMethod = document.getElementById('confirmValue').textContent;
            const notifyBudget = document.getElementById('notifyBudget').checked;
            const notifySummary = document.getElementById('notifySummary').checked;
            const analysisFocus = document.getElementById('analysisValue').textContent;

            // In a real app, save these values to Pinia store or send via API
            console.log({
                nickname, relationship, tone, useEmojis,
                confirmMethod, notifyBudget, notifySummary, analysisFocus
            });
            alert('设置已应用！');
             // Maybe navigate away or show success message
        }

        function switchTab(tabElement, targetSectionId) {
            // Deactivate all tabs and sections
            document.querySelectorAll('.tab-item').forEach(tab => tab.classList.remove('active'));
            document.querySelectorAll('.settings-section').forEach(section => section.classList.remove('active'));

            // Activate clicked tab and corresponding section
            tabElement.classList.add('active');
            document.getElementById(targetSectionId).classList.add('active');
        }

        function openModal(modalId) {
            document.getElementById(modalId).classList.add('active');
        }

        function closeModal(modalId) {
             document.getElementById(modalId).classList.remove('active');
        }

        // Function to handle option selection in modals
        function selectOption(tagElement, modalId, displayElementId, valueElementId) {
             const modalBody = tagElement.closest('.modal-body');
             const selectedValue = tagElement.textContent.trim();

            // Update selected state visually in the modal
            modalBody.querySelectorAll('.tag.selected').forEach(tag => tag.classList.remove('selected'));
            tagElement.classList.add('selected');

            // Update the display value outside the modal (if applicable)
            if (displayElementId) {
                 document.getElementById(displayElementId).textContent = selectedValue;
            }
            // Update the setting item value display
            if (valueElementId) {
                 document.getElementById(valueElementId).textContent = selectedValue;
            }

            // Close the modal after selection
            closeModal(modalId);
        }

         // Close modal if clicked outside the content
         window.addEventListener('click', function(event) {
            document.querySelectorAll('.modal.active').forEach(modal => {
                if (event.target === modal) {
                    closeModal(modal.id);
                }
            });
        });

        // Initial setup
        document.addEventListener('DOMContentLoaded', () => {
            // Ensure the first tab is active on load
            switchTab(document.querySelector('.tab-item'), 'tab-relationship');

            // Set initial trust level (example)
             const trust = 35; // Example value (0-100)
             document.getElementById('trustLevelBar').style.width = `${trust}%`;
        });

    </script>
</body>
</html>