<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>智能对话 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    .content {
      padding: 0;
      flex: 1;
      display: flex;
      flex-direction: column;
      background-color: #f5f5f5;
      position: relative;
      overflow: hidden;
      width: 100%;
    }
    
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-left, .nav-right {
      width: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
     .nav-right {
      justify-content: flex-end;
    }
    
    .nav-left i, .nav-right i {
      color: #666;
      font-size: 18px;
    }
        
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .chat-container {
      flex: 1;
      overflow-y: auto;
      padding: 10px 0 0;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    .message {
      margin-bottom: 16px;
      display: flex;
      flex-direction: column;
      position: relative;
      padding: 0 10px; /* Add horizontal padding to message container */
      box-sizing: border-box;
    }
    
    .timestamp {
      color: #999;
      font-size: 12px;
      text-align: center;
      margin: 20px 0;
    }
    
    .avatar {
      width: 40px; /* Standardized avatar size */
      height: 40px; /* Standardized avatar size */
      border-radius: 50%;
      object-fit: cover;
      border: 1px solid rgba(0, 0, 0, 0.05); /* Consistent border */
    }
    
    /* User message: Avatar on the right, bubble on the left of avatar */
    .user-message {
      display: flex;
      flex-direction: row-reverse; /* Avatar will be on the right */
      align-items: flex-end; /* Align items to the bottom */
    }

    .user-message .avatar {
      margin-left: 8px;
    }

    .user-message .bubble {
      background-color: #FF6B35;
      color: white;
      border-radius: 18px 4px 18px 18px;
      padding: 10px 14px;
      margin-right: 0; /* Bubble is to the left of avatar, so no margin-right needed here */
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
      max-width: calc(100% - 58px); /* 40px avatar + 8px margin + 10px padding */
      word-wrap: break-word;
    }

    /* AI message: Avatar on the left, bubble on the right of avatar */
    .ai-message {
      display: flex;
      flex-direction: row; /* Avatar will be on the left */
      align-items: flex-end; /* Align items to the bottom */
    }

    .ai-message .avatar {
      margin-right: 8px;
      /* For AI logo, ensure it's not distorted */
      object-fit: contain; /* Or 'scale-down' if 'contain' causes issues with small logos */
      background-color: white; /* If logo has transparent parts */
    }

    .ai-message .bubble {
      background-color: white;
      color: #333;
      border-radius: 4px 18px 18px 18px;
      padding: 10px 14px;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
      max-width: calc(100% - 58px);
      word-wrap: break-word;
    }
    
    .system-message {
      align-self: center;
      margin: 10px 0;
    }
    
    .system-message .bubble {
      background-color: rgba(0,0,0,0.05);
      color: #666;
      border-radius: 15px;
      padding: 6px 12px;
      font-size: 13px;
    }
    
    .transaction-card {
      background-color: white;
      border-radius: 12px;
      padding: 15px;
      margin-top: 10px;
      box-shadow: 0 2px 6px rgba(0,0,0,0.06);
      border: 1px solid rgba(0,0,0,0.03);
    }
    
    .transaction-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .transaction-category {
      display: flex;
      align-items: center;
    }
    
    .category-icon {
      width: 32px;
      height: 32px;
      border-radius: 8px;
      background-color: #FFF5F2;
      color: #FF6B35;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 10px;
      font-size: 16px;
    }
    
    .category-name {
      font-weight: 500;
    }
    
    .transaction-amount {
      font-weight: 600;
      font-size: 16px;
      color: #FF6B35;
    }
    
    .transaction-info {
      display: flex;
      justify-content: space-between;
      color: #999;
      font-size: 13px;
    }
    
    .transaction-actions {
      display: flex;
      justify-content: flex-end;
      margin-top: 12px;
      gap: 12px;
    }
    
    .action-button {
      background-color: #f5f5f5;
      color: #666;
      border: none;
      border-radius: 15px;
      padding: 5px 10px;
      font-size: 12px;
      cursor: pointer;
      display: flex;
      align-items: center;
    }
    
    .action-button i {
      margin-right: 4px;
      font-size: 12px;
    }
    
    .action-button.confirm {
      background-color: #FFF5F2;
      color: #FF6B35;
    }
    
    .input-container {
      padding: 8px 0;
      background-color: #fff;
      border-top: 1px solid rgba(0,0,0,0.05);
      display: flex;
      align-items: center;
      width: 100%;
      box-sizing: border-box;
      flex-shrink: 0;
      max-height: 56px;
    }
    
    .input-tools {
      display: flex;
      align-items: center;
      margin-left: 8px; /* Consistent left margin */
    }
    
    .tool-button {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
      font-size: 18px;
      margin-right: 5px;
      transition: background-color 0.2s;
      cursor: pointer;
    }
    
    .tool-button:active {
      background-color: rgba(0,0,0,0.05);
    }
    
    .input-box {
      flex: 1;
      display: flex;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 20px;
      padding: 8px 15px;
      margin: 0 8px; 
    }
    
    .message-input {
      width: 100%;
      border: none;
      background: transparent;
      font-size: 16px;
      outline: none;
      color: #333;
    }
    
    .message-input::placeholder {
      color: #999;
    }
    
    .send-button {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: #FF6B35;
      color: white;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 8px; /* Consistent right margin */
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
      transition: all 0.2s ease;
      cursor: pointer;
    }
    
    .send-button:active {
      transform: scale(0.95);
    }
    
    .voice-wave-container {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: white;
      display: none; /* Initially hidden */
      flex-direction: column;
      align-items: center;
      justify-content: center;
      z-index: 10;
    }
    
    .voice-wave-container.active {
      display: flex;
    }
    
    .wave-animation {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 30px;
      width: 100px;
      height: 100px;
      background-color: rgba(255, 107, 53, 0.1);
      border-radius: 50%;
      padding: 15px;
    }
    
    .wave-bar {
      width: 4px;
      height: 20px;
      background-color: #FF6B35;
      margin: 0 2px;
      border-radius: 2px;
      animation: waveAnimation 1.2s infinite ease-in-out;
    }
    
    @keyframes waveAnimation {
      0%, 100% { height: 10px; opacity: 0.6; }
      50% { height: 35px; opacity: 1; }
    }
    
    .voice-tip {
      color: #666;
      font-size: 14px;
      margin-bottom: 20px;
    }
    
    .cancel-voice {
      color: #FF6B35;
      font-size: 14px;
      padding: 8px 20px;
      border: 1px solid #FF6B35;
      border-radius: 20px;
      cursor: pointer;
    }
    
    .functions-panel {
      position: absolute;
      bottom: 0;
      left: 0;
      width: 100%;
      background-color: white;
      padding: 15px 15px 20px;
      transform: translateY(100%);
      opacity: 0;
      visibility: hidden; /* Use visibility for better transition control */
      transition: transform 0.3s ease, opacity 0.3s ease, visibility 0s 0.3s; /* Delay visibility change */
      border-top-left-radius: 20px;
      border-top-right-radius: 20px;
      box-shadow: 0 -5px 20px rgba(0,0,0,0.1);
      z-index: 1000;
      box-sizing: border-box;
    }
    
    .functions-panel.active {
      transform: translateY(0);
      opacity: 1;
      visibility: visible;
      transition: transform 0.3s ease, opacity 0.3s ease, visibility 0s 0s;
    }
    
    .functions-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 15px;
    }
    
    .function-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      cursor: pointer;
    }
    
    .function-icon {
      width: 60px;
      height: 60px;
      border-radius: 15px;
      background-color: #f5f5f5;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      font-size: 24px;
      color: #555;
      transition: transform 0.2s;
    }
    
    .function-icon:active {
      transform: scale(0.95);
    }
    
    .function-label {
      font-size: 12px;
      color: #666;
    }
    
    .image-viewer {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0,0,0,0.9);
      display: none; /* Initially hidden */
      z-index: 2000;
      justify-content: center;
      align-items: center;
    }
    
    .image-viewer.active {
      display: flex;
    }
    
    .viewer-image {
      max-width: 90%;
      max-height: 80%;
      object-fit: contain;
    }
    
    .close-viewer {
      position: absolute;
      top: 20px;
      right: 20px;
      color: white;
      font-size: 24px;
      cursor: pointer;
    }
    
    .bubble img {
      max-width: 200px;
      border-radius: 8px;
      margin: 5px 0;
      cursor: pointer;
      display: block;
    }
        
    @keyframes fadeIn {
      from { opacity: 0; transform: translateY(10px); }
      to { opacity: 1; transform: translateY(0); }
    }
    
    .message:not(.initial-message) { /* Avoid animating initial messages */
      animation: fadeIn 0.3s ease;
    }
    
    .typing-indicator {
      display: flex;
      padding: 0; /* Removed padding to fit better in bubble */
      align-items: center;
    }
    
    .typing-dot {
      width: 8px;
      height: 8px;
      margin: 0 3px;
      background-color: #FF6B35;
      opacity: 0.7;
      border-radius: 50%;
      animation: typingAnimation 1.4s infinite ease-in-out both; /* Added 'both' */
    }
     .typing-dot:nth-child(1) { animation-delay: 0s; }
     .typing-dot:nth-child(2) { animation-delay: 0.2s; }
     .typing-dot:nth-child(3) { animation-delay: 0.4s; }
    
    @keyframes typingAnimation {
      0%, 80%, 100% { transform: scale(0); opacity: 0.6; }
      40% { transform: scale(1.0); opacity: 1; }
    }
    
    .status-bar {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      padding: 3px 10px;
      background-color: #000;
      color: #fff;
      width: 100%;
      box-sizing: border-box;
      flex-shrink: 0;
      height: 22px;
      align-items: center;
    }
    
    .status-bar-left {
      font-weight: 500;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
      font-size: 11px;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <div class="status-bar">
      <div class="status-bar-left">21:48</div>
      <div class="status-bar-right">
        <span>5.98 KB/s</span>
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-three-quarters status-bar-icon"></i>
        <span style="margin-left: 2px;">22%</span>
      </div>
    </div>
    
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">账无忌</div>
      <div class="nav-right">
        <i class="fas fa-cog"></i>
      </div>
    </div>
    
    <div class="content">
      <div class="chat-container" id="chatContainer">
        <div class="timestamp">今天 21:45</div>
        
        <div class="message ai-message initial-message">
          <img src="../../src/assets/images/2LOGO.png" alt="账无忌" class="avatar">
          <div class="bubble">
            主人，晚上好，有什么需要我帮忙记录的吗？
          </div>
        </div>
        
        <div class="message user-message initial-message">
          <img src="https://i.pravatar.cc/150?img=68" alt="用户" class="avatar">
          <div class="bubble">
            今天电费交了四百二
          </div>
        </div>
        
        <div class="message ai-message initial-message">
          <img src="../../src/assets/images/2LOGO.png" alt="账无忌" class="avatar">
          <div class="bubble">
            <div class="transaction-card">
              <div class="transaction-header">
                <div class="transaction-category">
                  <div class="category-icon">
                    <i class="fas fa-bolt"></i>
                  </div>
                  <div class="category-name">住房</div>
                </div>
                <div class="transaction-amount">-420.00</div>
              </div>
              <div class="transaction-info">
                <div class="transaction-date">2025年03月28日</div>
                <div class="transaction-desc">电费</div>
              </div>
              <div class="transaction-actions">
                <button class="action-button">
                  <i class="fas fa-trash-alt"></i>
                  删除
                </button>
                <button class="action-button">
                  <i class="fas fa-edit"></i>
                  编辑
                </button>
                <button class="action-button confirm">
                  <i class="fas fa-check"></i>
                  确认
                </button>
              </div>
            </div>
          </div>
        </div>
        
        <div class="message ai-message initial-message">
          <img src="../../src/assets/images/2LOGO.png" alt="账无忌" class="avatar">
          <div class="bubble">
            哇，看来家里的电器朋友们的聚会很热闹呢！安全用电，温暖如春，每一度电都充满家的温馨。🏠☀️
          </div>
        </div>
        
        <div class="message user-message initial-message">
          <img src="https://i.pravatar.cc/150?img=68" alt="用户" class="avatar">
          <div class="bubble">
            <img src="https://via.placeholder.com/200x150/eee?text=充电卡照片" alt="充电卡照片" onclick="openImageViewer(this.src)">
            等我充满这张卡就来找你们玩儿
          </div>
        </div>
      </div>
      
      <div class="input-container">
        <div class="input-tools">
          <div class="tool-button" id="voiceButton">
            <i class="fas fa-microphone"></i>
          </div>
          <div class="tool-button" id="moreButton">
            <i class="fas fa-plus"></i>
          </div>
        </div>
        <div class="input-box">
          <input type="text" class="message-input" placeholder="说点什么..." id="messageInput">
        </div>
        <div class="send-button" id="sendButton">
          <i class="fas fa-paper-plane"></i>
        </div>
      </div>
      
      <div class="voice-wave-container" id="voiceContainer">
        <div class="wave-animation">
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
          <div class="wave-bar"></div>
        </div>
        <div class="voice-tip">正在聆听，请说话...</div>
        <div class="cancel-voice" id="cancelVoice">取消</div>
      </div>
      
      <div class="functions-panel" id="functionsPanel">
        <div class="functions-grid">
          <div class="function-item">
            <div class="function-icon" style="color: #4CAF50;"><i class="fas fa-image"></i></div>
            <div class="function-label">相册</div>
          </div>
          <div class="function-item">
            <div class="function-icon" style="color: #2196F3;"><i class="fas fa-camera"></i></div>
            <div class="function-label">拍照</div>
          </div>
          <div class="function-item">
            <div class="function-icon" style="color: #E91E63;"><i class="fas fa-calendar-alt"></i></div>
            <div class="function-label">查询账单</div>
          </div>
          <div class="function-item">
            <div class="function-icon" style="color: #9C27B0;"><i class="fas fa-chart-pie"></i></div>
            <div class="function-label">消费分析</div>
          </div>
          <div class="function-item">
            <div class="function-icon" style="color: #607D8B;"><i class="fas fa-piggy-bank"></i></div>
            <div class="function-label">预算管理</div>
          </div>
          <div class="function-item">
            <div class="function-icon" style="color: #795548;"><i class="fas fa-receipt"></i></div>
            <div class="function-label">报销单据</div>
          </div>
          <div class="function-item">
            <div class="function-icon" style="color: #FF6B35;"><i class="fas fa-lightbulb"></i></div>
            <div class="function-label">理财建议</div>
          </div>
        </div>
      </div>
    </div>
    
    <div class="image-viewer" id="imageViewer">
      <img src="" alt="放大查看" class="viewer-image" id="viewerImage">
      <div class="close-viewer" id="closeViewer">
        <i class="fas fa-times"></i>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const chatContainer = document.getElementById('chatContainer');
      const messageInput = document.getElementById('messageInput');
      const sendButton = document.getElementById('sendButton');
      const voiceButton = document.getElementById('voiceButton');
      const moreButton = document.getElementById('moreButton');
      const voiceContainer = document.getElementById('voiceContainer');
      const cancelVoice = document.getElementById('cancelVoice');
      const functionsPanel = document.getElementById('functionsPanel');
      const imageViewer = document.getElementById('imageViewer');
      const closeViewer = document.getElementById('closeViewer');
      const viewerImage = document.getElementById('viewerImage');
      
      document.querySelector('.nav-left').addEventListener('click', function() {
        alert('返回上一页'); // 替换为实际返回逻辑
      });
      
      document.querySelector('.nav-right').addEventListener('click', function() {
        alert('打开AI个性化设置'); // 替换为实际跳转逻辑
      });
      
      function sendMessage() {
        const messageText = messageInput.value.trim();
        if (messageText) {
          addUserMessage(messageText);
          messageInput.value = '';
          messageInput.placeholder = "说点什么..."; // Reset placeholder
          // Simulate AI reply
          setTimeout(simulateTyping, 500);
          setTimeout(function() {
            addAIMessage('我已经记录下来了，还有什么需要我帮忙的吗？');
          }, 2500);
        }
      }

      sendButton.addEventListener('click', sendMessage);
      
      messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
          e.preventDefault(); // Prevent form submission if inside a form
          sendMessage();
        }
      });
      
      let pressTimer;
      let isRecording = false;
      voiceButton.addEventListener('mousedown', function() {
        isRecording = false; // Reset flag
        pressTimer = setTimeout(function() {
          voiceContainer.classList.add('active');
          messageInput.placeholder = "松开 发送";
          isRecording = true;
        }, 300);
      });
      
      voiceButton.addEventListener('mouseup', function() {
        clearTimeout(pressTimer);
        if (isRecording) {
            voiceContainer.classList.remove('active');
            messageInput.placeholder = "说点什么...";
            // Simulate voice message
            addUserMessage("[语音消息]"); // Or actual voice processing logic
            setTimeout(simulateTyping, 500);
            setTimeout(function() {
                addAIMessage('好的，您的语音我已经听到了。');
            }, 2500);
        }
        isRecording = false;
      });

      voiceButton.addEventListener('mouseleave', function() { // If mouse leaves while pressed
        if (isRecording) {
          clearTimeout(pressTimer);
          voiceContainer.classList.remove('active');
          messageInput.placeholder = "说点什么...";
          isRecording = false;
          // Optionally, treat as cancel
          // console.log("Voice recording cancelled due to mouse leave");
        }
      });
      
      cancelVoice.addEventListener('click', function() {
        voiceContainer.classList.remove('active');
        messageInput.placeholder = "说点什么...";
        isRecording = false; // Ensure recording state is reset
      });
      
      moreButton.addEventListener('click', function() {
        const isActive = functionsPanel.classList.contains('active');
        if (isActive) {
          functionsPanel.classList.remove('active');
        } else {
          functionsPanel.classList.add('active');
        }
        // Ensure content scrolls to bottom if panel opens/closes
        setTimeout(scrollToBottom, 310); // After transition
      });
      
      function addUserMessage(text) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message user-message';
        
        const avatar = document.createElement('img');
        avatar.className = 'avatar';
        avatar.src = '../../src/assets/images/2LOGO.png';
        avatar.alt = '用户';
        
        const bubble = document.createElement('div');
        bubble.className = 'bubble';
        bubble.textContent = text; // For text messages
        // If message contains image, handle it here or in a separate function
        
        messageDiv.appendChild(bubble); // Bubble first for flex-direction: row-reverse
        messageDiv.appendChild(avatar);
        
        chatContainer.appendChild(messageDiv);
        scrollToBottom();
      }
      
      function addAIMessage(text) {
        const typingIndicator = chatContainer.querySelector('.ai-message.typing');
        if (typingIndicator) {
          typingIndicator.remove();
        }

        const messageDiv = document.createElement('div');
        messageDiv.className = 'message ai-message';
        
        const avatar = document.createElement('img');
        avatar.className = 'avatar';
        avatar.src = '../../src/assets/images/2LOGO.png';
        avatar.alt = '账无忌';
        
        const bubble = document.createElement('div');
        bubble.className = 'bubble';
        bubble.textContent = text;
        
        messageDiv.appendChild(avatar);
        messageDiv.appendChild(bubble);
        
        chatContainer.appendChild(messageDiv);
        scrollToBottom();
      }
      
      function simulateTyping() {
        const existingTyping = chatContainer.querySelector('.ai-message.typing');
        if (existingTyping) return; // Avoid multiple typing indicators

        const typingDiv = document.createElement('div');
        typingDiv.className = 'message ai-message typing'; // Add 'typing' class
        
        const avatar = document.createElement('img');
        avatar.className = 'avatar';
        avatar.src = '../../src/assets/images/2LOGO.png';
        avatar.alt = '账无忌';
        
        const bubble = document.createElement('div');
        bubble.className = 'bubble';
        
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        for (let i = 0; i < 3; i++) {
          const dot = document.createElement('div');
          dot.className = 'typing-dot';
          indicator.appendChild(dot);
        }
        
        bubble.appendChild(indicator);
        typingDiv.appendChild(avatar);
        typingDiv.appendChild(bubble);
        
        chatContainer.appendChild(typingDiv);
        scrollToBottom();
      }
      
      function scrollToBottom() {
        // Adding a slight delay can sometimes help ensure the scrollHeight is updated
        setTimeout(() => {
             chatContainer.scrollTop = chatContainer.scrollHeight;
        }, 0);
      }
      
      // Expose to global scope for inline onclick
      window.openImageViewer = function(src) {
        viewerImage.src = src;
        imageViewer.classList.add('active');
      }
      
      closeViewer.addEventListener('click', function() {
        imageViewer.classList.remove('active');
      });
      
      window.addEventListener('resize', scrollToBottom);
      
      // Initial scroll to bottom once everything is loaded
      window.addEventListener('load', function() {
          // Add class to initial messages to prevent animation
          const initialMessages = chatContainer.querySelectorAll('.message.initial-message');
          initialMessages.forEach(msg => msg.classList.remove('initial-message')); // Or handle animation differently
          scrollToBottom();
      });

    });
  </script>
</body>
</html>