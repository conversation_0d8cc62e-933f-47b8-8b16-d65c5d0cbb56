<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>账单 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden; /* 防止页面整体滚动 */
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      overflow-x: hidden; /* 防止水平滚动 */
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box; /* 确保padding不会增加宽度 */
    }
    
    /* 筛选区域样式 */
    .filter-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .date-selector {
      display: flex;
      align-items: center;
    }
    
    .date-text {
      font-size: 18px;
      font-weight: 600;
      margin-right: 8px;
      color: #333;
    }
    
    .filter-button {
      display: flex;
      align-items: center;
      background-color: #FFF5F2;
      padding: 8px 14px;
      border-radius: 20px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }
    
    .filter-button i {
      color: #FF6B35;
      font-size: 14px;
      margin-right: 6px;
    }
    
    .filter-button span {
      font-size: 14px;
      color: #333;
      font-weight: 500;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 16px;
      padding: 18px;
      margin-bottom: 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    /* 月度汇总卡片 */
    .summary-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      align-items: center;
    }
    
    .summary-title {
      font-size: 15px;
      color: #666;
      font-weight: 500;
    }
    
    .summary-link {
      font-size: 14px;
      color: #FF6B35;
      text-decoration: none;
      font-weight: 500;
    }
    
    .summary-stats {
      display: flex;
      margin-bottom: 20px;
    }
    
    .stat-item {
      flex: 1;
      text-align: center;
      padding: 0 5px; /* 减少内边距 */
      position: relative;
      min-width: 0; /* 允许flex项收缩 */
    }
    
    .stat-item:not(:last-child)::after {
      content: '';
      position: absolute;
      right: 0;
      top: 10%;
      height: 80%;
      width: 1px;
      background-color: rgba(0, 0, 0, 0.06);
    }
    
    .stat-label {
      font-size: 14px;
      color: #666;
      margin-bottom: 5px;
    }
    
    .stat-value {
      font-size: 18px; /* 减小字体大小 */
      font-weight: 600;
      white-space: nowrap; /* 防止换行 */
      overflow: hidden; /* 防止溢出 */
      text-overflow: ellipsis; /* 文本溢出时显示省略号 */
    }
    
    .stat-value.income {
      color: #10B981;
    }
    
    .stat-value.expense {
      color: #FF6B35;
    }
    
    /* 支出分类图表 */
    .chart-title {
      font-size: 15px;
      font-weight: 500;
      margin-bottom: 10px;
      color: #333;
    }
    
    .progress-bar {
      display: flex;
      gap: 4px;
      margin-bottom: 10px;
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
    }
    
    .progress-segment {
      height: 100%;
      border-radius: 4px;
    }
    
    .chart-legend {
      display: flex;
      flex-wrap: wrap;
      gap: 12px;
      font-size: 12px;
      color: #666;
    }
    
    .legend-item {
      display: flex;
      align-items: center;
    }
    
    .legend-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin-right: 6px;
    }
    
    /* 账单列表样式 */
    .date-heading {
      margin: 16px 0 10px;
      font-size: 15px;
      font-weight: 600;
      color: #333;
    }
    
    .list {
      margin: 0;
      padding: 0;
    }
    
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .list-item:last-child {
      border-bottom: none;
    }
    
    .item-left {
      display: flex;
      align-items: center;
    }
    
    .category-icon-container {
      width: 44px;
      height: 44px;
      border-radius: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 14px;
    }
    
    .list-item-title {
      font-weight: 500;
      font-size: 15px;
      margin-bottom: 4px;
      color: #333;
    }
    
    .list-item-subtitle {
      font-size: 13px;
      color: #999;
    }
    
    .list-item-right {
      text-align: right;
    }
    
    .list-item-amount {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 2px;
    }
    
    .amount-expense {
      color: #FF6B35;
    }
    
    .amount-income {
      color: #10B981;
    }
    
    /* 漂浮动画 */
    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0px);
      }
    }
    
    /* 浮动LOGO按钮样式 - 复制自首页 */
    .ai-fab {
      position: absolute;      
      bottom: 100px;       
      right: 20px;         
      width: 60px;         
      height: 60px;        
      border-radius: 50%;
      background-color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 100;
      overflow: visible;
      animation: float 3s ease-in-out infinite;
      padding: 0;
    }
    
    /* 浮动按钮中的图片样式 */
    .ai-fab img {
      width: 150%;  
      height: 150%; 
      object-fit: contain; 
      margin: 0; 
    }
    
    /* 提示气泡 */
    .ai-fab-tooltip {
      position: absolute;
      top: -45px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #333;
      color: white;
      font-size: 14px;
      padding: 6px 12px;
      border-radius: 20px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      opacity: 1;
    }
    
    .ai-fab-tooltip:after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid #333;
    }
    
    /* 浮动添加按钮 - 左下角，添加漂浮效果 */
    .fab {
      position: absolute;
      left: 20px;
      bottom: 100px;
      width: 56px;
      height: 56px;
      border-radius: 28px;
      background-color: #FF6B35;
      color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
      z-index: 10;
      cursor: pointer;
      animation: float 3s ease-in-out infinite;
      animation-delay: 1.5s; /* 错开与AI按钮的动画时间 */
    }
    
    .fab:active {
      transform: scale(0.95);
      animation-play-state: paused; /* 点击时暂停动画 */
    }
    
    /* 底部标签栏 */
    .tab-bar {
      height: 64px;
      background-color: white;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      flex-shrink: 0;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
    }
    
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 100%;
    }
    
    .tab-icon {
      font-size: 20px;
      color: #999;
      margin-bottom: 4px;
    }
    
    .tab-label {
      font-size: 12px;
      color: #999;
    }
    
    .tab-icon.active {
      color: #FF6B35;
    }
    
    .tab-label.active {
      color: #FF6B35;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left"></div>
      <div class="nav-title">账单</div>
      <div class="nav-right" style="display: flex; align-items: center;">
        <i class="fas fa-bell" style="color: #FF6B35; font-size: 16px;" id="reminder-icon"></i>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 日期和筛选 -->
      <div class="filter-section">
        <div class="date-selector">
          <div class="date-text">6月 2024</div>
          <i class="fas fa-chevron-down" style="color: #999; font-size: 14px;"></i>
        </div>
        
        <div class="filter-button">
          <i class="fas fa-filter"></i>
          <span>筛选</span>
        </div>
      </div>
      
      <!-- 月度汇总 -->
      <div class="card">
        <div class="summary-header">
          <div class="summary-title">本月汇总</div>
          <a href="#" class="summary-link">查看详情</a>
        </div>
        
        <div class="summary-stats">
          <div class="stat-item">
            <div class="stat-label">收入</div>
            <div class="stat-value income">￥9,800</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">支出</div>
            <div class="stat-value expense">￥3,520</div>
          </div>
          
          <div class="stat-item">
            <div class="stat-label">结余</div>
            <div class="stat-value">￥6,280</div>
          </div>
        </div>
        
        <!-- 支出分类 -->
        <div class="chart-title">主要支出</div>
        <div class="progress-bar">
          <div class="progress-segment" style="flex: 0.35; background-color: #FF6B35;"></div>
          <div class="progress-segment" style="flex: 0.25; background-color: #0EA5E9;"></div>
          <div class="progress-segment" style="flex: 0.20; background-color: #10B981;"></div>
          <div class="progress-segment" style="flex: 0.15; background-color: #8B5CF6;"></div>
          <div class="progress-segment" style="flex: 0.05; background-color: #EC4899;"></div>
        </div>
        
        <div class="chart-legend">
          <div class="legend-item">
            <div class="legend-dot" style="background-color: #FF6B35;"></div>
            <span>餐饮 35%</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot" style="background-color: #0EA5E9;"></div>
            <span>购物 25%</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot" style="background-color: #10B981;"></div>
            <span>交通 20%</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot" style="background-color: #8B5CF6;"></div>
            <span>娱乐 15%</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot" style="background-color: #EC4899;"></div>
            <span>其他 5%</span>
          </div>
        </div>
      </div>
      
      <!-- 按日期分组的账单 -->
      <div class="date-heading">今天</div>
      <div class="card">
        <div class="list">
          <div class="list-item">
            <div class="item-left">
              <div class="category-icon-container" style="background-color: #FFF5F2;">
                <i class="fas fa-utensils" style="color: #FF6B35; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">午餐</div>
                <div class="list-item-subtitle">12:30</div>
              </div>
            </div>
            <div class="list-item-right">
              <div class="list-item-amount amount-expense">-￥35.00</div>
            </div>
          </div>
          
          <div class="list-item">
            <div class="item-left">
              <div class="category-icon-container" style="background-color: #E0F2FE;">
                <i class="fas fa-shopping-bag" style="color: #0EA5E9; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">超市购物</div>
                <div class="list-item-subtitle">10:15</div>
              </div>
            </div>
            <div class="list-item-right">
              <div class="list-item-amount amount-expense">-￥129.80</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="date-heading">昨天</div>
      <div class="card">
        <div class="list">
          <div class="list-item">
            <div class="item-left">
              <div class="category-icon-container" style="background-color: #ECFDF5;">
                <i class="fas fa-money-bill-wave" style="color: #10B981; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">工资</div>
                <div class="list-item-subtitle">公司发放</div>
              </div>
            </div>
            <div class="list-item-right">
              <div class="list-item-amount amount-income">+￥8,500.00</div>
            </div>
          </div>
          
          <div class="list-item">
            <div class="item-left">
              <div class="category-icon-container" style="background-color: #FFE4E6;">
                <i class="fas fa-bus" style="color: #E11D48; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">交通费</div>
                <div class="list-item-subtitle">地铁</div>
              </div>
            </div>
            <div class="list-item-right">
              <div class="list-item-amount amount-expense">-￥15.00</div>
            </div>
          </div>
          
          <div class="list-item">
            <div class="item-left">
              <div class="category-icon-container" style="background-color: #FFF5F2;">
                <i class="fas fa-utensils" style="color: #FF6B35; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">晚餐</div>
                <div class="list-item-subtitle">外卖</div>
              </div>
            </div>
            <div class="list-item-right">
              <div class="list-item-amount amount-expense">-￥45.00</div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="date-heading">6月12日</div>
      <div class="card">
        <div class="list">
          <div class="list-item">
            <div class="item-left">
              <div class="category-icon-container" style="background-color: #F3E8FF;">
                <i class="fas fa-film" style="color: #8B5CF6; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">电影票</div>
                <div class="list-item-subtitle">周末娱乐</div>
              </div>
            </div>
            <div class="list-item-right">
              <div class="list-item-amount amount-expense">-￥80.00</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加按钮 - 左下角 -->
    <div class="fab">
      <i class="fas fa-plus"></i>
    </div>
    
    <!-- AI浮动按钮 - 右下角 -->
    <div class="ai-fab">
      <div class="ai-fab-tooltip">点击我聊天记账</div>
      <img src="2LOGO.png" alt="账无忌助手">
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <div class="tab-label">首页</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-list-ul tab-icon active"></i>
        <div class="tab-label active">账单</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-wallet tab-icon"></i>
        <div class="tab-label">资产</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-chart-pie tab-icon"></i>
        <div class="tab-label">分析</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <div class="tab-label">我的</div>
      </div>
    </div>
  </div>

  <script>
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 让AI按钮可点击，点击后跳转到AI聊天记账页面
      const aiButton = document.querySelector('.ai-fab');
      aiButton.addEventListener('click', function() {
        window.location.href = 'chat-accounting.html';
      });
      
      // 提醒图标点击事件
      const reminderIcon = document.getElementById('reminder-icon');
      reminderIcon.addEventListener('click', function() {
        console.log('跳转到账单提醒管理页面');
        // 实际项目中可以替换为: window.location.href = 'bill-reminders.html';
        alert('打开账单提醒管理页面，您可以在这里设置和管理所有账单提醒');
      });
      
      // 底部标签栏点击事件
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach((item, index) => {
        item.addEventListener('click', function() {
          switch(index) {
            case 0: // 首页
              window.location.href = 'home.html';
              break;
            case 1: // 账单
              // 已在当前页面
              break;
            case 2: // 资产
              window.location.href = 'assets.html';
              break;
            case 3: // 分析
              window.location.href = 'analysis.html';
              break;
            case 4: // 我的
              window.location.href = 'profile-new.html';
              break;
          }
        });
      });
    });
  </script>
</body>
</html>