<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>个人中心 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <!-- Removed link to external style.css -->
  <style>
    /* --- Base & Layout --- */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f9f9f9; /* Lighter background */
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #f9f9f9; /* Match body background */
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 8px 25px rgba(0,0,0,0.1); /* Softer shadow */
      display: flex;
      flex-direction: column;
      border: 12px solid #1c1c1e; /* Softer black */
    }

    /* --- Status Bar & Nav Bar --- */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      box-sizing: border-box;
      font-size: 14px;
      color: #1c1c1e;
      flex-shrink: 0;
      border-bottom: 1px solid #f0f0f0; /* Light border */
    }
    .status-bar-left { font-weight: 600; }
    .status-bar-right { display: flex; align-items: center; }
    .status-bar-icon { margin-left: 6px; }

    .nav-bar {
      background-color: #fff;
      /* Removed border and shadow for cleaner look with light background */
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      height: 44px;
    }
    .nav-left { width: 24px; color: #333; cursor: pointer; }
    .nav-right { width: 24px; }
    .nav-title {
      font-weight: 600;
      font-size: 17px;
      /* Removed orange underline for cleaner look */
    }

    /* --- Content Area --- */
    .content {
      flex: 1;
      padding: 15px; /* Consistent padding */
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f9f9f9;
      width: 100%;
      box-sizing: border-box;
    }

    /* --- Profile Header Card --- */
    .profile-header-card {
      background-color: white;
      border-radius: 18px; /* Softer radius */
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); /* Softer shadow */
      display: flex;
      align-items: center;
      cursor: pointer; /* Indicate tappable */
    }

    .avatar-wrapper {
      position: relative;
      margin-right: 15px;
      flex-shrink: 0;
    }

    .avatar {
      width: 64px; /* Larger avatar */
      height: 64px;
      border-radius: 50%;
      overflow: hidden;
      border: 2px solid #f0f0f0; /* Subtle border */
    }

    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-edit-badge { /* Optional edit badge */
      position: absolute;
      bottom: 0px;
      right: 0px;
      width: 20px;
      height: 20px;
      background-color: #FF6B35;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      border: 2px solid white;
    }

    .avatar-edit-badge i {
      color: white;
      font-size: 10px;
    }

    .user-info {
      flex: 1;
      min-width: 0; /* Prevent overflow issues */
    }

    .username {
      font-size: 18px; /* Larger username */
      font-weight: 600;
      color: #1c1c1e;
      margin-bottom: 5px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .user-id {
      font-size: 13px;
      color: #888; /* Lighter grey for ID */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }

    .profile-chevron {
        color: #bbb; /* Subtle chevron */
        margin-left: 10px;
    }


    /* --- General Card & List Styles --- */
    .card {
      background-color: white;
      border-radius: 18px;
      padding: 10px 20px; /* Adjusted padding for list cards */
      margin-bottom: 20px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    }

    .list {
      margin: 0;
      padding: 0;
    }

    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 16px 0; /* Increased padding */
      border-bottom: 1px solid #f5f5f5; /* Lighter border */
      cursor: pointer;
      transition: background-color 0.2s ease;
    }

    .list-item:last-child {
      border-bottom: none;
    }

    .list-item:hover { /* Subtle hover for desktop */
      background-color: #fafafa;
    }

    .item-left {
      display: flex;
      align-items: center;
      gap: 12px; /* Space between icon and label */
    }

    .item-icon {
      color: #FF6B35; /* Use accent color for icons */
      font-size: 16px; /* Consistent icon size */
      width: 20px; /* Ensure alignment */
      text-align: center;
    }

    .item-label {
      font-size: 15px; /* Slightly larger label */
      color: #333;
    }

    .item-right {
      display: flex;
      align-items: center;
      gap: 8px; /* Space between value and chevron */
    }

    .item-value {
      font-size: 14px;
      color: #555; /* Darker grey for set values */
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px; /* Prevent long values from pushing chevron */
    }

    .item-placeholder {
      font-size: 14px;
      color: #bbb; /* Lighter grey for placeholders */
    }

    .chevron-icon {
      color: #bbb; /* Consistent chevron color */
      font-size: 13px;
    }

    /* --- Section Title --- */
    .section-title {
      font-size: 14px;
      color: #888; /* Softer title color */
      margin: 0 0 10px 5px; /* Adjusted margin */
      font-weight: 500;
      text-transform: uppercase; /* Optional: Uppercase for distinction */
      letter-spacing: 0.5px; /* Optional: Spacing */
    }

    /* --- Specific Icons --- */
    /* Assign specific icons if needed, otherwise use generic */
    .item-icon.fa-mobile-alt { /* Example */ }


    /* Add some bottom padding to the content */
    .content {
        padding-bottom: 30px;
    }

  </style>
</head>
<body>
  <div class="phone-container">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">个人中心</div> <!-- Changed title slightly -->
      <div class="nav-right"></div>
    </div>

    <!-- Content Area -->
    <div class="content">
      <!-- Profile Header -->
      <div class="profile-header-card" onclick="navigateTo('edit-profile.html')"> <!-- Example navigation -->
        <div class="avatar-wrapper">
          <div class="avatar">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
          </div>
          <div class="avatar-edit-badge">
            <i class="fas fa-pencil-alt"></i>
          </div>
        </div>
        <div class="user-info">
          <div class="username">耀宸</div>
          <div class="user-id">ID: 10086</div> <!-- Example ID -->
        </div>
        <i class="fas fa-chevron-right profile-chevron"></i>
      </div>

      <!-- Basic Info Section -->
      <div class="section-title">基本资料</div>
      <div class="card">
        <div class="list">
          <div class="list-item" onclick="navigateTo('edit-phone.html')">
            <div class="item-left">
              <i class="fas fa-mobile-alt item-icon"></i>
              <span class="item-label">手机号</span>
            </div>
            <div class="item-right">
              <span class="item-value">138****0000</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>

          <div class="list-item" onclick="navigateTo('bind-email.html')">
            <div class="item-left">
              <i class="fas fa-envelope item-icon"></i>
              <span class="item-label">电子邮箱</span>
            </div>
            <div class="item-right">
              <span class="item-placeholder">未绑定</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>

          <div class="list-item" onclick="navigateTo('edit-birthday.html')">
            <div class="item-left">
              <i class="fas fa-birthday-cake item-icon"></i>
              <span class="item-label">生日</span>
            </div>
            <div class="item-right">
              <span class="item-value">1990-01-01</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>

          <div class="list-item" onclick="navigateTo('edit-occupation.html')">
            <div class="item-left">
              <i class="fas fa-briefcase item-icon"></i>
              <span class="item-label">职业</span>
            </div>
            <div class="item-right">
              <span class="item-placeholder">未设置</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- About Me Section -->
      <div class="section-title">关于我</div>
      <div class="card">
        <div class="list">
          <div class="list-item" onclick="navigateTo('edit-signature.html')">
            <div class="item-left">
              <i class="fas fa-pen-nib item-icon"></i>
              <span class="item-label">个性签名</span>
            </div>
            <div class="item-right">
              <span class="item-placeholder">点击设置个性签名</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>

          <div class="list-item" onclick="navigateTo('edit-financial-goal.html')">
            <div class="item-left">
              <i class="fas fa-bullseye item-icon"></i>
              <span class="item-label">财务目标</span>
            </div>
            <div class="item-right">
              <span class="item-value">存款30万</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>

          <div class="list-item" onclick="navigateTo('edit-consumption-tags.html')">
            <div class="item-left">
              <i class="fas fa-tags item-icon"></i>
              <span class="item-label">消费习惯标签</span>
            </div>
            <div class="item-right">
              <!-- Optional: Display some tags here if available -->
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>

      <!-- Settings Entry Section -->
      <div class="section-title">系统</div>
      <div class="card">
        <div class="list">
          <div class="list-item" onclick="navigateTo('settings.html')">
            <div class="item-left">
              <i class="fas fa-cog item-icon"></i>
              <span class="item-label">更多设置</span>
            </div>
            <div class="item-right">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>

    </div>
  </div>

  <script>
    // Basic navigation function (replace with actual routing/logic)
    function navigateTo(page) {
      console.log('Navigating to:', page);
      // Example: window.location.href = page;
      alert('跳转到: ' + page + ' (功能待实现)');
    }

    document.addEventListener('DOMContentLoaded', function() {
      // Back button functionality
      const backButton = document.querySelector('.nav-left');
      backButton.addEventListener('click', function() {
        // Using history.back() is often better than assuming a specific previous page
        window.history.back();
        // Alternatively, redirect to a specific page like the main 'My' tab
        // window.location.href = 'profile-main.html'; // Adjust if needed
      });

      // Note: Specific navigation for each list item is now handled by the
      // inline onclick="navigateTo('...')", which calls the helper function.
      // Keep the helper function or replace with your app's navigation system.
    });
  </script>
</body>
</html>