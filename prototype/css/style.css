/* 全局样式 */
:root {
  --primary-color: #4285f4; /* 修改为Google蓝色 */
  --primary-light: #93c5fd;
  --secondary-color: #10b981; /* 绿色 */
  --danger-color: #ef4444; /* 红色 */
  --warning-color: #f59e0b; /* 橙色 */
  --dark-color: #1f2937;
  --gray-color: #9ca3af;
  --light-gray: #f3f4f6;
  --bg-color: #ffffff;
  --text-primary: #111827;
  --text-secondary: #4b5563;
  --border-radius: 12px;
  --font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
  --device-width: 430px; /* iPhone 15 Pro Max宽度 */
  --device-height: 932px; /* iPhone 15 Pro Max高度 */
  --device-scale: 0.9; /* 默认屏幕缩放比例 */
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  color: var(--text-primary);
  background-color: #f9fafb;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
}

/* 适应不同的页面场景 */
body.index-page {
  height: 100vh;
  width: 100vw;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0;
  background-color: #f0f2f5;
}

body.phone-page {
  height: 100vh;
  width: 100vw;
  display: flex;
  justify-content: center;
  align-items: center;
  overflow: hidden;
  background-color: #f9fafb;
}

/* 模拟iPhone 15 Pro Max尺寸和样式 */
.phone-container {
  width: var(--device-width);
  height: var(--device-height);
  background-color: var(--bg-color);
  border-radius: 55px; /* 更大的圆角适合Pro Max */
  overflow: hidden;
  position: relative;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  border: 12px solid #000000; /* 黑色边框 */
  display: block;
}

/* 当在iframe中显示时使用不同样式 */
.iframe-view .phone-container {
  border: none;
  border-radius: 0;
  box-shadow: none;
  width: 100%;
  height: 100%;
}

/* 状态栏样式 */
.status-bar {
  height: 44px;
  background-color: var(--bg-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px; /* 更宽的边距适合大屏 */
  font-size: 14px; /* 更大字体 */
  font-weight: 600;
  color: var(--text-primary);
}

.status-bar-left {
  display: flex;
  align-items: center;
}

.status-bar-center {
  font-weight: 500;
}

.status-bar-right {
  display: flex;
  align-items: center;
}

.status-bar-icon {
  margin-left: 8px;
}

/* 导航栏样式 */
.nav-bar {
  height: 50px; /* 更高的导航栏 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px; /* 更宽的边距 */
  background-color: var(--bg-color);
}

.nav-title {
  font-size: 18px; /* 更大的标题 */
  font-weight: 600;
}

.nav-right, .nav-left {
  font-size: 17px;
  color: var(--primary-color);
}

/* 底部标签栏样式 */
.tab-bar {
  height: 90px; /* 更高的标签栏 */
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: var(--bg-color);
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  /* 确保不超出容器底部 */
  margin-bottom: 0;
  padding-bottom: 10px; /* 底部额外填充 */
}

.tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.tab-icon {
  font-size: 24px; /* 更大的图标 */
  color: var(--gray-color);
  margin-bottom: 4px;
}

.tab-icon.active {
  color: var(--primary-color);
}

.tab-label {
  font-size: 12px; /* 更大的标签文字 */
  color: var(--gray-color);
}

.tab-label.active {
  color: var(--primary-color);
}

/* 内容区域 */
.content {
  padding: 20px; /* 更大的内边距 */
  /* 修正高度计算 - 状态栏和导航栏高度增加 */
  height: calc(100% - 44px - 50px);
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: relative;
  /* 移除底部内边距，避免空白 */
  padding-bottom: 0;
}

/* 确保有底部标签栏的页面内容区域高度正确 */
.has-tab-bar .content {
  height: calc(100% - 44px - 50px - 90px);
}

/* 固定底部内容区域 */
.fixed-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20px; /* 更大的内边距 */
  background-color: var(--bg-color);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
}

/* 卡片样式 */
.card {
  background-color: var(--bg-color);
  border-radius: var(--border-radius);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
  padding: 16px;
  margin-bottom: 16px;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 按钮样式 */
.btn {
  padding: 12px 16px;
  border-radius: 10px;
  font-weight: 600;
  font-size: 16px;
  border: none;
  cursor: pointer;
  text-align: center;
  transition: background-color 0.2s;
}

.btn-primary {
  background-color: var(--primary-color);
  color: white;
}

.btn-secondary {
  background-color: var(--secondary-color);
  color: white;
}

.btn-danger {
  background-color: var(--danger-color);
  color: white;
}

.btn-outline {
  background-color: transparent;
  border: 1px solid var(--gray-color);
  color: var(--text-secondary);
}

/* 浮动操作按钮 */
.fab {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: var(--primary-color);
  position: absolute;
  bottom: 100px;
  right: 20px;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  color: white;
  font-size: 24px;
}

/* 输入框样式 */
.input-group {
  margin-bottom: 16px;
}

.input-label {
  display: block;
  margin-bottom: 8px;
  font-size: 14px;
  color: var(--text-secondary);
}

.input {
  width: 100%;
  padding: 12px 16px;
  border-radius: 10px;
  border: 1px solid #ddd;
  font-size: 16px;
  background-color: var(--light-gray);
}

.input:focus {
  outline: none;
  border-color: var(--primary-color);
}

/* 列表样式 */
.list {
  margin-bottom: 16px;
}

.list-item {
  padding: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-title {
  font-weight: 500;
}

.list-item-subtitle {
  font-size: 14px;
  color: var(--gray-color);
  margin-top: 4px;
}

.list-item-right {
  text-align: right;
}

.list-item-amount {
  font-weight: 600;
}

.amount-expense {
  color: var(--danger-color);
}

.amount-income {
  color: var(--secondary-color);
}

/* 进度条样式 */
.progress-container {
  height: 8px;
  background-color: var(--light-gray);
  border-radius: 4px;
  margin: 10px 0;
}

.progress-bar {
  height: 100%;
  border-radius: 4px;
  background-color: var(--primary-color);
}

.progress-danger {
  background-color: var(--danger-color);
}

.progress-warning {
  background-color: var(--warning-color);
}

.progress-success {
  background-color: var(--secondary-color);
}

/* 圆环进度样式 */
.circle-progress {
  position: relative;
  width: 120px;
  height: 120px;
  margin: 0 auto;
}

.circle-bg {
  fill: none;
  stroke: var(--light-gray);
  stroke-width: 10;
}

.circle-progress-bar {
  fill: none;
  stroke: var(--primary-color);
  stroke-width: 10;
  stroke-linecap: round;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 0.3s;
}

.circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.circle-percentage {
  font-size: 24px;
  font-weight: 700;
}

.circle-label {
  font-size: 14px;
  color: var(--gray-color);
}

/* 语音记账按钮样式 */
.voice-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background-color: var(--primary-color);
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.15);
  margin: 20px auto;
}

.voice-button .wave {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background-color: rgba(255, 255, 255, 0.2);
  position: absolute;
  animation: wave 1.5s infinite ease-out;
}

@keyframes wave {
  0% {
    transform: scale(0.8);
    opacity: 0.8;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

.voice-button i {
  color: white;
  font-size: 30px;
  z-index: 1;
}

/* 录音波形 */
.recording-waves {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  margin: 20px 0;
}

.wave-bar {
  width: 5px;
  height: 20px;
  background-color: var(--primary-color);
  margin: 0 2px;
  border-radius: 2px;
}

.animated-wave .wave-bar {
  animation: sound 0.5s infinite alternate;
}

@keyframes sound {
  0% {
    height: 5px;
  }
  100% {
    height: 40px;
  }
}

/* 工具栏 */
.toolbar {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
}

/* 分析图表区域 */
.chart-container {
  height: 200px;
  margin: 20px 0;
}

/* 颜色标签 */
.color-tag {
  display: inline-block;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  margin-right: 8px;
}

/* 图例 */
.chart-legend {
  display: flex;
  flex-wrap: wrap;
  margin-top: 16px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-right: 16px;
  margin-bottom: 8px;
  font-size: 12px;
}

/* 帮助美化界面的工具类 */
.mt-2 { margin-top: 8px; }
.mt-4 { margin-top: 16px; }
.mb-2 { margin-bottom: 8px; }
.mb-4 { margin-bottom: 16px; }
.mx-auto { margin-left: auto; margin-right: auto; }
.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-between { justify-content: space-between; }
.justify-center { justify-content: center; }
.text-center { text-align: center; }
.font-bold { font-weight: 700; }
.text-sm { font-size: 14px; }
.text-xs { font-size: 12px; }
.text-lg { font-size: 18px; }
.text-xl { font-size: 20px; }
.text-2xl { font-size: 24px; }
.text-primary { color: var(--primary-color); }
.text-secondary { color: var(--text-secondary); }
.text-danger { color: var(--danger-color); }
.text-success { color: var(--secondary-color); }
.w-full { width: 100%; }
.h-full { height: 100%; }
.rounded-full { border-radius: 9999px; }
.p-2 { padding: 8px; }
.p-4 { padding: 16px; }
.grid { display: grid; }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.gap-2 { gap: 8px; }
.gap-4 { gap: 16px; }

/* 容器样式调整 */
.index-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  padding: 20px;
  background-color: #f0f2f5;
  overflow-y: auto;
  max-height: 100vh;
  width: 100%;
}

/* 重设iframe容器样式 */
.prototype-container {
  display: flex;
  flex-direction: column;
  /* 高度调整为等比例缩放后的高度 */
  height: calc(var(--device-height) * var(--device-scale));
  overflow: hidden;
  margin-bottom: 20px;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  /* 确保边框颜色正确 */
  background-color: #FFFFFF;
}

.prototype-title {
  background-color: #4285f4;
  color: white;
  padding: 10px 15px;
  font-weight: 500;
  font-size: 16px;
  display: flex;
  align-items: center;
  border-top-left-radius: 8px;
  border-top-right-radius: 8px;
  /* 固定高度 */
  height: 40px;
}

/* 页面定制样式，应用LOGO蓝色 */
.page-title {
  background-color: #4285f4;
  color: white;
  padding: 10px 15px;
  text-align: center;
  font-weight: 500;
  font-size: 16px;
}

/* 确保iframe不溢出 */
iframe {
  border: none;
  width: 100%;
  /* 与prototype-container高度相减 */
  height: calc(100% - 40px);
  overflow: hidden;
  /* 保持边框与参考图一致 */
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
}

/* 媒体查询 - 小屏幕调整缩放比例 */
@media (max-height: 1000px) {
  :root {
    --device-scale: 0.8;
  }
}

@media (max-height: 800px) {
  :root {
    --device-scale: 0.7;
  }
}

/* 欢迎页星空背景 */
.starfield-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  z-index: 0;
}

#starfield {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1;
}

/* 欢迎页渐变背景 */
.welcome-bg {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #2b3346 0%, #0b111e 100%);
  z-index: 0;
}

/* 3D卡通形象 */
.avatar-container {
  position: relative;
  width: 180px;
  height: 180px;
  margin: 0 auto 20px;
  z-index: 2;
  transform-style: preserve-3d;
  perspective: 1000px;
}

.avatar-container img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transform: translateZ(0);
  transition: transform 0.5s ease;
}

/* 头像挥手动画 */
.avatar-container img.wave-animation {
  animation: wave-hand 1s ease-in-out;
}

@keyframes wave-hand {
  0% { transform: rotate(0deg); }
  20% { transform: rotate(-10deg); }
  40% { transform: rotate(10deg); }
  60% { transform: rotate(-10deg); }
  80% { transform: rotate(10deg); }
  100% { transform: rotate(0deg); }
}

/* 欢迎页内容 */
.welcome-content {
  position: relative;
  z-index: 2;
  text-align: center;
  padding: 0 20px;
}

.welcome-title {
  font-size: 28px;
  font-weight: 700;
  margin-bottom: 15px;
  color: #fff;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.welcome-slogan {
  color: rgba(255, 255, 255, 0.8);
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 40px;
}

/* 功能球设计 */
.function-balls {
  display: flex;
  justify-content: space-between;
  margin: 30px auto;
  max-width: 300px;
  position: relative;
  z-index: 2;
}

.function-ball {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2), 
              inset 0 1px 1px rgba(255, 255, 255, 0.2);
  opacity: 0;
  transform: translateY(20px) scale(0.8);
  transition: all 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.function-ball.show {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.function-ball.float {
  animation: float 2s ease-in-out;
}

@keyframes float {
  0% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0); }
}

.function-ball.pulse {
  animation: pulse 0.5s cubic-bezier(0.34, 1.56, 0.64, 1);
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

.function-ball::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.1) 60%);
  z-index: -1;
}

.function-ball::after {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  width: calc(100% + 4px);
  height: calc(100% + 4px);
  border-radius: 50%;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0) 50%, rgba(255, 255, 255, 0.2) 100%);
  z-index: -1;
  opacity: 0.5;
}

.function-icon {
  font-size: 24px;
  color: #fff;
  margin-bottom: 5px;
  position: relative;
}

.function-name {
  font-size: 10px;
  color: rgba(255, 255, 255, 0.9);
  font-weight: 500;
}

/* 功能特殊动画 */
.scan-animation {
  animation: scan 1.5s ease-in-out;
}

@keyframes scan {
  0% { transform: translateY(0); }
  25% { transform: translateY(-5px); }
  50% { transform: translateY(0); }
  75% { box-shadow: 0 0 0 rgba(255, 255, 255, 0); }
  80% { box-shadow: 0 0 15px rgba(255, 255, 255, 0.8); }
  100% { box-shadow: 0 0 0 rgba(255, 255, 255, 0); }
}

.frame-animation {
  animation: frame 1.5s ease-in-out;
}

@keyframes frame {
  0% { transform: scale(1); }
  20% { transform: scale(1.1); }
  40% { transform: scale(0.9); }
  60% { transform: scale(1.05); }
  80% { transform: scale(0.95); }
  100% { transform: scale(1); }
}

.bubble-animation {
  animation: bubble 1.5s ease-in-out;
}

@keyframes bubble {
  0% { transform: scale(1); opacity: 1; }
  20% { transform: scale(1.2); opacity: 0.8; }
  40% { transform: scale(1.4); opacity: 0.6; }
  60% { transform: scale(1.6); opacity: 0.4; }
  80% { transform: scale(1.8); opacity: 0.2; }
  100% { transform: scale(2); opacity: 0; }
}