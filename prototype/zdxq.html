<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>账单详情 - AI记账</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* 全局样式 */
    :root {
      --color-primary: #FF6B35;
      --color-income: #10B981;
      --text-primary: #333;
      --text-secondary: #666;
      --text-tertiary: #999;
      --bg-primary: #fff;
      --border-color: rgba(0, 0, 0, 0.05);
      --card-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
      --card-radius: 18px;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: var(--text-primary);
      background-color: var(--bg-primary);
      margin: 0;
      padding: 0;
    }
    
    /* 状态栏样式 */
    .status-bar {
      display: flex;
      justify-content: space-between;
      padding: 5px 15px;
      background-color: var(--bg-primary);
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 20px;
      background-color: var(--bg-primary);
      position: relative;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: var(--color-primary);
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域样式 */
    .content {
      padding: 0 20px 100px;
    }
    
    /* 卡片样式 */
    .card {
      border-radius: var(--card-radius);
      box-shadow: var(--card-shadow);
      margin-bottom: 20px;
      overflow: hidden;
      background-color: var(--bg-primary);
    }
    
    /* 图标圆圈样式 */
    .icon-circle {
      width: 60px;
      height: 60px;
      border-radius: 18px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 15px;
    }
    
    /* 金额样式 */
    .amount-expense {
      color: var(--color-primary);
      font-weight: 700;
      font-size: 32px;
    }
    
    .amount-income {
      color: var(--color-income);
      font-weight: 700;
      font-size: 32px;
    }
    
    /* 详情项样式 */
    .detail-item {
      display: flex;
      justify-content: space-between;
      padding: 16px 0;
      border-bottom: 1px solid var(--border-color);
    }
    
    .detail-item:last-child {
      border-bottom: none;
    }
    
    .detail-label {
      color: var(--text-secondary);
      font-size: 15px;
    }
    
    .detail-value {
      color: var(--text-primary);
      font-size: 15px;
      font-weight: 500;
      text-align: right;
      word-break: break-word;
      max-width: 60%;
    }
    
    /* 删除按钮样式 */
    .delete-button {
      background-color: #FFF1F0;
      color: #FF4D4F;
      border: none;
      border-radius: 24px;
      padding: 12px 0;
      width: 100%;
      font-size: 16px;
      font-weight: 500;
      margin-top: 20px;
      transition: all 0.3s ease;
    }
    
    .delete-button:active {
      background-color: #FFE4E6;
      transform: scale(0.98);
    }
    
    /* 模态框样式 */
    .modal {
      display: none;
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      z-index: 1000;
      justify-content: center;
      align-items: center;
    }
    
    .modal-content {
      background-color: var(--bg-primary);
      border-radius: 16px;
      padding: 24px;
      width: 80%;
      max-width: 320px;
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 16px;
      text-align: center;
    }
    
    .modal-buttons {
      display: flex;
      justify-content: space-between;
      margin-top: 24px;
    }
    
    .modal-button {
      border: none;
      border-radius: 20px;
      padding: 10px 0;
      width: 48%;
      font-size: 15px;
      font-weight: 500;
    }
    
    .modal-button-cancel {
      background-color: #F5F5F5;
      color: var(--text-secondary);
    }
    
    .modal-button-confirm {
      background-color: #FF4D4F;
      color: white;
    }
    
    /* 备注展开收起样式 */
    .note-content {
      max-height: 60px;
      overflow: hidden;
      transition: max-height 0.3s ease;
    }
    
    .note-content.expanded {
      max-height: 500px;
    }
    
    .note-toggle {
      color: var(--color-primary);
      font-size: 14px;
      margin-top: 5px;
      display: none;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left" style="font-size: 16px;"></i>
      </div>
      <div class="nav-title">账单详情</div>
      <div class="nav-right">
        <i class="fas fa-pencil-alt" style="font-size: 16px; color: var(--text-secondary);"></i>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 核心信息区 -->
      <div style="text-align: center; margin: 20px 0 30px;">
        <div style="display: flex; justify-content: center; margin-bottom: 15px;">
          <div class="icon-circle" style="background-color: #FFF5F2;">
            <i class="fas fa-utensils" style="color: var(--color-primary); font-size: 24px;"></i>
          </div>
        </div>
        <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">午餐</div>
        <div class="amount-expense">-￥35.00</div>
      </div>
      
      <!-- 详细信息卡片 -->
      <div class="card" style="padding: 20px;">
        <div class="detail-item">
          <div class="detail-label">类型</div>
          <div class="detail-value">支出</div>
        </div>
        
        <div class="detail-item">
          <div class="detail-label">时间</div>
          <div class="detail-value">2025-03-30 12:30</div>
        </div>
        
        <div class="detail-item">
          <div class="detail-label">账户</div>
          <div class="detail-value">招商银行储蓄卡(1234)</div>
        </div>
        
        <div class="detail-item">
          <div class="detail-label">备注</div>
          <div class="detail-value">
            <div class="note-content" id="noteContent">和同事在公司附近的餐厅吃的工作餐，可以报销。下次可以尝试对面新开的那家餐厅，据说很不错。</div>
            <div class="note-toggle" id="noteToggle">展开</div>
          </div>
        </div>
      </div>
      
      <!-- 删除按钮 -->
      <button class="delete-button" id="deleteButton">
        <i class="fas fa-trash-alt" style="margin-right: 8px;"></i>删除此账单
      </button>
    </div>
    
    <!-- 确认删除模态框 -->
    <div class="modal" id="deleteModal">
      <div class="modal-content">
        <div class="modal-title">确认删除这条记录吗？</div>
        <div style="text-align: center; color: var(--text-secondary); font-size: 14px;">删除后将无法恢复</div>
        <div class="modal-buttons">
          <button class="modal-button modal-button-cancel" id="cancelDelete">取消</button>
          <button class="modal-button modal-button-confirm" id="confirmDelete">确认</button>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 返回按钮点击事件
      const backButton = document.querySelector('.nav-left');
      backButton.addEventListener('click', function() {
        window.location.href = 'home.html';
      });
      
      // 编辑按钮点击事件
      const editButton = document.querySelector('.nav-right');
      editButton.addEventListener('click', function() {
        window.location.href = 'edit-bill.html';
      });
      
      // 备注展开收起功能
      const noteContent = document.getElementById('noteContent');
      const noteToggle = document.getElementById('noteToggle');
      
      // 检查备注内容高度，决定是否显示展开按钮
      if (noteContent.scrollHeight > 60) {
        noteToggle.style.display = 'block';
      }
      
      noteToggle.addEventListener('click', function() {
        if (noteContent.classList.contains('expanded')) {
          noteContent.classList.remove('expanded');
          noteToggle.textContent = '展开';
        } else {
          noteContent.classList.add('expanded');
          noteToggle.textContent = '收起';
        }
      });
      
      // 删除按钮点击事件
      const deleteButton = document.getElementById('deleteButton');
      const deleteModal = document.getElementById('deleteModal');
      
      deleteButton.addEventListener('click', function() {
        deleteModal.style.display = 'flex';
      });
      
      // 取消删除
      const cancelDelete = document.getElementById('cancelDelete');
      cancelDelete.addEventListener('click', function() {
        deleteModal.style.display = 'none';
      });
      
      // 确认删除
      const confirmDelete = document.getElementById('confirmDelete');
      confirmDelete.addEventListener('click', function() {
        console.log('删除账单');
        // 这里可以添加删除账单的逻辑
        // 删除成功后返回首页
        window.location.href = 'home.html';
      });
      
      // 点击模态框外部关闭模态框
      deleteModal.addEventListener('click', function(event) {
        if (event.target === deleteModal) {
          deleteModal.style.display = 'none';
        }
      });
    });
  </script>
</body>
</html>