<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>数据分析 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
      color: #666;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    /* 时间选择器样式 */
    .time-filter {
      display: flex;
      margin-bottom: 20px;
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
    }
    
    .time-filter::-webkit-scrollbar {
      display: none;
    }
    
    .time-option {
      padding: 8px 16px;
      background-color: #f0f0f0;
      color: #666;
      border-radius: 20px;
      margin-right: 10px;
      white-space: nowrap;
      font-size: 14px;
      transition: all 0.3s ease;
    }
    
    .time-option.active {
      background-color: #FF6B35;
      color: white;
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.2);
    }
    
    /* 数据概览样式 */
    .overview-section {
      display: flex;
      margin-bottom: 20px;
    }
    
    .overview-column {
      flex: 1;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .overview-column:first-child {
      padding-right: 15px;
      border-right: 1px solid #f0f0f0;
    }
    
    .overview-column:last-child {
      padding-left: 15px;
    }
    
    .amount {
      font-size: 22px;
      font-weight: 600;
      margin-bottom: 5px;
    }
    
    .amount.expense {
      color: #FF4D4F;
    }
    
    .amount.income {
      color: #52C41A;
    }
    
    .label {
      font-size: 14px;
      color: #999;
    }
    
    /* 环比样式 */
    .comparison-box {
      display: flex;
      background-color: #f8f8f8;
      border-radius: 10px;
      padding: 12px;
    }
    
    .comparison-item {
      flex: 1;
      text-align: center;
    }
    
    .comparison-item:first-child {
      border-right: 1px solid rgba(0,0,0,0.05);
    }
    
    .comparison-label {
      font-size: 13px;
      color: #999;
      margin-bottom: 5px;
    }
    
    .comparison-value {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .value-decrease {
      color: #52C41A;
    }
    
    .value-increase {
      color: #FF4D4F;
    }
    
    /* 分类数据样式 */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
    }
    
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin: 0;
    }
    
    .section-filter {
      display: flex;
      align-items: center;
      font-size: 14px;
      color: #999;
    }
    
    /* 分类图表样式 */
    .chart-container {
      display: flex;
      margin-bottom: 20px;
    }
    
    .pie-chart {
      width: 120px;
      height: 120px;
      position: relative;
    }
    
    .chart-center {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;
    }
    
    .chart-center-title {
      font-size: 14px;
      font-weight: 600;
    }
    
    .chart-center-value {
      font-size: 12px;
      color: #999;
    }
    
    .chart-legend {
      flex: 1;
      margin-left: 20px;
    }
    
    .legend-item {
      margin-bottom: 12px;
    }
    
    .legend-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 5px;
    }
    
    .legend-color {
      width: 10px;
      height: 10px;
      border-radius: 50%;
      margin-right: 8px;
    }
    
    .legend-name {
      font-size: 14px;
    }
    
    .legend-value {
      font-size: 14px;
      font-weight: 500;
    }
    
    .progress-bar-container {
      height: 5px;
      background-color: #f0f0f0;
      border-radius: 3px;
    }
    
    .progress-fill {
      height: 100%;
      border-radius: 3px;
    }
    
    .legend-info {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
      margin-top: 2px;
    }
    
    /* 按钮样式 */
    .btn {
      display: inline-block;
      border: none;
      border-radius: 12px;
      font-size: 14px;
      font-weight: 500;
      padding: 10px 16px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      width: 100%;
      box-sizing: border-box;
    }
    
    .btn-outline {
      background-color: transparent;
      border: 1px solid #FF6B35;
      color: #FF6B35;
    }
    
    .btn-outline:active {
      background-color: rgba(255, 107, 53, 0.05);
    }
    
    /* 趋势图样式 */
    .trend-chart {
      height: 200px;
      margin-bottom: 10px;
    }
    
    /* 预算进度样式 */
    .budget-item {
      margin-bottom: 12px;
    }
    
    .budget-header {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }
    
    .budget-category {
      display: flex;
      align-items: center;
    }
    
    .budget-info {
      font-size: 14px;
    }
    
    .progress-container {
      height: 6px;
      background-color: #f0f0f0;
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 5px;
    }
    
    .progress-bar {
      height: 100%;
      border-radius: 3px;
    }
    
    .progress-warning {
      background-color: #FAAD14;
    }
    
    .progress-success {
      background-color: #52C41A;
    }
    
    .progress-danger {
      background-color: #FF4D4F;
    }
    
    .budget-stats {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
    }
    
    .budget-over {
      color: #FF4D4F;
    }
    
    /* 底部标签栏 */
    .tab-bar {
      height: 60px;
      background-color: white;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid rgba(0,0,0,0.05);
      flex-shrink: 0;
    }
    
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 8px 0;
      flex: 1;
    }
    
    .tab-icon {
      font-size: 20px;
      color: #999;
      margin-bottom: 4px;
    }
    
    .tab-label {
      font-size: 12px;
      color: #999;
    }
    
    .tab-icon.active {
      color: #FF6B35;
    }
    
    .tab-label.active {
      color: #FF6B35;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left"></div>
      <div class="nav-title">数据分析</div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 时间选择器 -->
      <div class="time-filter">
        <div class="time-option active">本月</div>
        <div class="time-option">上月</div>
        <div class="time-option">近3个月</div>
        <div class="time-option">近半年</div>
        <div class="time-option">今年</div>
        <div class="time-option">自定义</div>
      </div>
      
      <!-- 收支概览 -->
      <div class="card">
        <h3 class="section-title">收支概览</h3>
        <div class="overview-section">
          <div class="overview-column">
            <div class="amount expense">￥3,520</div>
            <div class="label">支出</div>
          </div>
          <div class="overview-column">
            <div class="amount income">￥9,800</div>
            <div class="label">收入</div>
          </div>
        </div>
        
        <!-- 环比增长 -->
        <div class="comparison-box">
          <div class="comparison-item">
            <div class="comparison-label">支出环比</div>
            <div class="comparison-value">
              <i class="fas fa-arrow-down" style="color: #52C41A; font-size: 12px; margin-right: 5px;"></i>
              <span class="value-decrease">5.2%</span>
            </div>
          </div>
          <div class="comparison-item">
            <div class="comparison-label">收入环比</div>
            <div class="comparison-value">
              <i class="fas fa-arrow-up" style="color: #FF4D4F; font-size: 12px; margin-right: 5px;"></i>
              <span class="value-increase">12.8%</span>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 支出分类 -->
      <div class="card">
        <div class="section-header">
          <h3 class="section-title">支出分类</h3>
          <div class="section-filter">
            <div>按金额</div>
            <i class="fas fa-chevron-down" style="font-size: 12px; margin-left: 5px;"></i>
          </div>
        </div>
        
        <!-- 总支出金额显示 -->
        <div style="text-align: center; margin-bottom: 20px;">
          <div style="font-size: 16px; font-weight: 500; color: #666;">总支出</div>
          <div style="font-size: 24px; font-weight: 600; color: #333;">￥3,520</div>
        </div>
        
        <!-- 分类条形图 -->
        <div style="margin-bottom: 30px;">
          <!-- 餐饮类别 -->
          <div style="margin-bottom: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
              <div style="display: flex; align-items: center;">
                <i class="fas fa-utensils" style="color: white; background-color: #F59E0B; width: 24px; height: 24px; border-radius: 6px; display: flex; justify-content: center; align-items: center; margin-right: 8px;"></i>
                <span style="font-size: 15px; font-weight: 500;">餐饮</span>
              </div>
              <div style="display: flex; align-items: center;">
                <span style="font-size: 15px; font-weight: 600; margin-right: 5px;">￥1,232</span>
                <span style="font-size: 14px; color: #999;">35%</span>
              </div>
            </div>
            <div style="height: 10px; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; position: relative;">
              <div style="position: absolute; height: 100%; width: 35%; background-color: #F59E0B; border-radius: 5px;"></div>
            </div>
          </div>
          
          <!-- 购物类别 -->
          <div style="margin-bottom: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
              <div style="display: flex; align-items: center;">
                <i class="fas fa-shopping-bag" style="color: white; background-color: #0EA5E9; width: 24px; height: 24px; border-radius: 6px; display: flex; justify-content: center; align-items: center; margin-right: 8px;"></i>
                <span style="font-size: 15px; font-weight: 500;">购物</span>
              </div>
              <div style="display: flex; align-items: center;">
                <span style="font-size: 15px; font-weight: 600; margin-right: 5px;">￥880</span>
                <span style="font-size: 14px; color: #999;">25%</span>
              </div>
            </div>
            <div style="height: 10px; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; position: relative;">
              <div style="position: absolute; height: 100%; width: 25%; background-color: #0EA5E9; border-radius: 5px;"></div>
            </div>
          </div>
          
          <!-- 交通类别 -->
          <div style="margin-bottom: 16px;">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
              <div style="display: flex; align-items: center;">
                <i class="fas fa-car" style="color: white; background-color: #10B981; width: 24px; height: 24px; border-radius: 6px; display: flex; justify-content: center; align-items: center; margin-right: 8px;"></i>
                <span style="font-size: 15px; font-weight: 500;">交通</span>
              </div>
              <div style="display: flex; align-items: center;">
                <span style="font-size: 15px; font-weight: 600; margin-right: 5px;">￥704</span>
                <span style="font-size: 14px; color: #999;">20%</span>
              </div>
            </div>
            <div style="height: 10px; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; position: relative;">
              <div style="position: absolute; height: 100%; width: 20%; background-color: #10B981; border-radius: 5px;"></div>
            </div>
          </div>
          
          <!-- 其他类别 -->
          <div>
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 6px;">
              <div style="display: flex; align-items: center;">
                <i class="fas fa-ellipsis-h" style="color: white; background-color: #8B5CF6; width: 24px; height: 24px; border-radius: 6px; display: flex; justify-content: center; align-items: center; margin-right: 8px;"></i>
                <span style="font-size: 15px; font-weight: 500;">其他</span>
              </div>
              <div style="display: flex; align-items: center;">
                <span style="font-size: 15px; font-weight: 600; margin-right: 5px;">￥704</span>
                <span style="font-size: 14px; color: #999;">20%</span>
              </div>
            </div>
            <div style="height: 10px; background-color: #f0f0f0; border-radius: 5px; overflow: hidden; position: relative;">
              <div style="position: absolute; height: 100%; width: 20%; background-color: #8B5CF6; border-radius: 5px;"></div>
            </div>
          </div>
        </div>
        
        <!-- 查看更多分类按钮 -->
        <div style="border-top: 1px solid rgba(0,0,0,0.05); padding-top: 15px; margin-top: 10px;">
          <a href="#" class="btn btn-outline" style="text-decoration: none; display: block; text-align: center; color: #FF6B35; border: 1px solid #FF6B35; border-radius: 12px; padding: 10px 0; font-size: 14px; font-weight: 500;">查看更多分类</a>
        </div>
      </div>
      
      <!-- 消费趋势 -->
      <div class="card">
        <div class="section-header">
          <h3 class="section-title">消费趋势</h3>
          <div class="section-filter">
            <div>按周</div>
            <i class="fas fa-chevron-down" style="font-size: 12px; margin-left: 5px;"></i>
          </div>
        </div>
        
        <!-- 图表容器 -->
        <div class="trend-chart">
          <!-- 这里放折线图，用CSS模拟 -->
          <div style="height: 100%; position: relative; padding-bottom: 20px;">
            <!-- Y轴标签 -->
            <div style="position: absolute; top: 0; left: 0; height: 100%; display: flex; flex-direction: column; justify-content: space-between; color: #999; font-size: 10px; padding-right: 5px;">
              <div>￥2k</div>
              <div>￥1.5k</div>
              <div>￥1k</div>
              <div>￥0.5k</div>
              <div>￥0</div>
            </div>
            
            <!-- 图表内容 -->
            <div style="margin-left: 30px; height: 100%; position: relative;">
              <!-- 水平网格线 -->
              <div style="position: absolute; top: 0; left: 0; right: 0; height: 1px; background-color: rgba(0,0,0,0.05);"></div>
              <div style="position: absolute; top: 25%; left: 0; right: 0; height: 1px; background-color: rgba(0,0,0,0.05);"></div>
              <div style="position: absolute; top: 50%; left: 0; right: 0; height: 1px; background-color: rgba(0,0,0,0.05);"></div>
              <div style="position: absolute; top: 75%; left: 0; right: 0; height: 1px; background-color: rgba(0,0,0,0.05);"></div>
              <div style="position: absolute; bottom: 0; left: 0; right: 0; height: 1px; background-color: rgba(0,0,0,0.05);"></div>
              
              <!-- X轴标签 -->
              <div style="position: absolute; bottom: -20px; left: 0; right: 0; display: flex; justify-content: space-between; color: #999; font-size: 10px;">
                <div>第1周</div>
                <div>第2周</div>
                <div>第3周</div>
                <div>第4周</div>
              </div>
              
              <!-- 折线 - 用CSS border模拟 -->
              <svg style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;">
                <polyline points="0,150 80,80 160,120 240,40" style="fill:none; stroke:#FF6B35; stroke-width:2;" />
                <circle cx="0" cy="150" r="4" style="fill:#FF6B35;" />
                <circle cx="80" cy="80" r="4" style="fill:#FF6B35;" />
                <circle cx="160" cy="120" r="4" style="fill:#FF6B35;" />
                <circle cx="240" cy="40" r="4" style="fill:#FF6B35;" />
              </svg>
            </div>
          </div>
        </div>
        
        <div style="font-size: 14px; color: #999;">
          本月消费趋势呈<span style="color: #FF4D4F; font-weight: 500;">上升</span>趋势，第4周支出较高
        </div>
      </div>
      
      <!-- 预算执行情况 -->
      <div class="card" style="margin-bottom: 50px;">
        <div class="section-header">
          <h3 class="section-title">预算执行情况</h3>
          <a href="#" style="font-size: 14px; color: #FF6B35; text-decoration: none;">设置预算</a>
        </div>
        
        <div class="budget-item">
          <div class="budget-header">
            <div class="budget-category">
              <div class="legend-color" style="background-color: #F59E0B;"></div>
              <span style="font-size: 14px;">餐饮</span>
            </div>
            <div class="budget-info">￥1,232 / ￥1,500</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar progress-warning" style="width: 82%;"></div>
          </div>
          <div class="budget-stats">
            <span>已用82%</span>
            <span>剩余￥268</span>
          </div>
        </div>
        
        <div class="budget-item">
          <div class="budget-header">
            <div class="budget-category">
              <div class="legend-color" style="background-color: #0EA5E9;"></div>
              <span style="font-size: 14px;">购物</span>
            </div>
            <div class="budget-info">￥880 / ￥1,000</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar progress-success" style="width: 88%;"></div>
          </div>
          <div class="budget-stats">
            <span>已用88%</span>
            <span>剩余￥120</span>
          </div>
        </div>
        
        <div class="budget-item" style="margin-bottom: 0;">
          <div class="budget-header">
            <div class="budget-category">
              <div class="legend-color" style="background-color: #10B981;"></div>
              <span style="font-size: 14px;">交通</span>
            </div>
            <div class="budget-info">￥704 / ￥600</div>
          </div>
          <div class="progress-container">
            <div class="progress-bar progress-danger" style="width: 117%;"></div>
          </div>
          <div class="budget-stats">
            <span class="budget-over">超预算17%</span>
            <span class="budget-over">超出￥104</span>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <div class="tab-label">首页</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-list-ul tab-icon"></i>
        <div class="tab-label">账单</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-wallet tab-icon"></i>
        <div class="tab-label">资产</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-chart-pie tab-icon active"></i>
        <div class="tab-label active">分析</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <div class="tab-label">我的</div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 时间选择
      const timeOptions = document.querySelectorAll('.time-option');
      timeOptions.forEach(option => {
        option.addEventListener('click', function() {
          timeOptions.forEach(o => o.classList.remove('active'));
          this.classList.add('active');
        });
      });
      
      // 标签切换
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach((item, index) => {
        item.addEventListener('click', function() {
          // 这里可以添加页面跳转逻辑
          switch(index) {
            case 0: // 首页
              window.location.href = 'home.html';
              break;
            case 1: // 账单
              window.location.href = 'transactions.html';
              break;
            case 2: // 资产
              window.location.href = 'assets.html';
              break;
            case 3: // 分析
              // 已在当前页面
              break;
            case 4: // 我的
              window.location.href = 'profile-new.html';
              break;
          }
        });
      });
    });
  </script>
</body>
</html>