<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>个性化账无忌 - AI智能记账</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #fff;
    }
    
    /* 背景样式 - 轻微渐变背景 */
    .content {
      padding: 15px 20px;
      background-color: #F8F9FA;
      min-height: calc(100vh - 94px);
      padding-bottom: 0;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.03);
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    /* 导航标题下方装饰线条 */
    .nav-title::after {
      content: "";                    /* 创建伪元素 */
      display: block;                 /* 设置为块级元素 */
      width: 30px;                   /* 线条宽度 */
      height: 3px;                   /* 线条高度 */
      background: #FF6B35;           /* 橙色背景 */
      position: absolute;            /* 绝对定位 */
      bottom: -5px;                  /* 距离底部5px */
      left: 50%;                     /* 水平居中 */
      transform: translateX(-50%);    /* X轴偏移实现精确居中 */
      border-radius: 2px;            /* 圆角效果 */
    }
    
    /* 返回按钮 */
    .back-button {
      color: #333;
      font-size: 20px;
      display: flex;
      align-items: center;
      justify-content: center;
      width: 36px;
      height: 36px;
      border-radius: 50%;
      transition: background-color 0.2s;
    }
    
    .back-button:active {
      background-color: rgba(0, 0, 0, 0.05);
    }
    
    /* 卡片样式 */
    .card {
      background-color: #fff;
      border-radius: 18px;
      margin-bottom: 20px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.04);
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    /* AI头像展示区 */
    .ai-profile {
      position: relative;
      padding: 25px 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      background: linear-gradient(135deg, #FFF5F2, #FFF9F7);
    }
    
    .ai-avatar {
      width: 110px;
      height: 110px;
      border-radius: 35%;
      object-fit: cover;
      box-shadow: 0 8px 16px rgba(255, 107, 53, 0.2);
      border: 3px solid #fff;
      margin-bottom: 15px;
    }
    
    .ai-name {
      font-size: 24px;
      font-weight: 700;
      color: #FF6B35;
      margin: 0;
      text-shadow: 0 1px 0px #fff;
    }
    
    .ai-status {
      margin-top: 5px;
      display: inline-flex;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.7);
      padding: 4px 12px;
      border-radius: 50px;
      font-size: 13px;
      color: #666;
    }
    
    .ai-status i {
      color: #4CAF50;
      margin-right: 5px;
    }
    
    .relation-badge {
      position: absolute;
      top: 20px;
      right: 20px;
      background-color: #FF6B35;
      color: white;
      padding: 6px 12px;
      border-radius: 50px;
      font-size: 13px;
      font-weight: 500;
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    }
    
    /* 信任度进度条 */
    .trust-meter {
      width: 85%;
      margin: 15px auto 0;
    }
    
    .trust-bar {
      height: 6px;
      background-color: rgba(255, 107, 53, 0.2);
      border-radius: 3px;
      overflow: hidden;
      margin-bottom: 5px;
    }
    
    .trust-level {
      height: 100%;
      background-color: #FF6B35;
      border-radius: 3px;
    }
    
    .trust-label {
      display: flex;
      justify-content: space-between;
      font-size: 12px;
      color: #999;
    }
    
    /* 设置类别标签 */
    .settings-tabs {
      display: flex;
      align-items: center;
      margin-bottom: 15px;
      padding: 8px;
      background-color: #fff;
      border-radius: 15px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
    }
    
    .tab-item {
      flex: 1;
      padding: 8px 0;
      text-align: center;
      font-size: 14px;
      color: #666;
      border-radius: 10px;
      transition: all 0.3s ease;
    }
    
    .tab-item.active {
      color: #FF6B35;
      background-color: #FFF5F2;
      font-weight: 500;
    }
    
    /* 设置项目样式 */
    .setting-group {
      margin-bottom: 15px;
    }
    
    .setting-title {
      font-size: 14px;
      color: #666;
      margin-bottom: 8px;
      padding-left: 15px;
      margin-top: 20px;
    }
    
    .setting-item {
      display: flex;
      align-items: center;
      padding: 16px 20px;
      border-bottom: 1px solid rgba(0, 0, 0, 0.05);
      position: relative;
    }
    
    .setting-item:last-child {
      border-bottom: none;
    }
    
    .setting-item:active {
      background-color: #f9f9f9;
    }
    
    .setting-icon {
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      margin-right: 15px;
      font-size: 16px;
      color: white;
    }
    
    .icon-relationship {
      background-color: #1E88E5;
    }
    
    .icon-tone {
      background-color: #7B61FF;
    }
    
    .icon-style {
      background-color: #E91E63;
    }
    
    .icon-interests {
      background-color: #43A047;
    }
    
    .setting-content {
      flex: 1;
    }
    
    .setting-label {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 2px;
      color: #333;
    }
    
    .setting-desc {
      font-size: 13px;
      color: #999;
    }
    
    .setting-action {
      margin-left: 15px;
      color: #999;
    }
    
    /* 个性标签容器 */
    .personality-container {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      padding: 15px 20px 20px;
    }
    
    /* 个性标签样式 */
    .personality-tag {
      padding: 10px 18px;
      border-radius: 50px;
      background-color: #f5f5f5;
      color: #555;
      font-size: 14px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      transition: all 0.2s ease;
      user-select: none;
    }
    
    .personality-tag.selected {
      background-color: #FFF5F2;
      color: #FF6B35;
      border: 1px solid #FFCDB7;
      font-weight: 500;
    }
    
    .personality-tag:active {
      transform: scale(0.95);
    }
    
    /* 情感倾向滑块 */
    .emotion-slider {
      padding: 10px 25px 25px;
    }
    
    .emotion-scale {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 0 10px;
    }
    
    .emotion-label {
      font-size: 14px;
      color: #555;
      font-weight: 500;
    }
    
    .emotion-track {
      height: 6px;
      background: linear-gradient(to right, #1E88E5, #EEEEEE, #FF6B35);
      border-radius: 3px;
      position: relative;
      margin: 20px 10px;
    }
    
    .emotion-thumb {
      width: 22px;
      height: 22px;
      background-color: #fff;
      border: 2px solid #FF6B35;
      border-radius: 50%;
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    /* 保存按钮 */
    /* 保存按钮样式 */
    .save-button {
      /* 按钮尺寸和位置 */
      width: 70%;                          /* 设置按钮宽度为容器的85% */
      margin: 20px auto 35px;              /* 上边距20px,水平居中,下边距35px */
      height: 43px;                        /* 固定高度48px */
      
      /* 按钮外观 */
      background-color: #FF6B35;           /* 橙色背景 */
      color: white;                        /* 白色文字 */
      border: none;                        /* 无边框 */
      border-radius: 24px;                 /* 圆角效果 */
      font-size: 16px;                     /* 字体大小 */
      font-weight: 600;                    /* 字体加粗 */
      
      /* Flex布局使内容居中 */
      display: flex;
      align-items: center;                 /* 垂直居中 */
      justify-content: center;             /* 水平居中 */
      
      /* 视觉效果 */
      box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);  /* 橙色投影 */
      transition: all 0.2s ease;           /* 平滑过渡动画 */
    }
    
    .save-button i {
      margin-right: 8px;
    }
    
    .save-button:active {
      transform: translateY(2px);
      box-shadow: 0 2px 6px rgba(255, 107, 53, 0.3);
    }
    
    /* 锁定标签 */
    .locked-feature {
      position: absolute;
      top: 10px;
      right: 15px;
      background-color: rgba(0, 0, 0, 0.05);
      color: #999;
      padding: 3px 8px;
      border-radius: 50px;
      font-size: 11px;
      display: flex;
      align-items: center;
    }
    
    .locked-feature i {
      margin-right: 3px;
      font-size: 10px;
    }
    
    /* 弹窗样式 */
    .modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      display: none;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    .modal-content {
      width: 85%;
      max-height: 70%;
      background-color: #fff;
      border-radius: 20px;
      overflow: hidden;
      animation: modalFadeIn 0.3s ease;
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    }
    
    .modal-header {
      padding: 16px 20px;
      border-bottom: 1px solid #eee;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
    
    .modal-close {
      font-size: 20px;
      color: #999;
      width: 36px;
      height: 36px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
    }
    
    .modal-body {
      padding: 20px;
      overflow-y: auto;
      max-height: 400px;
    }
    
    @keyframes modalFadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">10:31</div>
      <div class="status-bar-right">
        <span>1.31 KB/s</span>
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <div class="back-button">
          <i class="fas fa-chevron-left"></i>
        </div>
      </div>
      <div class="nav-title">定制你的助手</div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- AI头像和信息显示 -->
      <div class="card ai-profile">
        <div class="relation-badge">理财助手</div>
        <img src="账无忌2LOGO.png" alt="账无忌" class="ai-avatar">
        <h2 class="ai-name">账无忌</h2>
        <div class="ai-status"><i class="fas fa-circle"></i> 在线学习中</div>
        
        <!-- 信任度进度条 -->
        <div class="trust-meter">
          <div class="trust-bar">
            <div class="trust-level" style="width: 35%;"></div>
          </div>
          <div class="trust-label">
            <span>初识</span>
            <span>熟悉</span>
            <span>默契</span>
          </div>
        </div>
      </div>
      
      <!-- 设置类别标签 -->
      <div class="settings-tabs">
        <div class="tab-item active">基础设置</div>
        <div class="tab-item">互动风格</div>
        <div class="tab-item">专业能力</div>
      </div>
      
      <!-- 基础设置项 -->
      <div class="card">
        <div class="setting-item">
          <div class="setting-icon icon-relationship">
            <i class="fas fa-users"></i>
          </div>
          <div class="setting-content">
            <div class="setting-label">互动关系</div>
            <div class="setting-desc">设置与助手的互动关系类型</div>
          </div>
          <div class="setting-action">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-icon icon-tone">
            <i class="fas fa-comment"></i>
          </div>
          <div class="setting-content">
            <div class="setting-label">对话习惯</div>
            <div class="setting-desc">设置助手回复的语气和风格</div>
          </div>
          <div class="setting-action">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-icon icon-style">
            <i class="fas fa-palette"></i>
          </div>
          <div class="setting-content">
            <div class="setting-label">语言风格</div>
            <div class="setting-desc">正式、轻松或其他沟通风格</div>
          </div>
          <div class="setting-action">
            <i class="fas fa-chevron-right"></i>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-icon icon-interests">
            <i class="fas fa-star"></i>
          </div>
          <div class="setting-content">
            <div class="setting-label">兴趣爱好</div>
            <div class="setting-desc">影响助手的知识领域和话题推荐</div>
          </div>
          <div class="setting-action">
            <i class="fas fa-chevron-right"></i>
          </div>
          <div class="locked-feature">
            <i class="fas fa-lock"></i> 即将推出
          </div>
        </div>
      </div>
      
      <!-- 性格特质选择 -->
      <div class="setting-title">性格特质</div>
      <div class="card">
        <div class="personality-container">
          <div class="personality-tag" onclick="togglePersonality(this)">活泼开朗</div>
          <div class="personality-tag" onclick="togglePersonality(this)">理性冷静</div>
          <div class="personality-tag selected" onclick="togglePersonality(this)">呆萌可爱</div>
          <div class="personality-tag" onclick="togglePersonality(this)">高冷严肃</div>
          <div class="personality-tag" onclick="togglePersonality(this)">幽默诙谐</div>
          <div class="personality-tag" onclick="togglePersonality(this)">知性优雅</div>
          <div class="personality-tag" onclick="togglePersonality(this)">温暖贴心</div>
          <div class="personality-tag" onclick="togglePersonality(this)">机智睿智</div>
        </div>
      </div>
      
      <!-- 情感倾向设置 -->
      <div class="setting-title">情感表达倾向</div>
      <div class="card">
        <div class="emotion-slider">
          <div class="emotion-scale">
            <div class="emotion-label">理性</div>
            <div class="emotion-label">平衡</div>
            <div class="emotion-label">感性</div>
          </div>
          
          <div class="emotion-track">
            <div class="emotion-thumb"></div>
          </div>
        </div>
      </div>
      
      <!-- 保存按钮 -->
      <button class="save-button" id="saveButton">
        <i class="fas fa-check-circle"></i>应用设置
      </button>
    </div>
    
    <!-- 关系选择弹窗 -->
    <div class="modal" id="relationshipModal">
      <div class="modal-content">
        <div class="modal-header">
          <div class="modal-title">选择互动关系</div>
          <div class="modal-close" onclick="closeModal('relationshipModal')">
            <i class="fas fa-times"></i>
          </div>
        </div>
        <div class="modal-body">
          <div class="personality-container">
            <div class="personality-tag" onclick="selectRelationship(this)">财务顾问</div>
            <div class="personality-tag" onclick="selectRelationship(this)">理财教练</div>
            <div class="personality-tag selected" onclick="selectRelationship(this)">理财助手</div>
            <div class="personality-tag" onclick="selectRelationship(this)">记账伙伴</div>
            <div class="personality-tag" onclick="selectRelationship(this)">金融导师</div>
            <div class="personality-tag" onclick="selectRelationship(this)">预算管家</div>
            <div class="personality-tag" onclick="selectRelationship(this)">财富管理师</div>
            <div class="personality-tag" onclick="selectRelationship(this)">省钱小能手</div>
            <div class="personality-tag" onclick="selectRelationship(this)">投资顾问</div>
            <div class="personality-tag" onclick="selectRelationship(this)">财务规划师</div>
          </div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 返回按钮功能
      document.querySelector('.back-button').addEventListener('click', function() {
        window.history.back();
      });
      
      // 保存按钮功能
      document.getElementById('saveButton').addEventListener('click', function() {
        // 这里可以添加保存逻辑
        alert('设置已保存!');
        window.location.href = 'home.html'; // 保存后跳转到首页
      });
      
      // 标签切换功能
      document.querySelectorAll('.tab-item').forEach((tab, index) => {
        tab.addEventListener('click', function() {
          document.querySelector('.tab-item.active').classList.remove('active');
          this.classList.add('active');
          // 这里可以添加切换不同设置面板的逻辑
        });
      });
      
      // 设置项点击打开相应弹窗
      document.querySelectorAll('.setting-item').forEach((item, index) => {
        item.addEventListener('click', function() {
          if (!this.querySelector('.locked-feature')) {
            if (index === 0) {
              openModal('relationshipModal');
            }
          }
        });
      });
      
      // 简单的拖动滑块功能演示
      const thumb = document.querySelector('.emotion-thumb');
      const track = document.querySelector('.emotion-track');
      
      track.addEventListener('click', function(e) {
        const trackRect = this.getBoundingClientRect();
        const clickX = e.clientX - trackRect.left;
        const percentage = (clickX / trackRect.width) * 100;
        thumb.style.left = `${Math.max(0, Math.min(100, percentage))}%`;
      });
    });
    
    // 切换性格标签的选中状态
    function togglePersonality(element) {
      const currentSelected = document.querySelector('.personality-container .personality-tag.selected');
      if (currentSelected) {
        currentSelected.classList.remove('selected');
      }
      element.classList.add('selected');
    }
    
    // 打开指定弹窗
    function openModal(modalId) {
      document.getElementById(modalId).style.display = 'flex';
    }
    
    // 关闭指定弹窗
    function closeModal(modalId) {
      document.getElementById(modalId).style.display = 'none';
    }
    
    // 选择关系类型
    function selectRelationship(element) {
      const relationshipValue = element.textContent.trim();
      document.querySelector('.relation-badge').textContent = relationshipValue;
      
      const currentSelected = document.querySelector('#relationshipModal .personality-tag.selected');
      if (currentSelected) {
        currentSelected.classList.remove('selected');
      }
      element.classList.add('selected');
      
      closeModal('relationshipModal');
    }
  </script>
</body>
</html>