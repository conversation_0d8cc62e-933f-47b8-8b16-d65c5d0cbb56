<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>登录 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 - 符合UI规范 */
    :root {
      /* 颜色系统 */
      --color-primary: #FF6B35;
      --color-background: #fff;
      --text-primary: #333333;
      --text-secondary: #666666;
      --text-hint: #999999;
      --border-color: #e5e5e5;

      /* 间距令牌 */
      --space-xs: 4px;
      --space-sm: 8px;
      --space-md: 16px;
      --space-lg: 24px;
      --space-xl: 32px;

      /* 圆角令牌 */
      --radius-sm: 4px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --radius-pill: 999px;

      /* 动画时间令牌 */
      --transition-fast: 0.2s;
      --transition-normal: 0.3s;
      --transition-slow: 0.5s;
    }

    html,
    body {
      font-family: PingFang SC, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: var(--text-primary);
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }

    .login-container {
      width: 370px;
      height: 760px;
      background-color: var(--color-background);
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }

    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: var(--color-background);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--space-md);
      box-sizing: border-box;
      font-size: 14px;
      color: var(--text-primary);
      flex-shrink: 0;
    }

    .status-bar-left {
      font-weight: 600;
    }

    .status-bar-right {
      display: flex;
      align-items: center;
    }

    .status-bar-icon {
      margin-left: var(--space-xs);
    }

    /* 导航栏样式 - 符合UI规范标题栏 */
    .nav-bar {
      background-color: var(--color-primary);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--space-md);
      /* 高度将通过JS根据平台动态设置 */
    }

    .nav-left {
      width: 24px;
      color: white;
    }

    .nav-right {
      width: 24px;
      text-align: right;
    }

    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }

    /* 内容区域 - 使用UI规范间距系统 */
    .content {
      flex: 1;
      padding: var(--space-md);
      background-color: var(--color-background);
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      overflow: hidden;
    }

    /* 登录LOGO区域 */
    .login-header {
      margin-top: var(--space-sm);
      margin-bottom: var(--space-lg);
      text-align: center;
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .app-logo {
      width: 120px;
      height: 120px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: var(--space-md);
    }

    .app-logo img {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }

    .welcome-subtitle {
      font-size: 15px;
      color: var(--text-secondary);
      margin: 0;
    }

    /* 表单样式 - 符合UI规范组件使用 */
    .login-form {
      margin-bottom: var(--space-sm);
    }

    .input-group {
      margin-bottom: var(--space-md);
    }

    .input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--space-xs);
    }

    .input-label {
      font-size: 15px;
      color: var(--text-secondary);
      font-weight: 500;
    }

    .input-action {
      font-size: 14px;
      color: var(--color-primary);
      text-decoration: none;
      font-weight: 500;
    }

    .input-container {
      position: relative;
    }

    .input {
      width: 100%;
      height: 48px;
      /* 使用Large按钮高度一致 */
      background-color: white;
      border: 1px solid var(--border-color);
      border-radius: var(--radius-lg);
      padding: 0 16px;
      font-size: 16px;
      box-sizing: border-box;
      outline: none;
      transition: all var(--transition-fast) ease;
    }

    .input:focus {
      border-color: rgba(255, 107, 53, 0.5);
      box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
    }

    .input-prefix {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      color: var(--text-secondary);
      font-weight: 500;
    }

    .input-with-prefix {
      padding-left: 48px;
    }

    /* 按钮样式 - 符合UI规范按钮组件 */
    .ai-button {
      display: inline-block;
      border: none;
      border-radius: var(--radius-lg);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-normal) ease;
      text-align: center;
      width: 100%;
      box-sizing: border-box;
    }

    .ai-button--large {
      height: 48px;
      padding: 0 32px;
      font-size: 18px;
      border-radius: 8px;
    }

    .ai-button--medium {
      height: 40px;
      padding: 0 24px;
      font-size: 16px;
      border-radius: 6px;
    }

    .ai-button--small {
      height: 32px;
      padding: 0 16px;
      font-size: 14px;
      border-radius: 4px;
    }

    .ai-button--primary {
      background-color: var(--color-primary);
      color: white;
    }

    .ai-button--primary:active {
      transform: scale(0.98);
      filter: brightness(0.95);
    }

    .ai-button--disabled {
      opacity: 0.6;
      filter: saturate(0.6);
      pointer-events: none;
    }

    .ai-button--loading .icon {
      animation: spin 1s linear infinite;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* 分隔线 */
    .divider {
      display: flex;
      align-items: center;
      margin: 30px 0;
      color: var(--text-hint);
      font-size: 14px;
    }

    .divider::before,
    .divider::after {
      content: "";
      flex: 1;
      height: 1px;
      background-color: var(--border-color);
    }

    .divider-text {
      padding: 0 15px;
    }

    /* 社交登录 */
    .social-login {
      display: flex;
      justify-content: center;
      gap: 25px;
      margin: var(--space-md) 0;
    }

    .social-btn {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .social-icon {
      width: 48px;
      height: 48px;
      border-radius: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: var(--space-sm);
      transition: transform var(--transition-fast) ease;
      overflow: hidden;
    }

    .social-icon:active {
      transform: scale(0.95);
    }

    .social-name {
      font-size: 13px;
      color: var(--text-secondary);
    }

    /* 隐私协议 */
    .privacy-terms {
      text-align: center;
      font-size: 12px;
      color: var(--text-hint);
      line-height: 1.5;
      margin-top: var(--space-sm);
    }

    .privacy-link {
      color: var(--color-primary);
      text-decoration: none;
    }

    /* 多端适配样式 */
    /* iOS安全区适配 */
    /* #ifdef APP-PLUS-IOS */
    .login-container {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }

    /* #endif */

    /* 小程序胶囊按钮适配 */
    /* #ifdef MP-WEIXIN */
    .nav-bar {
      padding-right: 100px;
    }

    /* #endif */

    /* H5 hover状态 */
    /* #ifdef H5 */
    .ai-button--primary:hover {
      background-color: #e05a2b;
    }

    .input-action:hover {
      text-decoration: underline;
    }

    /* #endif */
  </style>
</head>

<body>
  <div class="login-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>

    <!-- 导航栏 -->
    <div class="nav-bar" id="navBar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">登录账号</div>
      <div class="nav-right"></div>
    </div>

    <!-- 内容区域 -->
    <div class="content">
      <!-- 登录头部 -->
      <div class="login-header">
        <div class="app-logo">
          <img src="LOGO.png" alt="账无忌">
        </div>
        <p class="welcome-subtitle">登录账无忌，轻松管理您的财务</p>
      </div>

      <!-- 登录表单 -->
      <div class="login-form">
        <div class="input-group">
          <div class="input-label">手机号码</div>
          <div class="input-container">
            <span class="input-prefix">+86</span>
            <input type="tel" class="input input-with-prefix" placeholder="请输入手机号码">
          </div>
        </div>

        <div class="input-group">
          <div class="input-header">
            <div class="input-label">验证码</div>
            <a href="javascript:void(0)" class="input-action" id="getCodeBtn">获取验证码</a>
          </div>
          <div class="input-container">
            <input type="text" class="input" placeholder="请输入短信验证码">
          </div>
        </div>

        <!-- 登录按钮 -->
        <button class="ai-button ai-button--large ai-button--primary" id="loginBtn">登录</button>
      </div>

      <!-- 分隔线 -->
      <div class="divider">
        <span class="divider-text">其他登录方式</span>
      </div>

      <!-- 其他登录方式 -->
      <div class="social-login">
        <div class="social-btn">
          <div class="social-icon" style="background-color: #07C160;">
            <i class="fab fa-weixin" style="font-size: 28px; color: white;"></i>
          </div>
          <div class="social-name">微信</div>
        </div>

        <div class="social-btn">
          <div class="social-icon" style="background-color: #1677FF;">
            <i class="fas fa-mobile-alt" style="font-size: 24px; color: white;"></i>
          </div>
          <div class="social-name">一键登录</div>
        </div>
      </div>

      <!-- 隐私协议 -->
      <div class="privacy-terms">
        登录即表示您同意
        <a href="#" class="privacy-link">《用户协议》</a>
        和
        <a href="#" class="privacy-link">《隐私政策》</a>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 平台高度适配
      const navBar = document.getElementById('navBar');

      // 根据平台设置不同高度
      function setPlatformStyle() {
        // 检测平台
        const userAgent = navigator.userAgent.toLowerCase();
        let platform = 'h5';
        let height = '60px';

        if (/iphone|ipad|ipod/.test(userAgent)) {
          platform = 'ios';
          height = '88px'; // iOS (含状态栏)
        } else if (/android/.test(userAgent)) {
          platform = 'android';
          height = '56px'; // Android
        } else if (/micromessenger/.test(userAgent)) {
          platform = 'weapp';
          height = '90px'; // 小程序
        }

        // 设置导航栏高度
        navBar.style.height = height;

        // iOS特殊处理：添加paddingTop
        if (platform === 'ios') {
          navBar.style.paddingTop = '44px';
          navBar.style.height = 'calc(88px - 44px)';
        }

        // 小程序特殊处理：避开胶囊按钮
        if (platform === 'weapp') {
          navBar.style.paddingTop = '20px';
        }

        console.log('当前平台:', platform, '导航栏高度:', height);
      }

      // 设置平台特定样式
      setPlatformStyle();

      // 返回按钮功能
      document.querySelector('.nav-left').addEventListener('click', function () {
        history.back();
      });

      // 获取验证码功能
      document.getElementById('getCodeBtn').addEventListener('click', function () {
        const phoneInput = document.querySelector('input[type="tel"]');
        if (phoneInput.value.trim() === '') {
          alert('请输入手机号码');
          return;
        }

        // 倒计时效果
        let seconds = 60;
        const originalText = this.textContent;
        this.textContent = `${seconds}秒后重新获取`;
        this.style.color = 'var(--text-hint)';
        this.style.pointerEvents = 'none';

        const timer = setInterval(() => {
          seconds--;
          this.textContent = `${seconds}秒后重新获取`;
          if (seconds <= 0) {
            clearInterval(timer);
            this.textContent = originalText;
            this.style.color = 'var(--color-primary)';
            this.style.pointerEvents = 'auto';
          }
        }, 1000);

        // 模拟验证码发送
        console.log('验证码已发送');
      });

      // 登录按钮功能
      document.getElementById('loginBtn').addEventListener('click', function () {
        const phoneInput = document.querySelector('input[type="tel"]');
        const codeInput = document.querySelector('input[type="text"]');

        if (phoneInput.value.trim() === '') {
          alert('请输入手机号码');
          return;
        }

        if (codeInput.value.trim() === '') {
          alert('请输入验证码');
          return;
        }

        // 添加加载状态
        this.classList.add('ai-button--loading');
        this.innerHTML = '<i class="fas fa-spinner icon"></i> 登录中...';
        this.disabled = true;

        // 模拟登录成功
        setTimeout(() => {
          console.log('登录成功');
          window.location.href = 'home.html';
        }, 1000);
      });
    });
  </script>
</body>

</html>