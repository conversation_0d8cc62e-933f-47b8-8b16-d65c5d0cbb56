<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统计分析 - AI记账</title>
    <!-- 假设项目有一个全局样式文件，定义了CSS变量和基础组件样式 -->
    <!-- <link rel="stylesheet" href="../assets/styles/main.css"> -->
  <style>
        /* 模拟部分项目CSS变量和基础样式，实际项目中应在全局CSS中定义 */
    :root {
      --color-primary: #FF6B35;
            --color-primary-light: #FFF3E0; /* 假设一个浅橙色 */
            --color-success: #4CAF50;
            --color-text-primary: #333333;
            --color-text-secondary: #666666;
            --color-text-hint: #999999;
            --color-text-inverse: #FFFFFF;
            --color-border: #E0E0E0;
            --color-background-page: #F5F5F5; /* 页面背景 */
            --color-background-light: #FAFAFA; /* 浅色背景，如某些卡片内元素 */
            --card-bg: #FFFFFF;
            --radius-card: 12px;
      --radius-button: 8px;
            --radius-sm: 4px; /* 小圆角 */
            --spacing-xs: 4px;
            --spacing-sm: 8px;
            --spacing-md: 16px;
            --spacing-lg: 24px;

            --font-size-xs: 12px;
            --font-size-sm: 14px;
            --font-size-md: 16px;
            --font-size-lg: 18px;
            --font-size-xl: 20px;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            background-color: var(--color-background-page);
            color: var(--color-text-primary);
            font-size: var(--font-size-md);
        }

        .statistics-page {
            padding: var(--spacing-md);
            padding-bottom: 80px; /* 为底部导航栏留出空间 */
        }

        /* 通用组件模拟样式 - 实际项目中由App*组件提供 */
        .app-card {
            background-color: var(--card-bg);
            border-radius: var(--radius-card);
            padding: var(--spacing-sm) var(--spacing-md); /* 上下改为sm，左右md */
            margin-bottom: var(--spacing-md);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }

        .app-button {
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-button);
            border: 1px solid transparent;
            cursor: pointer;
            font-size: var(--font-size-sm);
            text-align: center;
        }
        .app-button--primary {
            background-color: var(--color-primary);
            color: var(--color-text-inverse);
        }
        .app-button--outline {
            background-color: transparent;
            border-color: var(--color-primary);
            color: var(--color-primary);
        }
         .app-button--outline.app-button--active { /* 模拟激活状态 */
            background-color: var(--color-primary);
            color: var(--color-text-inverse);
        }
        .app-button--ghost {
            background-color: transparent;
            border-color: transparent;
            color: var(--color-primary);
        }
        .app-button--icon {
            padding: var(--spacing-sm);
            width: 36px; /* 假设大小 */
            height: 36px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }
        .app-button--sm {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            height: 28px; /* 对应原型 h-7 */
        }


        .app-icon { /* 模拟AppIcon占位 */
            display: inline-block;
            width: 1em;
            height: 1em;
            fill: currentColor; /* 如果是SVG */
            font-size: inherit; /* 继承父级字体大小 */
        }
        /* 实际项目中AppIcon组件会处理图标的显示 */


        /* 页面特定组件的模拟样式 */
        .month-selector {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background-color: var(--card-bg);
            border-radius: 9999px; /* full */
            padding: var(--spacing-sm);
            margin-bottom: var(--spacing-md);
            box-shadow: 0 1px 3px rgba(0,0,0,0.05);
        }
        .month-selector__month-text {
            font-weight: 500; /* medium */
        }

        .core-indicators__grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: var(--spacing-md);
        }
        .core-indicators__item {
            background-color: var(--color-background-light);
            padding: var(--spacing-sm); /* p-3 in proto */
            border-radius: var(--radius-button); /* rounded-lg */
        }
        .core-indicators__item-title {
            font-size: var(--font-size-sm);
            color: var(--color-text-secondary);
            display: flex;
            align-items: center;
        }
        .core-indicators__item-title .app-icon {
            margin-right: var(--spacing-xs);
        }
        .core-indicators__item-value {
            font-size: var(--font-size-xl); /* text-2xl */
            font-weight: bold;
            margin-top: var(--spacing-xs);
        }
        .core-indicators__item-trend {
            font-size: var(--font-size-xs);
            margin-top: var(--spacing-xs);
        }
        .core-indicators__item-trend--positive { color: var(--color-success); }
        .core-indicators__item-trend--negative { color: var(--color-primary); } /* Orange for expense related negative */


    .section-header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-xs, 8px); /* 维持下方小间距 */
            margin-top: 0; 
        }
        .section-header__icon {
            color: var(--color-primary);
            font-size: 20px;
            margin-right: 4px;
        }
        .section-header__title {
            font-weight: 600;
            font-size: 17px;
            color: var(--text-primary, #333);
            position: relative;
            display: inline-block;
        }
        .section-header__title::after {
            content: '';
            display: block;
            width: 20px;
            height: 3px;
            background: var(--color-primary, #ff6b35);
            position: absolute;
            bottom: -5px;
            left: 0;
            border-radius: 2px;
        }

        .tabs { /* 模拟Tabs容器 */
        }
        .tabs__list { /* 模拟TabsList */
            display: flex;
            background-color: var(--color-background-light); /* gray-100 */
            border-radius: var(--radius-button);
            padding: var(--spacing-xs);
        }
        .tabs__trigger { /* 模拟TabsTrigger */
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: var(--font-size-xs);
            color: var(--color-text-secondary);
            border-radius: var(--radius-sm); /* 内部按钮的圆角 */
            cursor: pointer;
            border: none;
            background-color: transparent;
        }
        .tabs__trigger--active {
            background-color: var(--color-primary);
            color: var(--color-text-inverse);
        }

        .chart-placeholder {
            height: 192px; /* h-48 */
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: var(--color-background-light);
            border-radius: var(--radius-sm);
            overflow: hidden; /* 用于内部绝对定位 */
            padding-top: 24px; /* pt-6 for legend */
        }
        .chart-legend {
            position: absolute;
            top: var(--spacing-sm);
            right: var(--spacing-sm);
            display: flex;
            align-items: center;
            gap: var(--spacing-md); /* gap-6 in proto means more */
            font-size: var(--font-size-xs);
        }
        .chart-legend__item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
        }
        .chart-legend__color-dot {
            width: 12px; /* w-3 h-3 */
            height: 12px;
            border-radius: 50%;
        }
        .chart-bars-container { /* For bar chart placeholder */
            display: flex;
            height: 100%;
            width: calc(100% - 2 * var(--spacing-sm)); /* px-2 in proto */
            align-items: flex-end;
            /* gap: var(--spacing-xs); */ /* gap-1, 调整为由bar-item的padding控制间隙感 */
            justify-content: space-around; /* 让柱子均匀分布 */
        }
        .chart-bar-item {
            flex-grow: 0; /* 防止flex项默认填满 */
            width: calc(10% - 4px); /* 假设10个柱子，减去一些间隙 */
            max-width: 20px; /* 设置一个最大宽度，避免过宽 */
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-end;
            gap: var(--spacing-xs);
        }
        .chart-bar {
            width: 60%; /* w-3 approx */
            border-top-left-radius: var(--radius-sm);
            border-top-right-radius: var(--radius-sm);
        }
        .chart-bar__label {
            font-size: var(--font-size-xs);
            color: var(--color-text-hint);
        }


        .expense-composition__content {
            display: flex;
            gap: var(--spacing-md);
        }
        .expense-composition__pie-chart-area {
            width: 50%;
        }
        .pie-chart-placeholder {
            position: relative;
            width: 100%;
            padding-top: 100%; /* aspect-square */
        }
        .pie-chart-placeholder__rings {
            position: absolute;
            inset: 0;
            border-radius: 50%;
            border: 20px solid var(--color-primary); /* border-8 in proto, larger for effect */
            /* Simplified ring colors for placeholder */
            border-right-color: #FFB74D; /* orange-300 */
            border-bottom-color: #FFCC80; /* orange-200 */
            border-left-color: #FFA726; /* orange-400 */
        }
        .pie-chart-placeholder__center-hole {
            position: absolute;
            inset: 32px; /* inset-8 in proto */
            border-radius: 50%;
            background-color: var(--card-bg);
        }
        .pie-chart-placeholder__center-text {
            position: absolute;
            inset: 0;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        .pie-chart-placeholder__center-label {
            font-size: var(--font-size-sm);
            color: var(--color-text-hint);
        }
        .pie-chart-placeholder__center-value {
            font-size: var(--font-size-lg); /* text-xl in proto */
            font-weight: bold;
        }
        .expense-composition__legend-area {
            width: 50%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: var(--spacing-sm); /* gap-3 in proto */
        }
        .legend-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        .legend-item__color-dot {
            width: 12px; /* w-3 h-3 */
            height: 12px;
            border-radius: 50%;
        }
        .legend-item__label {
            font-size: var(--font-size-sm);
            flex: 1;
        }
        .legend-item__value {
            font-size: var(--font-size-sm);
            font-weight: 500; /* medium */
        }

        .budget-execution__list {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-md); /* space-y-4 */
        }
        .budget-item {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm); /* space-y-2 */
        }
        .budget-item__header {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        .budget-item__icon-wrapper {
            width: 32px; /* w-8 h-8 */
            height: 32px;
            background-color: var(--color-primary-light); /* orange-100 */
            border-radius: var(--radius-button); /* rounded-lg */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: var(--font-size-md); /* For emoji or icon */
        }
        .budget-item__details {
            flex: 1;
        }
        .budget-item__name {
            font-size: var(--font-size-sm);
            font-weight: 500; /* medium */
        }
        .budget-item__progress-bar-container { /* uni-app progress component simulation */
            height: 8px; /* h-2 */
            background-color: var(--color-background-light); /* gray-100 */
            border-radius: 9999px; /* rounded-full */
            overflow: hidden; /* Important for inner bar */
            margin-top: var(--spacing-xs);
        }
        .budget-item__progress-bar {
            height: 100%;
            background-color: var(--color-primary);
            border-radius: 9999px;
        }
        .budget-item__summary {
            display: flex;
            justify-content: space-between;
            font-size: var(--font-size-xs);
            padding-left: 40px; /* pl-10 (32px icon + 8px gap) */
            color: var(--color-text-hint);
        }
        .budget-item__summary-remaining {
            color: var(--color-success);
        }

        /* Header actions (button groups etc) */
        .section-header-actions {
            display: flex;
            gap: var(--spacing-sm);
        }

        /* Financial Insights Card Styles */
        .financial-insights__item {
            background-color: var(--color-background-light); /* e.g. bg-gray-50 or a lighter version of card bg */
            padding: var(--spacing-sm) var(--spacing-md);
            border-radius: var(--radius-button);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            margin-bottom: var(--spacing-sm);
            font-size: var(--font-size-sm);
        }
        .financial-insights__item:last-child {
            margin-bottom: 0;
        }
        .financial-insights__icon {
            font-size: var(--font-size-lg); /* Adjust as needed */
            width: 24px; /* for consistency */
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .financial-insights__icon--success {
            color: var(--color-success);
        }
        .financial-insights__icon--warning {
            color: var(--color-primary); /* Using primary for warning, as per original antd proto */
        }
        .financial-insights__icon--info { /* For thumbs up or general info */
            color: #2196F3; /* A blue info color */
        }


        /* Tab Bar Styles (项目AppTabBar风格) */
    .tab-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 60px;
            background-color: var(--bg-primary, #fff);
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid var(--border-color, #eee);
            box-shadow: 0 -4px 10px rgb(0 0 0 / 5%);
            padding-bottom: env(safe-area-inset-bottom);
            z-index: 1000;
        }
        .tab-bar__item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            color: var(--text-hint, #aaa);
            cursor: pointer;
            height: 100%;
            transition: color 0.3s;
        }
        .tab-bar__icon {
            font-size: 20px;
            margin-bottom: 4px;
            transition: all 0.3s;
        }
        .tab-bar__label {
            font-size: 12px;
            line-height: 1.2;
            transition: all 0.3s;
        }
        .tab-bar__item--active .tab-bar__icon,
        .tab-bar__item--active .tab-bar__label {
            color: var(--color-primary, #ff6b35);
            font-weight: 500;
        }

  </style>
</head>
<body>
    <div class="statistics-page">
        <!-- Month Selector -->
        <div class="month-selector statistics-page__month-selector">
            <!-- For AppIcon: icon="chevron-left" -->
            <button class="app-button app-button--ghost app-button--icon month-selector__nav-button" aria-label="上个月">
                <span class="app-icon" title="chevron-left">❮</span>
            </button>
            <div class="month-selector__month-text">2024年 5月</div>
            <!-- For AppIcon: icon="chevron-right" -->
            <button class="app-button app-button--ghost app-button--icon month-selector__nav-button" aria-label="下个月">
                <span class="app-icon" title="chevron-right">❯</span>
            </button>
        </div>

        <!-- Core Indicators Card -->
        <div class="app-card statistics-page__core-indicators">
            <div class="section-header">
                <!-- For AppIcon: icon="target" provider="s" -->
                <span class="app-icon section-header__icon" title="target-icon">🎯</span>
                <h2 class="section-header__title">核心指标</h2>
            </div>
            <div class="core-indicators__grid">
                <div class="core-indicators__item">
                    <div class="core-indicators__item-title">
                        <!-- For AppIcon: icon="arrow-up-right" -->
                        <span class="app-icon" style="color: var(--color-primary);" title="arrow-up-right">↗</span>
                        总支出
                    </div>
                    <div class="core-indicators__item-value">¥3,856</div>
                    <div class="core-indicators__item-trend core-indicators__item-trend--negative">↑ 12.5% (vs 上期)</div>
                </div>
                <div class="core-indicators__item">
                    <div class="core-indicators__item-title">
                        <!-- For AppIcon: icon="arrow-down-right" -->
                        <span class="app-icon" style="color: var(--color-success);" title="arrow-down-right">↘</span>
                        总收入
                    </div>
                    <div class="core-indicators__item-value">¥5,230</div>
                    <div class="core-indicators__item-trend core-indicators__item-trend--positive">↑ 8.3% (vs 上期)</div>
                </div>
                <div class="core-indicators__item">
                    <div class="core-indicators__item-title">
                         <!-- For AppIcon: icon="arrow-down-right" -->
                        <span class="app-icon" style="color: var(--color-success);" title="arrow-down-right">↘</span>
                        净收入
                    </div>
                    <div class="core-indicators__item-value">¥1,374</div>
                    <div class="core-indicators__item-trend core-indicators__item-trend--positive">↓ 2.1% (vs 上期)</div> <!-- Proto shows green, but should be red for decrease -->
                </div>
                <div class="core-indicators__item">
                    <div class="core-indicators__item-title">
                        <!-- For AppIcon: icon="arrow-up-right" -->
                        <span class="app-icon" style="color: var(--color-primary);" title="arrow-up-right">↗</span>
                        平均日消费
                    </div>
                    <div class="core-indicators__item-value">¥128</div>
                    <div class="core-indicators__item-trend core-indicators__item-trend--negative">↑ 5.2% (vs 上期)</div>
                </div>
      </div>
    </div>

        <!-- Income/Expense Trend Card -->
        <div class="app-card statistics-page__trend-chart">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md);">
                <div class="section-header" style="margin-bottom: 0;">
                    <!-- For AppIcon: icon="trending-up" -->
                    <span class="app-icon section-header__icon" title="trending-up-icon">📈</span>
                    <h2 class="section-header__title">收支趋势</h2>
                </div>
                <!-- 模拟 AppSegmentedControl 或 uni-segmented-control -->
                <div class="tabs trend-chart__tabs">
                    <div class="tabs__list">
                        <button class="tabs__trigger">近7天</button>
                        <button class="tabs__trigger tabs__trigger--active">近30天</button>
                        <button class="tabs__trigger">近90天</button>
                        <button class="tabs__trigger">近12月</button>
                    </div>
      </div>
    </div>
            <div class="chart-placeholder">
                <div class="chart-legend">
                    <div class="chart-legend__item">
                        <div class="chart-legend__color-dot" style="background-color: var(--color-success);"></div>
                        <span>收入</span>
      </div>
                    <div class="chart-legend__item">
                        <div class="chart-legend__color-dot" style="background-color: var(--color-primary);"></div>
                        <span>支出</span>
          </div>
        </div>
                <!-- Placeholder for bar chart -->
                <div class="chart-bars-container">
                    <!-- Bar 1 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 70%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 45%;"></div>
                        <div class="chart-bar__label">1</div>
                    </div>
                    <!-- Bar 2 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 60%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 50%;"></div>
                        <div class="chart-bar__label">2</div>
                    </div>
                    <!-- Bar 3 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 80%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 30%;"></div>
                        <div class="chart-bar__label">3</div>
                    </div>
                    <!-- Bar 4 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 50%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 65%;"></div>
                        <div class="chart-bar__label">4</div>
                    </div>
                    <!-- Bar 5 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 75%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 40%;"></div>
                        <div class="chart-bar__label">5</div>
          </div>
                     <!-- Bar 6 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 65%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 55%;"></div>
                        <div class="chart-bar__label">6</div>
          </div>
                     <!-- Bar 7 -->
                    <div class="chart-bar-item">
                        <div class="chart-bar" style="background-color: var(--color-success); height: 85%;"></div>
                        <div class="chart-bar" style="background-color: var(--color-primary); height: 25%;"></div>
                        <div class="chart-bar__label">7</div>
          </div>
          </div>
        </div>
      </div>

        <!-- Expense Composition Card -->
        <div class="app-card statistics-page__expense-composition">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md);">
                <div class="section-header" style="margin-bottom: 0;">
                    <!-- For AppIcon: icon="pie-chart" -->
                    <span class="app-icon section-header__icon" title="pie-chart-icon">🥧</span>
                    <h2 class="section-header__title">支出构成</h2>
                </div>
                <div class="section-header-actions">
                    <button class="app-button app-button--outline app-button--sm app-button--active">Top 5</button>
                    <button class="app-button app-button--outline app-button--sm">全部</button>
                </div>
            </div>
            <div class="expense-composition__content">
                <div class="expense-composition__pie-chart-area">
                    <div class="pie-chart-placeholder">
                        <div class="pie-chart-placeholder__rings"></div>
                        <div class="pie-chart-placeholder__center-hole"></div>
                        <div class="pie-chart-placeholder__center-text">
                            <div class="pie-chart-placeholder__center-label">总支出</div>
                            <div class="pie-chart-placeholder__center-value">¥3,856</div>
                        </div>
          </div>
        </div>
                <div class="expense-composition__legend-area">
                    <div class="legend-item">
                        <div class="legend-item__color-dot" style="background-color: var(--color-primary);"></div>
                        <div class="legend-item__label">餐饮美食</div>
                        <div class="legend-item__value">42%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-item__color-dot" style="background-color: #FFB74D;"></div> <!-- orange-400 -->
                        <div class="legend-item__label">交通出行</div>
                        <div class="legend-item__value">23%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-item__color-dot" style="background-color: #FFCC80;"></div> <!-- orange-300 -->
                        <div class="legend-item__label">购物消费</div>
                        <div class="legend-item__value">18%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-item__color-dot" style="background-color: #FFE0B2;"></div> <!-- orange-200 -->
                        <div class="legend-item__label">休闲娱乐</div>
                        <div class="legend-item__value">12%</div>
                    </div>
                    <div class="legend-item">
                        <div class="legend-item__color-dot" style="background-color: #BDBDBD;"></div> <!-- gray-300 -->
                        <div class="legend-item__label">其他</div>
                        <div class="legend-item__value">5%</div>
        </div>
      </div>
           </div>
        </div>

        <!-- Budget Execution Card -->
        <div class="app-card statistics-page__budget-execution">
             <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: var(--spacing-md);">
                <div class="section-header" style="margin-bottom: 0;">
                    <!-- For AppIcon: icon="target" -->
                    <span class="app-icon section-header__icon" title="target-icon">🎯</span>
                    <h2 class="section-header__title">预算执行</h2>
                </div>
                <button class="app-button app-button--outline app-button--sm">管理预算</button>
            </div>
            <div class="budget-execution__list">
                <div class="budget-item">
                    <div class="budget-item__header">
                        <div class="budget-item__icon-wrapper">
                            <!-- 实际项目中这里可能是 <AppIcon icon="food-category" /> -->
                            <span title="food-icon">🍽️</span>
                        </div>
                        <div class="budget-item__details">
                            <div class="budget-item__name">餐饮美食</div>
                            <!-- 模拟 uni-progress -->
                            <div class="budget-item__progress-bar-container">
                                <div class="budget-item__progress-bar" style="width: 85%;"></div>
                            </div>
            </div>
          </div>
                    <div class="budget-item__summary">
                        <span>已用 ¥1,275 / ¥1,500</span>
                        <span class="budget-item__summary-remaining">剩余 ¥225</span>
        </div>
      </div>
                <div class="budget-item">
                    <div class="budget-item__header">
                        <div class="budget-item__icon-wrapper">
                             <span title="traffic-icon">🚗</span>
        </div>
                        <div class="budget-item__details">
                            <div class="budget-item__name">交通出行</div>
                            <div class="budget-item__progress-bar-container">
                                <div class="budget-item__progress-bar" style="width: 60%;"></div>
              </div>
            </div>
          </div>
                    <div class="budget-item__summary">
                        <span>已用 ¥480 / ¥800</span>
                        <span class="budget-item__summary-remaining">剩余 ¥320</span>
                    </div>
                </div>
                 <div class="budget-item">
                    <div class="budget-item__header">
                        <div class="budget-item__icon-wrapper">
                             <span title="shopping-icon">🛍️</span>
                        </div>
                        <div class="budget-item__details">
                            <div class="budget-item__name">购物消费</div>
                            <div class="budget-item__progress-bar-container">
                                <div class="budget-item__progress-bar" style="width: 100%; background-color: var(--color-primary);"></div> <!-- 假设超支为红色 -->
              </div>
            </div>
          </div>
                    <div class="budget-item__summary">
                        <span>已用 ¥1,100 / ¥1,000</span>
                        <span style="color:var(--color-primary)">超支 ¥100</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Financial Insights Card -->
        <div class="app-card statistics-page__financial-insights">
            <div class="section-header">
                <!-- For AppIcon: icon="lightbulb" or similar -->
                <span class="app-icon section-header__icon" title="insights-icon">💡</span>
                <h2 class="section-header__title">财务洞察</h2>
            </div>
            <div class="financial-insights__list">
                <div class="financial-insights__item">
                    <!-- For AppIcon: icon="check-circle" -->
                    <span class="app-icon financial-insights__icon financial-insights__icon--success" title="check-circle-icon">✔️</span>
                    <span>您的餐饮支出较上期有所下降，继续保持！</span>
                </div>
                <div class="financial-insights__item">
                    <!-- For AppIcon: icon="alert-triangle" -->
                    <span class="app-icon financial-insights__icon financial-insights__icon--warning" title="alert-triangle-icon">⚠️</span>
                    <span>交通预算已超支，请关注下期该项支出。考虑优化出行方式。</span>
                </div>
                <div class="financial-insights__item">
                    <!-- For AppIcon: icon="thumbs-up" -->
                    <span class="app-icon financial-insights__icon financial-insights__icon--info" title="thumbs-up-icon">👍</span>
                    <span>本月储蓄率良好，已达到您设定目标的95%。</span>
                </div>
            </div>
        </div>

      </div>

    <!-- Bottom Tab Bar -->
    <div class="tab-bar statistics-page__tab-bar">
        <div class="tab-bar__item" onclick="navigateTo('home.html')">
            <span class="app-icon tab-bar__icon" title="home-icon">🏠</span>
            <span class="tab-bar__label">首页</span>
        </div>
        <div class="tab-bar__item" onclick="navigateTo('transactions.html')">
            <span class="app-icon tab-bar__icon" title="transactions-icon">📝</span>
            <span class="tab-bar__label">账单</span>
        </div>
        <div class="tab-bar__item" onclick="navigateTo('assets.html')">
            <span class="app-icon tab-bar__icon" title="assets-icon">💼</span>
            <span class="tab-bar__label">资产</span>
        </div>
        <div class="tab-bar__item tab-bar__item--active">
            <span class="app-icon tab-bar__icon" title="analysis-icon">📊</span>
            <span class="tab-bar__label">分析</span>
        </div>
        <div class="tab-bar__item" onclick="navigateTo('profile.html')">
            <span class="app-icon tab-bar__icon" title="profile-icon">👤</span>
            <span class="tab-bar__label">我的</span>
    </div>
  </div>

<script>
        function navigateTo(page) {
            // In a real uni-app, you'd use uni.switchTab or uni.navigateTo
            console.log("Navigating to: " + page);
            // window.location.href = page; // For simple HTML prototype navigation
        }
</script>
</body>
</html>
