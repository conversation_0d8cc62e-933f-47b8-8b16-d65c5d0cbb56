<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>我的 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .nav-right {
      display: flex;
      align-items: center;
    }
    
    .nav-icon {
      width: 24px;
      text-align: center;
      color: #666;
      margin-left: 15px;
      position: relative;
    }
    
    .notification-dot {
      position: absolute;
      top: -3px;
      right: -3px;
      width: 8px;
      height: 8px;
      background-color: #FF4D4F;
      border-radius: 50%;
    }
    
    @keyframes bellShake {
      0% { transform: rotate(0); }
      25% { transform: rotate(10deg); }
      50% { transform: rotate(0); }
      75% { transform: rotate(-10deg); }
      100% { transform: rotate(0); }
    }
    
    .bell-animation {
      animation: bellShake 0.5s infinite;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 6px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 16px;
      padding: 12px;
      margin-bottom: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    /* 用户信息卡片样式 */
    .user-card {
      display: flex;
      align-items: center;
      background: linear-gradient(135deg, #FF6B35, #FF8F6B);
      border-radius: 16px;
      padding: 10px;
      margin-bottom: 8px;
      color: white;
    }
    
    .user-avatar {
      width: 40px;
      height: 40px;
      background-color: white;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 10px;
      border: 1px solid rgba(255, 255, 255, 0.3);
    }
    
    .user-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .user-info {
      flex: 1;
    }
    
    .user-name-row {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 2px;
    }
    
    .user-name {
      font-size: 16px;
      font-weight: 600;
    }
    
    .user-id {
      font-size: 11px;
      opacity: 0.9;
    }
    
    .upgrade-btn {
      background-color: rgba(255, 255, 255, 0.2);
      border: 1px solid white;
      color: white;
      border-radius: 50px;
      font-size: 10px;
      padding: 2px 6px;
      display: inline-flex;
      align-items: center;
    }
    
    .upgrade-btn i {
      margin-right: 2px;
      font-size: 8px;
    }
    
    /* 统计卡片样式 */
    .stats-card {
      display: flex;
      justify-content: space-between;
      padding: 5px 0 0;
    }
    
    .stat-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      flex: 1;
    }
    
    .stat-value {
      font-size: 15px;
      font-weight: 600;
      color: #FF6B35;
      margin-bottom: 1px;
    }
    
    .stat-label {
      font-size: 9px;
      color: #666;
    }
    
    /* 模块标题 */
    .module-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 6px 4px 8px;
      display: flex;
      align-items: center;
    }
    
    .module-title i {
      color: #FF6B35;
      margin-right: 5px;
      font-size: 12px;
    }
    
    /* 功能区样式 */
    .functions-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 6px;
      padding: 2px 0;
    }
    
    .function-item {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .function-icon {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 3px;
    }
    
    .function-icon i {
      font-size: 16px;
    }
    
    .function-name {
      font-size: 10px;
      color: #333;
      text-align: center;
    }
    
    .icon-orange {
      background-color: #FFF5F2;
    }
    
    .icon-orange i {
      color: #FF6B35;
    }
    
    .icon-blue {
      background-color: #F0F7FF;
    }
    
    .icon-blue i {
      color: #3A7CFF;
    }
    
    .icon-purple {
      background-color: #F5F1FF;
    }
    
    .icon-purple i {
      color: #9A6AFF;
    }
    
    .icon-green {
      background-color: #ECFDF5;
    }
    
    .icon-green i {
      color: #10B981;
    }
    
    .icon-yellow {
      background-color: #FFF9E6;
    }
    
    .icon-yellow i {
      color: #F59E0B;
    }
    
    .icon-pink {
      background-color: #FFF1F5;
    }
    
    .icon-pink i {
      color: #EC4899;
    }
    
    .icon-gray {
      background-color: #F4F5F7;
    }
    
    .icon-gray i {
      color: #9CA3AF;
    }
    
    /* 评分卡片样式 */
    .score-card {
      background-color: white;
      border-radius: 16px;
      padding: 12px;
      margin-bottom: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
      display: flex;
      align-items: center;
    }
    
    .score-circle {
      width: 45px;
      height: 45px;
      border-radius: 50%;
      background: conic-gradient(#FF6B35 0% 75%, #f3f3f3 75% 100%);
      display: flex;
      justify-content: center;
      align-items: center;
      position: relative;
      margin-right: 10px;
    }
    
    .score-inner {
      width: 36px;
      height: 36px;
      border-radius: 50%;
      background-color: white;
      display: flex;
      justify-content: center;
      align-items: center;
      flex-direction: column;
    }
    
    .score-value {
      font-size: 16px;
      font-weight: 700;
      color: #FF6B35;
      line-height: 1;
    }
    
    .score-max {
      font-size: 9px;
      color: #999;
    }
    
    .score-info {
      flex: 1;
    }
    
    .score-title {
      font-weight: 600;
      font-size: 13px;
      margin-bottom: 2px;
      color: #333;
    }
    
    .score-desc {
      font-size: 12px;
      color: #666;
    }
    
    /* 免费领取VIP卡片样式 */
    .vip-card {
      background: linear-gradient(135deg, #8B5CF6, #6366F1);
      border-radius: 16px;
      padding: 10px;
      margin-bottom: 6px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
      color: white;
      display: flex;
      align-items: center;
    }
    
    .vip-icon {
      width: 32px;
      height: 32px;
      border-radius: 16px;
      background-color: rgba(255, 255, 255, 0.2);
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 8px;
      font-size: 16px;
    }
    
    .vip-info {
      flex: 1;
    }
    
    .vip-title {
      font-weight: 600;
      font-size: 13px;
      margin-bottom: 1px;
    }
    
    .vip-desc {
      font-size: 10px;
      opacity: 0.9;
    }
    
    .vip-button {
      background-color: white;
      color: #6366F1;
      border: none;
      border-radius: 30px;
      padding: 5px 10px;
      font-weight: 600;
      font-size: 12px;
    }
    
    /* 客服卡片样式 */
    .service-grid {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      gap: 8px;
      padding: 2px 0;
    }
    
    /* 底部标签栏 */
    .tab-bar {
      height: 55px;
      background-color: white;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      flex-shrink: 0;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
    }
    
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 100%;
    }
    
    .tab-icon {
      font-size: 20px;
      color: #999;
      margin-bottom: 3px;
    }
    
    .tab-label {
      font-size: 11px;
      color: #999;
    }
    
    .tab-icon.active {
      color: #FF6B35;
    }
    
    .tab-label.active {
      color: #FF6B35;
      font-weight: 500;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div style="width: 24px;"></div>
      <div class="nav-title">我的</div>
      <div class="nav-right">
        <div class="nav-icon" id="notification-icon">
          <i class="fas fa-bell bell-animation"></i>
          <div class="notification-dot"></div>
        </div>
        <div class="nav-icon" id="settings-icon">
          <i class="fas fa-cog"></i>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 用户信息 -->
      <div class="user-card">
        <div class="user-avatar">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
        </div>
        <div class="user-info">
          <div class="user-name-row">
            <div class="user-name">张先生</div>
            <button class="upgrade-btn">
              <i class="fas fa-crown"></i>升级会员
            </button>
          </div>
          <div class="user-id">ID: 123456789</div>
        </div>
      </div>
      
      <!-- 记账统计 -->
      <div class="card" style="padding: 8px;">
        <div class="stats-card">
          <div class="stat-item">
            <div class="stat-value">7</div>
            <div class="stat-label">连续打卡</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">42</div>
            <div class="stat-label">记账总天数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">128</div>
            <div class="stat-label">记账总笔数</div>
          </div>
        </div>
      </div>
      
      <!-- 财务评分 -->
      <div class="score-card" style="padding: 8px 12px;">
        <div class="score-circle">
          <div class="score-inner">
            <div class="score-value">78</div>
            <div class="score-max">/100</div>
          </div>
        </div>
        <div class="score-info">
          <div class="score-title">财务健康评分</div>
          <div class="score-desc">良好，支出合理可控</div>
        </div>
        <i class="fas fa-chevron-right" style="color: #999;"></i>
      </div>
      
      <!-- 免费领取VIP -->
      <div class="vip-card" style="padding: 8px 12px;">
        <div class="vip-icon">
          <i class="fas fa-gift"></i>
        </div>
        <div class="vip-info">
          <div class="vip-title">免费领取7天会员</div>
          <div class="vip-desc">邀请好友使用，双方均可获得奖励</div>
        </div>
        <button class="vip-button">立即领取</button>
      </div>
      
      <!-- 常用功能 -->
      <div class="module-title" style="margin: 4px 4px 6px;">
        <i class="fas fa-star"></i>常用功能
      </div>
      <div class="card" style="padding: 10px 12px 6px;">
        <div class="functions-grid">
          <div class="function-item">
            <div class="function-icon icon-blue">
              <i class="fas fa-book"></i>
            </div>
            <div class="function-name">账本管理</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-green">
              <i class="fas fa-file-export"></i>
            </div>
            <div class="function-name">账单导出</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-orange">
              <i class="fas fa-chart-pie"></i>
            </div>
            <div class="function-name">预算管理</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-purple">
              <i class="fas fa-tags"></i>
            </div>
            <div class="function-name">分类管理</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-gray">
              <i class="fas fa-magic"></i>
            </div>
            <div class="function-name">自动记账</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-yellow">
              <i class="fas fa-lightbulb"></i>
            </div>
            <div class="function-name">财务顾问</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-green">
              <i class="fas fa-user-edit"></i>
            </div>
            <div class="function-name">个人信息</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-orange">
              <i class="fas fa-file-invoice"></i>
            </div>
            <div class="function-name">账单提醒</div>
          </div>
        </div>
      </div>
      
      <!-- 客服服务 -->
      <div class="module-title" style="margin: 4px 4px 6px;">
        <i class="fas fa-headset"></i>客服服务
      </div>
      <div class="card" style="padding: 10px 12px 6px;">
        <div class="service-grid">
          <div class="function-item">
            <div class="function-icon" style="background-color: #F0FFF4; border: 1px solid #E6F7E9; position: relative; overflow: hidden;">
              <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%);">
                <path d="M11.0967 6.28125C11.3321 6.28125 11.5205 6.09293 11.5205 5.85751C11.5205 5.62209 11.3321 5.43376 11.0967 5.43376C10.8613 5.43376 10.6729 5.62209 10.6729 5.85751C10.6729 6.09293 10.8613 6.28125 11.0967 6.28125Z" fill="#07C160"/>
                <path d="M8.45663 5.43376C8.22121 5.43376 8.03289 5.62209 8.03289 5.85751C8.03289 6.09293 8.22121 6.28125 8.45663 6.28125C8.69205 6.28125 8.88038 6.09293 8.88038 5.85751C8.88038 5.62209 8.69205 5.43376 8.45663 5.43376Z" fill="#07C160"/>
                <path d="M4.9204 10.5663C5.15582 10.5663 5.34414 10.378 5.34414 10.1426C5.34414 9.90716 5.15582 9.71883 4.9204 9.71883C4.68498 9.71883 4.49665 9.90716 4.49665 10.1426C4.49665 10.378 4.68498 10.5663 4.9204 10.5663Z" fill="#07C160"/>
                <path d="M7.56046 9.71883C7.32504 9.71883 7.13672 9.90716 7.13672 10.1426C7.13672 10.378 7.32504 10.5663 7.56046 10.5663C7.79588 10.5663 7.98421 10.378 7.98421 10.1426C7.98421 9.90716 7.79588 9.71883 7.56046 9.71883Z" fill="#07C160"/>
                <path d="M11.1662 1.14258H4.83375C2.80998 1.14258 1.14258 2.80998 1.14258 4.83375V11.1662C1.14258 13.19 2.80998 14.8574 4.83375 14.8574H11.1662C13.19 14.8574 14.8574 13.19 14.8574 11.1662V4.83375C14.8574 2.80998 13.19 1.14258 11.1662 1.14258ZM6.23486 11.5811C5.80039 11.5811 5.45 11.5045 5.1018 11.4199L3.90741 12L4.38375 10.9326C3.71643 10.5698 3.27499 10.0029 3.27499 9.32111C3.27499 8.25238 4.38196 7.39844 6.23486 7.39844C7.85407 7.39844 8.91216 8.03671 8.91216 9.01294C8.91216 9.99455 7.87371 10.5811 6.23486 10.5811H6.23486ZM11.9831 9.18651L12.3433 10.0001L11.4389 9.52196C11.1709 9.57598 10.9028 9.60005 10.6347 9.60005C9.1261 9.60005 8.14628 8.86092 8.14628 7.8855C8.14628 6.92 9.1264 6.19714 10.6347 6.19714C12.0636 6.19714 13.0759 6.93983 13.0759 7.8855C13.0759 8.43738 12.6708 8.91551 11.9831 9.18651H11.9831ZM6.23486 10.1574C7.41962 10.1574 8.12126 9.76166 8.12126 9.01294C8.12126 8.26958 7.41962 7.82209 6.23486 7.82209C5.04992 7.82209 4.34858 8.26958 4.34858 9.01294C4.34858 9.76166 5.04992 10.1574 6.23486 10.1574ZM10.6347 6.62078C9.67273 6.62078 9.06708 7.13878 9.06708 7.8855C9.06708 8.6479 9.67273 9.1664 10.6347 9.1664C10.8636 9.1664 11.0926 9.1355 11.3216 9.08118L11.5859 9.18651L11.4509 8.93065C11.8854 8.73463 12.1547 8.3442 12.1547 7.88371C12.1547 7.13878 11.5494 6.62078 10.6347 6.62078Z" fill="#07C160"/>
              </svg>
            </div>
            <div class="function-name">客服微信</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-green">
              <i class="fas fa-comments"></i>
            </div>
            <div class="function-name">在线客服</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-orange">
              <i class="fas fa-question-circle"></i>
            </div>
            <div class="function-name">提交问题</div>
          </div>
          
          <div class="function-item">
            <div class="function-icon icon-purple">
              <i class="fas fa-book-open"></i>
            </div>
            <div class="function-name">使用教程</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <div class="tab-label">首页</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-list-ul tab-icon"></i>
        <div class="tab-label">账单</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-wallet tab-icon"></i>
        <div class="tab-label">资产</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-chart-pie tab-icon"></i>
        <div class="tab-label">分析</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon active"></i>
        <div class="tab-label active">我的</div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 设置图标点击事件
      const settingsIcon = document.getElementById('settings-icon');
      settingsIcon.addEventListener('click', function() {
        window.location.href = 'settings.html';
      });
      
      // 通知图标点击事件
      const notificationIcon = document.getElementById('notification-icon');
      notificationIcon.addEventListener('click', function() {
        alert('打开待办提醒列表：\n1. 信用卡还款：¥1,250 (2天后到期)\n2. 房租分期：¥1,500 (5天后到期)');
      });
      
      // 个人信息点击事件
      const personalInfoButton = document.querySelector('.function-item:nth-child(7)');
      personalInfoButton.addEventListener('click', function() {
        window.location.href = 'profile.html';
      });
      
      // 账单提醒点击事件
      const reminderButton = document.querySelector('.function-item:nth-child(8)');
      reminderButton.addEventListener('click', function() {
        window.location.href = 'bill-reminders.html';
      });
      
      // 财务健康评分点击事件
      const scoreCard = document.querySelector('.score-card');
      scoreCard.addEventListener('click', function() {
        alert('打开财务健康评分详情页面');
      });
      
      // 升级会员点击事件
      const upgradeBtn = document.querySelector('.upgrade-btn');
      upgradeBtn.addEventListener('click', function() {
        alert('打开会员购买页面');
      });
      
      // VIP领取点击事件
      const vipButton = document.querySelector('.vip-button');
      vipButton.addEventListener('click', function() {
        alert('打开邀请好友领取VIP页面');
      });
      
      // 财务顾问点击事件
      const advisorButton = document.querySelector('.function-item:nth-child(6)');
      advisorButton.addEventListener('click', function() {
        alert('打开财务顾问页面，提供个性化理财建议和预算规划');
      });
      
      // 自动记账点击事件
      const autoButton = document.querySelector('.function-item:nth-child(5)');
      autoButton.addEventListener('click', function() {
        alert('自动记账功能即将上线，敬请期待！');
      });
      
      // 客服功能点击事件
      const serviceButtons = document.querySelectorAll('.service-grid .function-item');
      serviceButtons.forEach((button, index) => {
        button.addEventListener('click', function() {
          switch(index) {
            case 0:
              alert('微信客服：账无忌小助手\nWeChat ID: ZhangWuJi-Helper');
              break;
            case 1:
              alert('正在连接在线客服...');
              break;
            case 2:
              alert('请描述您遇到的问题，我们将尽快回复');
              break;
            case 3:
              alert('打开使用教程页面');
              break;
          }
        });
      });
      
      // 常用功能点击事件
      const commonButtons = document.querySelectorAll('.functions-grid .function-item');
      commonButtons.forEach((button, index) => {
        if (index < 4) { // 只为前4个按钮添加事件(除了已有事件的按钮)
          button.addEventListener('click', function() {
            switch(index) {
              case 0:
                alert('打开账本管理页面');
                break;
              case 1:
                alert('打开账单导出页面');
                break;
              case 2:
                window.location.href = 'budget.html';
                break;
              case 3:
                window.location.href = 'categories.html';
                break;
            }
          });
        }
      });
      
      // 底部标签栏点击事件
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach((item, index) => {
        item.addEventListener('click', function() {
          switch(index) {
            case 0:
              window.location.href = 'home.html';
              break;
            case 1:
              window.location.href = 'transactions.html';
              break;
            case 2:
              window.location.href = 'assets.html';
              break;
            case 3:
              window.location.href = 'analysis.html';
              break;
            case 4:
              // 已在当前页面
              break;
          }
        });
      });
    });
  </script>
</body>
</html>