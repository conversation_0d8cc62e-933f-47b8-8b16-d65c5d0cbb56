<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>手势密码 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 - 符合UI规范 */
    :root {
      /* 颜色系统 */
      --color-primary: #FF6B35;
      --color-background: #fff;
      --text-primary: #333333;
      --text-secondary: #666666;
      --text-hint: #999999;
      --border-color: #e5e5e5;

      /* 间距令牌 */
      --space-xs: 4px;
      --space-sm: 8px;
      --space-md: 16px;
      --space-lg: 24px;
      --space-xl: 32px;

      /* 圆角令牌 */
      --radius-sm: 4px;
      --radius-md: 8px;
      --radius-lg: 12px;
      --radius-pill: 999px;

      /* 动画时间令牌 */
      --transition-fast: 0.2s;
      --transition-normal: 0.3s;
      --transition-slow: 0.5s;
    }

    html,
    body {
      font-family: PingFang SC, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: var(--text-primary);
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .pattern-container {
      width: 370px;
      height: 760px;
      background-color: var(--color-background);
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: var(--color-background);
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--space-md);
      box-sizing: border-box;
      font-size: 14px;
      color: var(--text-primary);
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: var(--space-xs);
    }
    
    /* 导航栏样式 - 符合UI规范标题栏 */
    .nav-bar {
      background-color: var(--color-primary);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 var(--space-md);
      /* 高度将通过JS根据平台动态设置 */
    }
    
    .nav-left {
      width: 24px;
      color: white;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-size: 18px;
      font-weight: 600;
      color: white;
    }

    /* 内容区域 - 使用UI规范间距系统 */
    .content {
      flex: 1;
      padding: var(--space-md);
      background-color: var(--color-background);
      width: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding-top: var(--space-xl);
    }
    
    /* 头像区域 */
    .avatar-container {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: var(--space-xl);
    }

    .avatar {
      width: 100px;
      height: 100px;
      border-radius: 50px;
      background-color: #f5f5f5;
      margin-bottom: var(--space-md);
      overflow: hidden;
      display: flex;
      justify-content: center;
      align-items: center;
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.08);
    }

    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .avatar-placeholder {
      font-size: 42px;
      color: var(--text-secondary);
    }

    .username {
      font-size: 20px;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--space-xs);
    }
    
    .subtitle {
      font-size: 14px;
      color: var(--text-secondary);
      text-align: center;
      line-height: 1.5;
    }

    /* 手势密码区域 */
    .pattern-area {
      margin-top: var(--space-lg);
      position: relative;
      width: 300px;
      height: 300px;
    }

    .pattern-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      grid-template-rows: repeat(3, 1fr);
      gap: 30px;
      width: 100%;
      height: 100%;
    }
    
    .pattern-dot {
      width: 80px;
      height: 80px;
      border-radius: 40px;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .pattern-dot::before {
      content: "";
      width: 16px;
      height: 16px;
      border-radius: 8px;
      background-color: #ccc;
      transition: all var(--transition-fast) ease;
    }

    .pattern-dot.active::before {
      background-color: var(--color-primary);
      transform: scale(1.2);
      box-shadow: 0 0 0 5px rgba(255, 107, 53, 0.2);
    }

    .pattern-line {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      pointer-events: none;
    }

    /* 提示和操作区域 */
    .pattern-message {
      margin-top: var(--space-lg);
      font-size: 16px;
      color: var(--text-secondary);
      text-align: center;
      height: 24px;
    }

    .error-message {
      color: #ff4d4f;
    }

    .actions {
      position: absolute;
      bottom: var(--space-lg);
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      gap: var(--space-md);
    }

    /* 按钮样式 - 符合UI规范按钮组件 */
    .ai-button {
      display: inline-block;
      border: none;
      border-radius: var(--radius-lg);
      font-weight: 500;
      cursor: pointer;
      transition: all var(--transition-normal) ease;
      text-align: center;
      box-sizing: border-box;
    }

    .ai-button--medium {
      height: 40px;
      padding: 0 var(--space-lg);
      font-size: 16px;
      border-radius: var(--radius-md);
    }

    .ai-button--primary {
      background-color: var(--color-primary);
      color: white;
    }
    
    .ai-button--outline {
      background-color: transparent;
      color: var(--color-primary);
      border: 1px solid var(--color-primary);
    }

    .ai-button--primary:active {
      transform: scale(0.98);
      filter: brightness(0.95);
    }

    .ai-button--outline:active {
      transform: scale(0.98);
      background-color: rgba(255, 107, 53, 0.05);
    }

    /* 多端适配样式 */
    /* iOS安全区适配 */
    /* #ifdef APP-PLUS-IOS */
    .pattern-container {
      padding-bottom: constant(safe-area-inset-bottom);
      padding-bottom: env(safe-area-inset-bottom);
    }

    /* #endif */

    /* 小程序胶囊按钮适配 */
    /* #ifdef MP-WEIXIN */
    .nav-bar {
      padding-right: 100px;
    }

    /* #endif */

    /* H5 hover状态 */
    /* #ifdef H5 */
    .ai-button--primary:hover {
      background-color: #e05a2b;
    }
    
    .ai-button--outline:hover {
      background-color: rgba(255, 107, 53, 0.05);
    }
    
    /* #endif */
  </style>
</head>

<body>
  <div class="pattern-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar" id="navBar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">手势密码</div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 头像区域 -->
      <div class="avatar-container">
        <div class="avatar">
          <div class="avatar-placeholder">
            <i class="fas fa-user"></i>
          </div>
        </div>
        <div class="username">叶同学</div>
        <div class="subtitle">请设置解锁图案<br>至少连接4个点</div>
      </div>
      
      <!-- 手势密码区域 -->
      <div class="pattern-area">
        <div class="pattern-grid">
          <div class="pattern-dot" data-index="0"></div>
          <div class="pattern-dot" data-index="1"></div>
          <div class="pattern-dot" data-index="2"></div>
          <div class="pattern-dot" data-index="3"></div>
          <div class="pattern-dot" data-index="4"></div>
          <div class="pattern-dot" data-index="5"></div>
          <div class="pattern-dot" data-index="6"></div>
          <div class="pattern-dot" data-index="7"></div>
          <div class="pattern-dot" data-index="8"></div>
        </div>
        <canvas class="pattern-line" id="patternCanvas"></canvas>
      </div>
      
      <!-- 提示信息 -->
      <div class="pattern-message" id="patternMessage"></div>

      <!-- 操作按钮 -->
      <div class="actions">
        <button class="ai-button ai-button--medium ai-button--outline" id="skipBtn">跳过</button>
        <button class="ai-button ai-button--medium ai-button--primary" id="resetBtn">重置</button>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function () {
      // 平台高度适配
      const navBar = document.getElementById('navBar');

      // 根据平台设置不同高度
      function setPlatformStyle() {
        // 检测平台
        const userAgent = navigator.userAgent.toLowerCase();
        let platform = 'h5';
        let height = '60px';

        if (/iphone|ipad|ipod/.test(userAgent)) {
          platform = 'ios';
          height = '88px'; // iOS (含状态栏)
        } else if (/android/.test(userAgent)) {
          platform = 'android';
          height = '56px'; // Android
        } else if (/micromessenger/.test(userAgent)) {
          platform = 'weapp';
          height = '90px'; // 小程序
        }

        // 设置导航栏高度
        navBar.style.height = height;

        // iOS特殊处理：添加paddingTop
        if (platform === 'ios') {
          navBar.style.paddingTop = '44px';
          navBar.style.height = 'calc(88px - 44px)';
        }

        // 小程序特殊处理：避开胶囊按钮
        if (platform === 'weapp') {
          navBar.style.paddingTop = '20px';
        }

        console.log('当前平台:', platform, '导航栏高度:', height);
      }

      // 设置平台特定样式
      setPlatformStyle();

      // 返回按钮功能
      document.querySelector('.nav-left').addEventListener('click', function () {
        history.back();
      });

      // 手势密码逻辑
      const patternDots = document.querySelectorAll('.pattern-dot');
      const patternCanvas = document.getElementById('patternCanvas');
      const patternMessage = document.getElementById('patternMessage');
      const resetBtn = document.getElementById('resetBtn');
      const skipBtn = document.getElementById('skipBtn');

      const ctx = patternCanvas.getContext('2d');
      let isDrawing = false;
      let currentPattern = [];
      let firstPattern = null;
      let isConfirming = false;

      // 设置画布尺寸
      function resizeCanvas() {
        patternCanvas.width = patternCanvas.offsetWidth;
        patternCanvas.height = patternCanvas.offsetHeight;
      }

      // 获取点的中心坐标
      function getDotCenter(dot) {
        const rect = dot.getBoundingClientRect();
        const canvasRect = patternCanvas.getBoundingClientRect();

        return {
          x: rect.left + rect.width / 2 - canvasRect.left,
          y: rect.top + rect.height / 2 - canvasRect.top
        };
      }

      // 绘制连线
      function drawPattern() {
        ctx.clearRect(0, 0, patternCanvas.width, patternCanvas.height);

        if (currentPattern.length === 0) return;

        ctx.beginPath();
        ctx.lineWidth = 4;
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        ctx.strokeStyle = '#FF6B35';

        const firstDot = patternDots[currentPattern[0]];
        const firstCenter = getDotCenter(firstDot);
        ctx.moveTo(firstCenter.x, firstCenter.y);

        for (let i = 1; i < currentPattern.length; i++) {
          const dot = patternDots[currentPattern[i]];
          const center = getDotCenter(dot);
          ctx.lineTo(center.x, center.y);
        }

        if (isDrawing && currentPoint) {
          ctx.lineTo(currentPoint.x, currentPoint.y);
        }

        ctx.stroke();
      }

      // 重置手势
      function resetPattern() {
        currentPattern = [];
        patternDots.forEach(dot => dot.classList.remove('active'));
        ctx.clearRect(0, 0, patternCanvas.width, patternCanvas.height);

        if (isConfirming) {
          isConfirming = false;
          patternMessage.textContent = '请设置解锁图案';
          patternMessage.classList.remove('error-message');
        }
      }

      // 检查点是否已经在当前模式中
      function isDotInPattern(index) {
        return currentPattern.includes(parseInt(index));
      }

      // 检查点是否可以添加
      function canAddDot(dot) {
        const dotIndex = parseInt(dot.dataset.index);
        return !isDotInPattern(dotIndex);
      }

      // 添加点到当前模式
      function addDotToPattern(dot) {
        const dotIndex = parseInt(dot.dataset.index);
        if (canAddDot(dot)) {
          currentPattern.push(dotIndex);
          dot.classList.add('active');
          drawPattern();

          // 检查是否有中间点需要自动添加
          if (currentPattern.length >= 2) {
            const last = currentPattern[currentPattern.length - 1];
            const secondLast = currentPattern[currentPattern.length - 2];

            // 检查两点是否在不同行不同列且中间有点
            const lastRow = Math.floor(last / 3);
            const lastCol = last % 3;
            const secondLastRow = Math.floor(secondLast / 3);
            const secondLastCol = secondLast % 3;

            if (Math.abs(lastRow - secondLastRow) === 2 && Math.abs(lastCol - secondLastCol) === 2) {
              // 对角线, 中间有点
              const middleIndex = 4; // 中心点
              if (!isDotInPattern(middleIndex)) {
                currentPattern.splice(currentPattern.length - 1, 0, middleIndex);
                patternDots[middleIndex].classList.add('active');
              }
            } else if (Math.abs(lastRow - secondLastRow) === 2 && lastCol === secondLastCol) {
              // 同列, 中间有点
              const middleIndex = 3 * (Math.min(lastRow, secondLastRow) + 1) + lastCol;
              if (!isDotInPattern(middleIndex)) {
                currentPattern.splice(currentPattern.length - 1, 0, middleIndex);
                patternDots[middleIndex].classList.add('active');
              }
            } else if (lastRow === secondLastRow && Math.abs(lastCol - secondLastCol) === 2) {
              // 同行, 中间有点
              const middleIndex = lastRow * 3 + Math.min(lastCol, secondLastCol) + 1;
              if (!isDotInPattern(middleIndex)) {
                currentPattern.splice(currentPattern.length - 1, 0, middleIndex);
                patternDots[middleIndex].classList.add('active');
              }
            }
          }
        }
      }

      // 验证模式
      function validatePattern() {
        if (currentPattern.length < 4) {
          // 至少连接4个点
          patternMessage.textContent = '至少连接4个点，请重试';
          patternMessage.classList.add('error-message');

          // 闪烁效果
          setTimeout(() => {
            resetPattern();
            patternMessage.textContent = '请设置解锁图案';
            patternMessage.classList.remove('error-message');
          }, 1000);

          return false;
        }

        if (!isConfirming) {
          // 第一次设置
          firstPattern = [...currentPattern];
          isConfirming = true;
          patternMessage.textContent = '请再次绘制相同图案';
          resetPattern();
          return false;
        } else {
          // 确认模式
          if (firstPattern.length !== currentPattern.length) {
            patternMessage.textContent = '与首次绘制不一致，请重试';
            patternMessage.classList.add('error-message');
            setTimeout(resetPattern, 1000);
            return false;
          }

          for (let i = 0; i < firstPattern.length; i++) {
            if (firstPattern[i] !== currentPattern[i]) {
              patternMessage.textContent = '与首次绘制不一致，请重试';
              patternMessage.classList.add('error-message');
              setTimeout(resetPattern, 1000);
              return false;
            }
          }

          // 成功设置
          patternMessage.textContent = '设置成功';
          patternMessage.classList.remove('error-message');

          // 模拟保存密码
          localStorage.setItem('patternPassword', JSON.stringify(firstPattern));

          // 成功后跳转
          setTimeout(() => {
            window.location.href = 'home.html';
          }, 1000);

          return true;
        }
      }

      // 初始化
      resizeCanvas();
      window.addEventListener('resize', resizeCanvas);

      // 触摸和鼠标事件处理
      let currentPoint = null;

      // 鼠标/触摸移动时
      function handleMove(clientX, clientY) {
        if (!isDrawing) return;

        const canvasRect = patternCanvas.getBoundingClientRect();
        currentPoint = {
          x: clientX - canvasRect.left,
          y: clientY - canvasRect.top
        };

        // 检查是否划过某个点
        patternDots.forEach(dot => {
          if (!canAddDot(dot)) return;

          const dotCenter = getDotCenter(dot);
          const distance = Math.sqrt(
            Math.pow(dotCenter.x - currentPoint.x, 2) +
            Math.pow(dotCenter.y - currentPoint.y, 2)
          );

          if (distance < 20) {
            addDotToPattern(dot);
          }
        });

        drawPattern();
      }

      // 开始绘制
      function handleStart(clientX, clientY) {
        isDrawing = true;
        currentPattern = [];
        patternDots.forEach(dot => dot.classList.remove('active'));

        // 检查第一个点
        patternDots.forEach(dot => {
          const rect = dot.getBoundingClientRect();
          const centerX = rect.left + rect.width / 2;
          const centerY = rect.top + rect.height / 2;
          const distance = Math.sqrt(
            Math.pow(centerX - clientX, 2) +
            Math.pow(centerY - clientY, 2)
          );

          if (distance < 40) {
            addDotToPattern(dot);
          }
        });

        handleMove(clientX, clientY);
      }

      // 结束绘制
      function handleEnd() {
        if (!isDrawing) return;
        isDrawing = false;
        currentPoint = null;

        if (currentPattern.length > 0) {
          validatePattern();
        }

        drawPattern();
      }

      // 鼠标事件
      patternCanvas.addEventListener('mousedown', e => {
        e.preventDefault();
        handleStart(e.clientX, e.clientY);
      });

      document.addEventListener('mousemove', e => {
        e.preventDefault();
        handleMove(e.clientX, e.clientY);
      });

      document.addEventListener('mouseup', e => {
        e.preventDefault();
        handleEnd();
      });

      // 触摸事件
      patternCanvas.addEventListener('touchstart', e => {
        e.preventDefault();
        const touch = e.touches[0];
        handleStart(touch.clientX, touch.clientY);
      });

      document.addEventListener('touchmove', e => {
        e.preventDefault();
        const touch = e.touches[0];
        handleMove(touch.clientX, touch.clientY);
      });

      document.addEventListener('touchend', e => {
        e.preventDefault();
        handleEnd();
      });

      // 重置按钮
      resetBtn.addEventListener('click', function () {
        firstPattern = null;
        isConfirming = false;
        resetPattern();
        patternMessage.textContent = '请设置解锁图案';
        patternMessage.classList.remove('error-message');
      });

      // 跳过按钮
      skipBtn.addEventListener('click', function () {
        window.location.href = 'home.html';
      });
    });
  </script>
</body>

</html>