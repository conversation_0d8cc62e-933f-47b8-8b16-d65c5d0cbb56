<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>预算设置 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
      color: #666;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    /* 按钮样式 */
    .btn {
      display: inline-block;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 500;
      padding: 14px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      width: 100%;
      box-sizing: border-box;
    }
    
    .btn-primary {
      background-color: #FF6B35;
      color: white;
      box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25);
    }
    
    .btn-outline {
      background-color: transparent;
      border: 1px solid #FF6B35;
      color: #FF6B35;
    }
    
    .btn-primary:active, .btn-outline:active {
      transform: translateY(1px);
    }
    
    /* 标签样式 */
    .tab-container {
      display: flex;
      background-color: #f0f0f0;
      border-radius: 10px;
      padding: 3px;
      margin-bottom: 20px;
    }
    
    .tab {
      flex: 1;
      text-align: center;
      padding: 10px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    .tab.active {
      background-color: #FF6B35;
      color: white;
    }
    
    /* 输入框样式 */
    .input-container {
      position: relative;
    }
    
    .input {
      width: 100%;
      height: 46px;
      background-color: white;
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 10px;
      padding: 0 15px;
      font-size: 15px;
      box-sizing: border-box;
      outline: none;
    }
    
    .input.large {
      font-size: 24px;
      font-weight: 600;
      padding-left: 40px;
    }
    
    .input-prefix {
      position: absolute;
      left: 16px;
      top: 50%;
      transform: translateY(-50%);
      color: #333;
      font-weight: 600;
      font-size: 24px;
    }
    
    /* 预算项样式 */
    .budget-item {
      margin-bottom: 20px;
    }
    
    .budget-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
    }
    
    .category-info {
      display: flex;
      align-items: center;
    }
    
    .category-icon {
      width: 36px;
      height: 36px;
      border-radius: 10px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
    }
    
    .category-details {
      display: flex;
      flex-direction: column;
    }
    
    .category-name {
      font-size: 16px;
      font-weight: 500;
    }
    
    .category-stats {
      color: #888;
      font-size: 12px;
    }
    
    .budget-amount {
      position: relative;
    }
    
    .amount-input {
      width: 120px;
      padding-left: 25px;
      text-align: right;
      height: 38px;
      border-radius: 8px;
      border: 1px solid rgba(0,0,0,0.1);
      outline: none;
    }
    
    .amount-prefix {
      position: absolute;
      left: 10px;
      top: 50%;
      transform: translateY(-50%);
      color: #333;
    }
    
    /* 进度条样式 */
    .progress-container {
      display: flex;
      align-items: center;
      gap: 10px;
      margin-left: 46px;
    }
    
    .progress-bar {
      flex: 1;
      height: 5px;
      background-color: #f0f0f0;
      border-radius: 3px;
      position: relative;
    }
    
    .progress {
      height: 100%;
      border-radius: 3px;
    }
    
    .progress-value {
      font-size: 12px;
      color: #888;
    }
    
    /* 开关样式 */
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
    }
    
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 20px;
      width: 20px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
    }
    
    input:checked + .slider {
      background-color: #FF6B35;
    }
    
    input:focus + .slider {
      box-shadow: 0 0 1px #FF6B35;
    }
    
    input:checked + .slider:before {
      transform: translateX(20px);
    }
    
    .slider.round {
      border-radius: 34px;
    }
    
    .slider.round:before {
      border-radius: 50%;
    }
    
    /* 工具类 */
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 15px;
    }
    
    .text-secondary {
      color: #888;
      font-size: 14px;
    }
    
    .text-primary {
      color: #FF6B35;
    }
    
    .w-full {
      width: 100%;
    }
    
    .mb-20 {
      margin-bottom: 20px;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">预算设置</div>
      <div class="nav-right">
        <i class="fas fa-history" style="color: #888;"></i>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 时间选择 -->
      <div class="mb-20">
        <div class="text-secondary" style="margin-bottom: 8px;">预算周期</div>
        <div class="tab-container">
          <div class="tab active">月度</div>
          <div class="tab">季度</div>
          <div class="tab">年度</div>
        </div>
      </div>
      
      <!-- 总预算 -->
      <div class="card mb-20">
        <h3 class="section-title">总预算</h3>
        
        <div class="input-container">
          <input type="text" class="input large" value="5,000.00">
          <div class="input-prefix">￥</div>
        </div>
        
        <div style="display: flex; justify-content: space-between; margin-top: 10px;">
          <div class="text-secondary">上月支出: ￥4,500</div>
          <div class="text-primary">复制为预算</div>
        </div>
      </div>
      
      <!-- 分类预算 -->
      <div class="card mb-20">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
          <h3 class="section-title" style="margin: 0;">分类预算</h3>
          <div class="text-secondary">已分配: ￥4,100</div>
        </div>
        
        <!-- 餐饮预算 -->
        <div class="budget-item">
          <div class="budget-header">
            <div class="category-info">
              <div class="category-icon" style="background-color: #FEF3C7;">
                <i class="fas fa-utensils" style="color: #F59E0B; font-size: 16px;"></i>
              </div>
              <div class="category-details">
                <div class="category-name">餐饮</div>
                <div class="category-stats">上月: ￥1,350</div>
              </div>
            </div>
            <div class="budget-amount">
              <input type="text" class="amount-input" value="1,500">
              <div class="amount-prefix">￥</div>
            </div>
          </div>
          
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress" style="width: 30%; background-color: #F59E0B;"></div>
            </div>
            <div class="progress-value">30%</div>
          </div>
        </div>
        
        <!-- 购物预算 -->
        <div class="budget-item">
          <div class="budget-header">
            <div class="category-info">
              <div class="category-icon" style="background-color: #E0F2FE;">
                <i class="fas fa-shopping-bag" style="color: #0EA5E9; font-size: 16px;"></i>
              </div>
              <div class="category-details">
                <div class="category-name">购物</div>
                <div class="category-stats">上月: ￥965</div>
              </div>
            </div>
            <div class="budget-amount">
              <input type="text" class="amount-input" value="1,000">
              <div class="amount-prefix">￥</div>
            </div>
          </div>
          
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress" style="width: 20%; background-color: #0EA5E9;"></div>
            </div>
            <div class="progress-value">20%</div>
          </div>
        </div>
        
        <!-- 交通预算 -->
        <div class="budget-item">
          <div class="budget-header">
            <div class="category-info">
              <div class="category-icon" style="background-color: #DCFCE7;">
                <i class="fas fa-bus" style="color: #10B981; font-size: 16px;"></i>
              </div>
              <div class="category-details">
                <div class="category-name">交通</div>
                <div class="category-stats">上月: ￥580</div>
              </div>
            </div>
            <div class="budget-amount">
              <input type="text" class="amount-input" value="600">
              <div class="amount-prefix">￥</div>
            </div>
          </div>
          
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress" style="width: 12%; background-color: #10B981;"></div>
            </div>
            <div class="progress-value">12%</div>
          </div>
        </div>
        
        <!-- 娱乐预算 -->
        <div class="budget-item" style="margin-bottom: 10px;">
          <div class="budget-header">
            <div class="category-info">
              <div class="category-icon" style="background-color: #F3E8FF;">
                <i class="fas fa-film" style="color: #8B5CF6; font-size: 16px;"></i>
              </div>
              <div class="category-details">
                <div class="category-name">娱乐</div>
                <div class="category-stats">上月: ￥780</div>
              </div>
            </div>
            <div class="budget-amount">
              <input type="text" class="amount-input" value="1,000">
              <div class="amount-prefix">￥</div>
            </div>
          </div>
          
          <div class="progress-container">
            <div class="progress-bar">
              <div class="progress" style="width: 20%; background-color: #8B5CF6;"></div>
            </div>
            <div class="progress-value">20%</div>
          </div>
        </div>
        
        <div style="display: flex; justify-content: center; margin-top: 15px;">
          <button class="btn btn-outline" style="padding: 8px 16px; font-size: 14px; width: auto;">
            <i class="fas fa-plus" style="margin-right: 5px;"></i> 添加分类预算
          </button>
        </div>
      </div>
      
      <!-- 预算提醒 -->
      <div class="card mb-20">
        <h3 class="section-title">预算提醒</h3>
        
        <div style="margin-bottom: 15px;">
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <div style="font-size: 14px;">总体预算预警</div>
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider round"></span>
            </label>
          </div>
          <div style="display: flex; align-items: center; gap: 10px;">
            <div class="progress-bar" style="position: relative;">
              <div class="progress" style="width: 80%; background-color: #FF6B35;"></div>
              <div style="position: absolute; top: -20px; left: 80%; transform: translateX(-50%); font-size: 12px; color: #FF6B35;">80%</div>
            </div>
            <div style="font-size: 14px; color: #888;">预警</div>
          </div>
        </div>
        
        <div>
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
            <div style="font-size: 14px;">分类预算预警</div>
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider round"></span>
            </label>
          </div>
          <div style="display: flex; align-items: center; gap: 10px;">
            <div class="progress-bar" style="position: relative;">
              <div class="progress" style="width: 90%; background-color: #FF4D4F;"></div>
              <div style="position: absolute; top: -20px; left: 90%; transform: translateX(-50%); font-size: 12px; color: #FF4D4F;">90%</div>
            </div>
            <div style="font-size: 14px; color: #888;">预警</div>
          </div>
        </div>
      </div>
      
      <!-- 保存按钮 -->
      <button class="btn btn-primary mb-20">保存预算设置</button>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 标签切换
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          tabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');
        });
      });
      
      // 返回按钮
      document.querySelector('.nav-left').addEventListener('click', function() {
        history.back();
      });
    });
  </script>
</body>
</html>
    }
  </style>
</body>
</html>