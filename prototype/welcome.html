<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>账无忌 - AI智能记账</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式重置 */
    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }
    
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #fff;
    }
    
    /* 状态栏 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      color: #333;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      position: relative;
      z-index: 100;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 滑动容器 */
    .swiper-container {
      width: 100%;
      height: calc(100% - 44px);
      overflow: hidden;
      position: relative;
    }
    
    .swiper-wrapper {
      display: flex;
      height: 100%;
      transition: transform 0.3s ease;
    }
    
    .swiper-slide {
      flex-shrink: 0;
      width: 100%;
      height: 100%;
      background: #fff;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      padding: 30px;
      position: relative;
      overflow: hidden;
      text-align: center;
    }
    
    /* 特别设置内容滑动页 */
    #slide2, #slide3, #slide4 {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
    }
    
    /* 滑动指示器 */
    .swiper-pagination {
      position: absolute;
      bottom: 80px;
      left: 0;
      right: 0;
      display: flex;
      justify-content: center;
      gap: 8px;
    }
    
    .swiper-pagination-bullet {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background-color: #ddd;
      opacity: 0.6;
      transition: all 0.3s ease;
    }
    
    .swiper-pagination-bullet-active {
      width: 20px;
      border-radius: 4px;
      background-color: #FF6B35;
      opacity: 1;
    }
    
    /* 引导页图标 */
    .feature-icon {
      width: 180px;
      height: 180px;
      margin-bottom: 40px;
      transition: all 0.5s ease;
      margin-left: auto;
      margin-right: auto;
      display: block;
    }
    
    .feature-icon-1, .feature-icon-2, .feature-icon-3 {
      max-width: 180px;
      max-height: 180px;
      width: 180px;
      height: 180px;
      margin: 0 auto 40px;
      display: block;
      background-position: center;
      background-size: contain;
      background-repeat: no-repeat;
    }
    
    .feature-icon-1 {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI5MCIgZmlsbD0iI0Y4RjlGQSIgLz48cGF0aCBkPSJNMTAwIDU1QzkxLjcxNiA1NSA4NSA2MS43MTYgODUgNzBWMTEwQzg1IDExOC4yODQgOTEuNzE2IDEyNSAxMDAgMTI1QzEwOC4yODQgMTI1IDExNSAxMTguMjg0IDExNSAxMTBWNzBDMTE1IDYxLjcxNiAxMDguMjg0IDU1IDEwMCA1NVoiIGZpbGw9IiNGRjZCMzUiIC8+PHBhdGggZD0iTTEzMCAxMDBDMTMwIDEwMy44NjYgMTI2Ljg2NiAxMDcgMTIzIDEwN0MxMTkuMTM0IDEwNyAxMTYgMTAzLjg2NiAxMTYgMTAwQzExNiA5Ni4xMzQgMTE5LjEzNCA5MyAxMjMgOTNDMTI2Ljg2NiA5MyAxMzAgOTYuMTM0IDEzMCAxMDBaIiBmaWxsPSIjRkY2QjM1IiAvPjxwYXRoIGQ9Ik03NyA5M0M3My4xMzQgOTMgNzAgOTYuMTM0IDcwIDEwMEM3MCAxMDMuODY2IDczLjEzNCAxMDcgNzcgMTA3QzgwLjg2NiAxMDcgODQgMTAzLjg2NiA4NCAxMDBDODQgOTYuMTM0IDgwLjg2NiA5MyA3NyA5M1oiIGZpbGw9IiNGRjZCMzUiIC8+PHBhdGggZD0iTTcwIDEzMEgxMzBWMTQwQzEzMCAxNTEuMDQ2IDEyMS4wNDYgMTYwIDExMCAxNjBIOTBDNzguOTU0MyAxNjAgNzAgMTUxLjA0NiA3MCAxNDBWMTMwWiIgZmlsbD0iI0ZGNkIzNSIgLz48L3N2Zz4=') no-repeat center/contain;
    }
    
    .feature-icon-2 {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI5MCIgZmlsbD0iI0Y4RjlGQSIgLz48cGF0aCBkPSJNMTMwIDYwSDcwQzY0LjQ3NzIgNjAgNjAgNjQuNDc3MiA2MCA3MFYxMzBDNjAgMTM1LjUyMyA2NC40NzcyIDE0MCA3MCAxNDBIMTMwQzEzNS41MjMgMTQwIDE0MCAxMzUuNTIzIDE0MCAxMzBWNzBDMTQwIDY0LjQ3NzIgMTM1LjUyMyA2MCAxMzAgNjBaIiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSIjRkZCQTA4IiBzdHJva2Utd2lkdGg9IjMiIC8+PHBhdGggZD0iTTc1IDg1SDEyNU02NSA5NUgxMzVNNzUgMTA1SDEyNU02NSAxMTVIMTM1IiBzdHJva2U9IiNGRkJBMDgiIHN0cm9rZS13aWR0aD0iMyIgLz48cGF0aCBkPSJNMTUwLjcwNyA4MC43MDcxTDE2MCAxMDBMMTUwLjcwNyAxMTkuMjkzIiBzdHJva2U9IiNGRkJBMDgiIHN0cm9rZS13aWR0aD0iMyIgLz48cGF0aCBkPSJNNDkuMjkyOSA4MC43MDcxTDQwIDEwMEw0OS4yOTI5IDExOS4yOTMiIHN0cm9rZT0iI0ZGQkEwOCIgc3Ryb2tlLXdpZHRoPSIzIiAvPjwvc3ZnPg==') no-repeat center/contain;
    }
    
    .feature-icon-3 {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI5MCIgZmlsbD0iI0Y4RjlGQSIgLz48cGF0aCBkPSJNMTAwIDUwQzcyLjM4NTggNTAgNTAgNzIuMzg1OCA1MCAxMDBDNTAgMTI3LjYxNCA3Mi4zODU4IDE1MCAxMDAgMTUwQzEyNy42MTQgMTUwIDE1MCAxMjcuNjE0IDE1MCAxMDBDMTUwIDcyLjM4NTggMTI3LjYxNCA1MCAxMDAgNTBaIiBzdHJva2U9IiM3QjYxRkYiIHN0cm9rZS13aWR0aD0iMyIgLz48cGF0aCBkPSJNMTAwIDUwVjEwMEwxNDAgODAiIHN0cm9rZT0iIzdCNjFGRiIgc3Ryb2tlLXdpZHRoPSI0IiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIC8+PHBhdGggZD0iTTExMCAxMzBINTBNMTUwIDEzMEg5MCIgc3Ryb2tlPSIjN0I2MUZGIiBzdHJva2Utd2lkdGg9IjMiIC8+PC9zdmc+') no-repeat center/contain;
    }
    
    .feature-icon-cover {
      background: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEwMCA1NUMxMDAgNTUgNzAgNjUgNDUgNzVWMTIwQzQ1IDE0MCAxMDAgMTYwIDEwMCAxNjBDMTAwIDE2MCAxNTUgMTQwIDE1NSAxMjBWNzVDMTMwIDY1IDEwMCA1NSAxMDAgNTVaIiBmaWxsPSIjRjhGOUZBIiBzdHJva2U9IiM0MDQwNDAiIHN0cm9rZS13aWR0aD0iNCIgLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMDAiIHI9IjE1IiBmaWxsPSIjRkY2QjM1IiAvPjxwYXRoIGQ9Ik0xMDAgNzBDMTAwIDcwIDgwIDc3LjUgNjUgODVWMTA3LjVDNjUgMTIwIDEwMCAxMzIuNSAxMDAgMTMyLjVDMTAwIDEzMi41IDEzNSAxMjAgMTM1IDEwNy41Vjg1QzEyMCA3Ny41IDEwMCA3MCAxMDAgNzBaIiBzdHJva2U9IiM0MDQwNDAiIHN0cm9rZS13aWR0aD0iMyIgLz48L3N2Zz4=') no-repeat center/contain;
    }
    
    /* 文本样式 */
    .feature-title {
      font-size: 26px;
      font-weight: bold;
      margin-bottom: 16px;
      color: #333;
      text-align: center;
      position: relative;
      display: block;
      width: 100%;
      margin-left: auto;
      margin-right: auto;
    }
    
    .feature-title::after {
      content: "";
      display: block;
      width: 40px;
      height: 4px;
      background: #FF6B35;
      position: absolute;
      bottom: -10px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .feature-badge {
      font-size: 16px;
      color: #666;
      background: #f8f9fa;
      border-radius: 30px;
      padding: 4px 15px;
      margin-bottom: 20px;
      display: inline-flex;
      align-items: center;
      margin-left: auto;
      margin-right: auto;
    }
    
    .feature-badge i {
      color: #FF6B35;
      margin-right: 5px;
    }
    
    .feature-desc {
      font-size: 16px;
      color: #666;
      text-align: center;
      margin: 0 auto 40px;
      line-height: 1.6;
      width: 100%;
      max-width: 300px;
    }
    
    /* 主页特殊元素 */
    .cover-title-1 {
      font-size: 40px;
      font-weight: bold;
      display: inline-block;
      color: #333;
      position: relative;
      margin-bottom: 40px;
      margin-left: auto;
      margin-right: auto;
    }
    
    .cover-title-1::after {
      content: "";
      display: block;
      width: 6px;
      height: 40px;
      background: #FF6B35;
      position: absolute;
      left: -15px;
      top: 5px;
    }
    
    .cover-title-2 {
      font-size: 40px;
      font-weight: bold;
      color: #FF6B35;
      margin-bottom: 40px;
    }
    
    .cover-desc {
      font-size: 18px;
      color: #666;
      margin-bottom: 50px;
      text-align: center;
      margin-left: auto;
      margin-right: auto;
      width: 90%;
    }
    
    .cover-highlight {
      font-size: 24px;
      color: #333;
      margin-top: 20px;
      margin-bottom: 30px;
      position: relative;
      text-align: center;
    }
    
    .cover-highlight::before,
    .cover-highlight::after {
      content: "";
      display: block;
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: #FF6B35;
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
    }
    
    .cover-highlight::before {
      left: -20px;
    }
    
    .cover-highlight::after {
      right: -20px;
    }
    
    /* 按钮样式 */
    .btn-start {
      background: #FF6B35;
      color: white;
      border: none;
      padding: 15px 30px;
      font-size: 18px;
      font-weight: bold;
      border-radius: 30px;
      margin-bottom: 15px;
      box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
      cursor: pointer;
      transition: all 0.3s ease;
      margin-left: auto;
      margin-right: auto;
      min-width: 200px;
    }
    
    .btn-start:active {
      transform: translateY(3px);
      box-shadow: 0 2px 5px rgba(255, 107, 53, 0.3);
    }
    
    .login-text {
      color: #666;
      font-size: 14px;
    }
    
    .login-link {
      color: #FF6B35;
      text-decoration: none;
      font-weight: bold;
    }
    
    /* 动画效果 */
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(20px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    .animate-fade {
      animation: fadeIn 0.8s ease forwards;
    }
    
    .delay-200 {
      animation-delay: 0.2s;
      opacity: 0;
    }
    
    .delay-400 {
      animation-delay: 0.4s;
      opacity: 0;
    }
    
    .delay-600 {
      animation-delay: 0.6s;
      opacity: 0;
    }
  </style>
</head>
<body class="phone-page">
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 滑动容器 -->
    <div class="swiper-container">
      <div class="swiper-wrapper">
        <!-- 引导页主页 -->
        <div class="swiper-slide" id="slide1">
          <img src="账无忌LOGO.png" alt="账无忌" class="feature-icon animate-fade" style="width: 180px; height: 180px; object-fit: contain;">
          <h1 class="cover-title-1 animate-fade delay-200">智能</h1>
          <h1 class="cover-title-2 animate-fade delay-400">记账</h1>
          <p class="cover-desc animate-fade delay-600">账无忌，让财务管理轻松自在</p>
          <p class="cover-highlight">AI驱动 安全可靠</p>
        </div>
        
        <!-- 特性1：语音记账 -->
        <div class="swiper-slide" id="slide2">
          <div class="feature-icon-1 animate-fade" style="margin:0 auto 40px;"></div>
          <h2 class="feature-title animate-fade delay-200">语音识别记账</h2>
          <div class="feature-badge animate-fade delay-400">
            <i class="fas fa-bolt"></i>快捷高效
          </div>
          <p class="feature-desc animate-fade delay-600">
            只需一句话，即可完成记账<br>
            解放双手，告别繁琐操作
          </p>
        </div>
        
        <!-- 特性2：票据识别 -->
        <div class="swiper-slide" id="slide3">
          <div class="feature-icon-2 animate-fade" style="margin:0 auto 40px;"></div>
          <h2 class="feature-title animate-fade delay-200">智能票据识别</h2>
          <div class="feature-badge animate-fade delay-400">
            <i class="fas fa-camera"></i>拍照即录
          </div>
          <p class="feature-desc animate-fade delay-600">
            一键拍照，智能识别票据信息<br>
            自动分类，精准记录每笔开支
          </p>
        </div>
        
        <!-- 特性3：智能分析 -->
        <div class="swiper-slide" id="slide4">
          <div class="feature-icon-3 animate-fade" style="margin:0 auto 40px;"></div>
          <h2 class="feature-title animate-fade delay-200">智能分析报告</h2>
          <div class="feature-badge animate-fade delay-400">
            <i class="fas fa-chart-pie"></i>一目了然
          </div>
          <p class="feature-desc animate-fade delay-600">
            个性化消费分析，智能财务建议<br>
            精准掌握收支状况，轻松规划未来
          </p>
          
          <button class="btn-start animate-fade delay-600">立即开始体验</button>
          <p class="login-text animate-fade delay-600">已有账号？<a href="login.html" class="login-link">立即登录</a></p>
        </div>
      </div>
      
      <!-- 滑动指示器 -->
      <div class="swiper-pagination">
        <div class="swiper-pagination-bullet swiper-pagination-bullet-active"></div>
        <div class="swiper-pagination-bullet"></div>
        <div class="swiper-pagination-bullet"></div>
        <div class="swiper-pagination-bullet"></div>
      </div>
    </div>
  </div>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      const swiperWrapper = document.querySelector('.swiper-wrapper');
      const slides = document.querySelectorAll('.swiper-slide');
      const bullets = document.querySelectorAll('.swiper-pagination-bullet');
      const startBtn = document.querySelector('.btn-start');
      
      let currentIndex = 0;
      let startX, moveX;
      let slideWidth = window.innerWidth;
      
      // 初始化确保所有页面正确位置
      function resetSlidePositions() {
        slideWidth = window.innerWidth;
        slides.forEach((slide, index) => {
          slide.style.width = `${slideWidth}px`;
        });
        swiperWrapper.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
      }
      
      // 更新指示器
      function updatePagination() {
        bullets.forEach((bullet, index) => {
          if (index === currentIndex) {
            bullet.classList.add('swiper-pagination-bullet-active');
          } else {
            bullet.classList.remove('swiper-pagination-bullet-active');
          }
        });
      }
      
      // 滑动到指定页
      function slideTo(index) {
        if (index < 0) index = 0;
        if (index > slides.length - 1) index = slides.length - 1;
        
        currentIndex = index;
        swiperWrapper.style.transform = `translateX(-${currentIndex * slideWidth}px)`;
        updatePagination();
      }
      
      // 触摸事件
      swiperWrapper.addEventListener('touchstart', (e) => {
        startX = e.touches[0].clientX;
        moveX = startX; // 初始化moveX，防止touchend时moveX未定义
      });
      
      swiperWrapper.addEventListener('touchmove', (e) => {
        moveX = e.touches[0].clientX;
        let offsetX = moveX - startX;
        let translateX = -currentIndex * slideWidth + offsetX;
        
        // 限制最大滑动距离
        if (translateX > 0) translateX = 0;
        if (translateX < -(slides.length - 1) * slideWidth) {
          translateX = -(slides.length - 1) * slideWidth;
        }
        
        swiperWrapper.style.transform = `translateX(${translateX}px)`;
        e.preventDefault(); // 防止页面滚动
      });
      
      swiperWrapper.addEventListener('touchend', (e) => {
        if (!moveX) return; // 如果没有移动，直接返回
        
        let offsetX = moveX - startX;
        
        if (Math.abs(offsetX) > slideWidth / 3) {
          if (offsetX > 0) {
            slideTo(currentIndex - 1);
          } else {
            slideTo(currentIndex + 1);
          }
        } else {
          slideTo(currentIndex);
        }
      });
      
      // 指示器点击事件
      bullets.forEach((bullet, index) => {
        bullet.addEventListener('click', () => {
          slideTo(index);
        });
      });
      
      // 开始按钮点击事件
      if (startBtn) {
        startBtn.addEventListener('click', () => {
          // 这里可以添加跳转到主页的逻辑
          window.location.href = 'main.html';
        });
      }
      
      // 窗口大小改变时更新滑动宽度
      window.addEventListener('resize', () => {
        resetSlidePositions();
      });
      
      // 初始化
      resetSlidePositions();
    });
  </script>
</body>
</html>