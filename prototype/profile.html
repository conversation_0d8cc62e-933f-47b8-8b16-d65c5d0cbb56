<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>个人信息 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
      color: #666;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 16px;
      padding: 15px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    /* 列表样式 */
    .list {
      margin: 0;
      padding: 0;
    }
    
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .list-item:last-child {
      border-bottom: none;
    }
    
    /* 头像样式 */
    .avatar-container {
      background-color: white;
      border-radius: 16px;
      margin-bottom: 12px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
      padding: 15px;
    }
    
    .avatar-wrapper {
      position: relative;
    }
    
    .avatar {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      overflow: hidden;
    }
    
    .avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .avatar-edit {
      position: absolute;
      bottom: 0;
      right: 0;
      width: 16px;
      height: 16px;
      background-color: #FF6B35;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
    
    .avatar-edit i {
      color: white;
      font-size: 8px;
    }
    
    .user-info {
      margin-left: 15px;
      text-align: left;
      flex: 1;
    }
    
    .username {
      font-size: 16px;
      font-weight: 600;
      color: #333;
      margin-bottom: 3px;
    }
    
    .user-id {
      font-size: 12px;
      color: #999;
    }
    
    /* 分区标题 */
    .section-title {
      font-size: 14px;
      color: #666;
      margin: 10px 5px 5px;
      font-weight: 500;
    }
    
    /* 列表项内文字样式 */
    .item-value {
      color: #666;
      margin-right: 10px;
    }
    
    .item-placeholder {
      color: #999;
      margin-right: 10px;
    }
    
    .chevron-icon {
      color: #999;
    }
    
    /* 按钮样式 */
    .btn {
      display: inline-block;
      border: none;
      border-radius: 10px;
      font-size: 15px;
      font-weight: 500;
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      width: 100%;
    }
    
    .btn-danger {
      background-color: #FF6B35;
      color: white;
    }
    
    .btn-danger:active {
      background-color: #e05a2b;
    }
    
    /* 简单列表 */
    .simple-list .list-item {
      padding: 10px 0;
    }
    
    .simple-item-title {
      font-size: 14px;
      color: #333;
    }
    
    .simple-item-value {
      font-size: 14px;
      color: #999;
      display: flex;
      align-items: center;
    }
    
    .simple-item-value i {
      margin-left: 5px;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">个人信息</div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 基本信息卡片 -->
      <div class="card">
        <div class="simple-list">
          <!-- 头像行 -->
          <div class="list-item">
            <div class="simple-item-title">头像</div>
            <div class="simple-item-value">
              <div class="avatar">
                <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
              </div>
              <i class="fas fa-chevron-right chevron-icon" style="margin-left: 15px;"></i>
            </div>
          </div>
          
          <!-- 昵称行 -->
          <div class="list-item">
            <div class="simple-item-title">昵称</div>
            <div class="simple-item-value">
              耀宸 <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 个人基本资料 -->
      <div class="section-title">基本资料</div>
      <div class="card">
        <div class="list">
          <div class="list-item">
            <div>手机号</div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">138****0000</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div>电子邮箱</div>
            <div style="display: flex; align-items: center;">
              <div class="item-placeholder">未绑定</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div>生日</div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">1990-01-01</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div>职业</div>
            <div style="display: flex; align-items: center;">
              <div class="item-placeholder">未设置</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 关于我 -->
      <div class="section-title">关于我</div>
      <div class="card">
        <div class="list">
          <div class="list-item">
            <div>个性签名</div>
            <div style="display: flex; align-items: center;">
              <div class="item-placeholder">点击设置个性签名</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div>财务目标</div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">存款30万</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div>消费习惯标签</div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 设置入口 -->
      <div class="card" style="padding: 12px 15px;">
        <div class="list-item" style="padding: 0;">
          <div style="display: flex; align-items: center;">
            <i class="fas fa-cog" style="color: #999; margin-right: 10px;"></i>
            <div>更多设置</div>
          </div>
          <div>
            <i class="fas fa-chevron-right chevron-icon"></i>
          </div>
        </div>
      </div>
    </div>
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 返回按钮功能
      const backButton = document.querySelector('.nav-left');
      backButton.addEventListener('click', function() {
        window.history.back();
      });
      
      // 更多设置按钮功能
      const settingsButton = document.querySelector('.list-item:has(i.fa-cog)');
      settingsButton.addEventListener('click', function() {
        window.location.href = 'settings.html';
      });
    });
  </script>
</body>
</html>