<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>资产 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    .nav-right {
      display: flex;
      align-items: center;
    }
    
    .nav-icon {
      width: 24px;
      text-align: center;
      color: #666;
      margin-left: 15px;
      position: relative;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 6px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
      padding-bottom: 80px;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 16px;
      padding: 12px;
      margin-bottom: 8px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    /* 资产总览样式 */
    .assets-overview {
      background: linear-gradient(135deg, #FF6B35, #FF8F6B);
      border-radius: 16px;
      padding: 15px;
      margin-bottom: 10px;
      color: white;
    }
    
    .assets-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 12px;
    }
    
    .assets-title {
      font-size: 14px;
      font-weight: 600;
    }
    
    .assets-toggle {
      font-size: 12px;
      display: flex;
      align-items: center;
    }
    
    .assets-toggle i {
      margin-left: 3px;
    }
    
    .assets-amount {
      font-size: 24px;
      font-weight: 700;
      margin-bottom: 5px;
    }
    
    .assets-change {
      font-size: 12px;
      display: flex;
      align-items: center;
    }
    
    .assets-change i {
      margin-right: 3px;
    }
    
    .assets-change.positive {
      color: #E2F8E9;
    }
    
    .assets-change.negative {
      color: #FFE8E8;
    }
    
    .assets-distribution {
      display: flex;
      margin-top: 15px;
      padding-top: 15px;
      border-top: 1px solid rgba(255, 255, 255, 0.2);
    }
    
    .asset-segment {
      flex: 1;
      text-align: center;
    }
    
    .segment-value {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 2px;
    }
    
    .segment-label {
      font-size: 11px;
      opacity: 0.8;
    }
    
    /* 添加资产按钮 */
    .add-asset-btn {
      position: absolute;
      bottom: 70px;
      right: 25px;
      width: 50px;
      height: 50px;
      background-color: #FF6B35;
      border-radius: 50%;
      display: flex;
      justify-content: center;
      align-items: center;
      color: white;
      box-shadow: 0 4px 12px rgba(255, 107, 53, 0.3);
      z-index: 100;
    }
    
    /* 资产类型导航 */
    .asset-nav {
      display: flex;
      overflow-x: auto;
      padding: 5px 5px 10px;
      margin-bottom: 5px;
      -webkit-overflow-scrolling: touch;
      scrollbar-width: none;
    }
    
    .asset-nav::-webkit-scrollbar {
      display: none;
    }
    
    .asset-nav-item {
      padding: 8px 15px;
      background-color: #fff;
      border-radius: 20px;
      font-size: 12px;
      margin-right: 8px;
      white-space: nowrap;
      color: #666;
      border: 1px solid #eee;
    }
    
    .asset-nav-item.active {
      background-color: #FF6B35;
      color: white;
      border: 1px solid #FF6B35;
    }
    
    /* 资产项样式 */
    .asset-item {
      display: flex;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid #f0f0f0;
    }
    
    .asset-item:last-child {
      border-bottom: none;
    }
    
    .asset-icon {
      width: 36px;
      height: 36px;
      border-radius: 8px;
      background-color: #F0F7FF;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #3A7CFF;
      margin-right: 12px;
      font-size: 16px;
    }
    
    .asset-info {
      flex: 1;
    }
    
    .asset-name {
      font-size: 14px;
      font-weight: 500;
      margin-bottom: 2px;
    }
    
    .asset-details {
      font-size: 11px;
      color: #999;
    }
    
    .asset-value {
      font-size: 15px;
      font-weight: 600;
      text-align: right;
    }
    
    .asset-value.positive {
      color: #10B981;
    }
    
    .asset-value.negative {
      color: #EF4444;
    }
    
    /* 银行卡样式 */
    .bank-card {
      background: linear-gradient(135deg, #3A7CFF, #6366F1);
      border-radius: 16px;
      padding: 15px;
      margin-bottom: 10px;
      color: white;
      position: relative;
      overflow: hidden;
    }
    
    .bank-card-pattern {
      position: absolute;
      right: -20px;
      bottom: -20px;
      width: 100px;
      height: 100px;
      opacity: 0.1;
      background-color: white;
      border-radius: 50%;
    }
    
    .bank-logo {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
    }
    
    .bank-name {
      font-weight: 600;
      font-size: 16px;
    }
    
    .card-type {
      font-size: 12px;
      padding: 2px 8px;
      background-color: rgba(255,255,255,0.2);
      border-radius: 10px;
    }
    
    .card-number {
      font-size: 14px;
      letter-spacing: 2px;
      margin-bottom: 15px;
    }
    
    .card-bottom {
      display: flex;
      justify-content: space-between;
      align-items: flex-end;
    }
    
    .card-holder {
      font-size: 11px;
      opacity: 0.8;
    }
    
    .card-holder-name {
      font-size: 14px;
    }
    
    .card-balance {
      text-align: right;
    }
    
    .balance-label {
      font-size: 11px;
      opacity: 0.8;
    }
    
    .balance-amount {
      font-size: 18px;
      font-weight: 600;
    }
    
    /* 债务负债样式 */
    .liability-item .asset-icon {
      background-color: #FFF1F5;
      color: #EC4899;
    }
    
    /* 投资资产样式 */
    .investment-item .asset-icon {
      background-color: #FFF9E6;
      color: #F59E0B;
    }
    
    /* 现金资产样式 */
    .cash-item .asset-icon {
      background-color: #ECFDF5;
      color: #10B981;
    }
    
    /* 无资产提示 */
    .no-assets {
      text-align: center;
      padding: 30px 0;
    }
    
    .no-assets-icon {
      font-size: 40px;
      color: #ccc;
      margin-bottom: 10px;
    }
    
    .no-assets-text {
      font-size: 14px;
      color: #999;
    }
    
    /* 模块标题 */
    .module-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 12px 4px 8px;
      display: flex;
      align-items: center;
    }
    
    .module-title i {
      color: #FF6B35;
      margin-right: 5px;
      font-size: 12px;
    }
    
    /* 底部标签栏 */
    .tab-bar {
      height: 55px;
      background-color: white;
      display: flex;
      justify-content: space-around;
      align-items: center;
      border-top: 1px solid rgba(0, 0, 0, 0.05);
      flex-shrink: 0;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.03);
    }
    
    .tab-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      flex: 1;
      height: 100%;
    }
    
    .tab-icon {
      font-size: 20px;
      color: #999;
      margin-bottom: 3px;
    }
    
    .tab-label {
      font-size: 11px;
      color: #999;
    }
    
    .tab-icon.active {
      color: #FF6B35;
    }
    
    .tab-label.active {
      color: #FF6B35;
      font-weight: 500;
    }
    
    /* 编辑资产按钮 */
    .edit-asset-btn {
      width: 24px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      color: #666;
      margin-left: 8px;
    }
    
    /* 资产分类管理入口 */
    .asset-category-manage {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 4px;
    }
    
    .asset-category-manage-btn {
      font-size: 12px;
      color: #666;
      display: flex;
      align-items: center;
    }
    
    .asset-category-manage-btn i {
      margin-right: 3px;
      font-size: 10px;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div style="width: 24px;">
        <i class="fas fa-arrow-left" id="back-button"></i>
      </div>
      <div class="nav-title">资产</div>
      <div class="nav-right">
        <div class="nav-icon" id="sync-icon">
          <i class="fas fa-sync-alt"></i>
        </div>
        <div class="nav-icon" id="filter-icon">
          <i class="fas fa-sliders-h"></i>
        </div>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 资产总览 -->
      <div class="assets-overview">
        <div class="assets-header">
          <div class="assets-title">总资产</div>
          <div class="assets-toggle">
            <span>隐藏金额</span>
            <i class="fas fa-eye-slash"></i>
          </div>
        </div>
        <div class="assets-amount">¥168,500.28</div>
        <div class="assets-change positive">
          <i class="fas fa-arrow-up"></i>
          <span>¥2,340.50 (1.4%) 本月</span>
        </div>
        
        <div class="assets-distribution">
          <div class="asset-segment">
            <div class="segment-value">¥102,500</div>
            <div class="segment-label">总资产</div>
          </div>
          <div class="asset-segment">
            <div class="segment-value">¥45,200</div>
            <div class="segment-label">总负债</div>
          </div>
          <div class="asset-segment">
            <div class="segment-value">¥57,300</div>
            <div class="segment-label">净资产</div>
          </div>
        </div>
      </div>
      
      <!-- 资产类型导航 -->
      <div class="asset-nav">
        <div class="asset-nav-item active">全部</div>
        <div class="asset-nav-item">银行卡</div>
        <div class="asset-nav-item">现金</div>
        <div class="asset-nav-item">投资</div>
        <div class="asset-nav-item">负债</div>
        <div class="asset-nav-item">债权</div>
        <div class="asset-nav-item">其他</div>
      </div>
      
      <!-- 银行卡模块 -->
      <div class="asset-category-manage">
        <div class="module-title">
          <i class="fas fa-university"></i>银行卡
        </div>
        <div class="asset-category-manage-btn" id="manage-bank">
          <i class="fas fa-cog"></i>管理
        </div>
      </div>
      
      <!-- 银行卡示例 -->
      <div class="bank-card">
        <div class="bank-card-pattern"></div>
        <div class="bank-logo">
          <div class="bank-name">招商银行</div>
          <div class="card-type">储蓄卡</div>
        </div>
        <div class="card-number">**** **** **** 8432</div>
        <div class="card-bottom">
          <div class="card-holder-info">
            <div class="card-holder">持卡人</div>
            <div class="card-holder-name">张先生</div>
          </div>
          <div class="card-balance">
            <div class="balance-label">余额</div>
            <div class="balance-amount">¥32,568.42</div>
          </div>
        </div>
      </div>
      
      <div class="bank-card" style="background: linear-gradient(135deg, #9A6AFF, #6366F1);">
        <div class="bank-card-pattern"></div>
        <div class="bank-logo">
          <div class="bank-name">中国建设银行</div>
          <div class="card-type">信用卡</div>
        </div>
        <div class="card-number">**** **** **** 5217</div>
        <div class="card-bottom">
          <div class="card-holder-info">
            <div class="card-holder">持卡人</div>
            <div class="card-holder-name">张先生</div>
          </div>
          <div class="card-balance">
            <div class="balance-label">可用额度</div>
            <div class="balance-amount">¥42,800.00</div>
          </div>
        </div>
      </div>
      
      <!-- 现金资产模块 -->
      <div class="asset-category-manage">
        <div class="module-title">
          <i class="fas fa-money-bill-wave"></i>现金资产
        </div>
        <div class="asset-category-manage-btn" id="manage-cash">
          <i class="fas fa-cog"></i>管理
        </div>
      </div>
      <div class="card">
        <div class="asset-item cash-item">
          <div class="asset-icon">
            <i class="fas fa-wallet"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">日常现金</div>
            <div class="asset-details">上次更新：2023-06-15</div>
          </div>
          <div class="asset-value positive">¥1,200.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
        
        <div class="asset-item cash-item">
          <div class="asset-icon">
            <i class="fas fa-piggy-bank"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">家庭小金库</div>
            <div class="asset-details">上次更新：2023-06-10</div>
          </div>
          <div class="asset-value positive">¥5,000.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
      </div>
      
      <!-- 投资资产模块 -->
      <div class="asset-category-manage">
        <div class="module-title">
          <i class="fas fa-chart-line"></i>投资资产
        </div>
        <div class="asset-category-manage-btn" id="manage-investment">
          <i class="fas fa-cog"></i>管理
        </div>
      </div>
      <div class="card">
        <div class="asset-item investment-item">
          <div class="asset-icon">
            <i class="fas fa-landmark"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">余额宝</div>
            <div class="asset-details">七日年化：1.82%</div>
          </div>
          <div class="asset-value positive">¥12,500.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
        
        <div class="asset-item investment-item">
          <div class="asset-icon">
            <i class="fas fa-funnel-dollar"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">定期存款</div>
            <div class="asset-details">年利率：2.1% | 到期：2024-01-15</div>
          </div>
          <div class="asset-value positive">¥50,000.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
        
        <div class="asset-item investment-item">
          <div class="asset-icon">
            <i class="fas fa-chart-pie"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">基金组合</div>
            <div class="asset-details">累计收益：+8.6%</div>
          </div>
          <div class="asset-value positive">¥24,231.86</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
      </div>
      
      <!-- 负债模块 -->
      <div class="asset-category-manage">
        <div class="module-title">
          <i class="fas fa-hand-holding-usd"></i>负债
        </div>
        <div class="asset-category-manage-btn" id="manage-liability">
          <i class="fas fa-cog"></i>管理
        </div>
      </div>
      <div class="card">
        <div class="asset-item liability-item">
          <div class="asset-icon">
            <i class="fas fa-home"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">房贷</div>
            <div class="asset-details">月供：¥3,200 | 剩余：180期</div>
          </div>
          <div class="asset-value negative">-¥420,000.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
        
        <div class="asset-item liability-item">
          <div class="asset-icon">
            <i class="fas fa-car"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">车贷</div>
            <div class="asset-details">月供：¥1,500 | 剩余：24期</div>
          </div>
          <div class="asset-value negative">-¥36,000.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
        
        <div class="asset-item liability-item">
          <div class="asset-icon">
            <i class="fas fa-credit-card"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">信用卡欠款</div>
            <div class="asset-details">最后还款日：2023-06-25</div>
          </div>
          <div class="asset-value negative">-¥9,200.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
      </div>
      
      <!-- 债权模块 -->
      <div class="asset-category-manage">
        <div class="module-title">
          <i class="fas fa-handshake"></i>债权
        </div>
        <div class="asset-category-manage-btn" id="manage-claim">
          <i class="fas fa-cog"></i>管理
        </div>
      </div>
      <div class="card">
        <div class="asset-item">
          <div class="asset-icon" style="background-color: #E2F8E9; color: #10B981;">
            <i class="fas fa-user-friends"></i>
          </div>
          <div class="asset-info">
            <div class="asset-name">李先生借款</div>
            <div class="asset-details">预计归还：2023-08-01</div>
          </div>
          <div class="asset-value positive">¥2,000.00</div>
          <div class="edit-asset-btn">
            <i class="fas fa-pen"></i>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 添加资产按钮 -->
    <div class="add-asset-btn">
      <i class="fas fa-plus"></i>
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <div class="tab-label">首页</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-list-ul tab-icon"></i>
        <div class="tab-label">账单</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-wallet tab-icon active"></i>
        <div class="tab-label active">资产</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-chart-pie tab-icon"></i>
        <div class="tab-label">分析</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <div class="tab-label">我的</div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 返回按钮事件
      const backButton = document.getElementById('back-button');
      backButton.addEventListener('click', function() {
        window.location.href = 'profile-new.html';
      });
      
      // 同步按钮事件
      const syncIcon = document.getElementById('sync-icon');
      syncIcon.addEventListener('click', function() {
        alert('正在同步资产数据...');
        setTimeout(() => {
          alert('资产数据同步完成！');
        }, 1500);
      });
      
      // 筛选按钮事件
      const filterIcon = document.getElementById('filter-icon');
      filterIcon.addEventListener('click', function() {
        alert('打开资产筛选页面');
      });
      
      // 资产导航事件
      const navItems = document.querySelectorAll('.asset-nav-item');
      navItems.forEach(item => {
        item.addEventListener('click', function() {
          navItems.forEach(i => i.classList.remove('active'));
          this.classList.add('active');
          alert(`已切换到${this.textContent}资产类别`);
        });
      });
      
      // 添加资产按钮事件
      const addAssetBtn = document.querySelector('.add-asset-btn');
      addAssetBtn.addEventListener('click', function() {
        alert('打开添加资产页面');
      });
      
      // 隐藏金额按钮事件
      const assetsToggle = document.querySelector('.assets-toggle');
      let isHidden = false;
      assetsToggle.addEventListener('click', function() {
        isHidden = !isHidden;
        const amountElements = document.querySelectorAll('.assets-amount, .segment-value, .asset-value, .balance-amount');
        
        if (isHidden) {
          amountElements.forEach(el => {
            el.dataset.originalText = el.textContent;
            el.textContent = '******';
          });
          this.querySelector('span').textContent = '显示金额';
          this.querySelector('i').classList.remove('fa-eye-slash');
          this.querySelector('i').classList.add('fa-eye');
        } else {
          amountElements.forEach(el => {
            el.textContent = el.dataset.originalText;
          });
          this.querySelector('span').textContent = '隐藏金额';
          this.querySelector('i').classList.remove('fa-eye');
          this.querySelector('i').classList.add('fa-eye-slash');
        }
      });
      
      // 底部标签栏点击事件
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach((item, index) => {
        item.addEventListener('click', function() {
          switch(index) {
            case 0:
              window.location.href = 'home.html';
              break;
            case 1:
              window.location.href = 'transactions.html';
              break;
            case 2:
              // 已在当前页面
              break;
            case 3:
              window.location.href = 'analysis.html';
              break;
            case 4:
              window.location.href = 'profile-new.html';
              break;
          }
        });
      });
      
      // 银行卡点击事件
      const bankCards = document.querySelectorAll('.bank-card');
      bankCards.forEach((card, index) => {
        card.addEventListener('click', function() {
          alert(`打开${index === 0 ? '招商银行储蓄卡' : '建设银行信用卡'}详情页面`);
        });
      });
      
      // 资产项点击事件
      const assetItems = document.querySelectorAll('.asset-item');
      assetItems.forEach(item => {
        item.addEventListener('click', function() {
          const assetName = this.querySelector('.asset-name').textContent;
          alert(`打开${assetName}资产详情页面`);
        });
      });
      
      // 编辑按钮事件
      const editBtns = document.querySelectorAll('.edit-asset-btn');
      editBtns.forEach(btn => {
        btn.addEventListener('click', function(e) {
          e.stopPropagation();
          const assetName = this.parentElement.querySelector('.asset-name').textContent;
          alert(`编辑${assetName}资产信息`);
        });
      });
      
      // 资产类别管理按钮事件
      const manageBtns = document.querySelectorAll('.asset-category-manage-btn');
      manageBtns.forEach(btn => {
        btn.addEventListener('click', function() {
          const categoryId = this.id.split('-')[1];
          alert(`打开${categoryId}类别管理页面`);
        });
      });
    });
  </script>
</body>
</html>
