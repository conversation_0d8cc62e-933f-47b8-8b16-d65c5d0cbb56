<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>设置 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
      color: #666;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    /* 卡片样式 */
    .card {
      background-color: white;
      border-radius: 16px;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.06);
      border: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    /* 列表样式 */
    .list {
      margin: 0;
      padding: 0;
    }
    
    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 0;
      border-bottom: 1px solid rgba(0, 0, 0, 0.03);
    }
    
    .list-item:last-child {
      border-bottom: none;
    }
    
    /* 用户卡片样式 */
    .user-card {
      display: flex;
      align-items: center;
    }
    
    .user-avatar {
      width: 48px;
      height: 48px;
      background-color: #FF6B35;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 12px;
      border: 1px solid rgba(255, 107, 53, 0.1);
    }
    
    .user-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .user-info {
      margin-left: 0;
      flex: 1;
    }
    
    .user-name {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 2px;
      color: #333;
    }
    
    .user-phone {
      font-size: 13px;
      color: #666;
    }
    
    /* 分区标题 */
    .section-title {
      font-size: 14px;
      color: #666;
      margin: 8px 5px 4px;
      font-weight: 500;
    }
    
    /* 功能图标容器 */
    .icon-container {
      width: 32px;
      height: 32px;
      border-radius: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
    }
    
    /* 颜色主题 */
    .icon-blue {
      background-color: #F0F9FF;
    }
    
    .icon-blue i {
      color: #0EA5E9;
    }
    
    .icon-green {
      background-color: #ECFDF5;
    }
    
    .icon-green i {
      color: #10B981;
    }
    
    .icon-orange {
      background-color: #FEF3C7;
    }
    
    .icon-orange i {
      color: #F59E0B;
    }
    
    /* 列表项内文字样式 */
    .item-value {
      font-size: 14px;
      color: #666;
      margin-right: 10px;
    }
    
    .chevron-icon {
      color: #999;
    }
    
    /* 切换开关样式 */
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
    }
    
    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }
    
    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #ccc;
      transition: .4s;
    }
    
    .slider:before {
      position: absolute;
      content: "";
      height: 20px;
      width: 20px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .4s;
    }
    
    input:checked + .slider {
      background-color: #FF6B35;
    }
    
    input:focus + .slider {
      box-shadow: 0 0 1px #FF6B35;
    }
    
    input:checked + .slider:before {
      transform: translateX(20px);
    }
    
    .slider.round {
      border-radius: 34px;
    }
    
    .slider.round:before {
      border-radius: 50%;
    }
    
    /* 按钮样式 */
    .btn {
      display: inline-block;
      border: none;
      border-radius: 10px;
      font-size: 15px;
      font-weight: 500;
      padding: 12px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      width: 100%;
    }
    
    .btn-danger {
      background-color: #FF6B35;
      color: white;
    }
    
    .btn-danger:active {
      background-color: #e05a2b;
    }
    
    .w-full {
      width: 100%;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">设置</div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 个人信息入口 -->
      <div class="card">
        <div class="user-card">
          <div class="user-avatar">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
          </div>
          <div class="user-info">
            <div class="user-name">张先生</div>
            <div class="user-phone">138****0000</div>
          </div>
          <div>
            <i class="fas fa-chevron-right chevron-icon"></i>
          </div>
        </div>
      </div>
      
      <!-- 功能模块合并卡片 -->
      <div class="card">
        <!-- 账户与安全 -->
        <div class="section-title" style="margin-top: 0; padding-left: 0;">账户与安全</div>
        <div class="list">
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-user-check"></i>
              </div>
              <div>实名认证</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">未认证</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-link"></i>
              </div>
              <div>账号绑定</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">微信、支付宝</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-key"></i>
              </div>
              <div>更改密码</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-lock"></i>
              </div>
              <div>手势密码</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">已开启</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-fingerprint"></i>
              </div>
              <div>生物识别</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">FaceID</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-shield-alt"></i>
              </div>
              <div>隐私设置</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
        
        <!-- 数据同步 -->
        <div class="section-title" style="margin-top: 8px; padding-left: 0; border-top: 1px solid rgba(0,0,0,0.05); padding-top: 8px;">数据与同步</div>
        <div class="list">
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-green">
                <i class="fas fa-sync-alt"></i>
              </div>
              <div>自动同步</div>
            </div>
            <label class="switch">
              <input type="checkbox" checked>
              <span class="slider round"></span>
            </label>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-green">
                <i class="fas fa-database"></i>
              </div>
              <div>云端备份</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">今天 09:15</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-green">
                <i class="fas fa-file-export"></i>
              </div>
              <div>导出数据</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-green">
                <i class="fas fa-trash-alt"></i>
              </div>
              <div>清除缓存</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">23.5MB</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
        
        <!-- 应用偏好 -->
        <div class="section-title" style="margin-top: 8px; padding-left: 0; border-top: 1px solid rgba(0,0,0,0.05); padding-top: 8px;">应用偏好</div>
        <div class="list">
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-orange">
                <i class="fas fa-tags"></i>
              </div>
              <div>分类管理</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-orange">
                <i class="fas fa-bell"></i>
              </div>
              <div>通知设置</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-orange">
                <i class="fas fa-globe"></i>
              </div>
              <div>语言设置</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">简体中文</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-orange">
                <i class="fas fa-moon"></i>
              </div>
              <div>深色模式</div>
            </div>
            <label class="switch">
              <input type="checkbox">
              <span class="slider round"></span>
            </label>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-orange">
                <i class="fas fa-dollar-sign"></i>
              </div>
              <div>货币单位</div>
            </div>
            <div style="display: flex; align-items: center;">
              <div class="item-value">人民币 (¥)</div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 关于应用 -->
      <div class="section-title">关于应用</div>
      <div class="card">
        <div class="list">
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-info-circle"></i>
              </div>
              <div>关于我们</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-headset"></i>
              </div>
              <div>联系客服</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-star"></i>
              </div>
              <div>应用评分</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-file-alt"></i>
              </div>
              <div>用户协议与隐私</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-container icon-blue">
                <i class="fas fa-code"></i>
              </div>
              <div>当前版本</div>
            </div>
            <div class="item-value">
              v1.0.0
            </div>
          </div>
        </div>
      </div>
      
      <!-- 退出登录 -->
      <button class="btn btn-danger" style="margin: 15px 0 10px;">
        退出登录
      </button>
    </div>
  </div>
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 返回按钮功能
      const backButton = document.querySelector('.nav-left');
      backButton.addEventListener('click', function() {
        window.history.back();
      });
      
      // 点击个人信息卡片跳转到个人资料页面
      const userCard = document.querySelector('.user-card');
      userCard.addEventListener('click', function() {
        window.location.href = 'profile.html';
      });
      
      // 退出登录按钮功能
      const logoutButton = document.querySelector('.btn-danger');
      logoutButton.addEventListener('click', function() {
        if(confirm('确认退出登录?')) {
          alert('已退出登录');
          window.location.href = 'login.html';
        }
      });
    });
  </script>
</body>
</html>