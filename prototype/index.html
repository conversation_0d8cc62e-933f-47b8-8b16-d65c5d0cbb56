<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AI记账App原型</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    :root {
      --device-width: 430px; /* iPhone 15 Pro Max宽度 */
      --device-height: 932px; /* iPhone 15 Pro Max高度 */
      --device-scale: 0.38; /* 在index页面的缩放比例 */
    }
    
    body {
      background-color: #f0f2f5;
      padding: 0;
      margin: 0;
      width: 100vw;
      min-height: 100vh;
      overflow-x: hidden;
      overflow-y: auto !important; /* 确保主页面可以滚动 */
    }
    
    /* 顶部标题栏 */
    .app-header {
      background-color: #4285f4;
      color: white;
      padding: 12px 20px;
      text-align: left;
      font-weight: bold;
      font-size: 20px;
      height: 60px;
      display: flex;
      align-items: center;
    }
    
    .description {
      max-width: 1600px;
      margin: 0 auto 20px;
      font-size: 14px;
      line-height: 1.6;
      color: #666;
      text-align: center;
      padding: 10px 20px;
    }
    
    /* 调整网格布局 */
    .prototypes-grid {
      display: grid;
      grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
      gap: 30px;
      margin: 0 auto;
      max-width: 1600px;
      padding: 0 20px 40px 20px;
      justify-content: center;
    }
    
    /* 调整每个原型容器 */
    .prototype-container {
      display: flex;
      flex-direction: column;
      width: 310px;
      height: 420px;
      background-color: white;
      overflow: hidden;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      border-radius: 12px;
      margin: 0 auto;
      transition: transform 0.2s, box-shadow 0.2s;
    }
    
    .prototype-container:hover {
      transform: translateY(-5px);
      box-shadow: 0 8px 16px rgba(0,0,0,0.15);
    }
    
    /* 原型标题样式 */
    .prototype-title {
      background-color: #4285f4;
      color: white;
      padding: 10px 15px;
      font-weight: 500;
      font-size: 14px;
      display: flex;
      align-items: center;
      border-top-left-radius: 8px;
      border-top-right-radius: 8px;
      height: 40px;
    }
    
    /* 调整iframe适配iPhone 15 Pro Max尺寸 */
    iframe {
      border: none;
      width: 430px;
      height: 932px;
      overflow: hidden;
      background-color: white;
      transform: scale(var(--device-scale));
      transform-origin: 0 0;
      position: absolute;
      top: 0;
      left: 0;
    }

    /* 响应式设计，根据屏幕大小调整 */
    @media (max-width: 1400px) {
      .prototypes-grid {
        grid-template-columns: repeat(2, 1fr);
      }
    }
    
    @media (max-width: 768px) {
      .prototypes-grid {
        grid-template-columns: 1fr;
      }
      .prototype-container {
        width: 100%;
        max-width: 260px;
      }
    }
    
    /* 模块分组样式 */
    .module-group {
      margin: 30px auto;
      max-width: 1600px;
      padding: 0 20px;
    }
    
    .module-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 15px;
      color: #333;
      border-left: 4px solid #4285f4;
      padding-left: 15px;
    }

    /* 调整iframe容器溢出处理 */
    .iframe-wrapper {
      width: 100%;
      height: 380px;
      overflow: hidden;
      position: relative;
      background-color: white;
      border-bottom-left-radius: 12px;
      border-bottom-right-radius: 12px;
    }
  </style>
</head>
<body class="index-page">
  <div class="app-header">
    AI记账应用原型设计 (iPhone 15 Pro Max)
  </div>
  
  <div class="description">
    基于语音交互的记账应用原型，支持iOS、Android和微信小程序多端适配。包含语音记账、预算分析、多设备同步等功能。
    原型尺寸：430×932像素 (iPhone 15 Pro Max)
  </div>

  <!-- 登录引导模块 -->
  <div class="module-group">
    <div class="module-title">欢迎与登录页面</div>
    <div class="prototypes-grid">
      <div class="prototype-container">
        <div class="prototype-title">欢迎页</div>
        <div class="iframe-wrapper">
          <iframe src="welcome.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">登录页面</div>
        <div class="iframe-wrapper">
          <iframe src="login.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">设置手势密码</div>
        <div class="iframe-wrapper">
          <iframe src="pattern.html" scrolling="no"></iframe>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 核心功能模块 -->
  <div class="module-group">
    <div class="module-title">核心功能页面</div>
    <div class="prototypes-grid">
      <div class="prototype-container">
        <div class="prototype-title">首页</div>
        <div class="iframe-wrapper">
          <iframe src="home.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">语音记账</div>
        <div class="iframe-wrapper">
          <iframe src="chat-accounting.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">记账确认</div>
        <div class="iframe-wrapper">
          <iframe src="transaction-confirm.html" scrolling="no"></iframe>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 账单与分析模块 -->
  <div class="module-group">
    <div class="module-title">账单与数据分析</div>
    <div class="prototypes-grid">
      <div class="prototype-container">
        <div class="prototype-title">账单列表</div>
        <div class="iframe-wrapper">
          <iframe src="transactions.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">账单提醒</div>
        <div class="iframe-wrapper">
          <iframe src="bill-reminders.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">数据分析</div>
        <div class="iframe-wrapper">
          <iframe src="analysis.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">预算设置</div>
        <div class="iframe-wrapper">
          <iframe src="budget.html" scrolling="no"></iframe>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 个人与设置模块 -->
  <div class="module-group">
    <div class="module-title">个人与设置</div>
    <div class="prototypes-grid">
      <div class="prototype-container">
        <div class="prototype-title">我的页面</div>
        <div class="iframe-wrapper">
          <iframe src="profile-new.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">设置页面</div>
        <div class="iframe-wrapper">
          <iframe src="settings.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">个人信息</div>
        <div class="iframe-wrapper">
          <iframe src="profile.html" scrolling="no"></iframe>
        </div>
      </div>
      
      <div class="prototype-container">
        <div class="prototype-title">分类管理</div>
        <div class="iframe-wrapper">
          <iframe src="categories.html" scrolling="no"></iframe>
        </div>
      </div>
    </div>
  </div>
  
  <!-- 资产管理模块 -->
  <div class="module-group">
    <div class="module-title">资产管理</div>
    <div class="prototypes-grid">
      <div class="prototype-container">
        <div class="prototype-title">资产总览</div>
        <div class="iframe-wrapper">
          <iframe src="assets.html" scrolling="no"></iframe>
        </div>
      </div>
    </div>
  </div>

  <script src="js/main.js"></script>
</body>
</html>