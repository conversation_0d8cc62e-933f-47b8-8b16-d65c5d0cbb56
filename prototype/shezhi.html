<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>设置 - 账无忌 (Redesigned)</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* Reset and Base Styles */
    *, *::before, *::after {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }

    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      color: #333;
      background-color: #f0f2f5; /* Slightly softer background */
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden; /* Prevent body scroll */
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    /* Phone Simulation Container */
    .phone-container {
      width: 375px; /* Common iPhone width */
      height: 812px; /* Common iPhone X/11/12 height */
      background-color: #ffffff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
      /* border: 1px solid #eee; /* Lighter border instead of thick black */
    }

    /* Status Bar */
    .status-bar {
      height: 44px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      font-size: 14px;
      font-weight: 500;
      color: #333;
      flex-shrink: 0;
      background-color: #fff; /* Match container background */
    }

    .status-bar-right i {
      margin-left: 6px;
      font-size: 13px;
      color: #555;
    }

    /* Navigation Bar */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid #f0f0f0; /* Softer border */
      position: relative;
      z-index: 10;
      width: 100%;
      flex-shrink: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      height: 50px; /* Slightly taller */
    }

    .nav-left, .nav-right {
      width: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #555;
      font-size: 18px;
      cursor: pointer;
    }

    .nav-right {
      justify-content: flex-end;
    }

    .nav-title {
      font-weight: 600;
      font-size: 17px;
      color: #111;
      position: relative;
    }
    /* Removing the underline for a cleaner look, title is enough */


    /* Content Area */
    .content {
      flex: 1;
      padding: 15px; /* Consistent padding */
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f9fa; /* Slightly different content background */
    }

    /* Card Styling */
    .card {
      background-color: #ffffff;
      border-radius: 12px; /* Softer corners */
      margin-bottom: 15px; /* Spacing between cards */
      box-shadow: 0 3px 10px rgba(0, 0, 0, 0.04); /* Subtle shadow */
      overflow: hidden; /* Ensure content respects border-radius */
    }

    /* User Card Specific Styles */
    .user-card-link {
      display: flex;
      align-items: center;
      padding: 20px 15px; /* More padding for user card */
      text-decoration: none;
      color: inherit;
      transition: background-color 0.2s ease;
    }
    .user-card-link:active {
        background-color: rgba(0,0,0,0.03);
    }


    .user-avatar {
      width: 55px; /* Slightly larger avatar */
      height: 55px;
      background-color: #FF6B35;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 15px;
      flex-shrink: 0;
      border: 2px solid #fff; /* Small white border */
      box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .user-avatar img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .user-info {
      flex-grow: 1;
      margin-right: 10px;
    }

    .user-name {
      font-size: 18px; /* Larger name */
      font-weight: 600;
      margin-bottom: 4px;
      color: #222;
    }

    .user-phone {
      font-size: 14px;
      color: #666;
    }

    /* Section Title */
    .section-title {
      font-size: 13px;
      color: #888;
      padding: 15px 15px 5px 15px; /* Padding around title */
      font-weight: 500;
      text-transform: uppercase; /* Optional: for distinction */
      letter-spacing: 0.5px; /* Optional */
      background-color: #f8f9fa; /* Title background slightly different */
      border-bottom: 1px solid #f0f0f0; /* Separator line */
    }
     /* No top margin needed as it's inside the card flow */

    /* List Item Styling */
    .list {
      margin: 0;
      padding: 0 15px; /* Padding inside the card for list items */
      list-style: none;
    }

    .list-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px 0; /* Vertical padding for items */
      text-decoration: none;
      color: inherit;
      transition: background-color 0.2s ease;
    }

    /* Add border only between items */
    .list-item:not(:last-child) {
      border-bottom: 1px solid #f0f0f0; /* Softer separator */
    }

    .list-item-content {
      display: flex;
      align-items: center;
      flex-grow: 1;
      margin-right: 10px; /* Space before value/chevron */
    }

    .list-item-icon {
      width: 28px; /* Consistent icon area width */
      text-align: center;
      margin-right: 15px; /* Space after icon */
      font-size: 18px; /* Icon size */
    }

    /* Icon Colors (Applied directly) */
    .icon-account { color: #3b82f6; } /* Blue */
    .icon-data { color: #10b981; } /* Green */
    .icon-prefs { color: #f59e0b; } /* Orange */
    .icon-about { color: #6366f1; } /* Indigo/Blue */

    .list-item-label {
      font-size: 15px;
      color: #333;
    }

    .list-item-value {
      font-size: 14px;
      color: #777; /* Lighter value text */
      margin-right: 8px;
      white-space: nowrap; /* Prevent wrapping */
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 150px; /* Limit width if needed */
    }

    .chevron-icon {
      color: #c0c0c0; /* Lighter chevron */
      font-size: 13px;
    }

    .list-item-action {
        display: flex;
        align-items: center;
    }

    /* Switch Styles */
    .switch {
      position: relative;
      display: inline-block;
      width: 44px;
      height: 24px;
      flex-shrink: 0; /* Prevent shrinking */
    }

    .switch input {
      opacity: 0;
      width: 0;
      height: 0;
    }

    .slider {
      position: absolute;
      cursor: pointer;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: #e0e0e0; /* Slightly darker off state */
      transition: .3s;
      border-radius: 34px;
    }

    .slider:before {
      position: absolute;
      content: "";
      height: 20px;
      width: 20px;
      left: 2px;
      bottom: 2px;
      background-color: white;
      transition: .3s;
      border-radius: 50%;
      box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    }

    input:checked + .slider {
      background-color: #FF6B35; /* Theme color for on state */
    }

    input:checked + .slider:before {
      transform: translateX(20px);
    }

    /* Button Styles */
    .btn {
      display: block; /* Make it block to take full width easily */
      width: 100%;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 500;
      padding: 14px 20px; /* Slightly larger padding */
      cursor: pointer;
      transition: background-color 0.3s ease, box-shadow 0.3s ease;
      text-align: center;
      margin-top: 20px; /* Space above button */
      margin-bottom: 10px; /* Space below button */
    }

    .btn-danger {
      background-color: #FF6B35;
      color: white;
      box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3); /* Subtle shadow matching theme */
    }

    .btn-danger:active {
      background-color: #e05a2b; /* Darker shade on press */
      box-shadow: 0 2px 5px rgba(255, 107, 53, 0.4);
    }
    
    /* Ensure links cover list items for tap targets */
    .list-item > a {
        display: contents; /* Make the link behave like its children in flexbox */
        text-decoration: none;
        color: inherit;
    }

  </style>
</head>
<body>
  <div class="phone-container">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal"></i>
        <i class="fas fa-wifi"></i>
        <i class="fas fa-battery-full"></i>
      </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">设置</div>
      <div class="nav-right"></div>
    </div>

    <!-- Content Area -->
    <div class="content">
      <!-- User Info Card -->
      <div class="card">
         <a href="profile.html" class="user-card-link">
            <div class="user-avatar">
              <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像">
            </div>
            <div class="user-info">
              <div class="user-name">张先生</div>
              <div class="user-phone">138****0000</div>
            </div>
            <div>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
         </a>
      </div>

      <!-- Account & Security Card -->
      <div class="card">
        <div class="section-title">账户与安全</div>
        <ul class="list">
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-user-check list-item-icon icon-account"></i>
              <span class="list-item-label">实名认证</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">未认证</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-link list-item-icon icon-account"></i>
              <span class="list-item-label">账号绑定</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">微信、支付宝</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-key list-item-icon icon-account"></i>
              <span class="list-item-label">更改密码</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-lock list-item-icon icon-account"></i>
              <span class="list-item-label">手势密码</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">已开启</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-fingerprint list-item-icon icon-account"></i>
              <span class="list-item-label">生物识别</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">FaceID</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-shield-alt list-item-icon icon-account"></i>
              <span class="list-item-label">隐私设置</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
        </ul>
      </div>

      <!-- Data & Sync Card -->
      <div class="card">
        <div class="section-title">数据与同步</div>
        <ul class="list">
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-sync-alt list-item-icon icon-data"></i>
              <span class="list-item-label">自动同步</span>
            </div>
            <div class="list-item-action">
              <label class="switch">
                <input type="checkbox" checked>
                <span class="slider"></span>
              </label>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-database list-item-icon icon-data"></i>
              <span class="list-item-label">云端备份</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">今天 09:15</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-file-export list-item-icon icon-data"></i>
              <span class="list-item-label">导出数据</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-trash-alt list-item-icon icon-data"></i>
              <span class="list-item-label">清除缓存</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">23.5MB</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
        </ul>
      </div>

      <!-- App Preferences Card -->
      <div class="card">
        <div class="section-title">应用偏好</div>
        <ul class="list">
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-tags list-item-icon icon-prefs"></i>
              <span class="list-item-label">分类管理</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-bell list-item-icon icon-prefs"></i>
              <span class="list-item-label">通知设置</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-globe list-item-icon icon-prefs"></i>
              <span class="list-item-label">语言设置</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">简体中文</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-moon list-item-icon icon-prefs"></i>
              <span class="list-item-label">深色模式</span>
            </div>
            <div class="list-item-action">
              <label class="switch">
                <input type="checkbox">
                <span class="slider"></span>
              </label>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-dollar-sign list-item-icon icon-prefs"></i>
              <span class="list-item-label">货币单位</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">人民币 (¥)</span>
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
        </ul>
      </div>

      <!-- About App Card -->
      <div class="card">
        <div class="section-title">关于应用</div>
        <ul class="list">
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-info-circle list-item-icon icon-about"></i>
              <span class="list-item-label">关于我们</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-headset list-item-icon icon-about"></i>
              <span class="list-item-label">联系客服</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-star list-item-icon icon-about"></i>
              <span class="list-item-label">应用评分</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-file-alt list-item-icon icon-about"></i>
              <span class="list-item-label">用户协议与隐私</span>
            </div>
            <div class="list-item-action">
              <i class="fas fa-chevron-right chevron-icon"></i>
            </div>
          </li>
          <li class="list-item">
            <div class="list-item-content">
              <i class="fas fa-code list-item-icon icon-about"></i>
              <span class="list-item-label">当前版本</span>
            </div>
            <div class="list-item-action">
              <span class="list-item-value">v1.0.0</span>
              <!-- No chevron for non-interactive item -->
            </div>
          </li>
        </ul>
      </div>

      <!-- Logout Button -->
      <button class="btn btn-danger">
        退出登录
      </button>

    </div> <!-- End Content -->
  </div> <!-- End Phone Container -->

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Back Button Functionality
      const backButton = document.querySelector('.nav-left');
      if (backButton) {
          backButton.addEventListener('click', function() {
            // In a real app, this might be different, e.g., navigation controller pop
            window.history.back();
          });
      }

      // Make list items with chevrons feel clickable (optional visual feedback)
       const clickableItems = document.querySelectorAll('.list-item');
       clickableItems.forEach(item => {
           // Check if it has a chevron or is the user card link
           const hasChevron = item.querySelector('.fa-chevron-right');
           const isUserCard = item.closest('.user-card-link'); // Check parent anchor
           if (hasChevron || isUserCard) {
               item.style.cursor = 'pointer';
               item.addEventListener('click', function(e) {
                   // Prevent default if it's just visual feedback on li
                   // If it's the user card link, let the anchor handle it
                   if (!isUserCard && !e.target.closest('label.switch') && !e.target.closest('input[type=checkbox]')) {
                      console.log('Navigating to:', item.querySelector('.list-item-label')?.textContent || 'Profile');
                      // Add actual navigation logic here if needed for non-anchor items
                      // e.g., window.location.href = 'some_page.html';
                   }
               });
           }
       });


      // Logout Button Functionality
      const logoutButton = document.querySelector('.btn-danger');
      if (logoutButton) {
          logoutButton.addEventListener('click', function() {
            // Use a more modern confirmation dialog if possible in a real app
            if(confirm('确认退出登录?')) {
              alert('已退出登录');
              // Redirect to login page
              // window.location.href = 'login.html'; // Uncomment for actual redirection
               console.log('Logout confirmed, redirecting...');
            }
          });
      }

       // Handle clicks on list items that lead somewhere (add specific URLs)
       // Example for "更改密码"
        const changePasswordItem = Array.from(document.querySelectorAll('.list-item-label'))
                                       .find(el => el.textContent === '更改密码');
        if (changePasswordItem) {
            changePasswordItem.closest('.list-item').addEventListener('click', () => {
                // window.location.href = 'change-password.html';
                console.log("Navigate to Change Password");
            });
        }
        // Add similar handlers for other navigation items as needed


    });
  </script>
</body>
</html>