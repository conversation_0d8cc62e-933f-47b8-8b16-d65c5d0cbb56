<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>账单 - 账无忌 (Improved Filters)</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    /* --- Base & Layout --- */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
      color: #333;
      background-color: #f0f2f5; /* Soft background */
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
    }

    .phone-container {
      width: 375px;
      height: 812px;
      background-color: #ffffff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 15px 45px rgba(0, 0, 0, 0.15);
      display: flex;
      flex-direction: column;
    }

    .content {
      flex: 1;
      padding: 15px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f9fa; /* Content background */
      width: 100%;
      box-sizing: border-box;
      padding-bottom: 80px; /* Space for FABs/Tabs */
    }

    /* --- Status Bar & Nav Bar --- */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 20px;
      box-sizing: border-box;
      font-size: 14px;
      font-weight: 500;
      color: #1c1c1e;
      flex-shrink: 0;
    }
    .status-bar-right i { margin-left: 6px; font-size: 13px; color: #555; }

    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid #f0f0f0; /* Soft border */
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      height: 50px;
    }
    .nav-left, .nav-right { width: 30px; display: flex; align-items: center; justify-content: center; }
    .nav-right { justify-content: flex-end; }
    .nav-title { font-weight: 600; font-size: 17px; color: #111; position: relative; }
    #reminder-icon { color: #555; font-size: 19px; cursor: pointer; transition: color 0.2s ease; }
    #reminder-icon:hover { color: #FF6B35; }

    /* --- IMPROVED Filter Section --- */
    .filter-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 25px; /* Increased space below */
      margin-top: 15px; /* Space from nav */
      padding: 0 5px; /* Align with card padding visually */
    }

    /* Date Navigator - Styled as a distinct element */
    .month-navigator {
      display: flex;
      align-items: center;
      background-color: #fff; /* White background */
      padding: 8px 10px; /* Adjusted padding */
      border-radius: 20px; /* Fully rounded */
      box-shadow: 0 2px 8px rgba(0,0,0,0.07); /* Slightly stronger shadow */
      border: 1px solid #f0f0f0; /* Lighter border */
    }
    .month-nav-arrow {
      font-size: 15px; /* Slightly smaller arrow */
      color: #FF6B35;
      padding: 5px; /* Smaller padding */
      cursor: pointer;
      border-radius: 50%;
      transition: background-color 0.2s ease;
      line-height: 1; /* Ensure icon vertical alignment */
      display: inline-flex; /* Better alignment */
      align-items: center;
      justify-content: center;
    }
    .month-nav-arrow:hover { background-color: #f0f0f0; }
    .month-nav-arrow:active { background-color: #e0e0e0; }
    .month-nav-date {
      font-size: 15px;
      font-weight: 600;
      margin: 0 8px; /* Increased space around date */
      color: #333;
      cursor: pointer;
      padding: 2px 4px; /* Minimal padding, relies on margin */
      border-radius: 8px;
      transition: background-color 0.2s ease;
      white-space: nowrap; /* Prevent wrapping */
    }
    .month-nav-date:hover { background-color: #f5f5f5; }

    /* View Toggle Buttons - Styled as a segmented control */
    .view-toggle-buttons {
      display: flex;
      background-color: #fff; /* White background */
      border-radius: 20px; /* Match date navigator */
      box-shadow: 0 2px 8px rgba(0,0,0,0.07); /* Match date navigator */
      padding: 4px; /* Inner padding */
      border: 1px solid #f0f0f0; /* Match date navigator */
    }
    .toggle-btn {
      font-size: 13px;
      font-weight: 500;
      padding: 6px 14px; /* Adjusted padding */
      border-radius: 16px; /* Rounded inner buttons */
      border: none;
      background-color: transparent; /* Transparent by default */
      color: #555;
      cursor: pointer;
      transition: background-color 0.3s ease, color 0.3s ease, box-shadow 0.3s ease;
      white-space: nowrap; /* Prevent wrapping */
    }
    .toggle-btn.active {
      background-color: #FF6B35;
      color: white;
      box-shadow: 0 1px 4px rgba(255, 107, 53, 0.3); /* Softer active shadow */
    }
    .toggle-btn:not(:active):hover {
       color: #111; /* Darker text on hover */
       /* Optional subtle background hover: background-color: #f8f9fa; */
    }
    /* Remove gap, buttons touch like segments */
    /* Add spacing via padding within .view-toggle-buttons instead */


    /* --- Card Styles --- */
    .card {
      background-color: white; border-radius: 18px; padding: 20px; margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05); border: 1px solid #f5f5f5;
    }

    /* --- Monthly Summary Card --- */
    .summary-header { display: flex; justify-content: space-between; margin-bottom: 18px; align-items: center; }
    .summary-title { font-size: 16px; color: #555; font-weight: 600; }
    .summary-link { font-size: 14px; color: #FF6B35; text-decoration: none; font-weight: 500; }
    .summary-stats { display: flex; margin-bottom: 22px; }
    .stat-item { flex: 1; text-align: center; padding: 0 10px; position: relative; min-width: 0; }
    .stat-item:not(:last-child)::after { content: ''; position: absolute; right: 0; top: 15%; height: 70%; width: 1px; background-color: #f0f0f0; }
    .stat-label { font-size: 13px; color: #888; margin-bottom: 6px; display: flex; align-items: center; justify-content: center; gap: 4px; }
    .stat-label i { font-size: 11px; color: #aaa; }
    .stat-value { font-size: 19px; font-weight: 700; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
    .stat-value.income { color: #10B981; } .stat-value.expense { color: #FF6B35; } .stat-value.balance { color: #333; }

    /* --- Chart Section --- */
    .chart-title { font-size: 16px; font-weight: 600; margin-bottom: 12px; color: #555; }
    .progress-bar { display: flex; gap: 2px; margin-bottom: 15px; height: 10px; border-radius: 5px; overflow: hidden; }
    .progress-segment { height: 100%; }
    .chart-legend { display: flex; flex-wrap: wrap; gap: 10px 15px; font-size: 12px; color: #666; }
    .legend-item { display: flex; align-items: center; }
    .legend-dot { width: 10px; height: 10px; border-radius: 50%; margin-right: 6px; }

    /* --- Transaction List --- */
    .date-heading { margin: 20px 0 12px 5px; font-size: 14px; font-weight: 600; color: #666; }
    .list { margin: 0; padding: 0; }
    .transactions-card { padding: 0 20px; }
    .list-item { display: flex; justify-content: space-between; align-items: center; padding: 16px 0; border-bottom: 1px solid #f5f5f5; transition: background-color 0.2s ease; }
    .list-item:last-child { border-bottom: none; }
    .list-item:hover { background-color: #fafafa; }
    .item-left { display: flex; align-items: center; min-width: 0; flex-grow: 1; margin-right: 10px; }
    .category-icon-container { width: 48px; height: 48px; border-radius: 16px; display: flex; justify-content: center; align-items: center; margin-right: 16px; flex-shrink: 0; }
    .category-icon-container i { font-size: 20px; }
    .list-item-details { min-width: 0; flex-grow: 1; }
    .list-item-title { font-weight: 500; font-size: 16px; margin-bottom: 5px; color: #333; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
    .list-item-subtitle { font-size: 13px; color: #999; }
    .list-item-right { text-align: right; flex-shrink: 0; }
    .list-item-amount { font-size: 16px; font-weight: 600; margin-bottom: 2px; white-space: nowrap; }
    .amount-expense { color: #FF6B35; } .amount-income { color: #10B981; }

    /* --- Floating Buttons & Tooltip --- */
    @keyframes float { 0% { transform: translateY(0px); } 50% { transform: translateY(-8px); } 100% { transform: translateY(0px); } }
    .fab { position: absolute; left: 25px; bottom: 90px; width: 56px; height: 56px; border-radius: 28px; background: linear-gradient(135deg, #FF6B35, #FF8C61); color: white; display: flex; justify-content: center; align-items: center; font-size: 24px; box-shadow: 0 6px 16px rgba(255, 107, 53, 0.35); z-index: 100; cursor: pointer; animation: float 3.5s ease-in-out infinite; animation-delay: 1.7s; transition: transform 0.1s ease-out; }
    .fab:active { transform: scale(0.95); animation-play-state: paused; }
    .ai-fab { position: absolute; bottom: 90px; right: 25px; width: 60px; height: 60px; border-radius: 50%; background-color: white; box-shadow: 0 5px 14px rgba(0, 0, 0, 0.12); display: flex; justify-content: center; align-items: center; z-index: 100; overflow: visible; animation: float 3.5s ease-in-out infinite; padding: 0; cursor: pointer; }
    .ai-fab img { width: 140%; height: 140%; object-fit: contain; margin: 0; }
    .ai-fab-tooltip { position: absolute; top: -48px; left: 50%; transform: translateX(-50%); background-color: #4A4A4A; color: white; font-size: 13px; padding: 7px 14px; border-radius: 18px; white-space: nowrap; box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15); opacity: 0; transition: opacity 0.3s ease; pointer-events: none; }
    .ai-fab:hover .ai-fab-tooltip { opacity: 1; pointer-events: auto; }
    .ai-fab-tooltip:after { content: ''; position: absolute; bottom: -6px; left: 50%; transform: translateX(-50%); width: 0; height: 0; border-left: 7px solid transparent; border-right: 7px solid transparent; border-top: 7px solid #4A4A4A; }

    /* --- Tab Bar --- */
    .tab-bar { height: 64px; background-color: white; display: flex; justify-content: space-around; align-items: center; border-top: 1px solid #f0f0f0; flex-shrink: 0; box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.04); position: absolute; bottom: 0; left: 0; right: 0; border-bottom-left-radius: 28px; border-bottom-right-radius: 28px; }
    .tab-item { display: flex; flex-direction: column; align-items: center; justify-content: center; flex: 1; height: 100%; cursor: pointer; position: relative; }
    .tab-icon { font-size: 20px; color: #aaa; margin-bottom: 4px; transition: color 0.2s ease; }
    .tab-label { font-size: 11px; color: #aaa; transition: color 0.2s ease, font-weight 0.2s ease; }
    .tab-item:hover .tab-icon, .tab-item:hover .tab-label { color: #666; }
    .tab-icon.active { color: #FF6B35; } .tab-label.active { color: #FF6B35; font-weight: 600; }

  </style>
</head>
<body>
  <div class="phone-container">
    <!-- Status Bar -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-full"></i>
      </div>
    </div>

    <!-- Navigation Bar -->
    <div class="nav-bar">
      <div class="nav-left"></div>
      <div class="nav-title">账单</div>
      <div class="nav-right">
        <i class="fas fa-bell" id="reminder-icon" title="账单提醒"></i>
      </div>
    </div>

    <!-- Content Area -->
    <div class="content">
      <!-- IMPROVED Date and Filter Controls -->
      <div class="filter-section">
        <div class="month-navigator">
          <i class="fas fa-chevron-left month-nav-arrow" id="prev-month"></i>
          <span class="month-nav-date" id="current-month-year">2025年 4月</span>
          <i class="fas fa-chevron-right month-nav-arrow" id="next-month"></i>
        </div>

        <div class="view-toggle-buttons" id="view-toggle">
          <button class="toggle-btn active" data-view="expense">支出</button>
          <button class="toggle-btn" data-view="income">收入</button>
          <button class="toggle-btn" data-view="balance">结余</button>
        </div>
      </div>

      <!-- Monthly Summary -->
      <div class="card">
        <div class="summary-header">
          <div class="summary-title">本月汇总 (<span id="summary-month">4月</span>)</div>
          <a href="#" class="summary-link">查看报告 <i class="fas fa-chevron-right" style="font-size: 0.8em;"></i></a>
        </div>
        <div class="summary-stats">
           <div class="stat-item"><div class="stat-label"><i class="fas fa-arrow-down"></i>收入</div><div class="stat-value income" id="summary-income">￥9,800.00</div></div>
           <div class="stat-item"><div class="stat-label"><i class="fas fa-arrow-up"></i>支出</div><div class="stat-value expense" id="summary-expense">￥3,520.50</div></div>
           <div class="stat-item"><div class="stat-label"><i class="fas fa-equals"></i>结余</div><div class="stat-value balance" id="summary-balance">￥6,279.50</div></div>
        </div>
        <!-- Chart Section -->
        <div id="chart-section">
          <div class="chart-title">主要支出分类</div>
          <div class="progress-bar">
            <div class="progress-segment" style="flex: 0.35; background-color: #FF6B35;"></div><div class="progress-segment" style="flex: 0.25; background-color: #0EA5E9;"></div><div class="progress-segment" style="flex: 0.20; background-color: #10B981;"></div><div class="progress-segment" style="flex: 0.15; background-color: #8B5CF6;"></div><div class="progress-segment" style="flex: 0.05; background-color: #EC4899;"></div>
          </div>
          <div class="chart-legend">
             <div class="legend-item"><div class="legend-dot" style="background-color: #FF6B35;"></div><span>餐饮 35%</span></div><div class="legend-item"><div class="legend-dot" style="background-color: #0EA5E9;"></div><span>购物 25%</span></div><div class="legend-item"><div class="legend-dot" style="background-color: #10B981;"></div><span>交通 20%</span></div><div class="legend-item"><div class="legend-dot" style="background-color: #8B5CF6;"></div><span>娱乐 15%</span></div><div class="legend-item"><div class="legend-dot" style="background-color: #EC4899;"></div><span>其他 5%</span></div>
          </div>
        </div>
      </div>

      <!-- Transactions List -->
      <div id="transactions-list">
        <div class="date-heading">今天 (4月14日)</div>
        <div class="card transactions-card">
          <div class="list">
            <div class="list-item"><div class="item-left"><div class="category-icon-container" style="background-color: #FFF5F2;"><i class="fas fa-utensils" style="color: #FF6B35;"></i></div><div class="list-item-details"><div class="list-item-title">午餐 - 快餐店</div><div class="list-item-subtitle">12:30 · 支付宝</div></div></div><div class="list-item-right"><div class="list-item-amount amount-expense">-￥35.00</div></div></div>
            <div class="list-item"><div class="item-left"><div class="category-icon-container" style="background-color: #E0F2FE;"><i class="fas fa-shopping-bag" style="color: #0EA5E9;"></i></div><div class="list-item-details"><div class="list-item-title">超市购物</div><div class="list-item-subtitle">10:15 · 微信支付</div></div></div><div class="list-item-right"><div class="list-item-amount amount-expense">-￥129.80</div></div></div>
          </div>
        </div>

        <div class="date-heading">昨天 (4月13日)</div>
        <div class="card transactions-card">
          <div class="list">
            <div class="list-item"><div class="item-left"><div class="category-icon-container" style="background-color: #ECFDF5;"><i class="fas fa-money-bill-wave" style="color: #10B981;"></i></div><div class="list-item-details"><div class="list-item-title">工资收入</div><div class="list-item-subtitle">公司发放 · 招商银行</div></div></div><div class="list-item-right"><div class="list-item-amount amount-income">+￥8,500.00</div></div></div>
            <div class="list-item"><div class="item-left"><div class="category-icon-container" style="background-color: #FEFCE8;"><i class="fas fa-bus-alt" style="color: #F59E0B;"></i></div><div class="list-item-details"><div class="list-item-title">交通费</div><div class="list-item-subtitle">地铁 · 交通卡</div></div></div><div class="list-item-right"><div class="list-item-amount amount-expense">-￥15.00</div></div></div>
            <div class="list-item"><div class="item-left"><div class="category-icon-container" style="background-color: #FFF5F2;"><i class="fas fa-utensils" style="color: #FF6B35;"></i></div><div class="list-item-details"><div class="list-item-title">晚餐 - 外卖</div><div class="list-item-subtitle">美团 · 微信支付</div></div></div><div class="list-item-right"><div class="list-item-amount amount-expense">-￥45.00</div></div></div>
          </div>
        </div>
         <div style="height: 20px;"></div>
      </div>

    </div> <!-- End Content -->

    <!-- Add Button -->
    <div class="fab" title="记一笔"><i class="fas fa-plus"></i></div>
    <!-- AI Button -->
    <div class="ai-fab" title="AI记账助理"><div class="ai-fab-tooltip">智能记账助理</div><img src="2LOGO.png" alt="账无忌助手"></div>

    <!-- Tab Bar -->
    <div class="tab-bar">
      <div class="tab-item" data-page="home.html"><i class="fas fa-home tab-icon"></i><div class="tab-label">首页</div></div>
      <div class="tab-item active" data-page="zhangdan.html"><i class="fas fa-list-ul tab-icon active"></i><div class="tab-label active">账单</div></div>
      <div class="tab-item" data-page="assets.html"><i class="fas fa-wallet tab-icon"></i><div class="tab-label">资产</div></div>
      <div class="tab-item" data-page="analysis.html"><i class="fas fa-chart-pie tab-icon"></i><div class="tab-label">分析</div></div>
      <div class="tab-item" data-page="profile.html"><i class="fas fa-user-circle tab-icon"></i><div class="tab-label">我的</div></div>
    </div>
  </div>

  <script>
    // --- JAVASCRIPT (Functionality remains the same as previous version) ---
    document.addEventListener('DOMContentLoaded', function() {
      // State
      let currentYear = 2025;
      let currentMonth = 4; // 1-12
      let currentView = 'expense'; // 'expense', 'income', 'balance'

      // DOM Elements
      const prevMonthBtn = document.getElementById('prev-month');
      const nextMonthBtn = document.getElementById('next-month');
      const monthYearDisplay = document.getElementById('current-month-year');
      const summaryMonthDisplay = document.getElementById('summary-month');
      const viewToggleContainer = document.getElementById('view-toggle');
      const toggleButtons = viewToggleContainer.querySelectorAll('.toggle-btn');
      const aiButton = document.querySelector('.ai-fab');
      const reminderIcon = document.getElementById('reminder-icon');
      const tabItems = document.querySelectorAll('.tab-item');
      const transactionsListContainer = document.getElementById('transactions-list');
      const chartContainer = document.getElementById('chart-section');

      // Functions
      function updateDisplay() {
        monthYearDisplay.textContent = `${currentYear}年 ${currentMonth}月`;
        summaryMonthDisplay.textContent = `${currentMonth}月`;
        console.log(`Updating view for: ${currentYear}-${currentMonth}, View: ${currentView}`);
        mockUpdateContentBasedOnView(); // Call mock update
      }

      function changeMonth(delta) {
        currentMonth += delta;
        if (currentMonth > 12) { currentMonth = 1; currentYear++; }
        else if (currentMonth < 1) { currentMonth = 12; currentYear--; }
        updateDisplay();
      }

      function selectYear() {
         const newYear = prompt(`输入年份 (当前: ${currentYear}):`, currentYear);
         const year = parseInt(newYear);
         if (!isNaN(year) && year > 1900 && year < 2100) { currentYear = year; updateDisplay(); }
         else if (newYear !== null) { alert('请输入有效的年份'); }
      }

      function setActiveView(view) {
            currentView = view;
            toggleButtons.forEach(btn => { btn.classList.toggle('active', btn.dataset.view === view); });
            updateDisplay();
       }

      function mockUpdateContentBasedOnView() {
           console.log("Mock updating content for view:", currentView);
           const listItems = transactionsListContainer.querySelectorAll('.list-item');
           const chartTitle = chartContainer.querySelector('.chart-title');
           const progressBar = chartContainer.querySelector('.progress-bar');
           const chartLegend = chartContainer.querySelector('.chart-legend');

           listItems.forEach(item => {
                const isIncome = item.querySelector('.amount-income');
                const isExpense = item.querySelector('.amount-expense');
                if (currentView === 'income') { item.style.display = isIncome ? 'flex' : 'none'; }
                else if (currentView === 'expense') { item.style.display = isExpense ? 'flex' : 'none'; }
                else { item.style.display = 'flex'; }
            });

            if(currentView === 'income') {
                chartTitle.textContent = '主要收入来源';
                progressBar.style.display = 'none'; chartLegend.style.display = 'none';
            } else {
                 chartTitle.textContent = '主要支出分类';
                 progressBar.style.display = 'flex'; chartLegend.style.display = 'flex';
            }

           const dateHeadings = transactionsListContainer.querySelectorAll('.date-heading');
           dateHeadings.forEach(heading => {
               const card = heading.nextElementSibling;
               if (card && card.classList.contains('transactions-card')) {
                   const visibleItems = Array.from(card.querySelectorAll('.list-item')).filter(item => item.style.display !== 'none');
                   const shouldDisplay = visibleItems.length > 0;
                   heading.style.display = shouldDisplay ? 'block' : 'none';
                   card.style.display = shouldDisplay ? 'block' : 'none';
               }
           });
       }

      // Event Listeners
      prevMonthBtn.addEventListener('click', () => changeMonth(-1));
      nextMonthBtn.addEventListener('click', () => changeMonth(1));
      monthYearDisplay.addEventListener('click', selectYear);
      viewToggleContainer.addEventListener('click', (event) => { if (event.target.classList.contains('toggle-btn')) { setActiveView(event.target.dataset.view); } });
      aiButton.addEventListener('click', () => { alert('启动智能记账助理 (功能待实现)'); });
      reminderIcon.addEventListener('click', () => { alert('打开账单提醒管理页面 (功能待实现)'); });
      tabItems.forEach(item => { item.addEventListener('click', function() { const targetPage = this.dataset.page; if (targetPage && !this.classList.contains('active')) { window.location.href = targetPage; } }); });

      // Initial Load
      updateDisplay();
    });
  </script>
</body>
</html>