<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>分类管理 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      box-shadow: 0 2px 8px rgba(0,0,0,0.03);
      position: relative;
      z-index: 5;
      width: 100%;
      flex-shrink: 0;
      box-sizing: border-box;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      height: 44px;
    }
    
    .nav-left {
      width: 24px;
      color: #666;
    }
    
    .nav-right {
      width: 24px;
      text-align: right;
    }
    
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 20px;
      overflow-y: auto;
      overflow-x: hidden;
      -webkit-overflow-scrolling: touch;
      background-color: #f8f8f8;
      width: 100%;
      box-sizing: border-box;
    }
    
    /* 分类卡片样式 */
    .card {
      background-color: white;
      border-radius: 12px;
      padding: 15px;
      margin-bottom: 15px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.05);
    }
    
    /* 分类项样式 */
    .category-item {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
    
    .category-icon {
      width: 40px;
      height: 40px;
      border-radius: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 12px;
    }
    
    .category-name {
      flex: 1;
      font-size: 15px;
      font-weight: 500;
    }
    
    .category-actions {
      display: flex;
      gap: 15px;
    }
    
    .action-icon {
      color: #999;
      cursor: pointer;
    }
    
    .action-icon.delete {
      color: #FF6B35;
    }
    
    /* 按钮样式 */
    .btn {
      display: inline-block;
      border: none;
      border-radius: 12px;
      font-size: 16px;
      font-weight: 500;
      padding: 14px 20px;
      cursor: pointer;
      transition: all 0.3s ease;
      text-align: center;
      width: 100%;
      box-sizing: border-box;
    }
    
    .btn-primary {
      background-color: #FF6B35;
      color: white;
      box-shadow: 0 4px 12px rgba(255, 107, 53, 0.25);
    }
    
    .btn-primary:active {
      background-color: #e05a2b;
      transform: translateY(1px);
    }
    
    /* 切换标签样式 */
    .tab-container {
      display: flex;
      background-color: #f0f0f0;
      border-radius: 10px;
      padding: 3px;
      margin-bottom: 20px;
    }
    
    .tab {
      flex: 1;
      text-align: center;
      padding: 10px;
      font-weight: 500;
      border-radius: 8px;
      transition: all 0.3s ease;
    }
    
    .tab.active {
      background-color: #FF6B35;
      color: white;
    }
    
    /* 模态框样式 */
    .modal-overlay {
      position: fixed;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 1000;
    }
    
    .modal {
      width: 300px;
      background-color: white;
      border-radius: 16px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
    
    .modal-title {
      font-size: 18px;
      font-weight: 600;
      margin-bottom: 20px;
      text-align: center;
    }
    
    .input-group {
      margin-bottom: 15px;
    }
    
    .input-label {
      display: block;
      margin-bottom: 8px;
      font-size: 14px;
      color: #555;
      font-weight: 500;
    }
    
    .input {
      width: 100%;
      height: 46px;
      background-color: white;
      border: 1px solid rgba(0,0,0,0.1);
      border-radius: 10px;
      padding: 0 15px;
      font-size: 15px;
      box-sizing: border-box;
      outline: none;
    }
    
    .icon-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
      margin-top: 10px;
    }
    
    .icon-item {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .icon-item:hover {
      transform: scale(1.05);
    }
    
    .icon-item.selected {
      border: 2px solid #FF6B35;
    }
    
    .color-grid {
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      gap: 10px;
      margin-top: 10px;
    }
    
    .color-item {
      width: 40px;
      height: 40px;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .color-item:hover {
      transform: scale(1.05);
    }
    
    .color-item.selected {
      border: 2px solid #FF6B35;
    }
    
    .btn-group {
      display: flex;
      gap: 10px;
      margin-top: 20px;
    }
    
    .btn-outline {
      background-color: transparent;
      border: 1px solid #FF6B35;
      color: #FF6B35;
    }
    
    .fixed-bottom {
      position: absolute;
      bottom: 30px;
      left: 0;
      right: 0;
      padding: 0 20px;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <i class="fas fa-chevron-left"></i>
      </div>
      <div class="nav-title">分类管理</div>
      <div class="nav-right">
        <i class="fas fa-plus" style="color: #FF6B35;"></i>
      </div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 切换标签 -->
      <div class="tab-container">
        <div class="tab active">支出</div>
        <div class="tab">收入</div>
      </div>
      
      <!-- 分类列表 -->
      <div class="card">
        <div style="color: #888; font-size: 14px; margin-bottom: 15px;">常用分类</div>
        
        <!-- 餐饮 -->
        <div class="category-item">
          <div class="category-icon" style="background-color: #FEF3C7;">
            <i class="fas fa-utensils" style="color: #F59E0B; font-size: 16px;"></i>
          </div>
          <div class="category-name">餐饮</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-arrows-alt action-icon"></i>
          </div>
        </div>
        
        <!-- 购物 -->
        <div class="category-item">
          <div class="category-icon" style="background-color: #E0F2FE;">
            <i class="fas fa-shopping-bag" style="color: #0EA5E9; font-size: 16px;"></i>
          </div>
          <div class="category-name">购物</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-arrows-alt action-icon"></i>
          </div>
        </div>
        
        <!-- 交通 -->
        <div class="category-item">
          <div class="category-icon" style="background-color: #DCFCE7;">
            <i class="fas fa-bus" style="color: #10B981; font-size: 16px;"></i>
          </div>
          <div class="category-name">交通</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-arrows-alt action-icon"></i>
          </div>
        </div>
        
        <!-- 娱乐 -->
        <div class="category-item">
          <div class="category-icon" style="background-color: #F3E8FF;">
            <i class="fas fa-film" style="color: #8B5CF6; font-size: 16px;"></i>
          </div>
          <div class="category-name">娱乐</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-arrows-alt action-icon"></i>
          </div>
        </div>
        
        <!-- 居家 -->
        <div class="category-item" style="margin-bottom: 0;">
          <div class="category-icon" style="background-color: #FCE7F3;">
            <i class="fas fa-home" style="color: #EC4899; font-size: 16px;"></i>
          </div>
          <div class="category-name">居家</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-arrows-alt action-icon"></i>
          </div>
        </div>
      </div>
      
      <!-- 自定义分类 -->
      <div class="card">
        <div style="color: #888; font-size: 14px; margin-bottom: 15px;">自定义分类</div>
        
        <!-- 学习 -->
        <div class="category-item">
          <div class="category-icon" style="background-color: #FFF5F2;">
            <i class="fas fa-book" style="color: #FF6B35; font-size: 16px;"></i>
          </div>
          <div class="category-name">学习</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-trash action-icon delete"></i>
          </div>
        </div>
        
        <!-- 医疗 -->
        <div class="category-item">
          <div class="category-icon" style="background-color: #E0F2FE;">
            <i class="fas fa-heartbeat" style="color: #0EA5E9; font-size: 16px;"></i>
          </div>
          <div class="category-name">医疗</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-trash action-icon delete"></i>
          </div>
        </div>
        
        <!-- 数码 -->
        <div class="category-item" style="margin-bottom: 0;">
          <div class="category-icon" style="background-color: #DCFCE7;">
            <i class="fas fa-laptop" style="color: #10B981; font-size: 16px;"></i>
          </div>
          <div class="category-name">数码</div>
          <div class="category-actions">
            <i class="fas fa-pen action-icon"></i>
            <i class="fas fa-trash action-icon delete"></i>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部按钮 -->
    <div class="fixed-bottom">
      <button class="btn btn-primary">
        <i class="fas fa-plus" style="margin-right: 8px;"></i> 添加自定义分类
      </button>
    </div>
  </div>
  
  <!-- 模态框 - 编辑分类 -->
  <div class="modal-overlay">
    <div class="modal">
      <div class="modal-title">编辑分类</div>
      
      <div class="input-group">
        <label class="input-label">分类名称</label>
        <input type="text" class="input" value="餐饮">
      </div>
      
      <div class="input-group">
        <label class="input-label">图标</label>
        <div class="icon-grid">
          <div class="icon-item" style="background-color: #f0f0f0;">
            <i class="fas fa-utensils" style="color: #999;"></i>
          </div>
          <div class="icon-item selected" style="background-color: #FEF3C7;">
            <i class="fas fa-utensils" style="color: #F59E0B;"></i>
          </div>
          <div class="icon-item" style="background-color: #f0f0f0;">
            <i class="fas fa-shopping-bag" style="color: #999;"></i>
          </div>
          <div class="icon-item" style="background-color: #f0f0f0;">
            <i class="fas fa-bus" style="color: #999;"></i>
          </div>
          <div class="icon-item" style="background-color: #f0f0f0;">
            <i class="fas fa-plus" style="color: #999;"></i>
          </div>
        </div>
      </div>
      
      <div class="input-group">
        <label class="input-label">颜色</label>
        <div class="color-grid">
          <div class="color-item selected" style="background-color: #FEF3C7;"></div>
          <div class="color-item" style="background-color: #E0F2FE;"></div>
          <div class="color-item" style="background-color: #DCFCE7;"></div>
          <div class="color-item" style="background-color: #F3E8FF;"></div>
          <div class="color-item" style="background-color: #FFF5F2;"></div>
        </div>
      </div>
      
      <div class="btn-group">
        <button class="btn btn-outline" style="flex: 1;">取消</button>
        <button class="btn btn-primary" style="flex: 1;">保存</button>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 标签切换
      const tabs = document.querySelectorAll('.tab');
      tabs.forEach(tab => {
        tab.addEventListener('click', function() {
          tabs.forEach(t => t.classList.remove('active'));
          this.classList.add('active');
        });
      });
      
      // 返回按钮
      document.querySelector('.nav-left').addEventListener('click', function() {
        history.back();
      });
      
      // 模态框关闭
      document.querySelector('.btn-outline').addEventListener('click', function() {
        document.querySelector('.modal-overlay').style.display = 'none';
      });
      
      // 编辑图标点击
      const editIcons = document.querySelectorAll('.fa-pen');
      editIcons.forEach(icon => {
        icon.addEventListener('click', function() {
          document.querySelector('.modal-overlay').style.display = 'flex';
        });
      });
    });
  </script>
</body>
</html>