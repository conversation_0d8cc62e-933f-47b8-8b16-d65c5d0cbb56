<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <title>记账 - 支出</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        :root {
            --color-primary: #FF6B35;
            --text-primary: #333;
            --text-secondary: #666;
            --text-light: #999;
            --bg-white: #fff;
            --bg-light-gray: #f9f9f9; /* Keyboard grid background */
            --bg-medium-gray: #f5f5f5; /* Remark input background */
            --bg-dark-gray: #eee; /* Keyboard function keys */
            --border-color: #f0f0f0;
            --radius-pill: 50px;
            --radius-large: 16px;
            --radius-medium: 12px;
            --radius-small: 8px;
            --shadow-light: 0 1px 3px rgba(0, 0, 0, 0.05);
            --shadow-medium: 0 2px 8px rgba(0, 0, 0, 0.08);
            --shadow-overlay: 0 -5px 20px rgba(0,0,0,0.1); /* Shadow for keyboard grid */
            --status-bar-height: env(safe-area-inset-top, 20px);
            --safe-area-bottom: env(safe-area-inset-bottom, 0px);
            /* Estimate height of overlay elements for content padding */
            --overlay-non-grid-height: 110px; /* Approx height: button + remark + amount + gaps */
            --keyboard-grid-height: 230px; /* Approx height of 4 rows of keys + gaps */
        }

        html,
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
            color: var(--text-primary);
            background-color: #ccc; /* Outer page background */
            margin: 0;
            padding: 0;
            height: 100%;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
        }

        .phone-container {
            width: 375px;
            height: 812px;
            background-color: var(--bg-white); /* Main app background */
            border-radius: 40px;
            overflow: hidden; /* Important: clips overlay */
            position: relative;
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
            display: flex;
            flex-direction: column;
            border: 12px solid #111; /* Phone bezel */
        }

        .status-bar {
            height: var(--status-bar-height);
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 15px;
            box-sizing: border-box;
            font-size: 14px;
            color: var(--text-primary);
            position: absolute;
            top: 12px;
            left: 0;
            right: 0;
            z-index: 10;
        }
         .status-bar-left { font-weight: 600; }
         .status-bar-right { display: flex; align-items: center; gap: 6px;}

        .nav-bar {
            background-color: var(--bg-white);
            padding: calc(var(--status-bar-height) + 18px) 15px 10px;
            display: flex;
            align-items: center;
            position: relative;
            flex-shrink: 0;
            border-bottom: 1px solid var(--border-color);
            z-index: 5;
        }
        .nav-close-btn {
            position: absolute;
            left: 15px;
            top: calc(var(--status-bar-height) + 22px);
            font-size: 20px;
            color: var(--text-secondary);
            cursor: pointer;
            z-index: 6;
        }
        .nav-tabs {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            width: 100%;
        }
        .tab-option {
            padding: 6px 15px;
            font-size: 15px;
            font-weight: 500;
            color: var(--text-secondary);
            background-color: var(--bg-medium-gray);
            border-radius: var(--radius-pill);
            transition: all 0.2s ease;
            cursor: pointer;
        }
        .tab-option.active {
            color: white;
            background-color: var(--color-primary);
            box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
        }

        .content {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
            background-color: var(--bg-white);
            position: relative;
            display: flex;
            flex-direction: column;
            /* Add padding-bottom so content can scroll above overlay */
             padding-bottom: calc(var(--overlay-non-grid-height) + var(--keyboard-grid-height) + 20px); /* Ensure last row visible */
        }

        .categories-container {
            padding: 15px 12px 15px; /* Reduced bottom padding here */
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            gap: 10px 8px;
            background-color: var(--bg-white);
            flex-shrink: 0;
        }
        .category-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            cursor: pointer;
            padding: 5px 0;
        }
        .category-icon-background {
            width: 50px;
            height: 50px;
            border-radius: var(--radius-large);
            display: flex;
            justify-content: center;
            align-items: center;
            margin-bottom: 6px;
            transition: all 0.2s ease, box-shadow 0.2s ease;
            box-shadow: var(--shadow-light);
        }
        .category-item.active .category-icon-background {
            transform: scale(1.1);
            box-shadow: var(--shadow-medium);
        }
        .category-icon-background i {
            font-size: 22px;
            color: white;
        }
        .category-name {
            font-size: 11px;
            color: var(--text-secondary);
            text-align: center;
            white-space: nowrap;
            max-width: 55px;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* --- Keyboard Overlay Area --- */
        /* This is now just a positioning container, NO background */
        .keyboard-overlay {
            position: fixed;
            bottom: 12px; /* Position relative to bezel */
            left: 12px;
            right: 12px;
            /* REMOVED background-color */
            /* REMOVED border-radius (will apply to grid background) */
            /* REMOVED padding (will apply to sections inside) */
            /* REMOVED box-shadow (will apply to grid background) */
            display: none; /* Hidden by default */
            flex-direction: column;
            align-items: stretch; /* Make children fill width */
            z-index: 20;
            /* REMOVED border-top */
            max-height: 75%; /* Adjust if needed */
            pointer-events: none; /* Allow clicks through the overlay container itself */
        }

        .phone-container.keyboard-active .keyboard-overlay {
            display: flex; /* Show when active */
        }

        /* Container for elements ABOVE the keyboard grid */
        .keyboard-top-section {
            padding: 0 15px 10px 15px; /* Padding around button/remark/amount */
            display: flex;
            flex-direction: column;
            gap: 10px; /* Gap between button and details area */
            pointer-events: auto; /* Enable clicks for this section */
            flex-shrink: 0;
        }

        .account-book-button {
            display: inline-flex;
            align-items: center;
            padding: 6px 12px;
            background-color: var(--color-primary);
            color: white;
            border-radius: var(--radius-pill);
            box-shadow: var(--shadow-medium); /* Enhanced shadow for floating look */
            cursor: pointer;
            align-self: flex-start;
            font-size: 13px;
            font-weight: 500;
            /* margin-left: 5px; /* Removed, padding on parent handles it */
            flex-shrink: 0;
        }
         .account-book-button i {
            font-size: 14px;
            margin-right: 6px;
         }

        /* Area for Remark and Amount */
        .keyboard-details-area {
             display: flex;
             flex-direction: column;
             /* *** REDUCED GAP between remark and amount *** */
             gap: 5px;
             flex-shrink: 0;
        }

        .remark-with-actions {
             display: flex;
             align-items: center;
             gap: 8px;
             background-color: var(--bg-medium-gray); /* Keep background for input */
             padding: 8px 10px;
             border-radius: var(--radius-medium);
             box-shadow: var(--shadow-light); /* Optional shadow for remark box */
        }

        .remark-input {
            flex-grow: 1; border: none; outline: none; font-size: 14px;
            color: var(--text-primary); background: transparent; padding: 0;
            height: 28px; line-height: 28px;
        }
         .remark-input::placeholder { color: var(--text-light); }

        .remark-action-buttons { display: flex; gap: 8px; flex-shrink: 0; }
        .remark-icon-button {
             background: var(--bg-dark-gray); border: none; width: auto; height: 28px;
             border-radius: var(--radius-small); padding: 0 8px; font-size: 12px;
             color: var(--text-secondary); display: flex; align-items: center;
             justify-content: center; gap: 4px; cursor: pointer; white-space: nowrap;
        }
        .remark-icon-button i { font-size: 16px; flex-shrink: 0; }
        .remark-icon-button span { font-size: 11px; font-weight: 500; margin-left: 2px; }
        .remark-icon-button.camera-only { width: 28px; padding: 0; }

        .amount-display-container {
            padding: 5px 5px 0px; /* Reduced bottom padding */
            text-align: right;
        }
        .amount-display {
            font-size: 30px; color: var(--text-primary); font-weight: 500;
            min-height: 36px; line-height: 36px;
            text-shadow: 0 0 3px rgba(255, 255, 255, 0.6); /* Keep shadow for visibility */
        }

        /* *** NEW: Container for the keyboard grid background *** */
        .keyboard-grid-background {
            background-color: var(--bg-light-gray);
            /* Apply rounding matching the original overlay */
            border-radius: 28px;
            padding: 10px; /* Inner padding for the grid */
            padding-bottom: calc(10px + var(--safe-area-bottom)); /* Include safe area */
            box-shadow: var(--shadow-overlay); /* Apply shadow here */
            border-top: 1px solid var(--border-color); /* Apply border here */
            pointer-events: auto; /* Enable clicks for the grid */
             flex-shrink: 0; /* Prevent shrinking */
        }

        .keyboard-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(4, auto);
            gap: 8px;
             /* Padding now handled by keyboard-grid-background */
        }

        .key {
            height: 48px; background-color: var(--bg-white); border-radius: var(--radius-small);
            display: flex; justify-content: center; align-items: center;
            font-size: 22px; font-weight: 400; color: var(--text-primary);
            user-select: none; box-shadow: var(--shadow-light); cursor: pointer;
            transition: background-color 0.1s ease;
        }
        .key:active { background-color: #e0e0e0; }
        .key.function, .key.operator {
            background-color: var(--bg-dark-gray); font-size: 18px; color: var(--text-secondary);
        }
         .key.function:active, .key.operator:active { background-color: #d5d5d5; }
        .key.backspace { grid-column: 4 / 5; grid-row: 1 / 2; }
        .key.plus { grid-column: 4 / 5; grid-row: 2 / 3; font-size: 24px; }
        .key.minus { grid-column: 4 / 5; grid-row: 3 / 4; font-size: 24px; }
        .key.chat-record { grid-column: 3 / 4; grid-row: 4 / 5; font-size: 14px; padding: 0 5px; }
        .key.ok {
            background-color: var(--color-primary); color: white; grid-column: 4 / 5;
            grid-row: 4 / 5; font-size: 16px; font-weight: 500;
        }
         .key.ok:active { background-color: #e05a2b; }
         .key.backspace i { font-size: 20px; }

    </style>
</head>
<body>
    <div class="phone-container" id="phoneContainer">
        <!-- Status Bar -->
        <div class="status-bar">
            <div class="status-bar-left">9:41</div>
            <div class="status-bar-right">
                <i class="fas fa-signal"></i> <i class="fas fa-wifi"></i> <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- Navigation Bar -->
        <div class="nav-bar">
            <div class="nav-close-btn" id="closeBtn"><i class="fas fa-times"></i></div>
            <div class="nav-tabs">
                <div class="tab-option active" data-type="expense">支出</div>
                <div class="tab-option" data-type="income">收入</div>
                <div class="tab-option" data-type="transfer">转账</div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="content" id="mainContent">
            <!-- Category Selection -->
            <div class="categories-container" id="categoriesContainer">
                <!-- Categories -->
                <div class="category-item" data-category="餐饮"><div class="category-icon-background" style="background-color: #FF6B35;"><i class="fas fa-utensils"></i></div><div class="category-name">餐饮</div></div>
                <div class="category-item" data-category="购物"><div class="category-icon-background" style="background-color: #4CAF50;"><i class="fas fa-shopping-cart"></i></div><div class="category-name">购物</div></div>
                <div class="category-item" data-category="日用"><div class="category-icon-background" style="background-color: #2196F3;"><i class="fas fa-shopping-basket"></i></div><div class="category-name">日用</div></div>
                <div class="category-item" data-category="交通"><div class="category-icon-background" style="background-color: #9C27B0;"><i class="fas fa-bus"></i></div><div class="category-name">交通</div></div>
                <div class="category-item" data-category="蔬菜"><div class="category-icon-background" style="background-color: #8BC34A;"><i class="fas fa-carrot"></i></div><div class="category-name">蔬菜</div></div>
                <div class="category-item" data-category="水果"><div class="category-icon-background" style="background-color: #FFEB3B;"><i class="fas fa-apple-alt" style="color:#827717"></i></div><div class="category-name">水果</div></div>
                <div class="category-item" data-category="零食"><div class="category-icon-background" style="background-color: #FF9800;"><i class="fas fa-cookie-bite"></i></div><div class="category-name">零食</div></div>
                <div class="category-item" data-category="运动"><div class="category-icon-background" style="background-color: #00BCD4;"><i class="fas fa-dumbbell"></i></div><div class="category-name">运动</div></div>
                <div class="category-item" data-category="娱乐"><div class="category-icon-background" style="background-color: #E91E63;"><i class="fas fa-gamepad"></i></div><div class="category-name">娱乐</div></div>
                <div class="category-item" data-category="通讯"><div class="category-icon-background" style="background-color: #3F51B5;"><i class="fas fa-phone"></i></div><div class="category-name">通讯</div></div>
                <div class="category-item" data-category="服饰"><div class="category-icon-background" style="background-color: #673AB7;"><i class="fas fa-tshirt"></i></div><div class="category-name">服饰</div></div>
                <div class="category-item" data-category="美容"><div class="category-icon-background" style="background-color: #F06292;"><i class="fas fa-spa"></i></div><div class="category-name">美容</div></div>
                <div class="category-item" data-category="住房"><div class="category-icon-background" style="background-color: #795548;"><i class="fas fa-home"></i></div><div class="category-name">住房</div></div>
                <div class="category-item" data-category="孩子"><div class="category-icon-background" style="background-color: #03A9F4;"><i class="fas fa-child"></i></div><div class="category-name">孩子</div></div>
                <div class="category-item" data-category="长辈"><div class="category-icon-background" style="background-color: #FFC107;"><i class="fas fa-user-friends"></i></div><div class="category-name">长辈</div></div>
                <div class="category-item" data-category="社交"><div class="category-icon-background" style="background-color: #FF5722;"><i class="fas fa-users"></i></div><div class="category-name">社交</div></div>
                <div class="category-item" data-category="旅行"><div class="category-icon-background" style="background-color: #009688;"><i class="fas fa-plane"></i></div><div class="category-name">旅行</div></div>
                <div class="category-item" data-category="烟酒"><div class="category-icon-background" style="background-color: #607D8B;"><i class="fas fa-wine-glass-alt"></i></div><div class="category-name">烟酒</div></div>
                <div class="category-item" data-category="医疗"><div class="category-icon-background" style="background-color: #F44336;"><i class="fas fa-briefcase-medical"></i></div><div class="category-name">医疗</div></div>
                <div class="category-item" data-category="书籍"><div class="category-icon-background" style="background-color: #CDDC39;"><i class="fas fa-book"></i></div><div class="category-name">书籍</div></div>
                <div class="category-item" data-category="学习"><div class="category-icon-background" style="background-color: #9E9E9E;"><i class="fas fa-graduation-cap"></i></div><div class="category-name">学习</div></div>
                <div class="category-item" data-category="宠物"><div class="category-icon-background" style="background-color: #FFCDD2;"><i class="fas fa-paw" style="color:#D32F2F"></i></div><div class="category-name">宠物</div></div>
                <div class="category-item" data-category="礼金"><div class="category-icon-background" style="background-color: #D32F2F;"><i class="fas fa-gift"></i></div><div class="category-name">礼金</div></div>
                <div class="category-item" data-category="维修"><div class="category-icon-background" style="background-color: #546E7A;"><i class="fas fa-tools"></i></div><div class="category-name">维修</div></div>
                <div class="category-item" data-category="自定义"><div class="category-icon-background" style="background-color: #BDBDBD;"><i class="fas fa-plus"></i></div><div class="category-name">自定义</div></div>
            </div>
            <!-- Spacer only needed if content is short -->
            <!-- <div style="flex-grow: 1;" id="contentSpacer"></div> -->
        </div> <!-- End .content -->

        <!-- Keyboard Overlay Positioning Container -->
        <div class="keyboard-overlay" id="keyboardOverlay">
            <!-- Section for Button, Remark, Amount (No Background) -->
            <div class="keyboard-top-section">
                 <div class="account-book-button">
                     <i class="fas fa-book-open"></i>
                     <span>默认账本</span>
                 </div>
                 <div class="keyboard-details-area">
                     <div class="remark-with-actions">
                         <input type="text" class="remark-input" id="remarkInput" placeholder="添加备注...">
                         <div class="remark-action-buttons">
                             <button class="remark-icon-button camera-only" aria-label="Add Photo"><i class="fas fa-camera"></i></button>
                             <button class="remark-icon-button" aria-label="Set Date"><i class="fas fa-calendar-alt"></i><span>今天</span></button>
                         </div>
                     </div>
                     <div class="amount-display-container">
                         <span class="amount-display" id="amountDisplay">0.00</span>
                     </div>
                 </div>
            </div>

            <!-- Section for Keyboard Grid Background and Grid -->
            <div class="keyboard-grid-background">
                <div class="keyboard-grid">
                     <!-- Keys -->
                     <div class="key" data-value="1">1</div><div class="key" data-value="2">2</div><div class="key" data-value="3">3</div><div class="key function backspace" data-action="backspace"><i class="fas fa-backspace"></i></div>
                     <div class="key" data-value="4">4</div><div class="key" data-value="5">5</div><div class="key" data-value="6">6</div><div class="key operator plus" data-action="+">+</div>
                     <div class="key" data-value="7">7</div><div class="key" data-value="8">8</div><div class="key" data-value="9">9</div><div class="key operator minus" data-action="-">-</div>
                     <div class="key function dot" data-action=".">.</div><div class="key" data-value="0">0</div><div class="key function chat-record" data-action="chat">聊天记</div><div class="key ok" data-action="confirm">确定</div>
                </div>
            </div>
        </div>

    </div> <!-- End .phone-container -->

    <script>
        // Javascript remains unchanged
        document.addEventListener('DOMContentLoaded', function() {
            const phoneContainer = document.getElementById('phoneContainer');
            const categoriesContainer = document.getElementById('categoriesContainer');
            const keyboardOverlay = document.getElementById('keyboardOverlay');
            const amountDisplay = document.getElementById('amountDisplay');
            const closeBtn = document.getElementById('closeBtn');
            const tabs = document.querySelectorAll('.tab-option');
            const keys = document.querySelectorAll('.keyboard-grid .key');
           // const contentSpacer = document.getElementById('contentSpacer'); // Spacer might not be needed now

            let currentInput = '0';
            let hasDecimal = false;
            let selectedCategory = null;
            let currentMode = 'expense';
            let currentCalculation = '';

            closeBtn.addEventListener('click', () => {
                console.log('Close clicked');
                hideKeyboard();
            });

            tabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    tabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    currentMode = this.dataset.type;
                    console.log('Switched to mode:', currentMode);
                    resetInput();
                    if (phoneContainer.classList.contains('keyboard-active')) {
                         hideKeyboard();
                    }
                });
            });

            categoriesContainer.addEventListener('click', function(event) {
                const categoryItem = event.target.closest('.category-item');
                if (!categoryItem) return;

                if (categoryItem.classList.contains('active')) {
                     if (!phoneContainer.classList.contains('keyboard-active')) {
                         showKeyboard();
                     }
                     return;
                }

                const currentActive = categoriesContainer.querySelector('.category-item.active');
                if (currentActive) {
                    currentActive.classList.remove('active');
                }

                categoryItem.classList.add('active');
                selectedCategory = categoryItem.dataset.category;
                console.log('Selected Category:', selectedCategory);
                showKeyboard();
                resetInput();
            });

            keys.forEach(key => {
                key.addEventListener('click', function() {
                    const value = this.dataset.value;
                    const action = this.dataset.action;

                    if (action === 'confirm') handleConfirm();
                    else if (action === 'backspace') handleBackspace();
                    else if (action === '.') handleDot();
                    else if (action === 'chat') handleChatRecord();
                    else if (action === '+' || action === '-') handleOperator(action);
                    else if (value !== undefined) handleNumber(value);

                    updateDisplay();
                });
            });

            function showKeyboard() {
                phoneContainer.classList.add('keyboard-active');
               // if (contentSpacer) contentSpacer.style.display = 'none'; // Spacer might not be needed
                const activeCategory = categoriesContainer.querySelector('.category-item.active');
                if (activeCategory) {
                    // Basic scroll into view if needed
                    // activeCategory.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
                }
            }

            function hideKeyboard() {
                phoneContainer.classList.remove('keyboard-active');
               // if (contentSpacer) contentSpacer.style.display = 'block'; // Spacer might not be needed
                const currentActiveCategory = categoriesContainer.querySelector('.category-item.active');
                if (currentActiveCategory) {
                    currentActiveCategory.classList.remove('active');
                }
                selectedCategory = null;
                resetInput();
                document.getElementById('remarkInput').value = '';
                document.getElementById('remarkInput').blur();
            }

            function resetInput() {
                currentInput = '0';
                hasDecimal = false;
                currentCalculation = '';
                updateDisplay();
            }

            function updateDisplay() {
                 let displayValue = currentInput;
                 if (displayValue === '-') displayValue = '0';
                 amountDisplay.textContent = displayValue || '0';

                 if (amountDisplay.textContent.length > 9) {
                     amountDisplay.style.fontSize = '24px';
                 } else {
                    amountDisplay.style.fontSize = '30px';
                 }
            }

             function handleNumber(value) {
                 if (currentInput.replace('-', '').length >= 12) return;
                 if (hasDecimal) {
                     const parts = currentInput.split('.');
                     if (parts[1] && parts[1].length >= 2) return;
                 }
                 if (currentInput === '0') {
                     currentInput = value;
                 } else if (currentInput === '-0') {
                     currentInput = '-' + value;
                 } else {
                     currentInput += value;
                 }
             }
             function handleDot() {
                 if (!hasDecimal) {
                     if (currentInput.replace('-', '').length >= 12) return;
                     currentInput += '.';
                     hasDecimal = true;
                 }
             }
             function handleBackspace() {
                if (currentInput.length > 1) {
                    const removedChar = currentInput.slice(-1);
                    currentInput = currentInput.slice(0, -1);
                    if (removedChar === '.') {
                        hasDecimal = false;
                    }
                    if (currentInput === '-') {
                        currentInput = '0';
                    }
                } else if (currentInput.length === 1 && currentInput !== '0') {
                    currentInput = '0';
                    hasDecimal = false;
                }
             }
             function handleOperator(op) {
                 console.log('Operator:', op, ' - Calculation logic can be added here.');
             }
             function handleChatRecord() { console.log('Chat Record clicked'); }
             function handleConfirm() {
                 const finalAmount = parseFloat(currentInput) || 0;
                 const remark = document.getElementById('remarkInput').value.trim();

                 if (!selectedCategory) {
                     console.warn('Please select a category first.');
                     return;
                 }
                 if (finalAmount <= 0 && currentInput !== '0') {
                     console.warn('Please enter a valid amount greater than 0.');
                     return;
                 }
                  if (finalAmount === 0 && currentInput === '0') {
                      console.warn('Please enter an amount.');
                      return;
                  }

                 console.log('Confirming Transaction:');
                 console.log('  Type:', currentMode);
                 console.log('  Category:', selectedCategory);
                 console.log('  Amount:', finalAmount);
                 console.log('  Remark:', remark || '(No remark)');
                 hideKeyboard();
             }

            // Initial setup
            resetInput();
           /* // Initial spacer state based on keyboard
           if (!phoneContainer.classList.contains('keyboard-active')) {
                 if(contentSpacer) contentSpacer.style.display = 'block';
            } else {
                 if(contentSpacer) contentSpacer.style.display = 'none';
            } */
        });
    </script>
</body>
</html>