"use client"

import { useState } from "react"
import {
  ArrowUpRight,
  ArrowDownRight,
  ChevronLeft,
  ChevronRight,
  Target,
  TrendingUp,
  PieChartIcon,
  Home,
  ListTodo,
  Wallet,
  BarChart2,
  User,
  AlertTriangle,
  CheckCircle,
  ThumbsUp,
} from "lucide-react"
import { Card } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { Ta<PERSON>, TabsList, TabsTrigger } from "@/components/ui/tabs"

export default function AccountingApp() {
  const [activeMonth, setActiveMonth] = useState("2024年 5月")

  return (
    <div className="flex flex-col h-screen bg-gray-50">
      {/* Main Content */}
      <div className="flex-1 overflow-auto px-4 pb-16">
        {/* Month Selector - 放在滚动区域内 */}
        <div className="flex justify-between items-center mb-4 bg-white rounded-full p-2 shadow-sm">
          <Button variant="ghost" size="icon" className="text-orange-500">
            <ChevronLeft className="h-5 w-5" />
          </Button>
          <div className="font-medium">{activeMonth}</div>
          <Button variant="ghost" size="icon" className="text-orange-500">
            <ChevronRight className="h-5 w-5" />
          </Button>
        </div>

        {/* Core Indicators */}
        <Card className="p-5 mb-4">
          <div className="flex items-center gap-2 mb-4">
            <Target className="text-orange-500 h-5 w-5" />
            <h2 className="font-medium text-lg">核心指标</h2>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600 flex items-center">
                <ArrowUpRight className="h-4 w-4 text-orange-500 mr-1" />
                总支出
              </div>
              <div className="text-2xl font-bold mt-1">¥3,856</div>
              <div className="text-xs text-orange-500 mt-1">↑ 12.5% (vs 上期)</div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600 flex items-center">
                <ArrowDownRight className="h-4 w-4 text-green-500 mr-1" />
                总收入
              </div>
              <div className="text-2xl font-bold mt-1">¥5,230</div>
              <div className="text-xs text-green-500 mt-1">↑ 8.3% (vs 上期)</div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600 flex items-center">
                <ArrowDownRight className="h-4 w-4 text-green-500 mr-1" />
                净收入
              </div>
              <div className="text-2xl font-bold mt-1">¥1,374</div>
              <div className="text-xs text-green-500 mt-1">↓ 2.1% (vs 上期)</div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg">
              <div className="text-sm text-gray-600 flex items-center">
                <ArrowUpRight className="h-4 w-4 text-orange-500 mr-1" />
                平均日消费
              </div>
              <div className="text-2xl font-bold mt-1">¥128</div>
              <div className="text-xs text-orange-500 mt-1">↑ 5.2% (vs 上期)</div>
            </div>
          </div>
        </Card>

        {/* Income/Expense Trend */}
        <Card className="p-5 mb-4">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <TrendingUp className="text-orange-500 h-5 w-5" />
              <h2 className="font-medium text-lg">收支趋势</h2>
            </div>

            <Tabs defaultValue="30days">
              <TabsList className="bg-gray-100">
                <TabsTrigger value="7days" className="text-xs">
                  近7天
                </TabsTrigger>
                <TabsTrigger value="30days" className="text-xs bg-orange-500 text-white">
                  近30天
                </TabsTrigger>
                <TabsTrigger value="90days" className="text-xs">
                  近90天
                </TabsTrigger>
                <TabsTrigger value="12months" className="text-xs">
                  近12月
                </TabsTrigger>
              </TabsList>
            </Tabs>
          </div>

          <div className="h-48 relative">
            {/* 修复图例重叠问题 */}
            <div className="absolute top-0 right-0 flex items-center gap-6 text-xs mb-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span>收入</span>
              </div>
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-orange-500 rounded-full"></div>
                <span>支出</span>
              </div>
            </div>

            {/* Placeholder for chart - in a real app, you'd use a charting library */}
            <div className="absolute inset-0 flex items-center justify-center pt-6">
              <div className="w-full h-full px-2">
                <div className="flex h-full">
                  {/* Chart bars */}
                  {Array.from({ length: 10 }).map((_, i) => {
                    const expenseHeight = 30 + Math.random() * 50
                    const incomeHeight = 40 + Math.random() * 60
                    return (
                      <div key={i} className="flex-1 flex flex-col items-center justify-end gap-1">
                        <div className="w-3 bg-green-500 rounded-t-sm" style={{ height: `${incomeHeight}%` }}></div>
                        <div className="w-3 bg-orange-500 rounded-t-sm" style={{ height: `${expenseHeight}%` }}></div>
                        <div className="text-xs text-gray-500">{i + 1}</div>
                      </div>
                    )
                  })}
                </div>
              </div>
            </div>
          </div>
        </Card>

        {/* Expense Composition */}
        <Card className="p-5 mb-4">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <PieChartIcon className="text-orange-500 h-5 w-5" />
              <h2 className="font-medium text-lg">支出构成</h2>
            </div>

            <div className="flex gap-2">
              <Button variant="outline" size="sm" className="text-xs h-7 bg-orange-500 text-white border-orange-500">
                Top 5
              </Button>
              <Button variant="outline" size="sm" className="text-xs h-7">
                全部
              </Button>
            </div>
          </div>

          <div className="flex gap-4">
            <div className="w-1/2">
              {/* Placeholder for pie chart */}
              <div className="relative w-full aspect-square">
                <div className="absolute inset-0 rounded-full border-8 border-orange-500 border-r-orange-300 border-b-orange-200 border-l-orange-400"></div>
                <div className="absolute inset-8 rounded-full bg-white"></div>
                <div className="absolute inset-0 flex items-center justify-center flex-col">
                  <div className="text-sm text-gray-500">总支出</div>
                  <div className="text-xl font-bold">¥3,856</div>
                </div>
              </div>
            </div>

            <div className="w-1/2 flex flex-col justify-center gap-3">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-500"></div>
                <div className="text-sm flex-1">餐饮美食</div>
                <div className="text-sm font-medium">42%</div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-400"></div>
                <div className="text-sm flex-1">交通出行</div>
                <div className="text-sm font-medium">23%</div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-300"></div>
                <div className="text-sm flex-1">购物消费</div>
                <div className="text-sm font-medium">18%</div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-orange-200"></div>
                <div className="text-sm flex-1">休闲娱乐</div>
                <div className="text-sm font-medium">12%</div>
              </div>

              <div className="flex items-center gap-2">
                <div className="w-3 h-3 rounded-full bg-gray-300"></div>
                <div className="text-sm flex-1">其他</div>
                <div className="text-sm font-medium">5%</div>
              </div>
            </div>
          </div>
        </Card>

        {/* Budget Execution */}
        <Card className="p-5 mb-4">
          <div className="flex justify-between items-center mb-4">
            <div className="flex items-center gap-2">
              <Target className="text-orange-500 h-5 w-5" />
              <h2 className="font-medium text-lg">预算执行</h2>
            </div>

            <Button variant="outline" size="sm" className="text-xs h-7">
              管理预算
            </Button>
          </div>

          <div className="space-y-4">
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-500">🍽️</span>
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium">餐饮美食</div>
                  <Progress value={85} className="h-2 bg-gray-100">
                    <div className="h-full bg-orange-500 rounded-full" style={{ width: "85%" }}></div>
                  </Progress>
                </div>
              </div>
              <div className="flex justify-between text-xs pl-10">
                <span>已用 ¥1,275 / ¥1,500</span>
                <span className="text-green-500">剩余 ¥225</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-500">🚗</span>
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium">交通出行</div>
                  <Progress value={110} className="h-2 bg-gray-100">
                    <div className="h-full bg-orange-500 rounded-full" style={{ width: "110%" }}></div>
                  </Progress>
                </div>
              </div>
              <div className="flex justify-between text-xs pl-10">
                <span>已用 ¥660 / ¥600</span>
                <span className="text-orange-500">超支 ¥60</span>
              </div>
            </div>

            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center">
                  <span className="text-orange-500">🎬</span>
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium">休闲娱乐</div>
                  <Progress value={40} className="h-2 bg-gray-100">
                    <div className="h-full bg-orange-500 rounded-full" style={{ width: "40%" }}></div>
                  </Progress>
                </div>
              </div>
              <div className="flex justify-between text-xs pl-10">
                <span>已用 ¥200 / ¥500</span>
                <span className="text-green-500">剩余 ¥300</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Financial Insights */}
        <Card className="p-5 mb-4">
          <div className="flex items-center gap-2 mb-4">
            <div className="text-orange-500">💡</div>
            <h2 className="font-medium text-lg">财务洞察</h2>
          </div>

          <div className="space-y-3">
            <div className="bg-gray-50 p-3 rounded-lg flex gap-3">
              <CheckCircle className="h-5 w-5 text-blue-500 shrink-0 mt-0.5" />
              <div className="text-sm">您的餐饮支出较上期有所下降，继续保持！</div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg flex gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-500 shrink-0 mt-0.5" />
              <div className="text-sm">交通预算已超支，请关注下期该项支出。考虑优化出行方式。</div>
            </div>

            <div className="bg-gray-50 p-3 rounded-lg flex gap-3">
              <ThumbsUp className="h-5 w-5 text-blue-500 shrink-0 mt-0.5" />
              <div className="text-sm">本月储蓄率良好，已达到您设定目标的95%。</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Bottom Navigation */}
      <div className="fixed bottom-0 left-0 right-0 bg-white border-t flex justify-between items-center px-6 py-2">
        <div className="flex flex-col items-center">
          <Home className="h-5 w-5 text-gray-500" />
          <span className="text-xs text-gray-500">首页</span>
        </div>

        <div className="flex flex-col items-center">
          <ListTodo className="h-5 w-5 text-gray-500" />
          <span className="text-xs text-gray-500">账单</span>
        </div>

        <div className="flex flex-col items-center">
          <Wallet className="h-5 w-5 text-gray-500" />
          <span className="text-xs text-gray-500">资产</span>
        </div>

        <div className="flex flex-col items-center">
          <BarChart2 className="h-5 w-5 text-orange-500" />
          <span className="text-xs text-orange-500">分析</span>
        </div>

        <div className="flex flex-col items-center">
          <User className="h-5 w-5 text-gray-500" />
          <span className="text-xs text-gray-500">我的</span>
        </div>
      </div>
    </div>
  )
}
