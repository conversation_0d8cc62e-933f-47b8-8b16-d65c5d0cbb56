<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>首页 - AI记账</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #fff;
    }
    
    /* 页面标题样式 */
    .nav-title {
      font-weight: 600;
      position: relative;
      display: inline-block;
    }
    
    .nav-title::after {
      content: "";
      display: block;
      width: 30px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      border-radius: 2px;
    }
    
    /* 卡片样式优化 */
    .card {
      border-radius: 18px;
      box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
      margin-bottom: 20px;
      overflow: hidden;
      transition: all 0.3s ease;
    }
    
    /* 预算卡片特殊样式 */
    .budget-card {
      background: linear-gradient(135deg, #FF6B35, #FF8C66);
      color: white;
      margin-bottom: 20px;
      border-radius: 18px;
      padding: 20px;
      position: relative;
      overflow: hidden;
    }
    
    .budget-card::after {
      content: "";
      position: absolute;
      top: -10%;
      right: -10%;
      width: 120px;
      height: 120px;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      z-index: 1;
    }
    
    .budget-card::before {
      content: "";
      position: absolute;
      bottom: -20%;
      left: -10%;
      width: 80px;
      height: 80px;
      background: rgba(255, 255, 255, 0.08);
      border-radius: 50%;
      z-index: 1;
    }
    
    /* 进度条样式 */
    .progress-bar {
      height: 10px;
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 5px;
      margin-bottom: 12px;
      overflow: hidden;
    }
    
    .progress-bar-inner {
      height: 100%;
      background-color: rgba(255, 255, 255, 0.9);
      border-radius: 5px;
      position: relative;
    }
    
    /* 快捷功能图标样式 */
    .shortcut-icon {
      width: 54px;
      height: 54px;
      border-radius: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 8px;
      transition: transform 0.3s ease;
    }
    
    .shortcut-icon:active {
      transform: scale(0.95);
    }
    
    .shortcut-item {
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    
    .shortcut-label {
      font-size: 12px;
      color: #666;
    }
    
    /* 账单列表项样式 */
    .list-item {
      padding: 14px;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      transition: background-color 0.2s ease;
    }
    
    .list-item:last-child {
      border-bottom: none;
    }
    
    .list-item:active {
      background-color: #f9f9f9;
    }
    
    .icon-circle {
      width: 44px;
      height: 44px;
      border-radius: 14px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 15px;
    }
    
    .list-item-title {
      font-weight: 600;
      font-size: 15px;
      margin-bottom: 4px;
    }
    
    .list-item-subtitle {
      color: #999;
      font-size: 13px;
    }
    
    .amount-expense {
      color: #FF6B35;
      font-weight: 600;
    }
    
    .amount-income {
      color: #10B981;
      font-weight: 600;
    }
    
    /* 卡片和分区标题 */
    .section-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .section-title {
      font-size: 17px;
      font-weight: 600;
      color: #333;
      position: relative;
      display: inline-block;
    }
    
    .section-title::after {
      content: "";
      display: block;
      width: 20px;
      height: 3px;
      background: #FF6B35;
      position: absolute;
      bottom: -5px;
      left: 0;
      border-radius: 2px;
    }
    
    .view-all {
      color: #FF6B35;
      font-size: 14px;
      text-decoration: none;
    }
    
    /* 浮动LOGO按钮样式 */
    .fab {
      position: fixed;      
      bottom: 100px;       
      right: 40px;         
      width: 60px;         
      height: 60px;        
      border-radius: 50%;
      background-color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      display: flex;
      justify-content: center;
      align-items: center;
      z-index: 100;
      overflow: visible;
      animation: float 3s ease-in-out infinite;
      padding: 0;
    }
    
    /* 浮动按钮中的图片样式 */
    .fab img {
      width: 150%;  
      height: 150%; 
      object-fit: contain; 
      margin: 0; 
    }
    
    /* 提示气泡 */
    .fab-tooltip {
      position: absolute;
      top: -45px;
      left: 50%;
      transform: translateX(-50%);
      background-color: #333;
      color: white;
      font-size: 14px;
      padding: 6px 12px;
      border-radius: 20px;
      white-space: nowrap;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      opacity: 1;
      animation: none;
    }
    
    .fab-tooltip:after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid #333;
    }
    
    /* 漂浮动画 */
    @keyframes float {
      0% {
        transform: translateY(0px);
      }
      50% {
        transform: translateY(-10px);
      }
      100% {
        transform: translateY(0px);
      }
    }
    
    /* 底部栏优化 */
    .tab-bar {
      height: 80px;
      border-top: none;
      box-shadow: 0 -4px 10px rgba(0, 0, 0, 0.05);
    }
    
    .tab-icon.active {
      color: #FF6B35;
    }
    
    .tab-label.active {
      color: #FF6B35;
      font-weight: 500;
    }
    
    /* 新增功能模块样式 */
    .feature-control {
      display: flex; 
      align-items: center; 
      background-color: #F8F9FA; 
      padding: 8px 12px; 
      border-radius: 12px;
      transition: all 0.2s ease;
    }
    
    .feature-control:active {
      background-color: #F0F0F0;
      transform: scale(0.98);
    }
    
    .feature-row {
      padding: 5px 20px; 
      display: flex; 
      justify-content: space-between; 
      align-items: center;
    }
    
    .reminder-card {
      margin: 10px 20px 15px; 
      background: linear-gradient(135deg, #FFF5F2, #FFEDE8); 
      border-radius: 16px; 
      padding: 15px; 
      box-shadow: 0 2px 10px rgba(255, 107, 53, 0.1);
    }
    
    .reminder-item {
      background-color: white; 
      border-radius: 12px; 
      padding: 12px; 
      margin-bottom: 10px; 
      display: flex; 
      justify-content: space-between; 
      align-items: center; 
      box-shadow: 0 2px 5px rgba(0, 0, 0, 0.03);
    }
    
    .reminder-item:last-child {
      margin-bottom: 0;
    }
    
    .pay-button {
      background-color: #FF6B35; 
      color: white; 
      border: none; 
      padding: 6px 12px; 
      border-radius: 20px; 
      font-size: 12px; 
      font-weight: 500;
      transition: all 0.2s ease;
    }
    
    .pay-button:active {
      transform: scale(0.95);
      background-color: #E55B25;
    }
    
    .calendar-icon {
      width: 32px; 
      height: 32px; 
      background-color: #F8F9FA; 
      border-radius: 10px; 
      display: flex; 
      justify-content: center; 
      align-items: center;
      transition: all 0.2s ease;
    }
    
    .calendar-icon:active {
      background-color: #F0F0F0;
    }
    
    .locked-feature {
      position: relative;
    }
    
    .lock-badge {
      position: absolute; 
      top: -8px; 
      right: -8px; 
      width: 16px; 
      height: 16px; 
      background-color: #FFEBEB; 
      border-radius: 50%; 
      display: flex; 
      justify-content: center; 
      align-items: center;
    }
    
    /* 细节动画 */
    @keyframes pulse {
      0% {
        transform: scale(1);
      }
      50% {
        transform: scale(1.05);
      }
      100% {
        transform: scale(1);
      }
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">9:41</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <div style="font-weight: 500; font-size: 14px; color: white; background-color: #FF6B35; padding: 5px 10px; border-radius: 16px; display: flex; align-items: center;">
          2025年03月 <i class="fas fa-chevron-down" style="font-size: 12px; color: white; margin-left: 3px;"></i>
        </div>
      </div>
      <div class="nav-title" style="position: absolute; left: 50%; transform: translateX(-50%);">账无忌</div>
      <div class="nav-right" style="display: flex; align-items: center;">
        <i class="fas fa-bell" style="color: #999; margin-right: 16px;"></i>
        <i class="fas fa-calendar-alt" style="color: #FF6B35; font-size: 16px;"></i>
      </div>
    </div>
    
    <!-- 功能模块区域 -->
    <div style="padding: 15px 15px 5px; background-color: #fff;">
      <div style="display: flex; justify-content: space-between; padding: 0 5px;">
        <div style="display: flex; flex-direction: column; align-items: center; width: 60px;" class="locked-feature">
          <div style="width: 42px; height: 42px; background-color: #FFF5F2; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin-bottom: 8px; position: relative;">
            <i class="fas fa-book-open" style="color: #FF6B35; font-size: 20px;"></i>
            <div class="lock-badge">
              <i class="fas fa-lock" style="color: #FF6B35; font-size: 8px;"></i>
            </div>
          </div>
          <span style="font-size: 12px; color: #666;">账本切换</span>
        </div>
        
        <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
          <div style="width: 42px; height: 42px; background-color: #F0F7FF; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin-bottom: 8px;">
            <i class="fas fa-calculator" style="color: #3A7CFF; font-size: 20px;"></i>
          </div>
          <span style="font-size: 12px; color: #666;">预算</span>
        </div>
        
        <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
          <div style="width: 42px; height: 42px; background-color: #FFF6E9; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin-bottom: 8px;">
            <i class="fas fa-wallet" style="color: #FFA53D; font-size: 20px;"></i>
          </div>
          <span style="font-size: 12px; color: #666;">资产</span>
        </div>
        
        <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
          <div style="width: 42px; height: 42px; background-color: #F5F1FF; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin-bottom: 8px;">
            <i class="fas fa-chart-line" style="color: #9A6AFF; font-size: 20px;"></i>
          </div>
          <span style="font-size: 12px; color: #666;">财务顾问</span>
        </div>
        
        <div style="display: flex; flex-direction: column; align-items: center; width: 60px;">
          <div style="width: 42px; height: 42px; background-color: #F5F6F8; border-radius: 12px; display: flex; justify-content: center; align-items: center; margin-bottom: 8px;">
            <i class="fas fa-calendar" style="color: #FF6B35; font-size: 20px;"></i>
          </div>
          <span style="font-size: 12px; color: #666;">日历</span>
        </div>
      </div>
    </div>
    
    <!-- 日期展示 -->
    <div style="padding: 5px 20px; font-size: 13px; color: #888; margin-top: 5px;">
      03月30日 星期日
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 本月预算摘要 -->
      <div class="budget-card">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; position: relative; z-index: 2;">
          <div>
            <div style="font-size: 15px; margin-bottom: 5px; opacity: 0.9;">本月预算</div>
            <div style="font-size: 30px; font-weight: 700;">￥5,000</div>
          </div>
          <div style="text-align: right;">
            <div style="font-size: 15px; margin-bottom: 5px; opacity: 0.9;">已使用</div>
            <div style="font-size: 22px; font-weight: 600;">￥3,520</div>
          </div>
        </div>
        
        <div class="progress-bar">
          <div class="progress-bar-inner" style="width: 70%;"></div>
        </div>
        
        <div style="display: flex; justify-content: space-between; font-size: 14px; position: relative; z-index: 2;">
          <div>70%</div>
          <div>剩余: ￥1,480</div>
        </div>
      </div>
      
      <!-- 快捷记账 -->
      <div style="margin-bottom: 20px;">
        <div class="section-header">
          <h3 class="section-title">快捷记账</h3>
          <a href="#" class="view-all">全部</a>
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(4, 1fr); gap: 15px;">
          <div class="shortcut-item">
            <div class="shortcut-icon" style="background-color: #F0F9FF;">
              <i class="fas fa-microphone" style="color: #2878FF; font-size: 22px;"></i>
            </div>
            <div class="shortcut-label">语音</div>
          </div>
          
          <div class="shortcut-item">
            <div class="shortcut-icon" style="background-color: #ECFDF5;">
              <i class="fas fa-camera" style="color: #10B981; font-size: 22px;"></i>
            </div>
            <div class="shortcut-label">扫票</div>
          </div>
          
          <div class="shortcut-item">
            <div class="shortcut-icon" style="background-color: #FFF5F2;">
              <i class="fas fa-plus" style="color: #FF6B35; font-size: 22px;"></i>
            </div>
            <div class="shortcut-label">支出</div>
          </div>
          
          <div class="shortcut-item">
            <div class="shortcut-icon" style="background-color: #ECFDF5;">
              <i class="fas fa-arrow-down" style="color: #10B981; font-size: 22px;"></i>
            </div>
            <div class="shortcut-label">收入</div>
          </div>
        </div>
      </div>
      
      <!-- 待办提醒卡片 -->
      <div class="reminder-card" style="margin: 0 0 20px;">
        <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px;">
          <div style="display: flex; align-items: center;">
            <i class="fas fa-bell" style="color: #FF6B35; font-size: 16px; margin-right: 8px;"></i>
            <span style="font-weight: 600; font-size: 15px; color: #333;">待办提醒</span>
          </div>
          <span style="color: #FF6B35; font-size: 13px; font-weight: 500;">查看全部</span>
        </div>
        
        <!-- 提醒项目 -->
        <div class="reminder-item">
          <div>
            <div style="font-weight: 500; font-size: 14px; margin-bottom: 5px;">信用卡还款提醒</div>
            <div style="color: #FF4D4F; font-size: 12px;">距离到期还有2天</div>
          </div>
          <button class="pay-button">立即还款</button>
        </div>
        
        <div class="reminder-item">
          <div>
            <div style="font-weight: 500; font-size: 14px; margin-bottom: 5px;">房租分期付款</div>
            <div style="color: #666; font-size: 12px;">11月15日到期</div>
          </div>
          <div style="color: #666; font-size: 13px; font-weight: 500;">￥1,200</div>
        </div>
      </div>
      
      <!-- 近期账单 -->
      <div class="card" style="padding: 20px;">
        <div class="section-header">
          <h3 class="section-title">近期账单</h3>
          <a href="#" class="view-all">查看更多</a>
        </div>
        
        <div class="list">
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-circle" style="background-color: #FFF5F2;">
                <i class="fas fa-utensils" style="color: #FF6B35; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">午餐</div>
                <div class="list-item-subtitle">今天 12:30</div>
              </div>
            </div>
            <div style="margin-left: auto;">
              <div class="amount-expense">-￥35.00</div>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-circle" style="background-color: #E0F2FE;">
                <i class="fas fa-shopping-bag" style="color: #0EA5E9; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">超市购物</div>
                <div class="list-item-subtitle">今天 10:15</div>
              </div>
            </div>
            <div style="margin-left: auto;">
              <div class="amount-expense">-￥129.80</div>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-circle" style="background-color: #ECFDF5;">
                <i class="fas fa-money-bill-wave" style="color: #10B981; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">工资</div>
                <div class="list-item-subtitle">昨天</div>
              </div>
            </div>
            <div style="margin-left: auto;">
              <div class="amount-income">+￥8,500.00</div>
            </div>
          </div>
          
          <div class="list-item">
            <div style="display: flex; align-items: center;">
              <div class="icon-circle" style="background-color: #FFE4E6;">
                <i class="fas fa-bus" style="color: #E11D48; font-size: 18px;"></i>
              </div>
              <div>
                <div class="list-item-title">交通费</div>
                <div class="list-item-subtitle">昨天</div>
              </div>
            </div>
            <div style="margin-left: auto;">
              <div class="amount-expense">-￥15.00</div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 浮动LOGO按钮 -->
    <div class="fab">
      <div class="fab-tooltip">点击我聊天记账</div>
      <img src="2LOGO.png" alt="账无忌助手">
    </div>
    
    <!-- 底部标签栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon active"></i>
        <div class="tab-label active">首页</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-list-ul tab-icon"></i>
        <div class="tab-label">账单</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-wallet tab-icon"></i>
        <div class="tab-label">资产</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-chart-pie tab-icon"></i>
        <div class="tab-label">分析</div>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <div class="tab-label">我的</div>
      </div>
    </div>
  </div>
  
  <script>
    // 在页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
      // 让LOGO按钮可点击，点击后跳转到语音记账页面
      const fabButton = document.querySelector('.fab');
      fabButton.addEventListener('click', function() {
        window.location.href = 'voice-input.html';
      });
      
      // 顶部日期选择点击效果
      const dateSelector = document.querySelector('.nav-left');
      dateSelector.addEventListener('click', function() {
        console.log('打开日期选择器');
        // 这里可以添加日期选择器弹窗逻辑
      });
      
      // 顶部日历图标点击效果
      const calendarIcon = document.querySelector('.nav-right .fa-calendar-alt');
      calendarIcon.addEventListener('click', function() {
        console.log('打开日历视图');
        // 这里可以添加日历视图弹窗逻辑
      });
      
      // 功能模块点击效果
      const featureModules = document.querySelectorAll('.content + div div[style*="flex-direction: column"]');
      featureModules.forEach((module, index) => {
        module.addEventListener('click', function() {
          // 根据索引判断是哪个功能
          switch(index) {
            case 0: // 账本切换
              alert('此功能仅对会员开放，开通会员即可使用多账本功能！');
              break;
            case 1: // 预算
              console.log('跳转到预算页面');
              window.location.href = 'budget.html';
              break;
            case 2: // 资产
              console.log('跳转到资产管理页面');
              window.location.href = 'assets.html';
              break;
            case 3: // 财务顾问
              console.log('跳转到财务顾问页面');
              // window.location.href = 'financial-advisor.html';
              break;
            case 4: // 日历
              console.log('打开日历视图');
              // 这里可以添加日历视图弹窗逻辑
              break;
          }
        });
      });
      
      // 提醒卡片"查看全部"点击效果
      const viewAllReminders = document.querySelector('.reminder-card span:last-child');
      viewAllReminders.addEventListener('click', function() {
        console.log('跳转到所有提醒页面');
        // 实际项目中可以替换为: window.location.href = 'reminders.html';
      });
      
      // 立即还款按钮点击效果
      const payNowButton = document.querySelector('.pay-button');
      payNowButton.addEventListener('click', function() {
        console.log('跳转到还款页面');
        // 实际项目中可以替换为: window.location.href = 'payment.html';
      });
      
      // 底部标签栏点击事件
      const tabItems = document.querySelectorAll('.tab-item');
      tabItems.forEach((item, index) => {
        item.addEventListener('click', function() {
          switch(index) {
            case 0: // 首页
              // 已在当前页面
              break;
            case 1: // 账单
              window.location.href = 'transactions.html';
              break;
            case 2: // 资产
              window.location.href = 'assets.html';
              break;
            case 3: // 分析
              window.location.href = 'analysis.html';
              break;
            case 4: // 我的
              window.location.href = 'profile-new.html';
              break;
          }
        });
      });
    });
  </script>
</body>
</html>