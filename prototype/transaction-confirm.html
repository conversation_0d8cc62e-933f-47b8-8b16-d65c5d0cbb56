<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>记账 - 账无忌</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <link rel="stylesheet" href="css/style.css">
  <style>
    /* 全局样式优化 */
    html, body {
      font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      color: #333;
      background-color: #f0f0f0;
      margin: 0;
      padding: 0;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      overflow: hidden;
    }
    
    .phone-container {
      width: 370px;
      height: 760px;
      background-color: #fff;
      border-radius: 40px;
      overflow: hidden;
      position: relative;
      box-shadow: 0 10px 30px rgba(0,0,0,0.2);
      display: flex;
      flex-direction: column;
      border: 12px solid #000;
    }
    
    /* 状态栏样式 */
    .status-bar {
      height: 44px;
      background-color: #fff;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 15px;
      box-sizing: border-box;
      font-size: 14px;
      color: #333;
      flex-shrink: 0;
    }
    
    .status-bar-left {
      font-weight: 600;
    }
    
    .status-bar-right {
      display: flex;
      align-items: center;
    }
    
    .status-bar-icon {
      margin-left: 5px;
    }
    
    /* 导航栏样式 */
    .nav-bar {
      background-color: #fff;
      border-bottom: 1px solid rgba(0,0,0,0.05);
      display: flex;
      align-items: center;
      padding: 12px 15px 15px;
      position: relative;
    }
    
    .nav-left {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    .back-button {
      width: 36px;
      height: 36px;
      border-radius: 18px;
      background-color: #f8f8f8;
      display: flex;
      align-items: center;
      justify-content: center;
      color: #666;
    }
    
    .nav-center {
      display: flex;
      flex: 1;
      justify-content: center;
      align-items: center;
      gap: 10px;
    }
    
    .tab-option {
      padding: 7px 15px;
      font-size: 14px;
      font-weight: 500;
      color: #888;
      background-color: #f5f5f5;
      border-radius: 18px;
      transition: all 0.2s ease;
      white-space: nowrap;
    }
    
    .tab-option.active {
      color: white;
      background-color: #FF6B35;
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.25);
    }
    
    .nav-right {
      width: 40px;
      height: 40px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    /* 内容区域 */
    .content {
      flex: 1;
      padding: 0;
      overflow-y: auto;
      overflow-x: hidden;
      background-color: #f9f9f9;
      position: relative;
      display: flex;
      flex-direction: column;
    }
    
    /* 类别选择区域 */
    .categories-container {
      padding: 20px 12px 10px;
      display: flex;
      flex-wrap: wrap;
      justify-content: space-between;
      background-color: white;
      border-bottom: 1px solid rgba(0,0,0,0.04);
    }
    
    .category-item {
      width: 20%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 16px;
    }
    
    .category-icon {
      width: 52px;
      height: 52px;
      border-radius: 16px;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-bottom: 8px;
      transition: all 0.2s ease;
      box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    }
    
    .category-icon.active {
      transform: scale(1.05);
      box-shadow: 0 3px 10px rgba(0,0,0,0.1);
    }
    
    .category-icon img, .category-icon i {
      font-size: 22px;
    }
    
    .category-name {
      font-size: 12px;
      color: #555;
      text-align: center;
      white-space: nowrap;
      max-width: 52px;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    
    /* 记账信息区域 */
    .accounting-container {
      flex: 1;
      background-color: #f9f9f9;
      padding: 0;
      display: flex;
      flex-direction: column;
    }
    
    /* 账本选择 */
    .account-options {
      display: flex;
      padding: 10px 15px;
      background-color: #f8f8f8;
      border-bottom: 1px solid #eee;
    }
    
    .account-option {
      display: flex;
      align-items: center;
      padding: 5px 10px;
      background-color: white;
      border-radius: 20px;
      margin-right: 10px;
      box-shadow: 0 1px 2px rgba(0,0,0,0.05);
    }
    
    .account-option i {
      font-size: 14px;
      margin-right: 5px;
      color: #FF6B35;
    }
    
    .account-option span {
      font-size: 13px;
      color: #333;
    }
    
    /* 备注输入区 */
    .remark-container {
      display: flex;
      background-color: white;
      padding: 12px 15px;
      border-bottom: 1px solid rgba(0,0,0,0.04);
      align-items: center;
      margin-bottom: 10px;
    }
    
    .remark-icon {
      width: 36px;
      height: 36px;
      border-radius: 12px;
      background-color: #f2f2f2;
      display: flex;
      justify-content: center;
      align-items: center;
      margin-right: 10px;
    }
    
    .remark-icon i {
      color: #777;
      font-size: 18px;
    }
    
    .remark-input {
      flex: 1;
      border: none;
      outline: none;
      font-size: 15px;
      color: #333;
      background: transparent;
      padding: 0;
      height: 36px;
    }
    
    .remark-input::placeholder {
      color: #aaa;
    }
    
    /* 金额展示 */
    .amount-display {
      flex: 1;
      padding: 0 25px;
      display: flex;
      align-items: center;
      justify-content: flex-end;
      font-size: 36px;
      color: #333;
      font-weight: 500;
      text-align: right;
    }
    
    /* 数字键盘 */
    .keyboard {
      background-color: #f8f8f8;
      padding: 10px;
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-gap: 10px;
    }
    
    .key {
      height: 54px;
      background-color: white;
      border-radius: 27px;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 24px;
      font-weight: 500;
      color: #333;
      user-select: none;
      box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .key:active {
      background-color: #f5f5f5;
    }
    
    .key.function {
      font-size: 18px;
      color: #666;
    }
    
    .key.operator {
      font-size: 20px;
      color: #666;
    }
    
    .key.ok {
      background-color: #FF6B35;
      color: white;
      grid-row: span 2;
      height: 100%;
    }
    
    .key.ok:active {
      background-color: #e05a2b;
    }
  </style>
</head>
<body>
  <div class="phone-container">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-bar-left">21:03</div>
      <div class="status-bar-right">
        <i class="fas fa-signal status-bar-icon"></i>
        <i class="fas fa-wifi status-bar-icon"></i>
        <i class="fas fa-battery-full status-bar-icon"></i>
      </div>
    </div>
    
    <!-- 导航栏 -->
    <div class="nav-bar">
      <div class="nav-left">
        <div class="back-button">
          <i class="fas fa-xmark"></i>
        </div>
      </div>
      <div class="nav-center">
        <div class="tab-option">收入</div>
        <div class="tab-option active">支出</div>
        <div class="tab-option">转账</div>
        <div class="tab-option">聊天记</div>
      </div>
      <div class="nav-right"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content">
      <!-- 类别选择 -->
      <div class="categories-container">
        <div class="category-item">
          <div class="category-icon active" style="background-color: #FFF5F2;">
            <i class="fas fa-utensils" style="color: #FF6B35;"></i>
          </div>
          <div class="category-name">餐饮</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #E0F7FA;">
            <i class="fas fa-shopping-basket" style="color: #00ACC1;"></i>
          </div>
          <div class="category-name">日用品</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #E3F2FD;">
            <i class="fas fa-bus" style="color: #2196F3;"></i>
          </div>
          <div class="category-name">交通</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #FFF3E0;">
            <i class="fas fa-birthday-cake" style="color: #FF9800;"></i>
          </div>
          <div class="category-name">零食</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #F1F8E9;">
            <i class="fas fa-apple-alt" style="color: #8BC34A;"></i>
          </div>
          <div class="category-name">水果</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #E8EAF6;">
            <i class="fas fa-glass-whiskey" style="color: #3F51B5;"></i>
          </div>
          <div class="category-name">饮料</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #F3E5F5;">
            <i class="fas fa-gamepad" style="color: #9C27B0;"></i>
          </div>
          <div class="category-name">娱乐</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #FFEBEE;">
            <i class="fas fa-shopping-cart" style="color: #F44336;"></i>
          </div>
          <div class="category-name">购物</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #FCE4EC;">
            <i class="fas fa-tshirt" style="color: #E91E63;"></i>
          </div>
          <div class="category-name">服饰</div>
        </div>
        <div class="category-item">
          <div class="category-icon" style="background-color: #FFF8E1;">
            <i class="fas fa-magic" style="color: #FFC107;"></i>
          </div>
          <div class="category-name">美妆</div>
        </div>
      </div>
      
      <!-- 记账信息 -->
      <div class="accounting-container">
        <!-- 选择账本 -->
        <div class="account-options">
          <div class="account-option">
            <i class="fas fa-book"></i>
            <span>默认账本</span>
          </div>
          <div class="account-option">
            <i class="fas fa-calendar-day"></i>
            <span>今天</span>
          </div>
        </div>
        
        <!-- 备注输入 -->
        <div class="remark-container">
          <div class="remark-icon">
            <i class="fas fa-camera"></i>
          </div>
          <input type="text" class="remark-input" placeholder="添加备注">
        </div>
        
        <!-- 金额显示 -->
        <div class="amount-display">
          <span>0.00</span>
        </div>
        
        <!-- 数字键盘 -->
        <div class="keyboard">
          <div class="key">1</div>
          <div class="key">2</div>
          <div class="key">3</div>
          <div class="key operator">-</div>
          <div class="key">4</div>
          <div class="key">5</div>
          <div class="key">6</div>
          <div class="key operator">+</div>
          <div class="key">7</div>
          <div class="key">8</div>
          <div class="key">9</div>
          <div class="key ok">确定</div>
          <div class="key function">.</div>
          <div class="key">0</div>
          <div class="key function"><i class="fas fa-backspace"></i></div>
        </div>
      </div>
    </div>
  </div>
  
  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // 类别选择
      const categoryIcons = document.querySelectorAll('.category-icon');
      categoryIcons.forEach(icon => {
        icon.addEventListener('click', function() {
          // 移除所有active类
          categoryIcons.forEach(i => i.classList.remove('active'));
          // 添加active类到当前点击的图标
          this.classList.add('active');
        });
      });
      
      // 数字键盘功能
      const keys = document.querySelectorAll('.key');
      const amountDisplay = document.querySelector('.amount-display span');
      let currentAmount = '0.00';
      
      keys.forEach(key => {
        key.addEventListener('click', function() {
          const value = this.textContent.trim();
          
          if (!isNaN(value) || value === '.') {
            // 数字或小数点
            if (currentAmount === '0.00' || currentAmount === '0') {
              currentAmount = value === '.' ? '0.' : value;
            } else {
              // 检查是否已有小数点
              if (value === '.' && currentAmount.includes('.')) {
                return;
              }
              // 最多保留两位小数
              if (currentAmount.includes('.')) {
                const parts = currentAmount.split('.');
                if (parts[1] && parts[1].length >= 2 && value !== '.') {
                  return;
                }
              }
              currentAmount += value;
            }
          } else if (this.classList.contains('function') && this.innerHTML.includes('backspace')) {
            // 退格键
            if (currentAmount.length > 1) {
              currentAmount = currentAmount.slice(0, -1);
              if (currentAmount === '') currentAmount = '0';
            } else {
              currentAmount = '0';
            }
          }
          
          // 更新显示
          if (currentAmount === '.') currentAmount = '0.';
          amountDisplay.textContent = currentAmount;
        });
      });
      
      // 返回按钮功能
      document.querySelector('.back-button').addEventListener('click', function() {
        history.back();
      });
    });
  </script>
</body>
</html>