# UI界面规则文档

+----------------------------------+
|       项目文档关系图              |
+----------------------------------+
|                                  |
|  架构设计文档.md                  |
|  (整体架构、技术选型、目录结构)    |
|          |                       |
|          v                       |
|  开发文档和规范.md                |
|  (开发规范、编码标准、命名约定)    |
|          |                       |
|          v                       |
|  UI界面规则文档.md                |
|  (UI设计、组件样式、布局规范)      |
|                                  |
+----------------------------------+

文档主要职责:
- 架构设计文档: 定义系统架构和技术栈选择，是最高层次的设计指导
- 开发文档和规范: 规定具体的开发实践和编码标准，是开发过程的实施指南
- UI界面规则文档: 详细说明UI组件和视觉规范，是界面实现的参考标准

## 一、核心原则

### 1. 命名空间隔离
- **页面级容器**: 页面的最外层容器 **推荐 (RECOMMENDED)** 使用唯一的、能清晰标识页面的类名（例如：`.budget-setup-page` 或 `.page-budget-setup`）。
- **组件内部/页面内容**: 在页面级容器内部的元素，以及所有组件（通用、业务、私有）内部的元素，其样式类名 **必须 (MUST)** 使用 **BEM (Block__Element--Modifier)** 命名法，例如：`.transaction-card__amount--positive`，遵循 `开发文档和规范.md` 4.1.3 节。
- **样式隔离**: **必须 (MUST)** 使用 `<style scoped>` 属性来隔离组件和页面样式。
- **全局选择器**: **严禁 (FORBID)** 在全局样式（如 `App.vue` 或 `uni.scss`）中使用标签选择器 (如 `div`, `span`) 或过于通用的类名 (如 `.title`, `.container`)，以避免全局污染和冲突。全局样式应仅限于 reset、基础排版、主题变量等。

### 2. 样式作用域控制
- 严格控制样式选择器层级
- 使用:deep()处理第三方组件样式
- 严禁使用!important覆盖样式，除非处理第三方组件样式
- 禁止在页面级样式中重写:root变量定义

### 3. 资源引用规范
- 图标统一使用uView Plus图标库（通过 uni_modules 或 NPM 安装的）
- 字体文件统一从@/assets/fonts引入
- 公共样式从@/styles/common引入
- 颜色值使用CSS变量形式：var(--color-primary, #FF6B35)

## 二、页面结构规范

### 1. 基础模板
```vue
<template>
  <view class="[page-name]-container">
    <!-- 页面内容 -->
  </view>
</template>

<script setup lang="ts">
// 引入声明
import { ref } from 'vue'

// 组件声明 - 确保使用一致的组件命名前缀
import AppButton from '@/components/common/AppButton.vue'
import AppCard from '@/components/common/AppCard.vue'
// AppIcon组件已全局注册，无需导入

// 状态定义
// 方法定义
</script>

<style lang="scss" scoped>
.[page-name]-container {
  // 页面级样式
}
</style>
```

### 2. 目录结构
```
pages/
  [page-name]/
    index.vue          // 页面主文件
    components/        // 页面私有组件
    styles/           // 页面私有样式
    utils/            // 页面工具函数
```

## 三、颜色与样式系统

### 1. 主题色系
- 主色：var(--color-primary, #FF6B35) /* 橙色系主题色，与原型UI一致 */
- 辅助色：
  - 成功色：var(--color-success, #4CAF50)
  - 警告色：var(--color-warning, #FFC107)
  - 错误色：var(--color-error, #F44336)
  - 信息色：var(--color-info, #2196F3)
- 文字色：
  - 主要文字：var(--text-primary, #333333)
  - 次要文字：var(--text-secondary, #666666)
  - 提示文字：var(--text-hint, #999999)
  - 反色文字：var(--text-inverse, #FFFFFF) /* 用于主色背景上的文字 */
- 背景色：
  - 主背景色：var(--bg-primary, #FFFFFF)
  - 次要背景色：var(--bg-secondary, #F5F5F5)
  - 卡片背景色：var(--card-bg, #FFFFFF)

### 2. 字体规范
- 主要字体：PingFang SC, Helvetica Neue, Helvetica, Arial, sans-serif
- 字号系统：
  - 超大标题：22px /* 页面主标题 */
  - 大标题：20px /* 卡片标题 */
  - 副标题：18px /* 次级标题 */
  - 正文：16px /* 主要内容 */
  - 辅助文字：14px /* 注释、提示 */
  - 小文字：12px /* 最小内容 */
- 字重系统：
  - 常规：400 (normal)
  - 中等：500 (medium)
  - 加粗：600 (semibold)
  - 特粗：700 (bold)

### 3. 间距规范
- 基础间距系统（基于8px的倍数设计）：
  - 页面边距：16px /* 与原型UI一致 */
  - 卡片外边距：16px /* 与原型UI一致 */
  - 卡片内边距：16px /* 与原型UI一致 */
  - 组件间距：8px, 16px, 24px, 32px
- 特定元素间距：
  - 表单项间距：16px
  - 按钮间距：8px
  - 列表项间距：12px
  - 图标与文字间距：4px

### 4. 阴影与高度系统
- 卡片阴影系统（与原型UI一致）：
  - 默认卡片：var(--shadow-card, 0 2px 8px rgba(0, 0, 0, 0.1))
  - 浮起卡片：var(--shadow-raised, 0 4px 16px rgba(0, 0, 0, 0.12))
  - 对话框阴影：var(--shadow-dialog, 0 8px 30px rgba(0, 0, 0, 0.18))
- 高度层级系统：
  - 基础内容：z-index: 1
  - 浮动内容：z-index: 10
  - 导航栏：z-index: 100
  - 弹出层：z-index: 1000
  - 对话框：z-index: 2000
  - 全局提示：z-index: 3000

### 5. 圆角系统（与原型UI一致）
- 卡片圆角：var(--radius-card, 12px) /* 卡片组件圆角，与原型UI一致 */
- 按钮圆角：var(--radius-button, 8px) /* 按钮圆角，与原型UI一致 */
- 输入框圆角：var(--radius-input, 8px)
- 图片圆角：var(--radius-image, 8px)
- 小圆角：var(--radius-sm, 4px) /* 小组件圆角 */
- 大圆角：var(--radius-lg, 16px) /* 特大组件圆角 */
- 全圆角：var(--radius-circle, 50%) /* 圆形组件 */

### 6. CSS变量规范
- **统一定义**: 所有全局 CSS 变量 **必须 (MUST)** 且 **只能 (ONLY)** 统一定义在 `src/assets/styles/variables.scss` 文件中。
- **命名规则**: 使用 `--[分类]-[功能]-[状态]` 格式，例如：`--color-primary`, `--font-size-medium`, `--spacing-card-y`。
- **强制使用**: 在组件或页面的 `<style scoped>` 块中设置颜色、字体、间距等样式时，**必须 (MUST)** 使用 `var(--variable-name, fallbackValue)` 函数引用已定义的全局 CSS 变量。
- **提供后备值**: 使用 `var()` 时，**必须 (MUST)** 提供一个有效的后备值，例如 `color: var(--color-primary, #FF6B35);`。
- **禁止重定义**: **严禁 (FORBID)** 在组件内部或页面级样式中重新定义已在 `variables.scss` 中定义的全局 CSS 变量。

### 7. 卡片式布局规范（适配原型UI）
卡片式布局是本应用UI的核心特色，原型UI中广泛使用卡片组件：

#### 卡片组件规范
- **外观标准**:
  - 卡片背景色: var(--card-bg, #FFFFFF)
  - 卡片阴影: var(--shadow-card, 0 2px 8px rgba(0, 0, 0, 0.1))
  - 卡片圆角: var(--radius-card, 12px)
  - 卡片外边距: 16px (各方向)
  - 卡片内边距: 16px

- **卡片组成部分**:
  - 卡片头部: 包含标题和可选的操作按钮
  - 卡片内容: 主要内容区域
  - 卡片底部: 可选，包含操作按钮或额外信息

- **卡片间距规范**:
  - 卡片头部与内容间距: 12px
  - 卡片内容与底部间距: 16px
  - 相邻卡片之间的间距: 16px

#### 卡片组件示例代码:
```scss
.card {
  background-color: var(--card-bg, #FFFFFF);
  box-shadow: var(--shadow-card, 0 2px 8px rgba(0, 0, 0, 0.1));
  border-radius: var(--radius-card, 12px);
  margin: 16px;
  padding: 16px;
  
  &__header {
    margin-bottom: 12px;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
  
  &__title {
    font-size: var(--font-size-large, 18px);
    font-weight: 500;
    color: var(--text-primary, #333333);
  }
  
  &__content {
    margin-bottom: 16px;
  }
  
  &__footer {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
```

## 四、图标使用规范

**核心规范**: 项目中所有图标的使用，**必须 (MUST)** 严格遵守 `@开发规范与指南-基础篇.md` 第 4 节 (`图标使用 (Icon Usage)`) 中定义的标准。

### 1. 基础规范

1. **统一组件**: **必须 (MUST)** 使用全局注册的 `<AppIcon>` 组件来渲染图标。该组件内部封装了 uView Plus 的 `<u-icon>` 组件。
2. **NPM 来源**: 所有图标资源**必须 (MUST)** 来自项目已集成的 uView Plus 图标库 (通过 `uni_modules` 引入或 NPM 安装 `uview-plus` 包)。
3. **按需加载**: uView Plus 组件（包括 `<u-icon>`）通过 uni-app 的 `easycom` 机制实现按需编译和自动引入。
4. **禁止其他方式**: **严禁 (FORBID)** 使用 `<i>` 标签、CDN 引入、直接使用 `<u-icon>` 或其他第三方图标库。Font Awesome 相关依赖和代码已从项目中移除。
5. **类别图标**: 对于业务类别图标，**必须 (MUST)** 使用专用的 `<CategorySelector>` 组件。

### 2. 图标尺寸规范

为保持界面视觉一致性，**必须 (MUST)** 遵循以下图标尺寸规范：

| 场景               | 推荐尺寸(rpx)  | 示例                                           |
|-------------------|---------------|------------------------------------------------|
| 导航栏图标         | 40rpx         | 顶部导航返回、菜单、搜索图标                    |
| 列表项图标         | 32rpx         | 列表项前的功能图标                             |
| 按钮内嵌图标       | 28rpx         | 按钮内的操作提示图标                           |
| 表单项图标         | 36rpx         | 输入框前缀图标、选择器图标                     |
| 标签/提示图标      | 24rpx         | 小提示、标记、状态指示图标                     |
| 分类/功能图标      | 48rpx         | 分类选择器、底部导航、功能入口图标             |

### 3. 图标颜色规范

图标颜色**必须 (MUST)** 使用 CSS 变量并遵循以下规范：

1. **主要图标**: 使用 `var(--color-text-primary)` 或 `var(--color-primary)`
2. **次要图标**: 使用 `var(--color-text-secondary)`
3. **提示/警告图标**: 使用 `var(--color-warning)`
4. **错误/删除图标**: 使用 `var(--color-error)`
5. **成功/确认图标**: 使用 `var(--color-success)`
6. **禁用图标**: 使用 `var(--color-text-disabled)`

### 4. 交互规范

1. **点击反馈**: 可交互图标应提供适当的点击反馈，如轻微放大或颜色变化
2. **禁用状态**: 通过 `color` 属性使用 `var(--color-text-disabled)` 表示禁用状态
3. **加载状态**: 可使用 uView Plus 的 loading 图标表示加载中状态
4. **激活状态**: 通过动态切换 `-fill` 后缀图标或改变颜色表示激活状态

### 5. 图标布局规范

1. **间距**: 图标与相邻文字的间距统一为 8rpx 
2. **对齐**: 图标应与文字垂直居中对齐，避免产生视觉跳动
3. **组合**: 图标与文字组合使用 `label` 和 `labelPos` 属性，保持一致的间距和对齐
4. **内边距**: 可点击的独立图标按钮应有足够的点击区域（至少 44rpx × 44rpx）

### 6. 典型场景应用示例

#### 6.1 底部导航图标
```vue
<AppTabBar>
  <AppTabBarItem>
    <AppIcon 
      icon="home" 
      :size="48" 
      :color="active === 'home' ? 'var(--color-primary)' : 'var(--color-text-secondary)'" 
    />
    <text>首页</text>
  </AppTabBarItem>
  <!-- 其他导航项 -->
</AppTabBar>
```

#### 6.2 表单输入前缀图标
```vue
<AppInput 
  v-model="searchText" 
  placeholder="搜索"
  prefix-icon="search"
  :prefix-icon-size="36"
  prefix-icon-color="var(--color-text-secondary)"
/>
```

#### 6.3 状态提示图标
```vue
<view class="status-indicator">
  <AppIcon 
    :icon="status === 'success' ? 'checkmark-circle-fill' : 'close-circle-fill'" 
    :color="status === 'success' ? 'var(--color-success)' : 'var(--color-error)'" 
    size="32"
  />
  <text>{{ status === 'success' ? '成功' : '失败' }}</text>
</view>
```

#### 6.4 列表项图标
```vue
<view class="list-item">
  <AppIcon icon="calendar" size="32" color="var(--color-text-primary)" />
  <text>日期选择</text>
  <view class="spacer"></view>
  <AppIcon icon="arrow-right" size="24" color="var(--color-text-secondary)" />
</view>
```

### 7. 图标无障碍

1. **语义化**: 使用 `aria-label` 为纯图标按钮提供屏幕阅读器支持
2. **替代文本**: 对于仅有图标的按钮，提供适当的tooltip或标签说明
3. **焦点样式**: 确保可交互的图标有清晰的键盘焦点状态

### 8. 图标加载优化

1. **preload**: 关键页面的核心图标可以预加载以提高首屏体验
2. **缓存**: 利用 easycom 机制的自动缓存功能减少重复加载
3. **按需加载**: 避免不必要的图标加载，特别是在性能敏感的页面

如有其他问题，请参考 `uView Plus.mdc` 文档，获取更详细的图标使用指南和最佳实践。

## 五、组件使用指南

### 1. 通用组件
- **强制使用**: **必须 (MUST)** 优先使用 `src/components/common/` 目录下提供的标准化通用组件（以 `App` 为前缀，如 `AppButton`, `AppCard` 等）。参考 `开发文档和规范.md` 4.1.4 节列出的核心组件。
- **保持一致**: 确保在不同页面中对同一通用组件（例如 `AppButton`）的**使用方式和传入的 props 保持一致**，以实现全局统一的外观和行为。禁止随意修改通用组件的默认样式或行为。
- **组件列表 (示例)**: （请与 `开发文档和规范.md` 4.1.4 保持一致）
- `AppButton`：统一按钮样式 (参考 `开发文档和规范.md` 4.1.4)
- `AppIcon`：统一图标封装 (**必须 (MUST)** 严格遵守 `开发文档和规范.md` 4.4 节规范)
- `AppCard`：统一卡片容器 (参考 `开发文档和规范.md` 4.1.4)
- 所有通用组件在 `pages` 目录中**必须 (MUST)** 保持一致的使用方式，优先使用 `开发文档和规范.md` 4.1.4 中列出的核心通用组件。

### 2. 业务组件
- 遵循就近原则，放在对应页面的components目录
- 组件名称需带有页面前缀
- 业务组件应当使用scoped样式隔离

### 3. 组件命名规范

为确保项目中组件命名的一致性，必须严格遵循以下规则：

#### 文件命名规则
- **通用组件**：**必须 (MUST)** 使用 **PascalCase** 格式，并以 `App` 前缀开头 (遵循 `开发文档和规范.md` 4.1 和 4.6.1 的 easycom 规则)。
  - **正确示例**: `AppButton.vue`, `AppCard.vue`, `AppIcon.vue` (*注意: 图标组件是特例，保持 `AppIcon.vue`*)
  - **错误示例**: `ai-button.vue`, `Button.vue`, `card.vue`
- **业务组件**：使用 PascalCase 格式 (与原文一致)。
  - **正确示例**: `TransactionList.vue`, `BudgetChart.vue`
- **页面私有组件**: 建议使用 PascalCase 并在组件名前加上页面名作为前缀 (如 `BudgetSetupCategoryPicker.vue`)。

#### 组件注册/使用名称规则 (Template中)
- **通用组件**：**必须 (MUST)** 使用 **PascalCase** 格式，以 `App` 前缀开头 (由 easycom 自动处理或全局/局部注册时使用此名称)。
  - **正确示例**: `<AppButton>`, `<AppCard>`, `<AppIcon>` (*图标组件特例*)
  - **错误示例**: `<aiButton>`, `<ai-card>`, `<aiIcon>`
- **业务组件**：使用 PascalCase 格式 (与原文一致)。
  - **正确示例**: `<TransactionList>`, `<BudgetChart>`

### 4. 组件设计系统

为确保UI一致性和提高开发效率，项目实施以下组件设计系统规范：

#### 原子组件规范
- **基础组件**必须通过设计系统验证，符合设计标准
- 每个基础组件必须具备以下特性：
  - 完整的属性（props）接口
  - 统一的事件命名
  - 完善的文档说明
  - 可访问性支持
  - 响应式设计

#### 组件库结构
```
components/
  ├── common/          # 通用原子组件
  │   ├── AppButton.vue     # 按钮组件
  │   ├── AppCard.vue       # 卡片组件
  │   └── AppIcon.vue       # 图标组件
  ├── business/        # 业务组件
  │   ├── CategorySelector.vue  # 类别选择组件
  │   └── ...
  └── pattern/         # 组件模式 (常用组合)
      ├── CardList.vue     # 卡片列表
      ├── SearchHeader.vue # 搜索头部
      └── ...
```

#### 模板库
项目提供以下高频页面模板，开发新页面时优先使用：

- **列表页模板**：包含搜索、筛选、分页功能
- **详情页模板**：包含头部、底部操作栏、内容区
- **表单页模板**：包含验证、提交、取消功能
- **数据展示模板**：包含图表、指标卡片

#### 设计令牌 (Design Tokens)
所有组件必须使用统一的设计令牌，而非硬编码值：

```scss
/* 间距令牌 */
--space-xs: 4rpx;
--space-sm: 8rpx;
--space-md: 16rpx;
--space-lg: 24rpx;
--space-xl: 32rpx;

/* 圆角令牌 */
--radius-sm: 4rpx;
--radius-md: 8rpx;
--radius-lg: 16rpx;
--radius-pill: 999rpx;

/* 动画时间令牌 */
--transition-fast: 0.2s;
--transition-normal: 0.3s;
--transition-slow: 0.5s;
```

### 5. 标题栏统一规范

#### 高度标准
- iOS：88px（包含状态栏）
- Android：56dp
- 小程序：自定义导航栏统一为90px
- H5：60px

#### 实现模板
```vue
<template>
  <!-- 使用平台适配函数获取高度 -->
  <view class="nav-bar" :style="{height: navHeight}">
    <text class="title">{{ title }}</text>
  </view>
</template>

<script setup>
import { getPlatformStyle } from '@/utils/platform'

const navHeight = getPlatformStyle('nav-height') // 对应架构文档的适配器实现
</script>

<style scoped>
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary);
}

.title {
  color: white;
  font-size: 18px;
  font-weight: 500;
}
</style>
```

#### 多端差异处理
```scss
/* 在全局样式中添加 */
.nav-bar {
  /* #ifdef MP-WEIXIN */
  padding-top: 20px; /* 小程序胶囊按钮下移 */
  /* #endif */
  
  /* #ifdef H5 */
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  /* #endif */
}
```

### 6. 按钮规范强化

#### AiButton 增强规范

##### 尺寸标准（单位：px）
| 类型   | 高度 | 左右边距 | 字体大小 | 圆角 |
|--------|------|----------|----------|------|
| Large  | 48   | 32       | 18       | 8    |
| Medium | 40   | 24       | 16       | 6    |
| Small  | 32   | 16       | 14       | 4    |

##### 状态样式
```scss
.ai-button {
  // 基础样式
  &--disabled {
    opacity: 0.6;
    filter: saturate(0.6);
  }
  
  // 按压效果（所有平台必须实现）
  &--active {
    transform: scale(0.98);
  }
  
  // 加载状态
  &--loading {
    .icon {
      animation: spin 1s linear infinite;
    }
  }
}
```

##### 使用示例
```vue
<AiButton 
  size="medium"
  type="primary"
  :loading="isSubmitting"
  @click="handleSubmit"
>
  确认提交
</AiButton>
```

### 7. 间距系统升级

#### 间距原子类系统

##### 间距比例（基于8px基准）
```scss
// _spacing.scss
@for $i from 0 through 10 {
  .m-#{$i} { margin: #{$i * 8}px !important; }
  .p-#{$i} { padding: #{$i * 8}px !important; }
  
  // 各方向细分
  .mt-#{$i} { margin-top: #{$i * 8}px !important; }
  .mr-#{$i} { margin-right: #{$i * 8}px !important; }
  // ...其他方向同理
}

/* 特殊半间距 */
.m-h-1 { margin: 4px !important; }
```

##### 卡片间距规范
| 元素                | 外边距 | 内边距 |
|---------------------|--------|--------|
| 卡片容器            | 16px   | 0      |
| 卡片内容区          | 0      | 16px   |
| 卡片标题与内容间距  | -      | 12px   |
| 卡片之间的间距      | 16px   | -      |

## 六、第三方UI框架集成规范

### 1. 框架选择与配置
- 项目应统一使用一种主要UI框架
- 配置全局主题变量，避免直接修改组件样式
- 构建自定义组件库时封装第三方组件，而非直接使用

### 2. 样式冲突解决方案
- 使用CSS Modules或scoped样式隔离第三方组件
- 自定义组件优先于直接使用框架组件
- 第三方组件样式覆盖应在专门的样式文件中集中管理
- 必要时使用:deep()选择器处理样式穿透

### 3. 框架升级策略
- 维护UI组件测试，确保框架升级不破坏现有功能
- 重大版本升级前进行完整UI回归测试
- 建立UI组件变更日志，记录样式和行为变化

## 七、多页面共存最佳实践

### 1. 样式隔离检查清单
- [ ] 使用scoped特性
- [ ] 检查类名前缀唯一性
- [ ] 避免全局样式污染
- [ ] 第三方组件样式正确引用
- [ ] 样式中不使用!important关键字

### 2. 资源引用检查清单
- [ ] 图标库正确引入
- [ ] 字体文件正确引用
- [ ] 样式变量统一管理
- [ ] 颜色值使用CSS变量引用

### 3. 跨平台适配检查清单
- [ ] iOS样式兼容
- [ ] Android样式兼容
- [ ] H5响应式适配
- [ ] 小程序特殊处理

### 4. 样式模块化清单
- [ ] 大型样式文件拆分为独立的SCSS模块
- [ ] 样式模块使用命名空间前缀
- [ ] 避免在Vue文件中编写大量样式
- [ ] 公共样式提取到共享模块

## 八、常见问题与解决方案

### 1. 样式冲突问题
- 症状：页面样式被其他页面影响
- 解决：检查类名前缀，确保scoped属性正确使用
- 排查：从全局样式、第三方组件样式和命名冲突三个方向排查

### 2. 图标显示异常
- **症状**: 图标无法显示或样式错误
- **解决**: 
  - 检查 uView Plus 是否已正确安装和配置（例如 `main.ts` 中引入 uView Plus 主题 `uni.scss`，`pages.json` 中配置 `easycom` 规则以正确识别 `uview-plus` 组件，包括 `<u-icon>` 或封装它的 `<AppIcon>`）。
  - 确保 `<AppIcon>` 组件的 `icon` (或 `name`) prop 传递的是有效的 uView Plus 图标名称。请参考 uView Plus 官方文档查找正确的图标名称。
  - 检查 `<AppIcon>` 组件本身封装逻辑是否正确，是否能正确将 props 传递给内部的 `<u-icon>`。
  - 确认没有其他全局样式或组件的 scoped 样式意外覆盖了图标的显示 (例如 `font-size: 0`, `display: none` 等)。
  - 检查 `color` prop 是否使用了 CSS 变量，如 `color="var(--text-primary)"`，并确认该 CSS 变量已定义且有效。
  - 查看浏览器控制台或小程序开发者工具的控制台，是否有关于组件未找到或 props 错误的提示。

### 3. 字体加载问题
- 症状：字体未正确应用
- 解决：检查字体文件路径和引用方式

### 4. 样式权重问题
- 症状：样式优先级混乱，无法正常覆盖
- 解决：遵循BEM命名规范，避免使用!important，控制选择器嵌套层级不超过3层

### 5. 样式检查与修复工具

项目提供了自动化工具来检测和修复样式问题：

```bash
# 检查样式问题
npm run style:check

# 自动修复样式问题
npm run style:fix
```

这些工具可以自动检测和修复以下问题：
- 缺少scoped属性的样式
- 过时的deep选择器（如`/deep/`和`::v-deep`）
- 缺少正确前缀的样式类

在开发过程中，应定期运行这些工具确保代码符合规范。

## 九、状态管理与生命周期规范

### 1. 状态隔离原则
- 页面级状态应使用局部状态或独立的状态模块
- 共享状态应明确定义访问边界，避免滥用全局状态
- 切换页面时主动清理不再需要的状态

### 2. 组件生命周期管理
- 在组件卸载时清理所有副作用(如监听器、定时器、订阅)
- 异步操作必须处理组件卸载后的情况，避免内存泄漏
- 使用AbortController或类似机制取消未完成的请求

```vue
// 正确的副作用清理示例
onMounted(() => {
  const timer = setInterval(() => {
    // 定时操作
  }, 1000);
  
  // 确保组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(timer);
  });
});
```

### 3. 页面切换处理
- 页面离开前保存必要状态，返回时恢复
- 使用keep-alive缓存频繁切换的页面，减少重复渲染
- 不同页面间通信应使用事件总线或状态管理工具，避免直接引用 

## 十、增强组件设计规范

### 1. 组件封装原则
- 组件应自包含，不依赖外部样式和状态
- 明确定义组件Props接口，使用TypeScript类型声明
- 组件内部状态不应泄露到外部
- 避免组件之间的直接引用和依赖

### 2. 组件通信规范
- 父子组件通信：Props向下，Events向上
- 兄弟组件通信：通过共同父组件或事件总线
- 跨层级组件通信：使用provide/inject或状态管理工具
- 禁止组件直接操作其他组件的DOM或状态

### 3. 组件复用策略
- 使用组合优于继承
- 可复用逻辑提取为组合式函数(composables)
- 复杂组件应提供完整文档和示例

## 十一、多端适配详细指南

### 1. 平台特性与差异处理

#### iOS平台特性
- 状态栏高度：44pt（导航栏需要预留）
- 底部安全区域：34pt（全面屏设备）
- 点击区域：最小44×44pt（确保可访问性）
- 字体：系统默认字体San Francisco，不支持自定义字体加载

```scss
/* iOS安全区域适配 */
.footer {
  padding-bottom: calc(10px + constant(safe-area-inset-bottom));
  padding-bottom: calc(10px + env(safe-area-inset-bottom));
}

/* iOS状态栏适配 */
.nav-bar {
  padding-top: calc(20px + constant(safe-area-inset-top));
  padding-top: calc(20px + env(safe-area-inset-top));
}
```

#### Android平台特性
- 设备碎片化：需要适配各种屏幕尺寸和分辨率
- 状态栏：高度不固定，约为24-32dp
- 导航手势：底部区域预留8-10dp
- 字体渲染差异：可能需要微调字体大小和行高

```scss
/* Android特定样式 */
/* #ifdef APP-PLUS-ANDROID */
.text-adjust {
  font-size: 15px; /* 稍微调整字体大小 */
  line-height: 1.5;
}
/* #endif */
```

#### 小程序平台特性
- 样式隔离：小程序组件样式默认隔离
- 页面限制：页面层级不能超过10层
- 最小尺寸：胶囊按钮固定高度及间距
- 网络图片：必须使用HTTPS协议

```scss
/* 小程序胶囊按钮适配 */
/* #ifdef MP-WEIXIN */
.header {
  padding-right: 100px;
}
/* #endif */
```

### 2. 端适配器实现

与架构文档中的端适配器对应，UI层需要实现以下适配机制：

#### 统一适配函数
```js
// utils/platform.js
export const getPlatformStyle = (key) => {
  // 共享样式变量，可根据平台返回不同值
  const styleMap = {
    'nav-height': {
      ios: '88px',
      android: '66px',
      h5: '60px',
      weapp: '90px'
    },
    'safe-bottom': {
      ios: 'calc(10px + env(safe-area-inset-bottom))',
      android: '10px',
      h5: '0',
      weapp: '10px'
    }
  };

  const platform = getPlatformType();
  return styleMap[key][platform] || styleMap[key]['ios'];
};

// 在组件中使用
const navHeight = getPlatformStyle('nav-height');
```

#### 统一媒体查询
```scss
/* 设备尺寸断点适配 */
@mixin screen-xs {
  @media screen and (max-width: 375px) {
    @content;
  }
}

@mixin screen-md {
  @media screen and (min-width: 376px) and (max-width: 720px) {
    @content;
  }
}

@mixin screen-lg {
  @media screen and (min-width: 721px) {
    @content;
  }
}

/* 使用示例 */
.card {
  margin: 15px;
  
  @include screen-xs {
    margin: 10px;
    font-size: 14px;
  }
  
  @include screen-lg {
    margin: 20px;
    max-width: 650px;
  }
}
```

## 十二、UI组件性能优化指南

### 1. 渲染性能优化

#### 减少重渲染
- 使用 `computed` 替代复杂的模板表达式
- 为v-for列表项添加唯一key
- 使用v-show替代v-if（频繁切换的场景）
- 避免watch深度监听大型对象

```vue
<!-- 推荐 -->
<view v-for="item in processedItems" :key="item.id">
  {{ item.name }}
</view>

<script setup>
const processedItems = computed(() => {
  return items.value.filter(item => item.visible)
                   .map(item => ({...item, name: formatName(item.name)}));
});
</script>

<!-- 避免 -->
<view v-for="item in items" :key="item.id" v-if="item.visible">
  {{ formatName(item.name) }}
</view>
```

#### 组件优化
- 为大型列表使用虚拟滚动
- 使用v-once渲染静态内容
- 用v-memo缓存基于条件的渲染
- 避免内联样式和过多的动态样式绑定

```vue
<!-- 虚拟列表示例 -->
<recycle-list :items="longList" :item-size="100">
  <template v-slot:item="{item}">
    <view class="list-item">{{ item.title }}</view>
  </template>
</recycle-list>

<!-- 静态内容优化 -->
<view v-once class="static-content">
  <!-- 不会重新渲染的静态内容 -->
</view>
```

### 2. 资源加载优化

#### 图片优化
- 使用适当格式：JPG用于照片，PNG用于图标和插图，SVG用于矢量图形
- 延迟加载：使用懒加载技术
- 图片尺寸：根据显示尺寸准备合适大小的图片
- WebP格式：在支持的平台使用WebP格式

```vue
<!-- 图片懒加载 -->
<image 
  v-for="img in images" 
  :key="img.id"
  :src="img.src" 
  lazy-load
  fade-show
  :style="{width: img.width + 'px'}"
/>
```

#### 初始加载优化
- 应用启动页：使用骨架屏减少白屏感
- 分包加载：小程序场景下使用分包加载
- 预加载策略：提前加载用户可能访问的内容

```scss
/* 骨架屏示例 */
.skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
  
  &-text {
    height: 16px;
    margin-bottom: 8px;
    width: 100%;
  }
  
  &-image {
    aspect-ratio: 16/9;
    width: 100%;
  }
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}
```

### 3. 样式性能优化

#### 选择器优化
- 避免使用通配符选择器 `*`
- 限制选择器嵌套深度（不超过3层）
- 减少使用 `:not()` 等高计算成本选择器
- 避免使用CSS表达式

```scss
/* 推荐 */
.dashboard-card__title {
  color: #333;
}

/* 避免 */
.dashboard .card .content .header .title {
  color: #333;
}
```

#### 动画性能
- 使用transform和opacity进行动画（不触发布局）
- 使用will-change提示浏览器（谨慎使用）
- 对复杂动画使用硬件加速
- 避免同时动画大量元素

```scss
/* 高性能动画 */
.slide-in {
  transform: translateX(0);
  transition: transform 0.3s ease;
  
  &-enter {
    transform: translateX(-100%);
  }
}

/* 硬件加速 */
.accelerated {
  transform: translateZ(0);
  will-change: transform, opacity;
}
```

### 4. 视觉回归测试

确保UI一致性和避免样式回退是保障产品质量的关键，项目采用以下视觉回归测试策略：

#### Storybook组件库
- 使用Storybook记录所有UI组件的各种状态和变体
- 每个组件必须包含以下故事(stories)：
  - 默认状态
  - 各种尺寸变体
  - 各种样式变体（主题色、危险、成功等）
  - 禁用状态
  - 加载状态
  - 错误状态
  - 边缘情况（超长文本、空内容等）

```js
// 组件Story示例 (stories/ai-button.stories.js)
export default {
  title: 'Components/Common/AiButton',
  component: AiButton
};

export const Default = () => ({
  components: { AiButton },
  template: '<ai-button>默认按钮</ai-button>'
});

export const Sizes = () => ({
  components: { AiButton },
  template: `
    <div>
      <ai-button size="mini">迷你按钮</ai-button>
      <ai-button size="small">小按钮</ai-button>
      <ai-button>普通按钮</ai-button>
      <ai-button size="large">大按钮</ai-button>
    </div>
  `
});

// 其他变体...
```

#### 自动化视觉测试
- 使用Chromatic或Percy等工具进行视觉回归测试
- 每次提交都自动比对UI变化
- 建立基准快照，用于跨版本比较

#### 多平台视觉测试
- 针对iOS、Android、H5和小程序进行差异化测试
- 小程序需通过真机截图比对功能差异
- 明确定义各平台可接受的视觉差异范围

#### 视觉测试流程
1. 开发新功能/修改现有组件
2. 运行本地Storybook验证各状态
3. 提交代码触发自动视觉测试
4. 审查视觉差异报告
5. 批准有意的变更，修复非预期变更
6. 更新基准快照

#### 测试覆盖范围优先级
1. **最高优先级**：核心交互组件（按钮、输入框、选择器）
2. **高优先级**：关键业务组件（交易列表、预算进度条）
3. **中优先级**：布局组件（卡片、分割线、标签）
4. **低优先级**：静态展示组件（图标、标题）

## 十三、工具链集成指南

### 1. Stylelint集成

#### 配置文件
创建 `.stylelintrc.json` 文件，设置规则：

```json
{
  "extends": ["stylelint-config-standard-scss"],
  "plugins": ["stylelint-scss"],
  "rules": {
    "indentation": 2,
    "string-quotes": "single",
    "selector-max-id": 0,
    "selector-max-universal": 0,
    "declaration-no-important": true,
    "selector-max-compound-selectors": 3,
    "color-hex-case": "lower",
    "color-hex-length": "short",
    "color-named": "never",
    "selector-no-qualifying-type": true,
    "selector-class-pattern": "^[a-z][a-zA-Z0-9_-]+$|^[a-z][a-zA-Z0-9]*(--|__)[a-z][a-zA-Z0-9]*$"
  }
}
```

#### 自动修复
添加到 `package.json` 中：

```json
"scripts": {
  "lint:style": "stylelint \"src/**/*.{vue,scss,css}\"",
  "lint:style:fix": "stylelint \"src/**/*.{vue,scss,css}\" --fix"
}
```

#### VSCode集成
项目中创建 `.vscode/settings.json` 文件：

```json
{
  "editor.codeActionsOnSave": {
    "source.fixAll.stylelint": true
  },
  "stylelint.validate": ["css", "scss", "vue"],
  "editor.formatOnSave": true
}
```

### 2. ESLint配置

#### 配置文件
创建 `.eslintrc.js` 文件：

```js
module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true
  },
  extends: [
    'plugin:vue/vue3-essential',
    'eslint:recommended',
    '@vue/eslint-config-typescript/recommended'
  ],
  rules: {
    'vue/multi-word-component-names': 'off',
    'vue/no-v-html': 'error',
    'vue/require-default-prop': 'error',
    'vue/no-unused-vars': 'error',
    'vue/html-indent': ['error', 2],
    'vue/html-self-closing': ['error', {
      'html': {
        'void': 'always',
        'normal': 'never',
        'component': 'always'
      }
    }]
  }
}
```

#### 脚本命令
添加到 `package.json`：

```json
"scripts": {
  "lint": "eslint \"src/**/*.{js,ts,vue}\"",
  "lint:fix": "eslint \"src/**/*.{js,ts,vue}\" --fix"
}
```

### 3. husky与lint-staged集成

自动在提交前运行检查，确保代码质量：

#### 安装

```bash
npm install --save-dev husky lint-staged
npx husky install
npx husky add .husky/pre-commit "npx lint-staged"
```

#### 配置

在 `package.json` 中添加：

```json
"lint-staged": {
  "*.{vue,js,ts}": [
    "eslint --fix",
    "git add"
  ],
  "*.{css,scss,vue}": [
    "stylelint --fix",
    "git add"
  ]
}
```

### 4. 自动测试集成

#### 组件测试配置
使用 Vitest 与 Vue Test Utils:

```bash
npm install --save-dev vitest @vue/test-utils happy-dom
```

创建 `vitest.config.ts`:

```ts
import { defineConfig } from 'vitest/config';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
  plugins: [vue()],
  test: {
    environment: 'happy-dom',
    globals: true
  }
});
```

#### 视觉回归测试
使用Storybook与Chromatic:

```bash
# 安装Storybook
npx storybook init

# 创建测试脚本
"scripts": {
  "test:visual": "chromatic --project-token=your-token"
}
```

## 十四、CSS模块化进阶指南

### 1. CSS样式隔离技术详解

#### Scoped CSS工作原理
Scoped CSS是Vue提供的样式隔离方案，通过在DOM元素上添加唯一的属性选择器实现样式隔离：

```vue
<style scoped>
.button { color: blue; }
</style>

<!-- 编译后 -->
<style>
.button[data-v-7a7a37b1] { color: blue; }
</style>
```

**特点：**
- 简单易用，只需添加`scoped`属性
- 样式只影响当前组件的DOM元素
- 父组件样式不会渗透到子组件
- 但可能影响子组件的根节点

#### CSS Modules工作原理
CSS Modules是一种将CSS类名局部化的解决方案，将类名编译为唯一标识符：

```vue
<template>
  <div :class="$style.container">
    <button :class="$style.button">点击我</button>
  </div>
</template>

<style module>
.container { padding: 20px; }
.button { color: blue; }
</style>

<!-- 编译后可能生成 -->
<div class="_container_12jgk_1">
  <button class="_button_12jgk_2">点击我</button>
</div>
```

**特点：**
- 更彻底的样式隔离
- 通过JavaScript引用类名，实现真正的局部作用域
- 支持组合和继承
- 可以设置自定义名称：`<style module="styles">`

### 2. 样式隔离技术选择指南

#### Scoped CSS适用场景
- 快速开发和原型设计
- 简单组件，无需跨组件样式共享
- 需要覆盖第三方组件样式时（配合`:deep()`）
- 新手友好，学习曲线平缓

```vue
<style lang="scss" scoped>
.card {
  // 仅影响当前组件的.card类
  margin: 15px;
  
  // 使用:deep()修改子组件样式（慎用）
  :deep(.title) {
    font-weight: bold;
  }
}
</style>
```

#### CSS Modules适用场景
- 大型应用和团队协作
- 需要严格避免样式冲突
- 组件库开发
- 可复用性和可维护性要求高

```vue
<template>
  <div :class="styles.container">
    <h2 :class="[styles.title, styles.primary]">标题</h2>
  </div>
</template>

<style module="styles" lang="scss">
.container {
  padding: 20px;
}
.title {
  font-size: 18px;
}
.primary {
  color: var(--color-primary, #FF6B35);
}
</style>
```

#### 混合使用策略
在复杂项目中，可根据组件复杂度和重用性混合使用两种技术：

- 公共组件库：优先使用CSS Modules
- 页面组件：使用Scoped CSS
- 共享样式：提取到独立的SCSS模块

### 3. Scoped CSS高级用法

#### 样式穿透
处理子组件或第三方组件样式：

```vue
<style lang="scss" scoped>
/* Vue 3.2+ 推荐写法 */
:deep(.child-class) {
  color: red;
}

/* 旧语法，不推荐 */
::v-deep .child-class {
  color: red;
}

/* 或 */
/deep/ .child-class {
  color: red;
}
</style>
```

#### 全局样式注入
在Scoped组件中定义全局样式（谨慎使用）：

```vue
<style lang="scss" scoped>
:global(.global-class) {
  color: green;
}
</style>
```

#### 插槽内容样式处理
插槽内容来自父组件，但渲染在子组件内，样式处理需要特别注意：

```vue
<!-- 子组件 Child.vue -->
<template>
  <div class="wrapper">
    <slot></slot>
  </div>
</template>

<!-- 父组件 -->
<template>
  <Child>
    <div class="slotted-content">插槽内容</div>
  </Child>
</template>

<style scoped>
/* 使用:slotted适配插槽内容样式 */
:slotted(.slotted-content) {
  color: orange;
}
</style>
```

### 4. CSS Modules高级用法

#### 组合复用样式
```vue
<style module>
.primary { color: var(--color-primary); }
.large { font-size: 18px; }

.button {
  padding: 10px 15px;
  border-radius: 4px;
  composes: primary large; /* 组合多个样式 */
}
</style>
```

#### 命名约定
选择一种命名约定并一致使用：

```vue
<!-- 驼峰命名 -->
<div :class="$style.buttonPrimary"></div>

<!-- 连字符命名 -->
<div :class="$style['button-primary']"></div>
```

#### TypeScript集成
为CSS Modules添加类型支持：

```typescript
// shims-scss.d.ts
declare module '*.module.scss' {
  const classes: { [key: string]: string };
  export default classes;
}

// 组件中使用
import styles from './styles.module.scss';
```

### 5. 样式变量管理增强方案

#### SCSS与CSS变量结合使用
创建统一的变量管理系统：

```scss
// _variables.scss - 源头
$primary-color: #FF6B35;
$primary-color-light: lighten($primary-color, 10%);

:root {
  // 转换为CSS变量供组件使用
  --color-primary: #{$primary-color};
  --color-primary-light: #{$primary-color-light};
}
```

#### 主题切换方案
```scss
// themes/_light.scss
:root {
  --bg-color: #ffffff;
  --text-color: #333333;
}

// themes/_dark.scss
:root.dark-theme {
  --bg-color: #121212;
  --text-color: #f1f1f1;
}

// 组件中使用主题变量
.card {
  background-color: var(--bg-color);
  color: var(--text-color);
  transition: background-color 0.3s, color 0.3s;
}
```

#### 响应式变量方案
根据断点自动调整变量值：

```scss
:root {
  --spacing-base: 16px;
  
  @media (max-width: 768px) {
    --spacing-base: 12px;
  }
  
  @media (max-width: 480px) {
    --spacing-base: 8px;
  }
}

.container {
  padding: var(--spacing-base);
  margin-bottom: calc(var(--spacing-base) * 2);
}
```

### 6. Agent模式下的CSS最佳实践

#### 模板设置与指令
创建标准模板指令，确保Agent生成的组件保持一致的样式隔离方式：

```
[指令模板]
在创建Vue组件时，务必遵循以下样式规则：
1. 使用<style lang="scss" scoped>为默认样式隔离方式
2. 所有类名必须使用页面名前缀，如[页面名]-[元素名]
3. 颜色值必须使用CSS变量，如var(--color-primary, #FF6B35)
4. 第三方组件样式调整使用:deep()选择器
```

#### 常见冲突预防
预先告知Agent常见的样式冲突场景及解决方案：

```
[冲突预防指南]
1. 避免使用通用类名如.container, .wrapper, .item
2. 使用BEM命名法增强类名唯一性：.page-name__element--modifier
3. 禁止在scoped样式中嵌套过深的选择器（不超过3层）
4. 优先使用局部变量，避免更改全局CSS变量
```

#### 调试与测试建议
为Agent提供调试和测试样式隔离的方法：

```
[调试指南]
1. 使用Vue Devtools识别样式作用域问题
2. 检查生成的HTML包含正确的scoped属性(如data-v-xxx)
3. 检查CSS Modules类名是否正确应用
4. 使用浏览器开发工具审查元素计算样式来源
```

### 7. 样式架构建议

#### 大型应用的样式组织
```
styles/
  ├── base/            # 基础样式
  │   ├── _reset.scss  # 重置默认样式
  │   ├── _typography.scss # 文字排版
  │   └── _variables.scss  # 全局变量
  ├── components/      # 组件样式模块
  │   └── ...
  ├── themes/          # 主题相关
  ├── mixins/          # SCSS混合和函数
  └── pages/           # 页面特定样式
```

#### 按需加载策略
```js
// 路由配置
const routes = [
  {
    path: '/analysis',
    component: () => import('../pages/analysis/index.vue'),
    // 配合样式按需加载
    beforeEnter: () => {
      import('../styles/pages/_analysis.scss')
    }
  }
]
```

#### 样式检查与自动化
添加样式检查和自动修复流程：

```json
// package.json
{
  "scripts": {
    "lint:style": "stylelint \"src/**/*.{vue,scss,css}\"",
    "lint:style:fix": "stylelint \"src/**/*.{vue,scss,css}\" --fix",
    "check:style-isolation": "自定义脚本检查样式隔离完整性"
  }
}
```

### 8. 性能优化建议

#### 减少样式规则数量
- 使用组合而非重复定义
- 提取公共样式到基础类
- 使用CSS变量减少重复颜色和尺寸值

#### 样式加载优化
- 关键路径CSS内联到HTML
- 非关键样式延迟加载
- 按路由分割样式文件

#### 媒体查询整合
合并相同断点的媒体查询，减少CSS体积：

```scss
/* 不推荐 */
.header {
  padding: 20px;
  
  @media (max-width: 768px) {
    padding: 10px;
  }
}

.footer {
  margin: 20px;
  
  @media (max-width: 768px) {
    margin: 10px;
  }
}

/* 推荐 */
.header { padding: 20px; }
.footer { margin: 20px; }

@media (max-width: 768px) {
  .header { padding: 10px; }
  .footer { margin: 10px; }
}
```

## 十六、CSS工具类与特殊规则

### 1. CSS工具类系统

CSS工具类（Utility Classes）是一种通过预定义的单一用途类名快速应用样式的方法。项目使用`tools/style/scripts/_utilities.scss`文件集中管理所有工具类。

#### 工具类设计原则

- **单一职责**: 每个工具类只做一件事
- **命名明确**: 类名应清晰表达其功能
- **按需使用**: 仅在需要时才应用工具类
- **文档完备**: 所有工具类必须在文档中列出

#### 核心工具类

```scss
/* 文本对齐 */
.text-left { text-align: left !important; }
.text-center { text-align: center !important; }
.text-right { text-align: right !important; }

/* 字体样式 */
.font-bold { font-weight: 600 !important; }
.font-normal { font-weight: 400 !important; }
.font-light { font-weight: 300 !important; }

/* 显示类型 */
.d-flex { display: flex !important; }
.d-block { display: block !important; }
.d-none { display: none !important; }

/* Flex布局 */
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.justify-start { justify-content: flex-start !important; }
.justify-center { justify-content: center !important; }
.justify-end { justify-content: flex-end !important; }
.justify-between { justify-content: space-between !important; }
.justify-around { justify-content: space-around !important; }
.align-start { align-items: flex-start !important; }
.align-center { align-items: center !important; }
.align-end { align-items: flex-end !important; }

/* 边距 - 遵循8px网格系统 */
.m-0 { margin: 0 !important; }
.mt-1 { margin-top: 8px !important; }
.mr-1 { margin-right: 8px !important; }
.mb-1 { margin-bottom: 8px !important; }
.ml-1 { margin-left: 8px !important; }
/* ... 其他边距类 ... */

/* 可见性 */
.visible { visibility: visible !important; }
.invisible { visibility: hidden !important; }

/* 溢出处理 */
.overflow-hidden { overflow: hidden !important; }
.overflow-auto { overflow: auto !important; }
.overflow-scroll { overflow: scroll !important; }
```

#### 使用指南

- **优先使用变量而非工具类**: 对于页面结构和组件样式，应优先使用CSS变量和组件scoped样式
- **工具类主要用于**:
  - 快速原型开发
  - 临时调整布局
  - 处理特殊情况的样式覆盖
  - 一次性的小调整

```vue
<!-- 正确使用工具类的示例 -->
<view class="transaction-card d-flex justify-between align-center mb-2">
  <text class="transaction-card__title font-bold">购物</text>
  <text class="transaction-card__amount text-right">-¥99.00</text>
</view>
```

### 2. !important规则的特例

虽然项目规范一般**禁止使用`!important`**，但以下情况构成特例：

#### 允许使用!important的场景

1. **工具类定义**: 所有工具类中允许使用`!important`以确保其能覆盖特定样式。
   ```scss
   .d-none { display: none !important; }
   ```

2. **第三方组件样式覆盖**: 当无法通过增加选择器特异性覆盖第三方组件样式时。
   ```scss
   /* 覆盖第三方UI库样式 */
   :deep(.third-party-dropdown .menu-item) {
     background-color: var(--bg-primary) !important;
   }
   ```

3. **多端平台兼容性处理**: 处理不同平台UI差异时的关键样式。
   ```scss
   /* #ifdef MP-WEIXIN */
   .mp-checkbox .wx-checkbox-input {
     border-radius: var(--radius-sm) !important;
   }
   /* #endif */
   ```

4. **全局UI关键修复**: 应用级别的关键UI修复，但应集中在`App.vue`中管理。
   ```scss
   /* App.vue - 隐藏原生TabBar */
   .uni-app--showtabbar uni-tabbar {
     display: none !important;
   }
   ```

#### !important使用规范

即使在上述允许场景中，使用`!important`时也应遵循以下规则：

1. **文档化**: 在代码注释中记录使用`!important`的原因
   ```scss
   /* 强制隐藏原生TabBar，确保自定义TabBar正常显示 */
   .uni-app--showtabbar uni-tabbar {
     display: none !important;
   }
   ```

2. **集中管理**: 项目中的`!important`应在以下位置集中管理：
   - `App.vue`全局样式中
   - `tools/style/scripts/_utilities.scss`工具类中
   - 专门的`overrides.scss`文件中(用于第三方组件)

3. **定期审查**: 在每次依赖更新后审查所有`!important`用法是否仍然必要

4. **替代方案**: 尽可能先尝试以下替代方案：
   - 增加选择器特异性
   - 使用`/deep/`或`:deep()`
   - 组件封装和样式隔离

### 3. 使用工具类的最佳实践

#### 何时使用
- 快速原型开发
- 微调UI元素
- 减少写入额外CSS的需要

#### 何时避免使用
- 创建复杂的布局
- 定义组件的核心样式
- 在大量元素上重复使用相同的工具类组合

#### 结合使用BEM和工具类
```vue
<div class="user-card d-flex mb-2">
  <div class="user-card__avatar mr-2"></div>
  <div class="user-card__content">
    <h3 class="user-card__name font-bold">用户名</h3>
    <p class="user-card__bio text-secondary">用户简介</p>
  </div>
</div>
```

### 4. 全局样式优先级

为避免样式冲突，项目样式应遵循以下优先级顺序（从高到低）：

1. 内联样式（仅用于动态样式）
2. 工具类（带`!important`）
3. 页面/组件scoped样式
4. 全局主题变量
5. 重置样式

## 十四、UI组件特殊规则

### 1. 图表组件与CSS变量处理

**图表组件（如QiunDataCharts等）是一类特殊的UI组件，它们无法直接识别和使用CSS变量**。处理这类组件时，必须遵循以下规则：

#### 图表组件CSS变量转换规则

1. **禁止直接传递CSS变量**：
   ```javascript
   // ❌ 错误：直接传递CSS变量给图表组件
   const chartOptions = {
     color: 'var(--color-primary)',
     background: 'var(--bg-primary)'
   };
   ```

2. **必须通过JS工具函数转换**：
   ```javascript
   // ✅ 正确：使用工具函数读取CSS变量的实际值
   import { getCssVariableValue } from '@/utils/colors';
   
   // 获取实际颜色值
   const primaryColor = getCssVariableValue('--color-primary', '#FF6B35');
   const bgColor = getCssVariableValue('--bg-primary', '#FFFFFF');
   
   // 传递实际颜色值给图表组件
   const chartOptions = {
     color: primaryColor,
     background: bgColor
   };
   ```

3. **在组件挂载时初始化颜色**：
   ```javascript
   onMounted(() => {
     // 初始化图表颜色
     chartColors.value = [
       getCssVariableValue('--color-primary', '#FF6B35'),
       getCssVariableValue('--color-success', '#4CAF50')
     ];
     
     // 更新图表配置
     updateChartOptions();
   });
   ```

4. **监听主题变化重新获取颜色**：
   ```javascript
   // 当主题变化时重新获取颜色值
   watch(isDarkMode, () => {
     // 重新获取颜色值
     primaryColor.value = getCssVariableValue('--color-primary', '#FF6B35');
     // 更新图表
     updateChart();
   });
   ```

#### 其他第三方组件的处理原则

对于其他无法直接支持CSS变量的第三方组件，应采用类似策略：

1. **封装适配器组件**：创建一个包装组件，在其中处理CSS变量转换
2. **使用工具函数**：统一使用项目提供的CSS变量读取工具函数
3. **响应式更新**：监听相关状态(如主题变化)，及时更新组件配置

#### 代码审查与优化原则

在代码审查时，应注意以下几点：

1. **保留颜色转换代码**：不要将获取实际颜色值的代码优化为直接使用CSS变量
2. **不强制统一样式**：对于图表组件，允许使用实际颜色值而非CSS变量
3. **检查工具函数使用**：确保使用了正确的工具函数获取CSS变量值
4. **验证主题切换**：确保在主题切换时，图表颜色能够正确更新

## 十五、更新日志

### v1.5.0 (2024-06-15)
- 新增图表组件CSS变量处理特殊规则
- 完善第三方组件适配文档
- 修正CSS变量处理方法

### v1.4.0 (2024-06-04)
- 新增标题栏统一规范
- 增强按钮组件规范
- 新增间距系统升级方案
- 优化多端导航栏实现

### v1.3.0 (2024-05-27)
- 新增CSS模块化进阶指南
- 增加CSS Modules与Scoped CSS的详细对比与使用指南
- 添加Agent模式开发的专项建议
- 完善样式变量管理和模块化方案

### v1.2.0 (2024-04-13)
- 新增多端适配详细指南
- 新增UI组件性能优化指南
- 新增工具链集成指南
- 完善文档结构和示例代码

### v1.1.0 (2024-04-12)
- 更新CSS变量规范
- 增加样式模块化清单
- 完善图标使用规范
- 新增权重与层级控制指南

### v1.0.0 (2024-04-06)
- 初始版本
- 建立基础规范框架
- 完善多页面样式隔离方案

### 8. 暗黑模式适配

- **颜色变量定义**：颜色变量必须支持暗黑模式切换，在 `variables.scss` 中定义：

```scss
/* 浅色模式（默认）变量 */
:root {
  /* 主题色 */
  --color-primary: #FF6B35;
  --color-primary-light: #FF8A65;
  --color-primary-dark: #E64A19;
  
  /* 辅助色 */
  --color-success: #4CAF50;
  --color-warning: #FFC107;
  --color-error: #F44336;
  --color-info: #2196F3;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F5F5F5;
  --card-bg: #FFFFFF;
  
  /* 文字色 */
  --text-primary: #333333;
  --text-secondary: #666666;
  --text-hint: #999999;
  --text-inverse: #FFFFFF;
  
  /* 边框与分割线 */
  --border-color: #E5E5E5;
  --divider-color: #EEEEEE;
  
  /* 阴影 */
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-raised: 0 4px 16px rgba(0, 0, 0, 0.12);
  --shadow-dialog: 0 8px 30px rgba(0, 0, 0, 0.18);
}

/* 暗黑模式变量 */
@media (prefers-color-scheme: dark) {
  :root {
    /* 主题色 - 在暗黑模式下使用更亮的版本 */
    --color-primary: #FF8A65;
    --color-primary-light: #FFAB91;
    --color-primary-dark: #FF6B35;
    
    /* 辅助色 - 调整亮度确保在暗背景上可辨识 */
    --color-success: #66BB6A;
    --color-warning: #FFCA28;
    --color-error: #EF5350;
    --color-info: #42A5F5;
    
    /* 背景色 */
    --bg-primary: #121212;
    --bg-secondary: #1E1E1E;
    --card-bg: #1E1E1E;
    
    /* 文字色 */
    --text-primary: #F5F5F5;
    --text-secondary: #AAAAAA;
    --text-hint: #777777;
    --text-inverse: #121212;
    
    /* 边框与分割线 */
    --border-color: #333333;
    --divider-color: #2A2A2A;
    
    /* 阴影 - 在暗黑模式下调整阴影颜色和强度 */
    --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-raised: 0 4px 16px rgba(0, 0, 0, 0.4);
    --shadow-dialog: 0 8px 30px rgba(0, 0, 0, 0.5);
  }
}
```

- **组件适配**：组件必须使用CSS变量，而非硬编码颜色值：

```scss
/* 卡片组件示例 */
.card {
  background-color: var(--card-bg, #FFFFFF);
  color: var(--text-primary, #333333);
  box-shadow: var(--shadow-card, 0 2px 8px rgba(0, 0, 0, 0.1));
  border-radius: var(--radius-card, 12px);
}

/* 按钮组件示例 */
.button--primary {
  background-color: var(--color-primary, #FF6B35);
  color: var(--text-inverse, #FFFFFF);
}
```

- **图标和图片适配**：确保图标和图片在暗黑模式下显示正常：

```scss
/* SVG图标颜色适配 */
.icon {
  fill: var(--text-primary, #333333);
  transition: fill 0.3s ease;
}

/* 图片适配 - 针对某些需要在暗黑模式下反转的浅色图片 */
.app-logo {
  transition: filter 0.3s ease;
}

@media (prefers-color-scheme: dark) {
  .app-logo--invert {
    filter: brightness(0.8) invert(1);
  }
}
```

- **手动主题切换**：提供手动切换明暗主题的功能：

```javascript
// src/utils/theme.js
export const THEME = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto' // 跟随系统
};

// 切换主题
export const toggleTheme = (theme) => {
  // 移除现有主题类
  document.documentElement.classList.remove('theme-light', 'theme-dark');
  
  if (theme === THEME.AUTO) {
    // 跟随系统
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
    document.documentElement.classList.add(prefersDark ? 'theme-dark' : 'theme-light');
  } else {
    // 指定主题
    document.documentElement.classList.add(`theme-${theme}`);
  }
  
  // 保存用户偏好
  uni.setStorageSync('theme-preference', theme);
};

// 初始化主题
export const initTheme = () => {
  const savedTheme = uni.getStorageSync('theme-preference') || THEME.AUTO;
  toggleTheme(savedTheme);
  
  // 监听系统主题变化
  if (savedTheme === THEME.AUTO) {
    window.matchMedia('(prefers-color-scheme: dark)').addEventListener('change', (e) => {
      if (uni.getStorageSync('theme-preference') === THEME.AUTO) {
        document.documentElement.classList.remove('theme-light', 'theme-dark');
        document.documentElement.classList.add(e.matches ? 'theme-dark' : 'theme-light');
      }
    });
  }
};
```

- **CSS类选择器适配**：在CSS中使用类选择器实现主题切换：

```scss
/* 通过类选择器覆盖变量 - 放在variables.scss中 */
:root.theme-light {
  --color-primary: #FF6B35;
  --bg-primary: #FFFFFF;
  /* 其他亮色主题变量... */
}

:root.theme-dark {
  --color-primary: #FF8A65;
  --bg-primary: #121212;
  /* 其他暗色主题变量... */
}
```

- **过渡动画**：添加主题切换的过渡动画，提升用户体验：

```scss
/* 全局过渡效果 - 放在App.vue的全局样式中 */
* {
  transition-property: color, background-color, border-color, box-shadow;
  transition-duration: 0.3s;
  transition-timing-function: ease;
}
```

#### 暗黑模式设计原则

1. **对比度保持**：在暗黑模式下，保持文本与背景的充分对比度，确保可读性。
2. **降低亮度**：暗黑模式中减少大面积白色，避免眩光。
3. **避免纯黑**：使用深灰色(#121212)而非纯黑色作为背景，减少眼睛疲劳。
4. **保持品牌一致性**：主题色在暗黑模式下应调整亮度，但保持色相一致。
5. **状态反馈**：确保交互状态（如点击、悬浮）在暗黑模式下依然清晰可辨。

## 14. UI组件特殊规则

### 14.1 图表组件特殊规则

对于图表组件（如QiunDataCharts）等无法直接识别CSS变量的组件，必须通过JS工具函数（如`utils/colors.ts`中的`getCssVariableValue`）读取CSS变量的实际颜色值，并通过props传递给图表组件。禁止在代码审查和优化中将此类颜色转换代码改为直接使用CSS变量。

### 14.2 图标组件使用规范

#### 14.2.1 基本原则

- **统一入口**：必须 (MUST) 通过全局注册的 `<AppIcon>` 组件使用 uView Plus 图标，禁止 (FORBID) 直接使用 `<u-icon>` 组件
- **CSS变量**：图标颜色必须 (MUST) 使用CSS变量，如 `color="var(--color-primary)"`
- **标准尺寸**：图标尺寸应遵循设计规范，使用预设尺寸（28rpx、36rpx等）
- **类别图标专用组件**：记账分类图标必须 (MUST) 使用 `<CategorySelector>` 组件，确保类别图标的一致性

#### 14.2.2 标准用法

```vue
<!-- 正确用法 -->
<AppIcon 
  icon="home" 
  size="28" 
  color="var(--color-primary)"
/>

<!-- 带交互的图标 -->
<AppIcon 
  icon="plus" 
  size="36" 
  color="var(--color-success)" 
  @click="handleAdd"
/>

<!-- 类别选择器 -->
<CategorySelector
  :modelValue="selectedCategoryId"
  @update:modelValue="handleCategoryChange"
/>
```

#### 14.2.3 图标命名规范

- 使用官方提供的图标名称，如 `home`、`photo`、`setting`
- 填充图标使用 `-fill` 后缀，如 `star-fill`（实心星星）
- 方向图标使用方向作为后缀，如 `arrow-left`、`arrow-down`

#### 14.2.4 配置与加载

- **easycom配置**：在 `pages.json` 中配置自动导入

```json
"easycom": {
  "autoscan": true,
  "custom": {
    "^u-icon$": "uview-plus/components/u-icon/u-icon.vue",
    "^u-(.*?)": "uview-plus/components/u-$1/u-$1.vue"
  }
}
```

- **样式引入**：在 `App.vue` 中引入全局样式

```scss
@import "uview-plus/index.scss";
```

- **静态资源**：确保字体文件正确放置在 `static/fonts/` 目录下

#### 14.2.5 禁止的图标实践

- 禁止 (FORBID) 使用任何其他图标库（如 Font Awesome）
- 禁止 (FORBID) 通过 `<i>` 标签使用图标  
- 禁止 (FORBID) 在 `<AppIcon>` 外部直接使用 `<u-icon>` 组件
- 禁止 (FORBID) 硬编码图标颜色（如 `color="#FF6B35"`），必须使用CSS变量
- 禁止 (FORBID) 组件内部调用 `loadIconFont` 或自行加载图标字体
