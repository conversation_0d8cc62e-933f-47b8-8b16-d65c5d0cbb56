{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "moduleResolution": "node", "esModuleInterop": true, "sourceMap": true, "skipLibCheck": true, "importHelpers": true, "allowSyntheticDefaultImports": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@tools/*": ["tools/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"], "types": ["@dcloudio/types"], "strictNullChecks": true, "noImplicitThis": true, "resolveJsonModule": true, "forceConsistentCasingInFileNames": true, "noUnusedLocals": true, "noUnusedParameters": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "eslint.config.mjs"], "exclude": ["node_modules", "dist", "unpackage", "temp-uni-preset"]}