<script setup lang="ts">
import { onError, onLaunch, onShow, onHide } from '@dcloudio/uni-app';
import { ref, watch, onMounted } from 'vue';
// import { useSystemStore } from '@/stores/system.store'; // 暂时注释
import { useUserStore } from '@/stores/user.store';
import { useTransactionStore } from '@/stores/transaction.store';
import { useCategoryStore } from '@/stores/category.store';
import config from '@/config';
import { getCurrentPlatform, PLATFORM, isIOSSimulator } from '@/utils/platform';
import { initMockData } from '@/utils/initMockData';
import { preloadFonts } from '@/utils/font-preloader'; // 保留字体预加载工具

// 在H5环境下预加载字体资源
// #ifdef H5
onMounted(() => {
  console.log('[App.vue] 预加载字体资源');
  preloadFonts(); // 预加载关键字体
});
// #endif

// iOS模拟器环境下特殊处理
// #ifdef APP-PLUS
onMounted(() => {
  const platform = getCurrentPlatform();
  console.log('[App.vue] 当前平台:', platform);
  
  // 特别针对iOS模拟器的处理
  if (platform === PLATFORM.IOS) {
    console.log('[App.vue] 检测到iOS平台，应用特定优化');
    try {
      // @ts-ignore
      if (plus && plus.os.name === 'iOS') {
        // 检测iOS模拟器
        // @ts-ignore
        const model = plus.device.model || '';
        if (model.includes('Simulator')) {
          console.log('[App.vue] 检测到iOS模拟器环境，应用特殊处理');
          document.documentElement.classList.add('ios-simulator');
          // 延迟加载字体，避免iOS模拟器渲染问题
          setTimeout(() => {
            preloadFonts();
          }, 500);
        }
      }
    } catch (error) {
      console.error('[App.vue] iOS平台检测失败:', error);
    }
  }
});
// #endif

onLaunch(() => {
  console.log('[DEBUG] App Launch');

  // 使用 uni-app API 获取初始主题状态
  try {
    const systemInfo = uni.getSystemInfoSync();
    if (systemInfo.osTheme) {
      const isDarkMode = systemInfo.osTheme === 'dark';
      console.log(`[DEBUG] 系统主题: ${isDarkMode ? '深色' : '浅色'}`);
      
      // 这里可以根据系统主题设置应用主题
      // const systemStore = useSystemStore();
      // systemStore.setTheme(isDarkMode ? 'dark' : 'light');
    }
  } catch (error) {
    console.error('[DEBUG] 获取系统主题失败:', error);
  }

  // 初始化mock数据 - 只在开发环境执行
  if (import.meta.env.DEV && import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    console.log('[DEBUG] 初始化Mock数据');
    initMockData(); // 如果没有localLedgerDB数据，初始化默认数据
  }

  // 初始化用户数据和交易数据
  const userStore = useUserStore();
  const transactionStore = useTransactionStore();
  const categoryStore = useCategoryStore();

  // 加载缓存的用户信息和交易数据
  userStore.loadUserInfo();
  transactionStore.loadTransactions();
  categoryStore.loadCategories();

  console.log('[DEBUG] 应用初始化完成');
});

onShow(() => {
  console.log('[DEBUG] App Show');
});

onHide(() => {
  console.log('[DEBUG] App Hide');
});

onError((err) => {
  console.error('[DEBUG] App Error:', err);
});
</script>

<template>
  <!-- App.vue 在 uni-app 中不需要页面模板内容，但必须有 template 标签 -->
</template>

<style lang="scss">
/* 必须在顶部导入所有样式，以确保@use规则位于其他规则之前 */
@use "src/assets/styles/variables.scss" as *;
@use "src/uni.scss" as *;
@use "uview-plus/index.scss" as *;

/* 基础重置与全局样式 */
body,
.uni-page-body {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 全局非scoped样式 */
page {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
    Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: var(--bg-primary, #ffffff);
  color: var(--text-primary, #333333);
  font-size: var(--font-size-base, 14px);
  line-height: 1.5;
  box-sizing: border-box;
}

/* 确保uni-app框架基础HTML元素样式正确 */
html {
  font-size: 16px;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
}

/* 图标字体声明 - 针对不同平台有不同的字体路径处理 */
/* 原始的H5专用声明 */
/* #ifdef H5 */
@font-face {
  font-family: 'uicon-iconfont';
  src: url('/static/fonts/uicon-iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
/* #endif */

/* iOS专用声明 - 特别针对iOS模拟器 */
/* #ifdef APP-PLUS */
@font-face {
  font-family: 'uicon-iconfont';
  /* iOS环境使用的字体路径 */
  src: url('static/fonts/uicon-iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
/* #endif */

/* 小程序专用字体声明 */
/* #ifdef MP */
@font-face {
  font-family: 'uicon-iconfont';
  src: url('~@/static/fonts/uicon-iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
/* #endif */

/* 确保图标在各种使用场景下正确显示 */
.icon-demo {
  width: 56rpx !important;
  height: 56rpx !important;
  display: flex !important;
  align-items: center;
  justify-content: center;
}

/* 添加iOS平台特定样式 */
/* #ifdef APP-PLUS */
.ios-platform {
  --status-bar-height: var(--ios-status-bar-height);
  --nav-height: var(--ios-nav-height);
  --tab-bar-height: var(--ios-tab-bar-height);
  --safe-area-bottom: var(--ios-safe-area-bottom);
  --safe-area-top: var(--ios-safe-area-top);
}

/* iOS模拟器特定样式 */
.ios-simulator {
  /* 修复iOS模拟器样式问题 */
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', sans-serif;
}
/* #endif */
</style>