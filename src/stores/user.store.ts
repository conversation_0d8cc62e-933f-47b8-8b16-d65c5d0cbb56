import type { LoginCredentials, LoginResponseData, UserInfo } from '@/types/api';
import { login as apiLogin, logout as apiLogout } from '@/api/auth'; // 导入真实的API或Mock包装器
import config from '@/config'; // 导入配置
import { storage } from '@/utils/storage'; // 导入统一存储工具
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

// 使用 setup store 定义
export const useUserStore = defineStore(
  'user',
  () => {
    // State
    const token = ref<string | null>(null);
    const userInfo = ref<UserInfo | null>(null);
    const hasSetGesture = ref(false);

    // Getters
    const isLoggedIn = computed(() => !!token.value && !!userInfo.value);
    const userId = computed(() => userInfo.value?.userId || '');
    const userName = computed(() => userInfo.value?.name || '');
    const avatar = computed(() => userInfo.value?.avatar || '');

    // Actions - 不再需要手动存储，由持久化插件自动完成
    function setToken(newToken: string | null) {
      token.value = newToken;
    }

    function setUserInfo(newUserInfo: UserInfo | null) {
      userInfo.value = newUserInfo;
    }

    function setGestureStatus(hasSet: boolean) {
      hasSetGesture.value = hasSet;
    }

    // 登录
    async function login(credentials: LoginCredentials): Promise<LoginResponseData | false> {
      try {
        console.log('[Store] Login attempt with:', credentials);
        const response = await apiLogin(credentials);

        if (response && response.token) {
          console.log('[Store] Login successful, token received');
          // 设置令牌
          setToken(response.token);

          // 如果API返回了用户信息，直接使用
          if (response.userInfo) {
            console.log('[Store] UserInfo received from API:', response.userInfo);
            setUserInfo(response.userInfo);

            // 如果是老用户(不需要设置手势密码)，则假设他们已经设置了手势密码
            // 注意：这个逻辑需要根据实际业务情况调整
            if (response.needSetupSecurity === false) {
              setGestureStatus(true);
            }
            else {
              setGestureStatus(false);
            }
          }

          return response; // 返回完整响应对象，以便调用方处理后续逻辑
        }

        console.log('[Store] Login failed, no token received');
        return false;
      }
      catch (error) {
        console.error('[Store] Login error:', error);
        return false;
      }
    }

    // 退出登录
    async function logout() {
      try {
        // 调用API登出
        await apiLogout();

        // 清除状态
        clearState();

        return true;
      }
      catch (error) {
        console.error('[Store] Logout error:', error);
        // 即使API调用失败，也要清除本地状态
        clearState();
        return true; // 返回成功，因为前端状态已清除
      }
    }

    // 清空状态
    function clearState() {
      setToken(null);
      setUserInfo(null);
      setGestureStatus(false);
    }

    // 加载初始状态（可选，如果使用持久化插件可能不需要）
    function loadFromStorage() {
      try {
        // 这部分逻辑只在不使用持久化插件时需要
        // 如果使用了持久化插件，这部分代码可以删除
        const storedToken = storage.getItem<string>(
          `${config.storage.prefix}${config.storage.keys.TOKEN}`,
        );
        if (storedToken) {
          setToken(storedToken);
        }

        const storedUserInfo = storage.getItem<UserInfo>(
          `${config.storage.prefix}${config.storage.keys.USER_INFO}`,
        );
        if (storedUserInfo) {
          setUserInfo(storedUserInfo);
        }

        const hasSetGestureStr = storage.getItem<string>(
          `${config.storage.prefix}${config.storage.keys.GESTURE_PASSWORD}`,
        );
        if (hasSetGestureStr) {
          setGestureStatus(hasSetGestureStr === 'true');
        }
      }
      catch (error) {
        console.error('[Store] Error loading state from storage:', error);
        clearState(); // 清除可能损坏的状态
      }
    }

    return {
      // 状态
      token,
      userInfo,
      hasSetGesture,
      // Getters
      isLoggedIn,
      userId,
      userName,
      avatar,
      // Actions
      setToken,
      setUserInfo,
      setGestureStatus,
      login,
      logout,
      clearState,
      loadFromStorage,
    };
  },
  {
    // 配置持久化选项
    persist: {
      key: `${config.storage.prefix}user`,
      // 使用统一的存储工具，确保多端兼容
      storage: {
        getItem(key) {
          return storage.getItem(key);
        },
        setItem(key, value) {
          storage.setItem(key, value);
        },
        removeItem(key) {
          storage.removeItem(key);
        },
      },
      paths: ['token', 'userInfo', 'hasSetGesture'],
    },
  },
);
