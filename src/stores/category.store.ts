import config from '@/config'; // 导入配置
import { storage } from '@/utils/storage'; // 导入统一存储工具
// import { getIconPrefix, getSafeIconName } from '@/utils/icon-manager'; // REMOVED
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

// 定义分类接口
export interface Category {
  id: string | number;
  name: string;
  icon: string; // uView Plus 图标名称 (例如: 'shopping-cart', 'car-fill')
  color: string; // 图标的前景色 (例如: 'var(--color-white)')
  iconContainerBgColor: string; // 图标容器的背景颜色 (例如: 'var(--color-primary)')
  type: 'income' | 'expense'; // 分类类型
}

// 初始默认分类数据 (可以放在这里或单独文件导入) - 此数组将被移除，逻辑合并到下面的默认支出/收入分类中
// const defaultCategories: Category[] = [ 
// ... (original defaultCategories content removed)
// ];

// 支出分类
export const defaultExpenseCategories: Category[] = [
  { id: 'cat-food', name: '餐饮', icon: 'shop', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-primary, #FF6B35)', type: 'expense' },
  { id: 'cat-shopping', name: '购物', icon: 'shopping-cart', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-info, #0EA5E9)', type: 'expense' },
  { id: 'cat-daily-use', name: '日用', icon: 'bag', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-success, #10B981)', type: 'expense' },
  { id: 'cat-transport', name: '交通', icon: 'car', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-error, #E11D48)', type: 'expense' },
  { id: 'cat-housing', name: '住房', icon: 'home', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-warning, #FFC107)', type: 'expense' },
  { id: 'cat-entertainment', name: '娱乐', icon: 'play-circle', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-purple, #9A6AFF)', type: 'expense' },
  { id: 'cat-study', name: '学习', icon: 'edit-pen', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-cyan, #06B6D4)', type: 'expense' },
  { id: 'cat-medical', name: '医疗', icon: 'heart', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-pink, #EC4899)', type: 'expense' },
  { id: 'cat-communication', name: '通讯', icon: 'phone', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-blue, #3B82F6)', type: 'expense' },
  { id: 'cat-gifts', name: '礼金', icon: 'gift', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-pink, #EC4899)', type: 'expense' },
  { id: 'cat-beauty', name: '美容', icon: 'woman', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-purple-light, #D8B4FE)', type: 'expense' },
  { id: 'cat-travel', name: '旅行', icon: 'map', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-teal, #14B8A6)', type: 'expense' },
  { id: 'cat-pet', name: '宠物', icon: 'tags', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-brown, #8B572A)', type: 'expense' },
  { id: 'cat-other-expense', name: '其他', icon: 'more-circle', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-gray, #6B7280)', type: 'expense' },
];

// 收入分类
export const defaultIncomeCategories: Category[] = [
  { id: 'cat-salary', name: '工资', icon: 'rmb-circle', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-success, #10B981)', type: 'income' },
  { id: 'cat-bonus', name: '奖金', icon: 'gift', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-primary, #FF6B35)', type: 'income' },
  { id: 'cat-investment', name: '投资', icon: 'level', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-info, #0EA5E9)', type: 'income' },
  { id: 'cat-parttime', name: '兼职', icon: 'account', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-purple, #9A6AFF)', type: 'income' },
  { id: 'cat-refund', name: '退款', icon: 'arrow-upward', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-error, #E11D48)', type: 'income' },
  { id: 'cat-others-income', name: '其他收入', icon: 'plus', color: 'var(--color-white, #FFFFFF)', iconContainerBgColor: 'var(--color-gray, #6B7280)', type: 'income' },
];

// 导入TransactionCategory接口类型
export interface TransactionCategory {
  id: string | number
  name: string
  icon: string
  type: 'income' | 'expense'
}

// 拓展Category类型，添加resolvedIcon字段
export interface CategoryWithResolvedIcon extends Category {
  resolvedIcon: string
}

export const useCategoryStore = defineStore(
  'category',
  () => {
    // State
    const expenseCategories = ref<Category[]>([...defaultExpenseCategories]);
    const incomeCategories = ref<Category[]>([...defaultIncomeCategories]);
    const customCategories = ref<Category[]>([]);
    const currentType = ref<'income' | 'expense'>('expense');
    const selectedCategoryId = ref<string | number | null>(null);
    const isLoading = ref(false);
    const error = ref<string | null>(null);

    // Getters
    const allCategories = computed(() => [
      ...expenseCategories.value,
      ...incomeCategories.value,
      ...customCategories.value,
    ]);

    const currentCategories = computed(() => currentType.value === 'income' ? incomeCategories.value : expenseCategories.value);

    const selectedCategory = computed((): Category | undefined => {
      if (!selectedCategoryId.value)
        return undefined;

      return [
        ...expenseCategories.value,
        ...incomeCategories.value,
        ...customCategories.value,
      ].find(cat => cat.id === selectedCategoryId.value);
    });

    const categoryToTransactionCategory = (category: Category): TransactionCategory => ({
        id: category.id,
        name: category.name,
        icon: category.icon,
        type: category.type,
      });

    const currentCategoriesWithResolvedIcons = computed((): Category[] => {
      const categories = currentType.value === 'income' ? incomeCategories.value : expenseCategories.value;
      // No mapping needed if category.icon is already the final uView Plus name
      return categories;
    });

    const allCategoriesWithResolvedIcons = computed((): Category[] => {
      // No mapping needed
      return [...expenseCategories.value, ...incomeCategories.value, ...customCategories.value];
    });

    const selectedCategoryWithResolvedIcon = computed((): Category | undefined => {
      if (!selectedCategoryId.value) return undefined;

      const category = allCategories.value.find(cat => cat.id === selectedCategoryId.value);

      if (!category) return undefined;
      // No mapping needed
      return category;
    });

    // Actions
    function setCurrentType(type: 'income' | 'expense') {
      currentType.value = type;
      // 重置选中状态
      selectedCategoryId.value = null;
    }

    function selectCategory(categoryId: string | number | null) {
      selectedCategoryId.value = categoryId;
    }

    function addCustomCategory(category: Omit<Category, 'id'>) {
      const newId = `custom-${Date.now()}`;
      const newCategory: Category = {
        ...category,
        id: newId,
      };

      customCategories.value.push(newCategory);
      return newCategory;
    }

    function updateCategory(id: string | number, updates: Partial<Omit<Category, 'id'>>) {
      // 确定分类属于哪个数组
      const isExpense = expenseCategories.value.some(cat => cat.id === id);
      const isIncome = incomeCategories.value.some(cat => cat.id === id);
      const isCustom = customCategories.value.some(cat => cat.id === id);

      // 更新对应数组中的分类
      if (isExpense) {
        expenseCategories.value = expenseCategories.value.map(cat =>
          cat.id === id ? { ...cat, ...updates } : cat,
        );
      }
      else if (isIncome) {
        incomeCategories.value = incomeCategories.value.map(cat =>
          cat.id === id ? { ...cat, ...updates } : cat,
        );
      }
      else if (isCustom) {
        customCategories.value = customCategories.value.map(cat =>
          cat.id === id ? { ...cat, ...updates } : cat,
        );
      }
    }

    function removeCustomCategory(id: string | number) {
      customCategories.value = customCategories.value.filter(cat => cat.id !== id);

      // 如果删除的是当前选中的分类，重置选中状态
      if (selectedCategoryId.value === id) {
        selectedCategoryId.value = null;
      }
    }

    function resetToDefaultCategories() {
      expenseCategories.value = [...defaultExpenseCategories];
      incomeCategories.value = [...defaultIncomeCategories];
      customCategories.value = [];
      selectedCategoryId.value = null;
    }

    function getCategoryById(id: string | number): Category | undefined {
      return allCategories.value.find(cat => cat.id === id);
    }

    function getCategoriesByType(type: 'income' | 'expense'): Category[] {
      return type === 'income' ? incomeCategories.value : expenseCategories.value;
    }

    function clearState() {
      expenseCategories.value = [...defaultExpenseCategories];
      incomeCategories.value = [...defaultIncomeCategories];
      customCategories.value = [];
      currentType.value = 'expense';
      selectedCategoryId.value = null;
      isLoading.value = false;
      error.value = null;
    }

    // 新增：异步加载分类数据（兼容API/本地/默认）
    async function fetchCategories() {
      try {
        isLoading.value = true;
        // 如果已经有数据，直接返回
        if (expenseCategories.value.length && incomeCategories.value.length) {
          return Promise.resolve();
        }
        
        // 这里可以扩展为从本地存储或API加载
        // 先检查本地存储是否有数据
        const storageKey = `${config.storage.prefix}category`;
        let stored = null;
        try {
          stored = storage.getItem(storageKey);
          if (stored) {
            const data = stored;
            if (data.expenseCategories?.length) 
              expenseCategories.value = data.expenseCategories;
            if (data.incomeCategories?.length)
              incomeCategories.value = data.incomeCategories;
            if (data.customCategories?.length)
              customCategories.value = data.customCategories;
              
            console.log('分类数据从本地存储加载成功', data);
            return Promise.resolve();
          }
        } catch (e) {
          console.warn('本地分类数据读取失败', e);
        }
        
        // 未找到存储数据，使用默认数据
        console.log('使用默认分类数据');
        expenseCategories.value = [...defaultExpenseCategories];
        incomeCategories.value = [...defaultIncomeCategories];
        customCategories.value = [];
        
        return Promise.resolve();
      } catch (err) {
        console.error('加载分类数据失败', err);
        error.value = '加载分类数据失败';
        return Promise.reject(err);
      } finally {
        isLoading.value = false;
      }
    }

    return {
      // State
      expenseCategories,
      incomeCategories,
      customCategories,
      currentType,
      selectedCategoryId,
      isLoading,
      error,

      // Getters
      allCategories,
      currentCategories,
      selectedCategory,
      categoryToTransactionCategory,
      currentCategoriesWithResolvedIcons,
      allCategoriesWithResolvedIcons,
      selectedCategoryWithResolvedIcon,

      // Actions
      setCurrentType,
      selectCategory,
      addCustomCategory,
      updateCategory,
      removeCustomCategory,
      resetToDefaultCategories,
      getCategoryById,
      getCategoriesByType,
      clearState,
      fetchCategories,
    };
  },
  {
    persist: {
      key: `${config.storage.prefix}category`,
      // 使用统一的存储工具，确保多端兼容
      storage: {
        getItem(key) {
          return storage.getItem(key);
        },
        setItem(key, value) {
          storage.setItem(key, value);
        },
        removeItem(key) {
          storage.removeItem(key);
        },
      },
      paths: ['expenseCategories', 'incomeCategories', 'customCategories'],
    },
  },
);
