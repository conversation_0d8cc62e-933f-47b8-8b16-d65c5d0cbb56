import { defineStore } from 'pinia';
import { 
  getTransactions as apiGetTransactions, 
  getMonthlyTransactions as apiGetMonthlyTransactions,
  createTransaction as apiCreateTransaction,
  updateTransaction as apiUpdateTransaction,
  deleteTransaction as apiDeleteTransaction,
  getTransactionById as apiGetTransactionById 
} from '@/api/transaction';
import transactionMock from '@/api/mocks/transaction.mock';
import type { 
  Transaction, 
  TransactionListParams,
  TransactionListResponse,
  CreateTransactionPayload,
  UpdateTransactionPayload
} from '@/types/transaction';

const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true';

export const useTransactionStore = defineStore('transaction', {
  state: () => ({
    transactions: [] as Transaction[],
    monthlyTransactions: [] as Transaction[],
    currentTransaction: null as Transaction | null,
    recentTransactions: [] as Transaction[],
    isLoading: false,
    error: null as string | null,
    // 分页相关
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0
    }
  }),

  getters: {
    // 获取最近的几笔交易（用于首页展示）
    getRecentTransactions: (state) => (limit: number = 3): Transaction[] => {
      return state.recentTransactions.slice(0, limit);
    },

    // 获取某个日期的交易记录
    getTransactionsByDate: (state) => (dateStr: string): Transaction[] => {
      return state.monthlyTransactions.filter(tx => tx.date === dateStr);
    },

    // 按支出/收入统计金额
    getTotalsByType: (state) => {
      const income = state.monthlyTransactions
        .filter(tx => tx.type === 'income')
        .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
        
      const expense = state.monthlyTransactions
        .filter(tx => tx.type === 'expense')
        .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
        
      return { income, expense, balance: income - expense };
    }
  },

  actions: {
    // 获取交易列表（带分页和筛选）
    async fetchTransactions(params: TransactionListParams) {
      this.isLoading = true;
      this.error = null;
      
      try {
        const response = useMock 
          ? await transactionMock.getTransactions(params)
          : await apiGetTransactions(params);
        
        this.transactions = response.items;
        this.pagination = response.pagination;
        
        // 更新最近交易（如果获取的是最新数据）
        if (!params.startDate && !params.endDate) {
          this.recentTransactions = response.items.slice(0, 5);
        }
        
        return response;
      } catch (error) {
        console.error('Error fetching transactions:', error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取月度交易数据
    async fetchMonthlyTransactions(year: number, month: number) {
      this.isLoading = true;
      this.error = null;
      
      try {
        const transactions = useMock
          ? await transactionMock.getMonthlyTransactions(year, month)
          : await apiGetMonthlyTransactions(year, month);
          
        this.monthlyTransactions = transactions;
        
        // 更新最近交易
        this.recentTransactions = transactions
          .sort((a, b) => b.date.localeCompare(a.date))
          .slice(0, 5);
          
        return transactions;
      } catch (error) {
        console.error(`Error fetching monthly transactions for ${year}-${month}:`, error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取月度交易数据 - 支持通过参数对象传递
    async fetchMonthlyTransactions(params: { year: number; month: number; categoryId?: string }) {
      this.isLoading = true;
      this.error = null;
      
      try {
        // 确保解构参数
        const { year, month, categoryId } = params;
        
        if (typeof year !== 'number' || typeof month !== 'number') {
          throw new Error('Invalid year or month parameter');
        }
        
        console.log(`Fetching monthly transactions for ${year}-${month}${categoryId ? `, category: ${categoryId}` : ''}`);
        
        // 从API或Mock获取数据
        let transactions = useMock
          ? await transactionMock.getMonthlyTransactions(year, month)
          : await apiGetMonthlyTransactions(year, month);
          
        // 如果有类别筛选，在前端进行筛选
        if (categoryId) {
          transactions = transactions.filter(tx => {
            if (typeof tx.category === 'object') {
              return tx.category.id === categoryId;
            }
            return false;
          });
        }
        
        this.monthlyTransactions = transactions;
        
        // 更新最近交易
        this.recentTransactions = transactions
          .sort((a, b) => b.date.localeCompare(a.date))
          .slice(0, 5);
          
        return transactions;
      } catch (error) {
        console.error(`Error fetching monthly transactions:`, error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 获取单个交易详情
    async fetchTransactionById(id: string) {
      this.isLoading = true;
      this.error = null;
      
      try {
        const transaction = useMock
          ? await transactionMock.getTransactionById(id)
          : await apiGetTransactionById(id);
          
        this.currentTransaction = transaction;
        return transaction;
      } catch (error) {
        console.error(`Error fetching transaction ${id}:`, error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 创建新交易
    async addTransaction(payload: CreateTransactionPayload) {
      this.isLoading = true;
      this.error = null;
      
      try {
        const newTransaction = useMock
          ? await transactionMock.createTransaction(payload)
          : await apiCreateTransaction(payload);
        
        // 更新本地状态
        this.transactions = [newTransaction, ...this.transactions];
        
        // 如果是当前月度数据，也更新月度数据
        const [txYear, txMonth] = newTransaction.date.split('-').map(Number);
        const now = new Date();
        if (txYear === now.getFullYear() && txMonth === now.getMonth() + 1) {
          this.monthlyTransactions = [newTransaction, ...this.monthlyTransactions];
        }
        
        // 更新最近交易
        this.recentTransactions = [newTransaction, ...this.recentTransactions].slice(0, 5);
        
        return newTransaction;
      } catch (error) {
        console.error('Error adding transaction:', error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 更新交易
    async updateTransaction(id: string, payload: UpdateTransactionPayload) {
      this.isLoading = true;
      this.error = null;
      
      try {
        const updatedTransaction = useMock
          ? await transactionMock.updateTransaction(id, payload)
          : await apiUpdateTransaction(id, payload);
        
        // 更新本地状态
        this.transactions = this.transactions.map(tx => 
          tx.id === id ? updatedTransaction : tx
        );
        
        this.monthlyTransactions = this.monthlyTransactions.map(tx => 
          tx.id === id ? updatedTransaction : tx
        );
        
        this.recentTransactions = this.recentTransactions.map(tx => 
          tx.id === id ? updatedTransaction : tx
        );
        
        if (this.currentTransaction?.id === id) {
          this.currentTransaction = updatedTransaction;
        }
        
        return updatedTransaction;
      } catch (error) {
        console.error(`Error updating transaction ${id}:`, error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 删除交易
    async removeTransaction(id: string) {
      this.isLoading = true;
      this.error = null;
      
      try {
        useMock
          ? await transactionMock.deleteTransaction(id)
          : await apiDeleteTransaction(id);
        
        // 更新本地状态
        this.transactions = this.transactions.filter(tx => tx.id !== id);
        this.monthlyTransactions = this.monthlyTransactions.filter(tx => tx.id !== id);
        this.recentTransactions = this.recentTransactions.filter(tx => tx.id !== id);
        
        if (this.currentTransaction?.id === id) {
          this.currentTransaction = null;
        }
        
        return true;
      } catch (error) {
        console.error(`Error removing transaction ${id}:`, error);
        this.error = error instanceof Error ? error.message : String(error);
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 清空状态（退出登录等场景使用）
    clearState() {
      this.transactions = [];
      this.monthlyTransactions = [];
      this.currentTransaction = null;
      this.recentTransactions = [];
      this.isLoading = false;
      this.error = null;
      this.pagination = {
        page: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0
      };
    }
  }
}); 