/**
 * 聊天状态管理
 */
import { defineStore } from 'pinia';
import { 
  Message, 
  MessageType, 
  MessageSource, 
  MessageStatus,
  ChatMessage,
  TextMessage,
  ImageMessage,
  VoiceMessage,
  TransactionMessage
} from '@/types/chat';
import * as chatApi from '@/api/chat';
import { v4 as uuidv4 } from 'uuid';

export const useChatStore = defineStore('chat', {
  state: () => ({
    /** 消息列表 */
    messages: [] as Message[],
    /** 消息是否正在加载 */
    loading: false,
    /** 是否还有更多历史消息 */
    hasMore: true,
    /** 是否显示语音输入界面 */
    showVoiceInput: false,
    /** 是否显示更多功能面板 */
    showFunctionsPanel: false,
    /** 是否正在发送消息 */
    sending: false,
    /** 是否显示图片预览 */
    showImageViewer: false,
    /** 预览的图片URL */
    previewImageUrl: '',
    /** 分页参数 */
    pagination: {
      pageSize: 20,
      pageNum: 1,
      total: 0,
      pages: 0
    }
  }),

  getters: {
    /**
     * 最后一条消息
     */
    lastMessage(state): Message | undefined {
      if (state.messages.length === 0) return undefined;
      return state.messages[state.messages.length - 1];
    },

    /**
     * 检查是否有AI正在输入
     */
    isAiTyping(state): boolean {
      const last = this.lastMessage;
      if (!last) return false;
      
      return last.source === MessageSource.AI && 
             last.status === MessageStatus.SENDING;
    }
  },

  actions: {
    /**
     * 加载聊天历史记录
     * @param refresh 是否刷新（重新加载第一页）
     */
    async loadMessages(refresh = false) {
      if (this.loading) return;
      
      try {
        this.loading = true;
        
        if (refresh) {
          this.pagination.pageNum = 1;
          this.hasMore = true;
        }
        
        // 如果没有更多消息，直接返回
        if (!this.hasMore && !refresh) {
          this.loading = false;
          return;
        }
        
        const response = await chatApi.getChatHistory({
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.pageNum,
        });
        
        if (response.code === 0) {
          const { messages, total, pages } = response.data;
          
          this.pagination.total = total;
          this.pagination.pages = pages;
          
          // 更新hasMore状态
          this.hasMore = this.pagination.pageNum < this.pagination.pages;
          
          if (refresh) {
            // 如果是刷新，直接替换所有消息
            this.messages = messages;
          } else {
            // 合并消息并去重
            const newMessages = [...messages, ...this.messages];
            const uniqueMessages = this.removeDuplicateMessages(newMessages);
            this.messages = uniqueMessages;
          }
          
          // 增加页码，为下次加载做准备
          this.pagination.pageNum++;
        }
      } catch (error) {
        console.error('加载聊天历史失败', error);
      } finally {
        this.loading = false;
      }
    },
    
    /**
     * 去除重复消息
     */
    removeDuplicateMessages(messages: Message[]): Message[] {
      const uniqueMap = new Map<string, Message>();
      
      // 以消息ID为键去重
      messages.forEach(message => {
        uniqueMap.set(message.id, message);
      });
      
      // 转回数组并按时间排序
      return Array.from(uniqueMap.values())
        .sort((a, b) => a.createdAt - b.createdAt);
    },
    
    /**
     * 发送文本消息
     * @param content 消息内容
     */
    async sendTextMessage(content: string) {
      if (!content.trim() || this.sending) return;
      
      // 创建临时消息对象，显示在UI中
      const tempMessage: TextMessage = {
        id: uuidv4(),
        type: MessageType.TEXT,
        source: MessageSource.USER,
        content: content.trim(),
        createdAt: Date.now(),
        status: MessageStatus.SENDING
      };
      
      // 添加到消息列表
      this.messages.push(tempMessage);
      
      // 添加AI正在输入的临时消息
      this.addTypingIndicator();
      
      try {
        this.sending = true;
        
        // 构造API请求参数
        const message: ChatMessage = {
          type: MessageType.TEXT,
          content: content.trim()
        };
        
        // 发送API请求
        const response = await chatApi.sendMessage(message);
        
        // 更新发送状态
        this.updateMessageStatus(tempMessage.id, MessageStatus.SENT);
        
        // 移除输入指示器，显示实际回复
        this.removeTypingIndicator();
        
        // 如果是交易类消息，需要特殊处理
        if (response.data.type === MessageType.TRANSACTION) {
          this.handleTransactionResponse(response.data as TransactionMessage);
        } else {
          // 添加AI回复
          this.messages.push(response.data);
        }
      } catch (error) {
        console.error('发送消息失败', error);
        this.updateMessageStatus(tempMessage.id, MessageStatus.FAILED);
        this.removeTypingIndicator();
      } finally {
        this.sending = false;
      }
    },
    
    /**
     * 处理交易类型的响应
     */
    handleTransactionResponse(transaction: TransactionMessage) {
      // 添加交易卡片消息
      this.messages.push(transaction);
      
      // 5.3 添加文字跟进消息
      setTimeout(() => {
        const followUpMessage: TextMessage = {
          id: uuidv4(),
          type: MessageType.TEXT,
          source: MessageSource.AI,
          content: transaction.transaction.amount > 0 
            ? '收入已记录，继续保持哦！💰' 
            : '支出已记录，理财有道，省钱有术！💸',
          createdAt: Date.now(),
          status: MessageStatus.SENT
        };
        
        this.messages.push(followUpMessage);
      }, 500);
    },
    
    /**
     * 添加AI正在输入的指示器
     */
    addTypingIndicator() {
      const typingMessage: TextMessage = {
        id: 'typing-indicator',
        type: MessageType.TEXT,
        source: MessageSource.AI,
        content: '', // 内容为空，由组件显示输入动画
        createdAt: Date.now(),
        status: MessageStatus.SENDING
      };
      
      this.messages.push(typingMessage);
    },
    
    /**
     * 移除输入指示器
     */
    removeTypingIndicator() {
      const index = this.messages.findIndex(msg => msg.id === 'typing-indicator');
      if (index !== -1) {
        this.messages.splice(index, 1);
      }
    },
    
    /**
     * 更新消息状态
     * @param id 消息ID
     * @param status 新状态
     */
    updateMessageStatus(id: string, status: MessageStatus) {
      const message = this.messages.find(msg => msg.id === id);
      if (message) {
        message.status = status;
      }
    },
    
    /**
     * 重发失败的消息
     * @param id 消息ID
     */
    async resendMessage(id: string) {
      const message = this.messages.find(msg => msg.id === id);
      if (!message || message.status !== MessageStatus.FAILED) return;
      
      // 如果是文本消息
      if (message.type === MessageType.TEXT) {
        // 更新状态为发送中
        this.updateMessageStatus(id, MessageStatus.SENDING);
        
        // 取出内容重发
        await this.sendTextMessage((message as TextMessage).content);
      }
      // 可以添加其他类型消息的重发逻辑
    },
    
    /**
     * 发送语音消息
     * @param file 语音文件
     */
    async sendVoiceMessage(file: File) {
      if (this.sending) return;
      
      this.sending = true;
      
      try {
        // 语音识别
        const recognizeResult = await chatApi.recognizeVoice(file);
        
        if (recognizeResult.code === 0) {
          const { transcript, possibleTransaction } = recognizeResult.data;
          
          // 创建语音消息
          const voiceMessage: VoiceMessage = {
            id: uuidv4(),
            type: MessageType.VOICE,
            source: MessageSource.USER,
            audioUrl: URL.createObjectURL(file),
            duration: 5, // 假设固定时长，实际应该从音频文件中获取
            transcript,
            createdAt: Date.now(),
            status: MessageStatus.SENT
          };
          
          // 添加到消息列表
          this.messages.push(voiceMessage);
          
          // 添加AI正在输入的临时消息
          this.addTypingIndicator();
          
          // 如果识别到了可能的交易，模拟发送消息
          if (possibleTransaction) {
            // 构造交易消息
            const transactionMessage: TransactionMessage = {
              id: uuidv4(),
              type: MessageType.TRANSACTION,
              source: MessageSource.AI,
              createdAt: Date.now(),
              transaction: possibleTransaction,
              status: MessageStatus.SENT
            };
            
            // 移除输入指示器
            this.removeTypingIndicator();
            
            // 添加交易卡片
            this.messages.push(transactionMessage);
            
            // 添加文字跟进消息
            setTimeout(() => {
              const followUpMessage: TextMessage = {
                id: uuidv4(),
                type: MessageType.TEXT,
                source: MessageSource.AI,
                content: possibleTransaction.amount > 0 
                  ? '收入已记录，继续保持哦！💰' 
                  : '支出已记录，理财有道，省钱有术！💸',
                createdAt: Date.now(),
                status: MessageStatus.SENT
              };
              
              this.messages.push(followUpMessage);
            }, 500);
          } else {
            // 如果没有识别到交易，发送普通文本消息
            await this.sendTextMessage(transcript);
          }
        }
      } catch (error) {
        console.error('发送语音消息失败', error);
      } finally {
        this.sending = false;
      }
    },
    
    /**
     * 发送图片消息
     * @param file 图片文件
     */
    async sendImageMessage(file: File) {
      if (this.sending) return;
      
      this.sending = true;
      
      try {
        // 上传图片
        const uploadResult = await chatApi.uploadImage(file);
        
        if (uploadResult.code === 0) {
          // 创建图片消息
          const imageMessage: ImageMessage = {
            id: uuidv4(),
            type: MessageType.IMAGE,
            source: MessageSource.USER,
            imageUrl: URL.createObjectURL(file), // 使用本地临时URL显示
            createdAt: Date.now(),
            status: MessageStatus.SENT
          };
          
          // 添加到消息列表
          this.messages.push(imageMessage);
          
          // 模拟AI回复
          this.addTypingIndicator();
          
          setTimeout(() => {
            this.removeTypingIndicator();
            
            const aiMessage: TextMessage = {
              id: uuidv4(),
              type: MessageType.TEXT,
              source: MessageSource.AI,
              content: '我已收到你发送的图片！如果这是票据或收据，我可以尝试识别上面的消费信息并帮你记账。',
              createdAt: Date.now(),
              status: MessageStatus.SENT
            };
            
            this.messages.push(aiMessage);
          }, 1000);
        }
      } catch (error) {
        console.error('发送图片消息失败', error);
      } finally {
        this.sending = false;
      }
    },
    
    /**
     * 预览图片
     * @param url 图片URL
     */
    previewImage(url: string) {
      this.previewImageUrl = url;
      this.showImageViewer = true;
    },
    
    /**
     * 关闭图片预览
     */
    closeImageViewer() {
      this.showImageViewer = false;
      this.previewImageUrl = '';
    },
    
    /**
     * 切换语音输入界面
     */
    toggleVoiceInput() {
      this.showVoiceInput = !this.showVoiceInput;
      if (this.showVoiceInput) {
        this.showFunctionsPanel = false;
      }
    },
    
    /**
     * 切换功能面板
     */
    toggleFunctionsPanel() {
      this.showFunctionsPanel = !this.showFunctionsPanel;
      if (this.showFunctionsPanel) {
        this.showVoiceInput = false;
      }
    },
    
    /**
     * 清空聊天记录
     */
    clearMessages() {
      this.messages = [];
      this.pagination.pageNum = 1;
      this.hasMore = true;
    }
  }
}); 