import type {
  CreateTransactionPayload,
  Transaction,
  TransactionListParams,
  UpdateTransactionPayload,
} from '@/types/transaction';
import config from '@/config';
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';
import { useCategoryStore } from './category.store';
import { 
  getTransactionById,
  getTransactions as apiGetTransactions,
  createTransaction as apiCreateTransaction,
  updateTransaction as apiUpdateTransaction,
  deleteTransaction as apiDeleteTransaction 
} from '@/api/transaction';
import transactionMock from '@/api/mocks/transaction.mock';

// 定义是否使用Mock数据的变量
const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true';

export const useTransactionStore = defineStore(
  'transaction',
  () => {
    // 引用category store
    const categoryStore = useCategoryStore();

    // State
    const transactions = ref<Transaction[]>([]);
    const monthlyTransactions = ref<Transaction[]>([]);
    const currentTransaction = ref<Transaction | null>(null);
    const recentTransactions = ref<Transaction[]>([]);
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const pagination = ref({
      page: 1,
      pageSize: 20,
      total: 0,
      totalPages: 0,
    });

    // 本地存储key统一
    const LOCAL_DB_KEY = 'localLedgerDB:transactions';

    // Getters
    const getRecentTransactions = (limit: number = 3): Transaction[] => recentTransactions.value.slice(0, limit);

    const getTransactionsByDate = (dateStr: string): Transaction[] => monthlyTransactions.value.filter(tx => tx.date === dateStr);

    const getTotalsByType = computed(() => {
      const income = monthlyTransactions.value
        .filter(tx => tx.type === 'income')
        .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);

      const expense = monthlyTransactions.value
        .filter(tx => tx.type === 'expense')
        .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);

      return { income, expense, balance: income - expense };
    });

    const getCategoryPercentages = computed<CategoryPercentage[]>(() => {
      // 只统计支出类型交易
      const expenseTransactions = transactions.value?.filter(t => t.type === 'expense') || [];
      // 如果没有支出数据，返回空数组
      if (expenseTransactions.length === 0) {
        return [];
        }
      
      // 计算总支出
      const totalExpense = Math.abs(
        expenseTransactions.reduce((sum, t) => sum + Math.abs(t.amount), 0)
      );
      
      if (totalExpense === 0) {
        return [];
      }

      // 按分类ID归类并计算金额
      const categoryAmounts: Record<string, { 
        name: string; 
        amount: number; 
        id: string; 
        icon?: string; 
        color?: string;
      }> = {};
      
      expenseTransactions.forEach(t => {
        try {
          const categoryId = typeof t.category === 'object' 
            ? t.category?.id 
            : String(t.category);
          
          if (!categoryId) {
            console.warn('Transaction without category ID:', t);
            return;
          }
          
          // 获取分类信息
          let categoryName = '未分类';
          let categoryIcon = 'question-circle';
          let categoryColor = '#BDBDBD';
          
          if (typeof t.category === 'object' && t.category?.name) {
            categoryName = t.category.name;
            categoryIcon = t.category.icon || categoryIcon;
            categoryColor = t.category.bgColor || categoryColor;
          } else {
            // 尝试从categoryStore获取分类详情
        const category = categoryStore.getCategoryById(categoryId);
            if (category) {
              categoryName = category.name;
              categoryIcon = category.icon || categoryIcon;
              categoryColor = category.bgColor || categoryColor;
            }
          }
          
          if (!categoryAmounts[categoryId]) {
            categoryAmounts[categoryId] = { 
          id: categoryId,
              name: categoryName, 
              amount: 0,
              icon: categoryIcon,
              color: categoryColor
            };
          }
          
          categoryAmounts[categoryId].amount += Math.abs(t.amount);
        } catch (error) {
          console.error('Error processing transaction category:', error, t);
        }
      });

      // 转换为数组并计算百分比
      const result = Object.values(categoryAmounts).map(cat => ({
        id: cat.id,
        name: cat.name,
        icon: cat.icon || 'question-circle',
        color: cat.color || '#BDBDBD',
        amount: cat.amount,
        percentage: (cat.amount / totalExpense) * 100
      }));
      
      // 按金额降序排序
      return result.sort((a, b) => b.amount - a.amount);
    });

    // Actions
    async function fetchTransactions(params: TransactionListParams & { forceRefresh?: boolean }) {
      isLoading.value = true;
      error.value = null;

      try {
        console.log('获取交易数据，参数:', params);
        
        // 判断是否需要强制刷新
        const needRefresh = params.forceRefresh === true;
        if (needRefresh) {
          console.log('强制刷新交易数据，跳过缓存');
        }
        
        // 如果未设置月份参数且不是强制刷新，尝试直接使用当前缓存数据
        const hasDateFilter = params.startDate && params.endDate;
        if (!needRefresh && !hasDateFilter && transactions.value.length > 0) {
          console.log('使用缓存的交易数据');
          return {
            items: transactions.value,
            pagination: pagination.value
          };
        }
        
        const response = await apiGetTransactions(params);

        // 当有日期筛选或强制刷新时，完全替换transactions
        // 否则合并新数据到现有数据中
        if (hasDateFilter || needRefresh) {
          transactions.value = response.items;
        } else if (response.items.length > 0) {
          // 合并新旧数据，避免重复
          const existingIds = new Set(transactions.value.map(t => t.id));
          const newItems = response.items.filter(item => !existingIds.has(item.id));
          transactions.value = [...transactions.value, ...newItems];
        }
        
        pagination.value = response.pagination;

        // 更新最近交易（如果获取的是最新数据）
        if (!params.startDate && !params.endDate) {
          recentTransactions.value = response.items.slice(0, 5);
        }
        
        // 如果有日期参数，更新monthlyTransactions
        if (params.startDate && params.endDate) {
          // 提取日期格式的年月信息
          const startParts = params.startDate.split('-').map(Number);
          if (startParts.length >= 2) {
            const [year, month] = startParts;
            console.log(`更新月度交易数据: ${year}年${month}月`);
            
            // 筛选符合当前年月的交易
            monthlyTransactions.value = response.items.map(tx => resolveTransactionCategories(tx));
          }
        }

        // 保存到本地存储
        if (needRefresh) {
          saveToStorage();
        }

        return response;
      }
      catch (err) {
        console.error('Error fetching transactions:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function fetchMonthlyTransactions(params: {
      year: number
      month: number
      categoryId?: string
      forceRefresh?: boolean
    }) {
      isLoading.value = true;
      error.value = null;

      try {
        // 确保解构参数
        const { year, month, categoryId, forceRefresh } = params;

        if (typeof year !== 'number' || typeof month !== 'number') {
          throw new TypeError('Invalid year or month parameter');
        }

        console.log(
          `Fetching monthly transactions for ${year}-${month}${categoryId ? `, category: ${categoryId}` : ''}${forceRefresh ? ' (forced refresh)' : ''}`,
        );
        
        // 强制刷新时重新从API获取所有交易
        if (forceRefresh) {
          console.log('强制刷新月度交易数据');
          
          // 获取月份起始日期和结束日期
          const startDate = `${year}-${String(month).padStart(2, '0')}-01`;
          const lastDay = new Date(year, month, 0).getDate();
          const endDate = `${year}-${String(month).padStart(2, '0')}-${String(lastDay).padStart(2, '0')}`;
          
          // 使用fetchTransactions获取月度数据
          const response = await fetchTransactions({
            startDate,
            endDate,
            forceRefresh: true
          });
          
          console.log(`获取到 ${response.items.length} 条月度交易数据`);
        }
        
        // 检查transactions是否为空，如果为空则先尝试从本地存储加载
        if (transactions.value.length === 0 && !forceRefresh) {
          console.log('交易列表为空，尝试从本地存储加载数据');
          loadFromStorage();
        }
        
        // 1. 先筛选出符合年月条件的交易
        const filteredTransactions = transactions.value.filter(tx => {
          const datePart = tx.date.split('T')[0]; // 先获取日期部分 YYYY-MM-DD
          const [y, m] = datePart.split('-').map(Number); // 然后再安全地分割年和月
          if (categoryId) {
            if (typeof tx.category === 'object') {
              return y === year && m === month && tx.category.id === categoryId;
            }
            return y === year && m === month && (tx.category === categoryId || tx.categoryId === categoryId);
          }
          return y === year && m === month;
        });
        
        // 2. 对每个交易应用resolveTransactionCategories确保分类信息完整
        monthlyTransactions.value = filteredTransactions.map(tx => resolveTransactionCategories(tx));

        console.log(`为 ${year}-${month} 月筛选出 ${monthlyTransactions.value.length} 条交易记录`);

        // 更新最近交易
        recentTransactions.value = [...monthlyTransactions.value]
          .sort((a, b) => b.date.localeCompare(a.date)).slice(0, 5);

        return monthlyTransactions.value;
      }
      catch (err) {
        console.error('Error fetching monthly transactions:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function fetchTransactionById(id: string) {
      isLoading.value = true;
      error.value = null;

      try {
        // 优先在本地Pinia transactions查找
        const localTx = transactions.value.find(tx => tx.id === id);
        if (localTx) {
          currentTransaction.value = localTx;
          return localTx;
        }
        // 本地找不到再调用API
        const transaction = await getTransactionById(id);
        currentTransaction.value = transaction;
        return transaction;
      }
      catch (err) {
        console.error(`Error fetching transaction ${id}:`, err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function addTransaction(payload: CreateTransactionPayload) {
      console.log('开始添加交易:', payload);
      isLoading.value = true;
      error.value = null;

      try {
        // 使用Mock API或真实API
        if (useMock) {
          // 标准化金额（收入为正，支出为负）
          let amount = payload.amount;
          if (payload.type === 'expense' && amount > 0) {
            amount = -amount;
          } else if (payload.type === 'income' && amount < 0) {
            amount = Math.abs(amount);
          }
          
          // 手动创建交易记录
          const newTransaction: Transaction = {
            id: `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
            type: payload.type,
            amount: amount,
            categoryId: payload.categoryId,
            category: categoryStore.getCategoryById(payload.categoryId),
            date: typeof payload.date === 'string' ? payload.date : 
                  payload.date instanceof Date ? payload.date.toISOString().slice(0,19) :
                  new Date().toISOString().slice(0,19),
            description: payload.description || '',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };
          
          // 添加到状态
          transactions.value.unshift(newTransaction);
          
          // 如果是当月交易，添加到月度交易列表
          const now = new Date();
          const year = now.getFullYear();
          const month = now.getMonth() + 1;
          const txDate = new Date(newTransaction.date);
          
          if (txDate.getFullYear() === year && txDate.getMonth() + 1 === month) {
            monthlyTransactions.value.unshift(newTransaction);
          }
          
          // 更新最近交易
          recentTransactions.value = [newTransaction, ...recentTransactions.value].slice(0, 5);
          
          // 保存到本地存储
          saveToStorage();
          console.log('交易已添加并保存到本地存储:', newTransaction);
          
          // 显示成功提示
          uni.showToast({
            title: '记账成功',
            icon: 'success'
          });
          
          return newTransaction;
        } else {
          // 使用真实API添加交易
          try {
            const newTransaction = await apiCreateTransaction(payload);
            
            // 添加到状态
            transactions.value.unshift(newTransaction);
            
            // 如果是当月交易，添加到月度交易列表
            const now = new Date();
            const year = now.getFullYear();
            const month = now.getMonth() + 1;
            const txDate = new Date(newTransaction.date);
            
            if (txDate.getFullYear() === year && txDate.getMonth() + 1 === month) {
              monthlyTransactions.value.unshift(newTransaction);
            }
            
            // 更新最近交易
            recentTransactions.value = [newTransaction, ...recentTransactions.value].slice(0, 5);
            
            // 显示成功提示
            uni.showToast({
              title: '记账成功',
              icon: 'success'
            });
            
            return newTransaction;
          } catch (error) {
            console.error('调用API添加交易失败:', error);
            throw error;
          }
        }
      } catch (err) {
        console.error('添加交易失败:', err);
        error.value = err instanceof Error ? err.message : String(err);
        
        // 显示错误提示
        uni.showToast({
          title: '添加交易失败',
          icon: 'none'
        });
        
        throw err;
      } finally {
        isLoading.value = false;
      }
    }

    async function updateTransaction(id: string, payload: UpdateTransactionPayload) {
      isLoading.value = true;
      error.value = null;

      try {
        // 查找现有交易
        const index = transactions.value.findIndex(tx => tx.id.toString() === id.toString());
        if (index === -1) {
          throw new Error(`Transaction with ID ${id} not found`);
        }

        // 记录传入的日期参数
        console.log('更新交易记录前，原始日期参数:', payload.date);

        // 确保 payload.date 是 'YYYY-MM-DDTHH:mm:ss' 格式
        let standardizedDate = '';
        if (payload.date) {
          if (typeof payload.date === 'string') {
            // 如果已经是带T的格式，直接用
            if (payload.date.includes('T')) {
              standardizedDate = payload.date.slice(0, 19); // 取 YYYY-MM-DDTHH:mm:ss
            } else {
              // 如果只是 YYYY-MM-DD，则尝试保留原交易的时间，或补00:00:00
              const originalTime = transactions.value[index].date.split('T')[1] || '00:00:00';
              standardizedDate = `${payload.date.split('T')[0]}T${originalTime}`;
            }
          } else if (payload.date instanceof Date) {
            standardizedDate = payload.date.toISOString().slice(0, 19);
          }
        } else {
          // 如果没有传入 date，则使用原交易的日期
          standardizedDate = transactions.value[index].date;
        }
        

        // 处理分类信息更新
        let category;
        if (payload.categoryId) {
          // 如果更新中包含categoryId，获取完整的分类信息
          const categoryInfo = categoryStore.getCategoryById(payload.categoryId.toString());
          if (categoryInfo) {
            category = categoryInfo;
          } else {
            console.warn(`Category with ID ${payload.categoryId} not found during update, using current category`);
          }
        }

        // 创建更新后的交易对象
        const updatedTransaction = {
          ...transactions.value[index],
          ...payload,
          date: standardizedDate, // 使用标准化后的日期
          // 如果有更新的category，使用完整对象
          ...(category && { category }),
        };

        // 调试日志：输出最终使用的日期
        console.log('更新交易记录使用的日期:', updatedTransaction.date, '原始日期参数:', payload.date);

        // 确保分类信息完整
        const transactionWithCategory = resolveTransactionCategories(updatedTransaction);
        console.log('更新交易记录，包含分类信息:', transactionWithCategory.category);

        // 更新交易列表
        transactions.value[index] = transactionWithCategory;

        // 更新月度交易和最近交易
        const monthlyIndex = monthlyTransactions.value.findIndex(tx => tx.id.toString() === id.toString());
        if (monthlyIndex !== -1) {
          monthlyTransactions.value[monthlyIndex] = transactionWithCategory;
        }

        // 更新最近交易列表
        const recentIndex = recentTransactions.value.findIndex(tx => tx.id.toString() === id.toString());
        if (recentIndex !== -1) {
          recentTransactions.value[recentIndex] = transactionWithCategory;
        }

        // 更新当前交易（如果正在查看这个交易）
        if (currentTransaction.value && currentTransaction.value.id.toString() === id.toString()) {
          currentTransaction.value = transactionWithCategory;
        }

        // 保存到本地存储
        saveToStorage();

        return transactionWithCategory;
      }
      catch (err) {
        console.error(`Error updating transaction ${id}:`, err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function removeTransaction(id: string) {
      isLoading.value = true;
      error.value = null;

      try {
        transactions.value = transactions.value.filter(tx => tx.id !== id);
        saveToStorage();
        monthlyTransactions.value = monthlyTransactions.value.filter(tx => tx.id !== id);
        recentTransactions.value = recentTransactions.value.filter(tx => tx.id !== id);

        if (currentTransaction.value?.id === id) {
          currentTransaction.value = null;
        }

        return true;
      }
      catch (err) {
        console.error(`Error removing transaction ${id}:`, err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    function clearState() {
      transactions.value = [];
      monthlyTransactions.value = [];
      currentTransaction.value = null;
      recentTransactions.value = [];
      isLoading.value = false;
      error.value = null;
      pagination.value = {
        page: 1,
        pageSize: 20,
        total: 0,
        totalPages: 0,
      };
    }

    // 初始化时从本地存储加载
    function loadFromStorage() {
      const stored = uni.getStorageSync(LOCAL_DB_KEY);
      if (stored && Array.isArray(stored)) {
        // 为每个交易记录应用resolveTransactionCategories
        transactions.value = stored.map(tx => resolveTransactionCategories(tx));
      } else {
        transactions.value = [];
      }
      
      // 初始化月度和最近交易
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth() + 1;
      
      // 筛选当前月份的交易
      monthlyTransactions.value = transactions.value.filter(tx => {
        const [y, m] = tx.date.split('-').map(Number);
        return y === year && m === month;
      });
      
      // 排序获取最近交易
      recentTransactions.value = [...transactions.value]
        .sort((a, b) => b.date.localeCompare(a.date))
        .slice(0, 5);
        
      console.log(`已从本地存储加载${transactions.value.length}条交易记录，当月${monthlyTransactions.value.length}条`);
    }

    // 每次变更后写入本地存储
    function saveToStorage() {
      uni.setStorageSync(LOCAL_DB_KEY, transactions.value);
    }

    // 导出账本数据（JSON字符串，结构与云API一致）
    function exportLedgerData() {
      return JSON.stringify({
        transactions: transactions.value,
        // 预留: categories, assets, 等
      });
    }

    // 导入账本数据（结构与云API一致）
    function importLedgerData(data) {
      if (data && Array.isArray(data.transactions)) {
        transactions.value = data.transactions;
        saveToStorage();
        loadFromStorage();
        return true;
      }
      return false;
    }

    function clearAllTransactions() {
      transactions.value = [];
      monthlyTransactions.value = [];
      recentTransactions.value = [];
      currentTransaction.value = null;
      saveToStorage();
    }

    /**
     * 将交易与完整分类对象关联起来
     * 确保交易记录中的分类是完整的对象，而非仅ID
     */
    function resolveTransactionCategories(tx: Transaction): Transaction {
      // 首先确保tx是一个克隆对象，避免直接修改原对象
      const transaction = { ...tx };
      const txType = transaction.type || 'expense';

      if (typeof transaction.category === 'string' || typeof transaction.category === 'number') {
        // 使用transactionMock中的方法获取完整分类对象
        if (useMock) {
          // 从mock数据中获取完整分类对象（包含bgColor等所有字段）
          const mockCategory = transactionMock.getCategoryObject(transaction.category, txType);
          transaction.category = {
            ...mockCategory,
            bgColor: mockCategory.bgColor || getDefaultBgColor(txType), // 确保有bgColor
          };
        } else {
          // 从categoryStore获取分类
          const category = categoryStore.getCategoryById(transaction.category);
          if (category) {
            // 确保使用完整的分类对象，包含所有字段（id, name, icon, bgColor, type）
            transaction.category = {
              id: category.id,
              name: category.name,
              icon: category.icon,
              bgColor: category.bgColor, // 确保包含bgColor字段
              type: category.type,
            };
          } else {
            // 如果在store中找不到，尝试获取对应类型的默认分类
            const defaultCategories = categoryStore.getCategoriesByType(txType);
            if (defaultCategories && defaultCategories.length > 0) {
              transaction.category = {
                ...defaultCategories[0],
              };
            } else {
              // 最后的后备选项
              transaction.category = {
                id: 'unknown',
                name: '未分类',
                icon: 'question-circle',
                bgColor: getDefaultBgColor(txType),
                type: txType,
              };
            }
          }
        }
      } else if (transaction.category && typeof transaction.category === 'object') {
        // 如果已经是对象但缺少必要字段，尝试补充
        if (!transaction.category.bgColor) {
          // 尝试从store获取完整的分类信息
          const fullCategory = categoryStore.getCategoryById(transaction.category.id);
          if (fullCategory) {
            transaction.category = {
              ...transaction.category,
              bgColor: fullCategory.bgColor, // 添加bgColor字段
              icon: transaction.category.icon || fullCategory.icon, // 确保有icon
              type: transaction.category.type || fullCategory.type || txType, // 确保有type
            };
          } else {
            // 如果还是找不到，使用默认背景色
            transaction.category = {
              ...transaction.category,
              bgColor: getDefaultBgColor(txType),
              type: transaction.category.type || txType,
            };
          }
        }
      } else if (!transaction.category) {
        // 如果没有分类信息，创建一个默认分类
        const defaultCategories = categoryStore.getCategoriesByType(txType);
        if (defaultCategories && defaultCategories.length > 0) {
          transaction.category = {
            ...defaultCategories[0],
          };
        } else {
          transaction.category = {
            id: 'unknown',
            name: '未分类',
            icon: 'question-circle',
            bgColor: getDefaultBgColor(txType),
            type: txType,
          };
        }
      }

      return transaction;
    }

    // 辅助函数：根据交易类型获取默认背景色
    function getDefaultBgColor(type: 'income' | 'expense'): string {
      return type === 'income' 
        ? 'var(--color-success, #4CAF50)' // 收入默认使用绿色
        : 'var(--color-error, #F44336)';  // 支出默认使用红色
    }

    // 添加setMonthlyTransactions方法
    function setMonthlyTransactions(txList: Transaction[]) {
      if (!Array.isArray(txList)) {
        console.error('setMonthlyTransactions: Invalid data format, expected array');
        return;
      }
      
      // 更新月度交易数据
      monthlyTransactions.value = txList.map(tx => resolveTransactionCategories(tx));
      
      console.log(`交易Store: 已更新${monthlyTransactions.value.length}条月度交易数据`);
      
      // 更新最近交易
      if (txList.length > 0) {
        recentTransactions.value = [...monthlyTransactions.value]
          .sort((a, b) => b.date.localeCompare(a.date))
          .slice(0, 5);
      }
    }

    return {
      // State
      transactions,
      monthlyTransactions,
      currentTransaction,
      recentTransactions,
      isLoading,
      error,
      pagination,

      // Getters
      getRecentTransactions,
      getTransactionsByDate,
      getTotalsByType,
      getCategoryPercentages,

      // Actions
      fetchTransactions,
      fetchMonthlyTransactions,
      fetchTransactionById,
      addTransaction,
      updateTransaction,
      removeTransaction,
      setMonthlyTransactions,
      clearState,
      loadFromStorage,
      saveToStorage,
      exportLedgerData,
      importLedgerData,
      clearAllTransactions
    };
  },
  {
    persist: {
      enabled: false,
    },
  },
);
