import {
  createAsset as apiCreateAsset,
  deleteAsset as apiDeleteAsset,
  getAssetById as apiGetAssetById,
  getAssets as apiGetAssets,
  getAssetTypes as apiGetAssetTypes,
  getBudgetData as apiGetBudgetData,
  updateAsset as apiUpdateAsset,
} from '@/api/asset'; // Renamed API imports (assuming they exist)
import assetMock from '@/api/mocks/asset.mock'; // Import mock data
import config from '@/config';
import { storage } from '@/utils/storage'; // 导入统一存储工具
import { defineStore } from 'pinia';
import { computed, ref } from 'vue';

// 资产类型定义（通常会放在 types 目录中）
interface Asset {
  id: string
  name: string
  type: string
  icon?: string
  balance: number
  color?: string
  isDefault?: boolean
  createdAt?: string
  updatedAt?: string
}

interface AssetType {
  id: string
  name: string
  icon: string
}

interface Budget {
  total: number
  used: number
  remaining: number
  percentage: number
}

const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // Check mock environment variable

// 资产类型数据
const assetTypes: AssetType[] = [
  { id: 'cash', name: '现金', icon: 'money-bill' },
  { id: 'bank', name: '银行卡', icon: 'credit-card' },
  { id: 'credit', name: '信用卡', icon: 'credit-card' },
  { id: 'alipay', name: '支付宝', icon: 'alipay' },
  { id: 'wechat', name: '微信', icon: 'weixin' },
  { id: 'other', name: '其他', icon: 'wallet' },
];

// 模拟预算数据（在实际应用中会从预算模块获取）
const mockBudgetData = {
  total: 5000, // 总预算
  used: 3520, // 已使用
  remaining: 1480, // 剩余
  percentage: 70, // 使用百分比
};

export const useAssetStore = defineStore(
  'asset',
  () => {
    // State
    const assets = ref<Asset[]>([]);
    const assetTypesList = ref<AssetType[]>([]);
    const currentAsset = ref<Asset | null>(null);
    const isLoading = ref(false);
    const error = ref<string | null>(null);
    const budget = ref<Budget>({
      total: 0,
      used: 0,
      remaining: 0,
      percentage: 0,
    });

    // Getters
    const totalAssets = computed(() => assets.value
        .filter(asset => asset.balance > 0)
        .reduce((sum, asset) => sum + asset.balance, 0));

    const totalLiabilities = computed(() => assets.value
        .filter(asset => asset.balance < 0)
        .reduce((sum, asset) => sum + Math.abs(asset.balance), 0));

    const netWorth = computed(() => assets.value.reduce((sum, asset) => sum + asset.balance, 0));

    const assetsByType = computed(() => {
      const result = {} as Record<string, Asset[]>;

      assets.value.forEach((asset) => {
        if (!result[asset.type]) {
          result[asset.type] = [];
        }
        result[asset.type].push(asset);
      });

      return result;
    });

    const budgetPercentage = computed(() => budget.value.total > 0
        ? Math.round((budget.value.used / budget.value.total) * 100)
        : 0);

    const defaultAsset = computed(() => assets.value.find(asset => asset.isDefault) || assets.value[0] || null);

    const totalBalance = computed(() => assets.value.reduce((sum, asset) => sum + asset.balance, 0));

    const balanceByType = computed(() => {
      const result: Record<string, number> = {};

      assets.value.forEach((asset) => {
        if (!result[asset.type]) {
          result[asset.type] = 0;
        }
        result[asset.type] += asset.balance;
      });

      return result;
    });

    // Actions
    async function fetchAssets() {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        const assetList = useMock
          ? await assetMock.getAssets() // Use mock function
          : await apiGetAssets(); // Use real API function

        assets.value = assetList;
        return assetList;
      }
      catch (err) {
        console.error('Error fetching assets:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function fetchAssetTypes() {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        const types = useMock
          ? await assetMock.getAssetTypes() // Use mock function
          : await apiGetAssetTypes(); // Use real API function

        assetTypesList.value = types;
        return types;
      }
      catch (err) {
        console.error('Error fetching asset types:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function fetchAssetById(id: string) {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        const asset = useMock
          ? await assetMock.getAssetById(id) // Use mock function
          : await apiGetAssetById(id); // Use real API function

        // The mock function should handle the 'not found' case internally

        currentAsset.value = asset;
        return asset;
      }
      catch (err) {
        console.error(`Error fetching asset ${id}:`, err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function fetchBudgetData() {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        const budgetData = useMock
          ? await assetMock.getBudgetData() // Use mock function
          : await apiGetBudgetData(); // Use real API function

        budget.value = budgetData;
        return budgetData;
      }
      catch (err) {
        console.error('Error fetching budget data:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function addAsset(newAsset: Omit<Asset, 'id'>) {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        const asset = useMock
          ? await assetMock.createAsset(newAsset) // Use mock function
          : await apiCreateAsset(newAsset); // Use real API function

        assets.value = [...assets.value, asset];
        return asset;
      }
      catch (err) {
        console.error('Error adding asset:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function updateAsset(id: string, payload: Partial<Asset>) {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        const updatedAsset = useMock
          ? await assetMock.updateAsset(id, payload) // Use mock function
          : await apiUpdateAsset(id, payload); // Use real API function

        const assetIndex = assets.value.findIndex(a => a.id === id);

        if (assetIndex === -1) {
          throw new Error(`Asset with ID ${id} not found`);
        }

        // 直接修改数组中的元素
        assets.value = assets.value.map(asset => (asset.id === id ? updatedAsset : asset));

        if (currentAsset.value?.id === id) {
          currentAsset.value = updatedAsset;
        }

        return updatedAsset;
      }
      catch (err) {
        console.error(`Error updating asset ${id}:`, err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function removeAsset(id: string) {
      isLoading.value = true;
      error.value = null;

      try {
        // Use mock or real API based on env var
        useMock
          ? await assetMock.removeAsset(id) // Use mock function
          : await apiDeleteAsset(id); // Use real API function

        // 直接过滤数组
        assets.value = assets.value.filter(asset => asset.id !== id);

        if (currentAsset.value?.id === id) {
          currentAsset.value = null;
        }

        return true;
      }
      catch (err) {
        console.error(`Error removing asset ${id}:`, err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    async function loadInitialData() {
      isLoading.value = true;
      error.value = null;

      try {
        // Load initial data for the page
        return Promise.allSettled([fetchAssets(), fetchBudgetData()]);
      }
      catch (err) {
        console.error('Error loading initial asset data:', err);
        error.value = err instanceof Error ? err.message : String(err);
        throw err;
      }
      finally {
        isLoading.value = false;
      }
    }

    function clearState() {
      assets.value = [];
      assetTypesList.value = [];
      currentAsset.value = null;
      isLoading.value = false;
      error.value = null;
      budget.value = {
        total: 0,
        used: 0,
        remaining: 0,
        percentage: 0,
      };
    }

    return {
      // State
      assets,
      assetTypes: assetTypesList,
      currentAsset,
      isLoading,
      error,
      budget,

      // Getters
      totalAssets,
      totalLiabilities,
      netWorth,
      assetsByType,
      budgetPercentage,
      defaultAsset,
      totalBalance,
      balanceByType,

      // Actions
      fetchAssets,
      fetchAssetTypes,
      fetchAssetById,
      fetchBudgetData,
      addAsset,
      updateAsset,
      removeAsset,
      loadInitialData,
      clearState,
    };
  },
  {
    persist: {
      key: `${config.storage.prefix}asset`,
      // 使用统一的存储工具，确保多端兼容
      storage: {
        getItem(key) {
          return storage.getItem(key);
        },
        setItem(key, value) {
          storage.setItem(key, value);
        },
        removeItem(key) {
          storage.removeItem(key);
        },
      },
      paths: ['assets', 'assetTypes', 'budget'],
    },
  },
);
