import { getPlatformType } from '@/utils/platform';
/**
 * 错误处理hooks
 * 提供统一的错误提示、自动消失、震动反馈等功能
 */
import { reactive } from 'vue';

/**
 * 错误信息类型定义
 */
interface ErrorState {
  message: string
  visible: boolean
  timer: number | null
}

/**
 * 错误处理选项
 */
interface ErrorOptions {
  /** 自动消失时间(ms)，0表示不自动消失 */
  duration?: number
  /** 是否启用震动反馈 */
  vibrate?: boolean
  /** 错误类型（用于样式区分） */
  type?: 'error' | 'warning' | 'info'
}

/**
 * 提供统一的错误处理功能
 * @returns 错误处理相关方法和状态
 */
export function useErrorHandler() {
  // 错误状态
  const errorState = reactive<ErrorState>({
    message: '',
    visible: false,
    timer: null,
  });

  // 当前平台
  const platform = getPlatformType();

  /**
   * 根据错误类型设置友好的中文错误提示
   * @param error - 错误对象或错误信息
   * @returns 友好的中文错误提示
   */
  const getErrorMessage = (error: unknown): string => {
    // 转换错误对象为字符串
    const errorMessage
      = typeof error === 'string' ? error : (error as Error)?.message || '未知错误';

    // 匹配错误信息返回友好提示
    if (errorMessage.includes('Canvas') || errorMessage.includes('canvas')) {
      return '手势密码初始化失败，请重试';
    }
    else if (errorMessage.includes('phone') || errorMessage.includes('Phone')) {
      return '请输入正确的手机号码';
    }
    else if (
      errorMessage.includes('code')
      || errorMessage.includes('Code')
      || errorMessage.includes('验证码')
    ) {
      return '验证码错误，请重新输入';
    }
    else if (errorMessage.includes('network') || errorMessage.includes('Network')) {
      return '网络连接错误，请检查网络设置';
    }
    else if (errorMessage.includes('timeout') || errorMessage.includes('Timeout')) {
      return '请求超时，请稍后重试';
    }
    else if (
      errorMessage.includes('import')
      || errorMessage.includes('module')
      || errorMessage.includes('fetch')
    ) {
      console.error('[ErrorHandler] 模块导入错误，可能是Mock数据结构问题:', errorMessage);
      return '系统加载错误，请刷新页面';
    }
    else {
      // 记录未处理的错误类型，便于后续扩展
      console.error('[ErrorHandler] 未匹配的错误类型:', errorMessage);
      return '操作失败，请重试';
    }
  };

  /**
   * 显示错误信息
   * @param message - 错误信息内容
   * @param options - 错误处理选项
   */
  const showError = (message: string, options: ErrorOptions = {}) => {
    const { duration = 3000, vibrate = true, type = 'error' } = options;

    // 清除可能存在的定时器
    if (errorState.timer) {
      clearTimeout(errorState.timer);
      errorState.timer = null;
    }

    // 设置错误信息
    errorState.message = message;
    errorState.visible = true;

    // 添加震动反馈（仅在App环境）
    if (vibrate) {
      // #ifdef APP-PLUS
      uni.vibrateShort();
      // #endif
    }

    // 设置自动消失
    if (duration > 0) {
      errorState.timer = window.setTimeout(() => {
        clearError();
      }, duration);
    }

    // 根据错误类型决定是否打印到控制台
    if (type === 'error') {
      console.error('[ErrorHandler]', message);
    }
    else if (type === 'warning') {
      console.warn('[ErrorHandler]', message);
    }
  };

  /**
   * 处理错误信息，提供用户友好提示
   * @param error - 错误对象或错误信息
   * @param options - 错误处理选项
   */
  const handleError = (error: unknown, options: ErrorOptions = {}) => {
    // 获取友好错误提示
    const message = getErrorMessage(error);

    // 记录原始错误到控制台
    if (typeof error === 'object' && error !== null && 'stack' in error) {
      console.error('[ErrorHandler] 原始错误堆栈:', (error as Error).stack);
    }

    // 显示错误提示
    showError(message, options);
  };

  /**
   * 清除错误信息
   */
  const clearError = () => {
    errorState.message = '';
    errorState.visible = false;

    if (errorState.timer) {
      clearTimeout(errorState.timer);
      errorState.timer = null;
    }
  };

  /**
   * 检查API响应中的错误
   * @param response - API响应对象
   * @param options - 错误处理选项
   * @returns 是否存在错误
   */
  const checkApiError = (response: any, options: ErrorOptions = {}): boolean => {
    // 检查API响应是否包含错误信息
    if (response?.code && response.code !== 0 && response.code !== 200) {
      // API返回了错误码
      showError(response.message || `请求失败(${response.code})`, options);
      return true;
    }

    // 无错误
    return false;
  };

  /**
   * 表单提交前验证
   * @param validationFn - 自定义验证函数，返回验证结果
   * @param options - 错误处理选项
   * @returns 验证是否通过
   */
  const validateForm = (
    validationFn: () => { valid: boolean, message: string },
    options: ErrorOptions = {},
  ): boolean => {
    const result = validationFn();
    if (!result.valid) {
      showError(result.message, options);
      return false;
    }
    return true;
  };

  return {
    errorState,
    showError,
    handleError,
    clearError,
    checkApiError,
    validateForm,
    getErrorMessage,
  };
}
