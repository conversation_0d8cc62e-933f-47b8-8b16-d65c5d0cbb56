import { applyPlatformBehavior, getPlatformType } from '@/utils/platform';
/**
 * 键盘适配hooks
 * 处理键盘弹出和收起时的页面布局调整
 */
import { onMounted, onUnmounted, reactive, ref } from 'vue';

/**
 * 键盘状态类型定义
 */
interface KeyboardState {
  /** 键盘高度（像素） */
  height: number
  /** 键盘是否可见 */
  visible: boolean
  /** 内容区域底部填充（适配键盘） */
  contentPadding: number
  /** 自定义适配距离（像素），可用于微调 */
  offsetDistance: number
}

/**
 * 键盘适配选项
 */
interface KeyboardAdapterOptions {
  /** 键盘适配时额外的底部间距（像素） */
  extraPadding?: number
  /** 是否自动处理输入框聚焦，确保输入框不被键盘遮挡 */
  handleInputFocus?: boolean
  /** 是否隐藏页面上某些元素（如logo、描述文字等）当键盘弹出时 */
  hideElementsWhenKeyboardVisible?: boolean
  /** 键盘弹出时应用到容器的class名称 */
  keyboardVisibleClass?: string
}

/**
 * 提供键盘弹出和收起时的页面布局适配功能
 * @param options - 键盘适配选项
 * @returns 键盘适配相关方法和状态
 */
export function useKeyboardAdapter(options: KeyboardAdapterOptions = {}) {
  const {
    extraPadding = 20,
    handleInputFocus = true,
    hideElementsWhenKeyboardVisible = false,
    keyboardVisibleClass = 'keyboard-visible',
  } = options;

  // 键盘状态
  const keyboardState = reactive<KeyboardState>({
    height: 0,
    visible: false,
    contentPadding: 0,
    offsetDistance: 0,
  });

  // 获取当前平台
  const platform = getPlatformType();

  // 容器元素引用
  const containerRef = ref<HTMLElement | null>(null);

  // 输入框元素聚焦状态
  const focusedInputs = reactive<Set<HTMLElement>>(new Set());

  /**
   * 处理键盘高度变化
   * @param height - 键盘高度（像素）
   */
  const handleKeyboardHeightChange = (height: number) => {
    // 更新键盘状态
    keyboardState.height = height;
    keyboardState.visible = height > 0;

    console.log(`[KeyboardAdapter] 键盘高度变化: ${height}px, 平台: ${platform}`);

    // 当键盘弹出时调整内容区域
    if (height > 0) {
      // 计算内容区域需要的底部填充，确保输入框不被键盘遮挡
      const paddingBottom = height + extraPadding;
      keyboardState.contentPadding = paddingBottom;

      // 应用键盘可见样式类
      if (containerRef.value && keyboardVisibleClass) {
        containerRef.value.classList.add(keyboardVisibleClass);
      }

      // 处理输入框聚焦
      if (handleInputFocus) {
        adjustFocusedInputPosition(height);
      }
    }
    else {
      // 键盘收起时重置填充
      keyboardState.contentPadding = 0;

      // 移除键盘可见样式类
      if (containerRef.value && keyboardVisibleClass) {
        containerRef.value.classList.remove(keyboardVisibleClass);
      }
    }
  };

  /**
   * 调整聚焦输入框的位置
   * @param keyboardHeight - 键盘高度
   */
  const adjustFocusedInputPosition = (keyboardHeight: number) => {
    // 获取当前聚焦的输入框
    const focusedInputElement = Array.from(focusedInputs)[focusedInputs.size - 1];
    if (!focusedInputElement || !containerRef.value)
      return;

    // 计算输入框底部与视口底部的距离
    const inputRect = focusedInputElement.getBoundingClientRect();
    const containerRect = containerRef.value.getBoundingClientRect();
    const viewportHeight = window.innerHeight;

    // 输入框底部到视口底部的距离
    const distanceToBottom = viewportHeight - inputRect.bottom;

    // 如果输入框会被键盘遮挡
    if (distanceToBottom < keyboardHeight + 10) {
      // 10px额外空间
      // 计算需要滚动的距离
      const scrollDistance = keyboardHeight - distanceToBottom + 10;

      // 设置偏移距离
      keyboardState.offsetDistance = scrollDistance;

      // 平滑滚动
      containerRef.value.scrollBy({
        top: scrollDistance,
        behavior: 'smooth',
      });
    }
  };

  /**
   * 监听输入框聚焦事件
   */
  const setupInputFocusListeners = () => {
    if (!containerRef.value)
      return;

    // 获取容器内的所有输入元素
    const inputElements = containerRef.value.querySelectorAll('input, textarea');

    // 添加聚焦事件监听器
    inputElements.forEach((input) => {
      input.addEventListener('focus', () => {
        // 添加到聚焦输入集合
        focusedInputs.add(input as HTMLElement);
        // 如果键盘已经显示，调整输入框位置
        if (keyboardState.visible) {
          adjustFocusedInputPosition(keyboardState.height);
        }
      });

      input.addEventListener('blur', () => {
        // 从聚焦输入集合中移除
        focusedInputs.delete(input as HTMLElement);
      });
    });
  };

  /**
   * 设置容器元素
   * @param element - 容器元素
   */
  const setContainerElement = (element: HTMLElement) => {
    containerRef.value = element;
    // 设置容器元素后，设置输入框聚焦监听
    if (handleInputFocus) {
      setupInputFocusListeners();
    }
  };

  /**
   * 手动隐藏键盘
   */
  const hideKeyboard = () => {
    // 使用工具函数隐藏键盘
    applyPlatformBehavior('hideKeyboard');
  };

  /**
   * 生命周期钩子 - 组件挂载
   */
  onMounted(() => {
    // #ifdef APP-PLUS
    // 处理iOS和Android键盘弹出问题
    if (platform === 'ios' || platform === 'android') {
      console.log(`[KeyboardAdapter] 初始化${platform}平台特定键盘行为`);

      // 监听键盘高度变化
      uni.onKeyboardHeightChange((res) => {
        handleKeyboardHeightChange(res.height);
      });
    }
    // #endif
  });

  /**
   * 生命周期钩子 - 组件卸载
   */
  onUnmounted(() => {
    // 移除键盘高度变化监听
    // #ifdef APP-PLUS
    uni.offKeyboardHeightChange();
    // #endif

    // 清空聚焦输入集合
    focusedInputs.clear();

    // 重置状态
    keyboardState.height = 0;
    keyboardState.visible = false;
    keyboardState.contentPadding = 0;
    keyboardState.offsetDistance = 0;
  });

  return {
    keyboardState,
    setContainerElement,
    handleKeyboardHeightChange,
    hideKeyboard,
    platform,
  };
}
