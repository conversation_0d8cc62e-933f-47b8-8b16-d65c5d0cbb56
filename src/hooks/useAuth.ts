import {
  login as apiLogin,
  logout as apiLogout,
  register as apiRegister,
  sendSmsCode as apiSendSmsCode,
} from '@/api/auth';
import { useUserStore } from '@/stores/user.store';
import { computed, ref } from 'vue';

/**
 * 认证相关的 Composition API Hook
 */
export function useAuth() {
  const userStore = useUserStore();
  const isLoading = ref(false); // 加载状态
  const error = ref<Error | null>(null); // 错误状态

  // 从 store 获取计算属性，方便直接使用
  const isLoggedIn = computed(() => userStore.isLoggedIn);
  const userName = computed(() => userStore.userName);
  const userAvatar = computed(() => userStore.userAvatar);

  /**
   * 处理登录逻辑
   * @param credentials - 登录凭证
   * @returns {Promise<boolean>} - 登录是否成功
   */
  const handleLogin = async (credentials: Record<string, any>): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      const result = await apiLogin(credentials);
      // 使用 store 的 action 更新状态和持久化 token
      userStore.setToken(result.token);
      userStore.setUserInfo(result.userInfo);

      // 设置手势密码状态
      if (result.needSetupSecurity === false) {
        userStore.setGestureStatus(true);
      }
      else {
        userStore.setGestureStatus(false);
      }

      return true; // 登录成功
    }
    catch (err: any) {
      console.error('Login failed in useAuth:', err);
      error.value = err instanceof Error ? err : new Error(err.message || '登录失败');
      // 清理可能残留的状态 (如果需要)
      // userStore.logout();
      return false; // 登录失败
    }
    finally {
      isLoading.value = false;
    }
  };

  /**
   * 发送短信验证码
   * @param phone - 手机号码
   * @param type - 验证码类型 (login, register, reset)
   * @returns {Promise<boolean>} - 发送是否成功
   */
  const sendSmsCode = async (
    phone: string,
    type: 'login' | 'register' | 'reset',
  ): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      await apiSendSmsCode(phone, type);
      return true; // 发送成功
    }
    catch (err: any) {
      console.error('Send SMS code failed in useAuth:', err);
      error.value = err instanceof Error ? err : new Error(err.message || '发送验证码失败');
      return false; // 发送失败
    }
    finally {
      isLoading.value = false;
    }
  };

  /**
   * 处理注册逻辑
   * @param userInfo - 用户注册信息
   * @returns {Promise<boolean>} - 注册是否成功
   */
  const handleRegister = async (userInfo: Record<string, any>): Promise<boolean> => {
    isLoading.value = true;
    error.value = null;
    try {
      await apiRegister(userInfo);
      // 注册成功通常提示用户去登录，或根据业务逻辑自动登录
      uni.showToast({ title: '注册成功，请登录', icon: 'success' });
      return true; // 注册成功
    }
    catch (err: any) {
      console.error('Register failed in useAuth:', err);
      error.value = err instanceof Error ? err : new Error(err.message || '注册失败');
      return false; // 注册失败
    }
    finally {
      isLoading.value = false;
    }
  };

  /**
   * 处理退出登录逻辑
   */
  const handleLogout = async () => {
    isLoading.value = true;
    error.value = null;
    try {
      await apiLogout(); // 调用API（如果需要通知后端）
      await userStore.logout(); // 调用 store 的 action 清理本地状态和 token
      uni.showToast({ title: '已退出登录', icon: 'none' });
    }
    catch (err: any) {
      console.error('Logout failed in useAuth:', err);
      error.value = err instanceof Error ? err : new Error(err.message || '退出登录失败');
      // 即使退出失败，也尝试清理本地状态
      await userStore.logout();
    }
    finally {
      isLoading.value = false;
    }
  };

  // 导出响应式状态和方法
  return {
    isLoading,
    error,
    isLoggedIn,
    userName,
    userAvatar,
    handleLogin,
    handleRegister,
    handleLogout,
    sendSmsCode,
  };
}
