import config from '@/config';
/**
 * 表单验证hooks
 * 提供常用表单验证功能，如手机号、验证码、用户名等
 */

/**
 * 验证结果类型定义
 */
interface ValidationResult {
  valid: boolean
  message: string
}

/**
 * 验证规则类型定义
 */
interface ValidationRule {
  validate: (value: any) => boolean
  message: string
}

/**
 * 表单验证hooks
 * @returns 表单验证相关方法
 */
export function useValidation() {
  /**
   * 通用验证方法
   * @param value - 需要验证的值
   * @param rules - 验证规则数组
   * @returns 验证结果
   */
  const validate = (value: any, rules: ValidationRule[]): ValidationResult => {
    // 如果值为空且不是必填项，则跳过验证
    if (
      (value === '' || value === null || value === undefined)
      && !rules.some(rule => rule.validate === requiredValidator)
    ) {
      return { valid: true, message: '' };
    }

    // 按顺序验证规则
    for (const rule of rules) {
      if (!rule.validate(value)) {
        return { valid: false, message: rule.message };
      }
    }

    return { valid: true, message: '' };
  };

  /**
   * 必填验证器
   * @param value - 需要验证的值
   * @returns 是否通过验证
   */
  const requiredValidator = (value: any): boolean => {
    if (Array.isArray(value))
      return value.length > 0;
    if (typeof value === 'object' && value !== null)
      return Object.keys(value).length > 0;
    return value !== '' && value !== null && value !== undefined;
  };

  /**
   * 手机号格式验证器
   * @param value - 手机号
   * @returns 是否通过验证
   */
  const phoneValidator = (value: string): boolean => config.validation.phoneRegex.test(value);

  /**
   * 验证码长度验证器
   * @param value - 验证码
   * @param length - 需要的验证码长度，默认6位
   * @returns 是否通过验证
   */
  const codeValidator = (value: string, length = 6): boolean => {
    if (typeof value !== 'string')
      return false;
    return value.length === length && /^\d+$/.test(value);
  };

  /**
   * 用户名长度验证器
   * @param value - 用户名
   * @returns 是否通过验证
   */
  const usernameValidator = (value: string): boolean => {
    if (typeof value !== 'string')
      return false;
    const minLength = 2;
    const maxLength = config.validation.usernameMaxLength;
    return value.length >= minLength && value.length <= maxLength;
  };

  /**
   * 密码验证器
   * @param value - 密码
   * @returns 是否通过验证
   */
  const passwordValidator = (value: string): boolean => {
    if (typeof value !== 'string')
      return false;
    const minLength = config.validation.passwordMinLength;
    return value.length >= minLength;
  };

  /**
   * 邮箱格式验证器
   * @param value - 邮箱
   * @returns 是否通过验证
   */
  const emailValidator = (value: string): boolean => {
    if (typeof value !== 'string')
      return false;
    const emailRegex = /^[\w.%+-]+@[a-z0-9.-]+\.[a-z]{2,}$/i;
    return emailRegex.test(value);
  };

  /**
   * 日期格式验证器
   * @param value - 日期字符串
   * @returns 是否通过验证
   */
  const dateValidator = (value: string): boolean => {
    if (typeof value !== 'string')
      return false;
    const date = new Date(value);
    return !isNaN(date.getTime());
  };

  /**
   * 验证手机号
   * @param phoneNumber - 手机号
   * @param customMessage - 自定义错误信息
   * @returns 验证结果
   */
  const validatePhone = (phoneNumber: string, customMessage?: string): ValidationResult => validate(phoneNumber, [
      {
        validate: phoneValidator,
        message: customMessage || '请输入正确的手机号码',
      },
    ]);

  /**
   * 验证必填项
   * @param value - 值
   * @param fieldName - 字段名称
   * @returns 验证结果
   */
  const validateRequired = (value: any, fieldName = '该项'): ValidationResult => validate(value, [
      {
        validate: requiredValidator,
        message: `请输入${fieldName}`,
      },
    ]);

  /**
   * 验证短信验证码
   * @param code - 验证码
   * @param customMessage - 自定义错误信息
   * @returns 验证结果
   */
  const validateSmsCode = (code: string, customMessage?: string): ValidationResult => validate(code, [
      {
        validate: value => codeValidator(value),
        message: customMessage || '请输入6位数字验证码',
      },
    ]);

  /**
   * 验证用户名
   * @param username - 用户名
   * @param customMessage - 自定义错误信息
   * @returns 验证结果
   */
  const validateUsername = (username: string, customMessage?: string): ValidationResult => validate(username, [
      {
        validate: usernameValidator,
        message: customMessage || `用户名长度应在2-${config.validation.usernameMaxLength}个字符之间`,
      },
    ]);

  /**
   * 验证密码
   * @param password - 密码
   * @param customMessage - 自定义错误信息
   * @returns 验证结果
   */
  const validatePassword = (password: string, customMessage?: string): ValidationResult => validate(password, [
      {
        validate: passwordValidator,
        message: customMessage || `密码长度不能少于${config.validation.passwordMinLength}位`,
      },
    ]);

  /**
   * 验证邮箱
   * @param email - 邮箱
   * @param customMessage - 自定义错误信息
   * @returns 验证结果
   */
  const validateEmail = (email: string, customMessage?: string): ValidationResult => validate(email, [
      {
        validate: emailValidator,
        message: customMessage || '请输入正确的邮箱地址',
      },
    ]);

  return {
    validate,
    validatePhone,
    validateRequired,
    validateSmsCode,
    validateUsername,
    validatePassword,
    validateEmail,
    // 导出验证器方便自定义验证规则
    validators: {
      requiredValidator,
      phoneValidator,
      codeValidator,
      usernameValidator,
      passwordValidator,
      emailValidator,
      dateValidator,
    },
  };
}
