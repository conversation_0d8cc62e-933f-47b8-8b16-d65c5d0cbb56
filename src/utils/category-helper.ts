import type { TransactionCategory } from '@/types/transaction';

// 默认颜色和图标
const DEFAULT_STYLE = {
  backgroundColor: 'var(--bg-secondary, #f5f5f5)', // Use CSS variable for default
  color: 'var(--text-secondary, #666)',
};
const DEFAULT_ICON = 'question-circle'; // Default icon

// 示例：实际应用中应从配置或API获取
const categoryStyles: Record<string, { icon: string, bgColor: string, color: string }> = {
  // 支出
  餐饮: {
    icon: 'utensils',
    bgColor: 'var(--cat-bg-orange, #FFF5F2)',
    color: 'var(--cat-color-orange, #FF6B35)',
  },
  购物: {
    icon: 'cart-shopping',
    bgColor: 'var(--cat-bg-blue, #E0F2FE)',
    color: 'var(--cat-color-blue, #0EA5E9)',
  },
  交通: {
    icon: 'bus-alt',
    bgColor: 'var(--cat-bg-yellow, #FEFCE8)',
    color: 'var(--cat-color-yellow, #F59E0B)',
  },
  娱乐: {
    icon: 'gamepad',
    bgColor: 'var(--cat-bg-purple, #F3E8FF)',
    color: 'var(--cat-color-purple, #8B5CF6)',
  },
  住房: {
    icon: 'house',
    bgColor: 'var(--cat-bg-indigo, #E0E7FF)',
    color: 'var(--cat-color-indigo, #4F46E5)',
  },
  通讯: {
    icon: 'phone',
    bgColor: 'var(--cat-bg-cyan, #DFFEFF)',
    color: 'var(--cat-color-cyan, #0891B2)',
  },
  医疗健康: {
    icon: 'medkit',
    bgColor: 'var(--cat-bg-red, #FEE2E2)',
    color: 'var(--cat-color-red, #EF4444)',
  },
  其他支出: {
    icon: 'circle-question',
    bgColor: 'var(--cat-bg-gray, #F3F4F6)',
    color: 'var(--cat-color-gray, #6B7280)',
  },
  // 收入
  工资: {
    icon: 'wallet',
    bgColor: 'var(--cat-bg-green, #ECFDF5)',
    color: 'var(--cat-color-green, #10B981)',
  },
  奖金: {
    icon: 'gift',
    bgColor: 'var(--cat-bg-emerald, #D1FAE5)',
    color: 'var(--cat-color-emerald, #059669)',
  },
  理财收入: {
    icon: 'chart-line',
    bgColor: 'var(--cat-bg-lime, #F7FEE7)',
    color: 'var(--cat-color-lime, #84CC16)',
  },
};

/**
 * 获取分类的背景和图标颜色
 * @param category - 分类对象或名称字符串
 * @returns { backgroundColor: string; color: string; }
 */
export function getCategoryStyle(category: TransactionCategory | string | undefined | null): { backgroundColor: string, color: string } {
  if (!category) {
    return DEFAULT_STYLE;
  }
  const categoryName = typeof category === 'string' ? category : category.name;
  const style = categoryStyles[categoryName];
  if (style) {
    return { backgroundColor: style.bgColor, color: style.color };
  }
  return DEFAULT_STYLE;
}

/**
 * 获取分类的图标名称
 * @param category - 分类对象或名称字符串
 * @returns 图标名称 (Font Awesome)
 */
export function getCategoryIconName(category: TransactionCategory | string | undefined | null): string {
  if (!category) {
    return DEFAULT_ICON;
  }
  const categoryName = typeof category === 'string' ? category : category.name;
  const style = categoryStyles[categoryName];
  if (style && style.icon) {
    return style.icon;
  }
  // 如果是对象但没找到映射，尝试用对象里的icon
  if (typeof category === 'object' && category.icon) {
    return category.icon;
  }
  return DEFAULT_ICON;
}

// 用于进度条的预定义颜色数组
const progressBarColors = [
  'var(--cat-color-orange, #FF6B35)',  // 橙色
  'var(--cat-color-blue, #0EA5E9)',    // 蓝色
  'var(--cat-color-purple, #8B5CF6)',  // 紫色
  'var(--cat-color-green, #10B981)',   // 绿色
  'var(--cat-color-red, #EF4444)',     // 红色
  'var(--cat-color-yellow, #F59E0B)',  // 黄色
  'var(--cat-color-indigo, #4F46E5)',  // 靛蓝色
  'var(--cat-color-lime, #84CC16)',    // 青柠色
  'var(--cat-color-cyan, #0891B2)',    // 青色
  'var(--cat-color-emerald, #059669)', // 祖母绿色
  'var(--cat-color-gray, #6B7280)',    // 灰色
  'var(--cat-color-pink, #EC4899)',    // 粉色
  'var(--cat-color-teal, #14B8A6)',    // 蓝绿色
  'var(--cat-color-amber, #D97706)',   // 琥珀色
  'var(--cat-color-fuchsia, #D946EF)', // 紫红色
];

/**
 * 为分类数据生成唯一的颜色，确保每个分类有不同的颜色
 * @param categories - 分类数据数组
 * @param colorKey - 颜色字段名称（如果存在）
 * @returns 添加了唯一颜色的分类数组
 */
/**
 * 为分类数据生成唯一的颜色，确保每个分类有不同的颜色
 * @param categories - 分类数据数组
 * @returns 添加了唯一颜色的分类数组
 */
export function getUniqueColors<T extends { color?: string; id: string; name: string; }>(
  categories: T[],
): T[] {
  // 增强防御性检查
  if (!categories || !Array.isArray(categories) || categories.length === 0) {
    console.warn('getUniqueColors接收到空或无效的分类数组');
    return [];
  }

  try {
    // 创建一个结果数组的副本
    const result = [...categories];
    
    // 先尝试使用分类的原有颜色
    const usedColorIndices = new Set<number>();
    const colorMap = new Map<string, number>(); // 保存id到颜色索引的映射

    // 第一步：为已有颜色的分类分配其原有颜色在数组中的索引
    result.forEach((cat) => {
      if (!cat || typeof cat !== 'object') {
        console.warn('分类项不是有效对象:', cat);
        return;
      }
      
      if (cat.color) {
        try {
          // 尝试找到匹配的预定义颜色索引
          const colorIndex = progressBarColors.findIndex(color => 
            color === cat.color || 
            // 检查CSS变量的默认值是否匹配
            (cat.color?.includes('var(') && 
             color.indexOf('#') !== -1 &&
             cat.color?.includes(color.substring(color.indexOf('#')))),
          );
          
          if (colorIndex !== -1 && !usedColorIndices.has(colorIndex)) {
            usedColorIndices.add(colorIndex);
            colorMap.set(cat.id, colorIndex);
          }
        } catch (error) {
          console.error('处理分类颜色时出错:', error, cat);
        }
      }
    });

    // 第二步：为没有颜色或颜色冲突的分类分配新的唯一颜色
    result.forEach((cat, index) => {
      if (!cat || typeof cat !== 'object') return;
      
      if (!cat.id) {
        console.warn('分类项缺少ID:', cat);
        // 为没有ID的分类创建一个临时ID
        cat.id = `temp-cat-${index}`;
      }
      
      if (!colorMap.has(cat.id)) {
        // 找到一个未使用的颜色索引
        let newColorIndex = 0;
        while (usedColorIndices.has(newColorIndex) && newColorIndex < progressBarColors.length) {
          newColorIndex++;
        }
        
        // 如果所有颜色都被使用了，循环使用
        if (newColorIndex >= progressBarColors.length) {
          newColorIndex = index % progressBarColors.length;
        }
        
        // 分配颜色并标记为已使用
        cat.color = progressBarColors[newColorIndex];
        usedColorIndices.add(newColorIndex);
        colorMap.set(cat.id, newColorIndex);
      } else {
        // 确保使用的是progressBarColors中的颜色
        const colorIndex = colorMap.get(cat.id);
        if (colorIndex !== undefined && colorIndex >= 0 && colorIndex < progressBarColors.length) {
          cat.color = progressBarColors[colorIndex];
        } else {
          // 处理无效的colorIndex
          const fallbackIndex = index % progressBarColors.length;
          cat.color = progressBarColors[fallbackIndex];
          console.warn(`分类'${cat.name}' (${cat.id})的颜色索引无效 (${colorIndex})，使用备用颜色`);
        }
      }
    });

  return result;
  } catch (error) {
    console.error('getUniqueColors函数执行出错:', error);
    // 返回原始数组，确保至少有数据显示，但给每个项添加默认颜色
    return categories.map((cat, index) => {
      if (cat && !cat.color) {
        // 分配一个基于索引的默认颜色
        const defaultIndex = index % progressBarColors.length;
        return { ...cat, color: progressBarColors[defaultIndex] };
      }
      return cat;
    });
  }
}
