import config from '@/config'; // For potential future use (e.g., prefixing keys)

// Define constants for storage keys to avoid magic strings
const KEY_PREFIX = config.storage.prefix; // Use the prefix from config

// 用户相关键
const USER_TOKEN_KEY = config.storage.keys.TOKEN;
const USER_INFO_KEY = config.storage.keys.USER_INFO;
const HAS_SET_GESTURE_KEY = config.storage.keys.GESTURE_PASSWORD;
const THEME_MODE_KEY = config.storage.keys.THEME;

// 本地账本数据库 (localLedgerDB) 相关键
const LEDGER_PREFIX = `${KEY_PREFIX}ledger_`;
const TRANSACTIONS_KEY = `${LEDGER_PREFIX}transactions`;
const CATEGORIES_KEY = `${LEDGER_PREFIX}categories`;
const ASSETS_KEY = `${LEDGER_PREFIX}assets`;
const BUDGET_KEY = `${LEDGER_PREFIX}budget`;
const CHAT_MESSAGES_KEY = `${LEDGER_PREFIX}chat_messages`;
const CONFIRMED_TRANSACTIONS_KEY = `${LEDGER_PREFIX}confirmed_transactions`;

/**
 * Sets an item in the synchronous local storage.
 * Automatically stringifies objects/arrays.
 * @param key - The key under which to store the value.
 * @param value - The value to store. Can be any type that can be JSON-stringified.
 * @returns True if the operation was successful, false otherwise.
 */
function setItem(key: string, value: any): boolean {
  try {
    const data = JSON.stringify(value);
    uni.setStorageSync(key, data);
    console.log(`[Storage] Set key "${key}" successfully.`);
    return true;
  }
  catch (error) {
    console.error(`[Storage] Error setting key "${key}":`, error);
    // Potentially handle specific errors like quota exceeded
    // uni.showToast({ title: '本地存储空间不足', icon: 'none' });
    return false;
  }
}

/**
 * Gets an item from the synchronous local storage.
 * Automatically parses JSON strings.
 * @param key - The key of the item to retrieve.
 * @param defaultValue - The default value to return if the key doesn't exist or an error occurs.
 * @returns The retrieved value, parsed if it was JSON, or the defaultValue.
 */
function getItem<T>(key: string, defaultValue: T | null = null): T | null {
  try {
    const data = uni.getStorageSync(key);
    if (data === '' || data === null || typeof data === 'undefined') {
      console.log(`[Storage] Key "${key}" not found, returning default value.`);
      return defaultValue;
    }
    // Attempt to parse JSON, fallback to raw data if parsing fails
    try {
      const parsed = JSON.parse(data);
      console.log(`[Storage] Get key "${key}" successfully (parsed).`);
      return parsed as T;
    }
    catch (parseError) {
      console.warn(`[Storage] Value for key "${key}" is not valid JSON, returning raw value.`);
      return data as T; // Return raw data if it's not JSON
    }
  }
  catch (error) {
    console.error(`[Storage] Error getting key "${key}":`, error);
    return defaultValue;
  }
}

/**
 * Removes an item from the synchronous local storage.
 * @param key - The key of the item to remove.
 * @returns True if the operation was successful, false otherwise.
 */
function removeItem(key: string): boolean {
  try {
    uni.removeStorageSync(key);
    console.log(`[Storage] Removed key "${key}" successfully.`);
    return true;
  }
  catch (error) {
    console.error(`[Storage] Error removing key "${key}":`, error);
    return false;
  }
}

/**
 * Clears all items from the synchronous local storage managed by this app (using prefix if defined).
 * Note: `uni.clearStorageSync()` clears everything, potentially affecting other apps/modules
 * if not used carefully. A safer approach might be to remove keys individually based on prefix.
 * This implementation uses the safer approach if KEY_PREFIX is set.
 * @returns True if the operation was successful, false otherwise.
 */
function clear(): boolean {
  try {
    if (KEY_PREFIX) {
      const { keys } = uni.getStorageInfoSync();
      keys.forEach((key) => {
        if (key.startsWith(KEY_PREFIX)) {
          uni.removeStorageSync(key);
        }
      });
      console.log(`[Storage] Cleared all items with prefix "${KEY_PREFIX}".`);
    }
    else {
      // Fallback to clearing everything if no prefix is defined (use with caution)
      uni.clearStorageSync();
      console.warn('[Storage] Cleared all synchronous storage (no prefix defined).');
    }
    return true;
  }
  catch (error) {
    console.error('[Storage] Error clearing storage:', error);
    return false;
  }
}

/**
 * 清除所有本地账本数据
 * 只清除与localLedgerDB相关的数据，保留用户偏好设置等
 * @returns 是否成功清除
 */
function clearLedgerData(): boolean {
  try {
    const ledgerKeys = [
      TRANSACTIONS_KEY,
      CATEGORIES_KEY, 
      ASSETS_KEY,
      BUDGET_KEY,
      CHAT_MESSAGES_KEY,
      CONFIRMED_TRANSACTIONS_KEY
    ];
    
    ledgerKeys.forEach(key => {
      uni.removeStorageSync(key);
    });
    
    console.log('[Storage] Cleared all ledger data successfully.');
    return true;
  } catch (error) {
    console.error('[Storage] Error clearing ledger data:', error);
    return false;
  }
}

/**
 * 获取本地账本存储信息
 * @returns 包含总大小和各类数据条目数的信息
 */
function getLedgerStorageInfo() {
  try {
    const info = uni.getStorageInfoSync();
    const ledgerKeys = info.keys.filter(key => key.startsWith(LEDGER_PREFIX));
    
    const result = {
      totalSize: info.currentSize,
      limitSize: info.limitSize,
      ledgerKeys: ledgerKeys.length,
      transactions: getItem(TRANSACTIONS_KEY, [])?.length || 0,
      categories: getItem(CATEGORIES_KEY, [])?.length || 0,
      assets: getItem(ASSETS_KEY, [])?.length || 0,
      chatMessages: getItem(CHAT_MESSAGES_KEY, [])?.length || 0,
    };
    
    return result;
  } catch (error) {
    console.error('[Storage] Error getting ledger storage info:', error);
    return null;
  }
}

// TODO: Integrate encryption/decryption using crypto.js when available
// Example structure:
// import { encrypt, decrypt } from '@/utils/crypto';
// const ENCRYPTED_KEYS = [USER_TOKEN_KEY]; // Keys to automatically encrypt/decrypt

export const storage = {
  // 基础方法
  setItem,
  getItem,
  removeItem,
  clear,
  
  // 账本数据方法
  clearLedgerData,
  getLedgerStorageInfo,
  
  // 用户相关键
  USER_TOKEN_KEY,
  USER_INFO_KEY,
  HAS_SET_GESTURE_KEY,
  THEME_MODE_KEY,
  
  // 本地账本数据库键
  TRANSACTIONS_KEY,
  CATEGORIES_KEY,
  ASSETS_KEY,
  BUDGET_KEY,
  CHAT_MESSAGES_KEY,
  CONFIRMED_TRANSACTIONS_KEY,
};
