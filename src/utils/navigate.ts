/**
 * 导航工具函数模块
 * 提供应用内各种导航跳转的统一封装
 */

/**
 * 导航到首页
 * 使用 switchTab 确保跳转到 tabBar 页面
 */
export function navigateHome(): void {
  console.log('[NAV] 导航到首页');
  uni.switchTab({
    url: '/pages/home/<USER>',
    success: () => console.log('[NAV] 跳转到首页成功'),
    fail: err => console.error('[NAV] 跳转到首页失败:', err),
  });
}

/**
 * 导航到登录页
 * 使用 reLaunch 清除当前页面栈
 */
export function navigateToLogin(): void {
  console.log('[NAV] 导航到登录页');
  uni.reLaunch({
    url: '/pages/auth/login',
    success: () => console.log('[NAV] 跳转到登录页成功'),
    fail: err => console.error('[NAV] 跳转到登录页失败:', err),
  });
}

/**
 * 导航到设置页
 */
export function navigateToSettings(): void {
  console.log('[NAV] 导航到设置页');
  uni.navigateTo({
    url: '/pages/profile/settings',
    success: () => console.log('[NAV] 跳转到设置页成功'),
    fail: err => console.error('[NAV] 跳转到设置页失败:', err),
  });
}

/**
 * 返回上一页
 * @param delta 返回的页面数，默认 1
 */
export function navigateBack(delta: number = 1): void {
  console.log(`[NAV] 返回上一页，delta: ${delta}`);
  uni.navigateBack({
    delta,
    success: () => console.log('[NAV] 返回成功'),
    fail: (err) => {
      console.error('[NAV] 返回失败:', err);
      // 返回失败时自动跳转到首页
      navigateHome();
    },
  });
}

/**
 * 导航到交易记录页
 */
export function navigateToTransactionList(): void {
  console.log('[NAV] 导航到交易记录页');
  uni.switchTab({
    url: '/pages/transaction/list',
    success: () => console.log('[NAV] 跳转到交易记录页成功'),
    fail: err => console.error('[NAV] 跳转到交易记录页失败:', err),
  });
}

/**
 * 安全返回，如果无法返回则导航到首页
 * @param confirmOptions 确认对话框选项，如果提供则显示确认对话框
 */
export function safeNavigateBack(confirmOptions?: {
  title: string
  content: string
  confirmText?: string
  cancelText?: string
}): void {
  if (confirmOptions) {
    uni.showModal({
      title: confirmOptions.title,
      content: confirmOptions.content,
      confirmText: confirmOptions.confirmText || '确定',
      cancelText: confirmOptions.cancelText || '取消',
      success: (res) => {
        if (res.confirm) {
          navigateBack();
        }
      },
    });
  }
  else {
    navigateBack();
  }
}

/**
 * 检查页面栈并导航
 * 如果页面栈中包含指定页面，则返回到该页面
 * 否则跳转到指定页面
 * @param pageUrl 页面路径
 * @param pagePattern 页面路径包含的字符串，用于匹配
 */
export function navigateToOrBack(pageUrl: string, pagePattern: string): void {
  const pages = getCurrentPages();
  const targetIndex = pages.findIndex(page => page.route?.includes(pagePattern));

  if (targetIndex >= 0) {
    // 计算需要返回的页面数
    const delta = pages.length - 1 - targetIndex;
    if (delta > 0) {
      navigateBack(delta);
    }
  }
  else {
    // 没有找到目标页面，直接跳转
    if (
      pageUrl.includes('/pages/home/')
      || pageUrl.includes('/pages/transaction/')
      || pageUrl.includes('/pages/analysis/')
      || pageUrl.includes('/pages/profile/')
    ) {
      // TabBar 页面使用 switchTab
      uni.switchTab({ url: pageUrl });
    }
    else {
      // 非 TabBar 页面使用 navigateTo
      uni.navigateTo({ url: pageUrl });
    }
  }
}

/**
 * 检查当前的来源页面
 * @param patterns 页面路径模式数组，用于匹配
 * @returns 是否来自指定的页面
 */
export function isFromPages(patterns: string[]): boolean {
  const pages = getCurrentPages();
  if (pages.length <= 1)
    return false;

  // 检查上一个页面是否匹配任一模式
  const prevPage = pages[pages.length - 2];
  return patterns.some(pattern => prevPage.route?.includes(pattern));
}
