/**
 * 字体预加载工具
 * 用于确保关键字体资源在应用启动时就开始加载，特别是在H5环境和iOS模拟器中
 */

import { uViewPlusIconConfig } from '@/config/icon.config';
import { isIOSSimulator, getPlatformType, PLATFORM } from './platform';

/**
 * 创建预加载链接
 * 用于在文档头部添加预加载标签
 * @param href 资源URL
 * @param as 资源类型
 * @param type 资源MIME类型
 */
function createPreloadLink(href: string, as: string = 'font', type: string = 'font/ttf') {
  // 只在H5环境执行
  // #ifdef H5
  if (typeof document === 'undefined') return;

  // 检查是否已存在相同的预加载链接
  const existingLink = document.querySelector(`link[href="${href}"]`);
  if (existingLink) return;

  // 创建预加载链接
  const link = document.createElement('link');
  link.rel = 'preload';
  link.href = href;
  link.as = as;
  link.type = type;
  link.crossOrigin = 'anonymous';
  
  // 添加到文档头部
  document.head.appendChild(link);
  console.log(`[字体预加载] 已预加载资源: ${href}`);
  // #endif
}

/**
 * 预加载所有关键字体资源
 * @param forcePlatform 强制为特定平台加载
 */
export function preloadFonts(forcePlatform?: string) {
  try {
    // 获取平台类型
    const platform = forcePlatform || getPlatformType();
    
    // 预加载图标字体 - 不同平台可能有不同路径
    if (platform === PLATFORM.IOS || platform === PLATFORM.H5) {
      // iOS和H5环境
      let fontPath = uViewPlusIconConfig.fontPath;
      
      // iOS模拟器环境特殊处理
      if (platform === PLATFORM.IOS && isIOSSimulator()) {
        console.log('[字体预加载] 检测到iOS模拟器环境，使用特殊字体路径');
        // 尝试使用多种路径格式，提高iOS模拟器环境下的字体加载成功率
        // 在iOS模拟器中手动加载字体
        // #ifdef APP-PLUS
        try {
          // @ts-ignore - plus对象在iOS模拟器可能有特殊行为
          if (plus && plus.io) {
            console.log('[字体预加载] 尝试使用plus.io API手动加载字体');
          }
        } catch (err) {
          console.error('[字体预加载] plus.io API不可用:', err);
        }
        // #endif
        
        // 为iOS模拟器设置字体文件路径
        fontPath = 'static/fonts/uicon-iconfont.ttf';
      }
      
      // H5环境下使用预加载链接
      // #ifdef H5
      createPreloadLink(fontPath, 'font', 'font/ttf');
      // #endif
      
      console.log(`[字体预加载] 为${platform}平台预加载字体: ${fontPath}`);
    }
    
    // App环境下加载字体
    // #ifdef APP-PLUS
    if (platform === PLATFORM.IOS || platform === PLATFORM.ANDROID) {
      // 尝试主动触发字体加载
      const dummyEl = document.createElement('view');
      dummyEl.style.fontFamily = 'uicon-iconfont';
      dummyEl.style.opacity = '0';
      dummyEl.textContent = 'A';
      
      // 插入并移除，触发字体加载
      if (document.body) {
        document.body.appendChild(dummyEl);
        setTimeout(() => {
          if (document.body.contains(dummyEl)) {
            document.body.removeChild(dummyEl);
          }
        }, 100);
      }
      
      console.log('[字体预加载] App环境下触发字体加载');
    }
    // #endif
  } catch (error) {
    console.error('[字体预加载] 字体预加载失败:', error);
  }
  
  console.log('[字体预加载] 字体预加载已初始化');
}

/**
 * 检查字体是否已加载
 * @param fontFamily 字体名称
 * @returns Promise<boolean> 字体是否加载成功
 */
export function checkFontLoaded(fontFamily: string): Promise<boolean> {
  // 只在H5环境执行
  // #ifdef H5
  if (typeof document === 'undefined') {
    return Promise.resolve(false);
  }
  
  return new Promise((resolve) => {
    // 创建使用目标字体的元素
    const testSpan = document.createElement('span');
    testSpan.style.visibility = 'hidden';
    testSpan.style.position = 'absolute';
    testSpan.style.fontFamily = `${fontFamily}, monospace`;
    testSpan.style.fontSize = '24px';
    testSpan.innerHTML = 'Test Font';
    document.body.appendChild(testSpan);
    
    // 获取元素宽度
    const width = testSpan.offsetWidth;
    
    // 修改为回退字体
    testSpan.style.fontFamily = 'monospace';
    const fallbackWidth = testSpan.offsetWidth;
    
    // 清理元素
    document.body.removeChild(testSpan);
    
    // 如果宽度不同，说明第一个字体已加载
    const isLoaded = width !== fallbackWidth;
    console.log(`[字体检查] 字体 ${fontFamily} 加载状态: ${isLoaded}`);
    resolve(isLoaded);
  });
  // #endif
  
  // 非H5环境 或 iOS模拟器
  // #ifndef H5
  // iOS模拟器需要特殊处理
  if (getPlatformType() === PLATFORM.IOS && isIOSSimulator()) {
    console.log('[字体检查] iOS模拟器环境下假设字体已加载');
    return Promise.resolve(true);
  }
  
  // 其他APP环境
  return Promise.resolve(true);
  // #endif
}

/**
 * 补丁修复iOS模拟器上的图标显示问题
 * 这个函数会在检测到iOS模拟器环境时自动调用
 */
export function fixIOSSimulatorIconDisplay() {
  if (getPlatformType() !== PLATFORM.IOS || !isIOSSimulator()) {
    return;
  }
  
  console.log('[字体修复] 开始修复iOS模拟器图标显示问题');
  
  // 重新预加载字体
  preloadFonts(PLATFORM.IOS);
  
  // #ifdef APP-PLUS
  try {
    // 尝试刷新UI
    setTimeout(() => {
      // 强制刷新
      const dummyEl = document.createElement('view');
      dummyEl.style.position = 'absolute';
      dummyEl.style.opacity = '0';
      dummyEl.style.zIndex = '-1';
      document.body.appendChild(dummyEl);
      
      setTimeout(() => {
        if (document.body.contains(dummyEl)) {
          document.body.removeChild(dummyEl);
        }
        console.log('[字体修复] iOS模拟器图标修复完成');
      }, 300);
    }, 500);
  } catch (error) {
    console.error('[字体修复] iOS模拟器修复失败:', error);
  }
  // #endif
}

// 自动执行修复
if (getPlatformType() === PLATFORM.IOS && isIOSSimulator()) {
  console.log('[字体预加载] 检测到iOS模拟器环境，自动执行修复');
  setTimeout(fixIOSSimulatorIconDisplay, 1000);
}

export default {
  preloadFonts,
  checkFontLoaded,
  createPreloadLink,
  fixIOSSimulatorIconDisplay
}; 