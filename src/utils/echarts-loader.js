// 确保 echarts 全局加载
import * as echarts from 'echarts/core';
import { 
  <PERSON><PERSON>hart,
  <PERSON><PERSON>hart,
  LineChart
} from 'echarts/charts';
import {
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent
} from 'echarts/components';
import {
  CanvasRenderer
} from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  BarChart,
  PieChart,
  LineChart,
  GridComponent,
  TooltipComponent,
  TitleComponent,
  LegendComponent,
  CanvasRenderer
]);

// 确保全局 echarts 对象始终存在
if (typeof window !== 'undefined' && !window.echarts) {
  window.echarts = echarts;
}

// 创建 chart 实例跟踪器
const chartInstances = new Map();

/**
 * 获取或创建图表实例
 * @param {string} id - 图表容器ID
 * @param {HTMLElement} [element] - 容器元素
 * @returns {object} echarts实例
 */
export function getChartInstance(id, element = null) {
  // 如果已存在实例且未被销毁，返回该实例
  if (chartInstances.has(id)) {
    const existingInstance = chartInstances.get(id);
    if (existingInstance && !existingInstance.isDisposed()) {
      return existingInstance;
    }
  }

  // 如果提供了元素，创建新实例
  if (element) {
    const instance = echarts.init(element);
    instance.__chartId = id; // 添加自定义ID
    chartInstances.set(id, instance);
    return instance;
  }

  return null;
}

/**
 * 销毁图表实例
 * @param {string} id - 图表容器ID
 */
export function disposeChartInstance(id) {
  if (chartInstances.has(id)) {
    const instance = chartInstances.get(id);
    if (instance && !instance.isDisposed()) {
      instance.dispose();
    }
    chartInstances.delete(id);
  }
}

/**
 * 获取所有图表实例
 * @returns {Map} 所有图表实例的Map
 */
export function getAllChartInstances() {
  return chartInstances;
}

export default echarts; 