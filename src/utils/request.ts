import config from '@/config'; // 引入配置文件
import { useUserStore } from '@/stores/user.store';

// 基础配置
const BASE_URL = import.meta.env.VITE_API_BASE_URL || config.api.BASE_URL; // 优先使用环境变量
const TIMEOUT = config.api.TIMEOUT || 10000; // 从配置获取超时时间
const RETRY_COUNT = config.api.RETRY_COUNT || 3; // 从配置获取重试次数

// 封装请求函数
function httpRequest<T = any>(options: UniApp.RequestOptions): Promise<T> {
  const userStore = useUserStore(); // 获取用户 store

  return new Promise((resolve, reject) => {
    // --- 请求拦截 --- (在这里添加逻辑，例如设置请求头)
    const header = {
      ...options.header,
      'Content-Type': 'application/json;charset=UTF-8',
    };
    // 如果存在 token 且配置了自动包含token，则添加到请求头
    if (userStore.token && config.api.AUTO_INCLUDE_TOKEN) {
      header.Authorization = `Bearer ${userStore.token}`;
    }

    uni.request({
      ...options,
      url: BASE_URL + options.url, // 拼接完整请求地址
      timeout: options.timeout || TIMEOUT, // 设置超时时间
      header, // 设置处理后的请求头

      success: (res) => {
        // --- 响应拦截 --- (在这里处理响应数据)
        if (res.statusCode >= 200 && res.statusCode < 300) {
          // 假设成功的响应体结构为 { code: 0, data: ..., message: '' }
          const responseData = res.data as { code: number, data: T, message: string };

          if (responseData.code === 0) {
            // 请求成功，返回核心数据
            resolve(responseData.data);
          }
          else if (responseData.code === 401) {
            // 假设 401 为未授权或 token 过期
            console.error('Request Error: Unauthorized or Token Expired');
            // 可以触发重新登录逻辑
            userStore.logout(); // 示例：调用登出 action
            uni.showToast({ title: '请重新登录', icon: 'none' });
            // 跳转到登录页 (根据实际路由配置)
            // uni.navigateTo({ url: '/pages/auth/login' });
            reject(new Error(responseData.message || 'Unauthorized'));
          }
          else {
            // 其他业务错误
            uni.showToast({ title: responseData.message || '请求失败', icon: 'none' });
            reject(
              new Error(responseData.message || `Request failed with code ${responseData.code}`),
            );
          }
        }
        else {
          // HTTP 状态码错误
          handleHttpError(res.statusCode, res.data);
          reject(new Error(`HTTP Error ${res.statusCode}`));
        }
      },

      fail: (err) => {
        // --- 错误处理 --- (处理网络错误、超时等)
        handleRequestError(err);
        reject(err);
      },
    });
  });
}

// HTTP 状态码错误处理
function handleHttpError(statusCode: number, data: any) {
  let message = `HTTP Error ${statusCode}`;
  switch (statusCode) {
    case 400:
      message = '请求参数错误';
      break;
    case 401:
      message = '未授权，请登录';
      // 可能需要跳转登录页或刷新 token
      break;
    case 403:
      message = '禁止访问';
      break;
    case 404:
      message = '请求资源不存在';
      break;
    case 500:
    case 502:
    case 503:
    case 504:
      message = '服务器错误，请稍后重试';
      break;
    // 可以添加更多 case
  }
  console.error('HTTP Error:', statusCode, data);
  uni.showToast({ title: data?.message || message, icon: 'none' });
}

// 请求错误处理 (网络、超时等)
function handleRequestError(err: any) {
  let message = '请求失败';
  if (err.errMsg) {
    if (err.errMsg.includes('timeout')) {
      message = '请求超时';
    }
    else if (err.errMsg.includes('network error')) {
      message = '网络连接错误';
    }
    else {
      message = err.errMsg; // 显示具体的 uni.request 错误信息
    }
  }
  console.error('Request Fail:', err);
  uni.showToast({ title: message, icon: 'none' });
}

// 添加重试机制
// TODO: 根据项目需求扩展实现重试逻辑

// 导出封装好的 request 函数
export const request = {
  get: <T = any>(
    url: string,
    options?: Omit<UniApp.RequestOptions, 'url' | 'method'>,
  ): Promise<T> => httpRequest<T>({ ...options, url, method: 'GET' }),
  post: <T = any>(
    url: string,
    data?: any,
    options?: Omit<UniApp.RequestOptions, 'url' | 'method' | 'data'>,
  ): Promise<T> => httpRequest<T>({ ...options, url, data, method: 'POST' }),
  put: <T = any>(
    url: string,
    data?: any,
    options?: Omit<UniApp.RequestOptions, 'url' | 'method' | 'data'>,
  ): Promise<T> => httpRequest<T>({ ...options, url, data, method: 'PUT' }),
  delete: <T = any>(
    url: string,
    options?: Omit<UniApp.RequestOptions, 'url' | 'method'>,
  ): Promise<T> => httpRequest<T>({ ...options, url, method: 'DELETE' }),
};
