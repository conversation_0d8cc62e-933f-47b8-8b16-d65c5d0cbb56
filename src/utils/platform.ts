/**
 * 平台适配工具库
 * 用于处理多端差异，包含平台识别、样式获取、安全区域适配、特殊行为处理等功能
 */

import config from '@/config';

/**
 * 获取当前平台类型
 * @returns {string} 'ios' | 'android' | 'h5' | 'weapp' | 'unknown'
 */
export function getPlatformType(): 'ios' | 'android' | 'h5' | 'weapp' | 'unknown' {
  try {
    // 增强平台检测逻辑
    // #ifdef APP-PLUS
    const systemInfo = uni.getSystemInfoSync();
    if (systemInfo.platform === 'ios') return 'ios';
    if (systemInfo.platform === 'android') return 'android';
    // #endif
    
    // #ifdef H5
    return 'h5';
    // #endif
    
    // #ifdef MP-WEIXIN
    return 'weapp';
    // #endif
    
    // 兼容配置中的平台类型
    return config.platform.style.getPlatformType();
  } catch (error) {
    console.error('获取平台类型失败:', error);
    return 'unknown';
  }
}

/**
 * 获取当前平台类型 (兼容旧API)
 * @returns {string} 'ios' | 'android' | 'h5' | 'weapp' | 'unknown'
 */
export function getCurrentPlatform(): 'ios' | 'android' | 'h5' | 'weapp' | 'unknown' {
  return getPlatformType();
}

/**
 * 平台类型枚举
 */
export const PLATFORM = {
  IOS: 'ios',
  ANDROID: 'android',
  H5: 'h5',
  WEAPP: 'weapp',
  UNKNOWN: 'unknown'
};

/**
 * 检查当前环境是否为iOS模拟器
 * @returns {boolean} 是否为iOS模拟器
 */
export function isIOSSimulator(): boolean {
  // #ifdef APP-PLUS
  try {
    const systemInfo = uni.getSystemInfoSync();
    // iOS模拟器下model会包含'Simulator'字符串
    return systemInfo.platform === 'ios' && 
           (systemInfo.model.includes('Simulator') || 
            systemInfo.deviceModel?.includes('Simulator') || 
            // 其他特征检测
            (systemInfo.brand === 'Apple' && systemInfo.deviceId === 'simulator'));
  } catch (e) {
    console.error('检测iOS模拟器失败:', e);
    return false;
  }
  // #endif
  
  return false;
}

/**
 * 获取平台特定样式
 * @param {string} key - 样式键名
 * @returns {string} 样式值
 */
export function getPlatformStyle(key: string): string {
  // 针对iOS平台(特别是iOS模拟器)特殊处理某些样式
  if (getPlatformType() === 'ios') {
    switch (key) {
      case 'navBarHeight':
        // iOS模拟器可能需要特殊处理
        return isIOSSimulator() ? '88px' : '88px';
      case 'statusBarHeight':
        return isIOSSimulator() ? '44px' : '44px'; 
      case 'safeAreaBottom':
        // 根据是否有刘海屏返回不同的安全区域值
        return `env(safe-area-inset-bottom, ${isIOSSimulator() ? '0' : '34px'})`;
      // 其他iOS特有样式...
    }
  }
  
  // 使用配置中的平台样式
  return config.platform.style.getPlatformStyle(key);
}

/**
 * 获取安全区域内边距
 * @returns {object} 包含top, right, bottom, left的对象
 */
export function getSafeAreaInsets(): {
  top: number
  right: number
  bottom: number
  left: number
} {
  // 基础安全区域
  const insets = { top: 0, right: 0, bottom: 0, left: 0 };

  // #ifdef APP-PLUS
  try {
    const systemInfo = uni.getSystemInfoSync();
    // iOS
    if (systemInfo.platform === 'ios') {
      const safeArea = systemInfo.safeArea;
      // 刘海屏或模拟器环境
      if (safeArea) {
        insets.top = safeArea.top;
        insets.right = systemInfo.screenWidth - safeArea.right;
        insets.bottom = systemInfo.screenHeight - safeArea.bottom;
        insets.left = safeArea.left;
      } else if (isIOSSimulator()) {
        // 针对未能正确获取safeArea的iOS模拟器环境
        insets.top = 44; // 模拟器默认状态栏高度
        insets.bottom = 0; // 模拟器默认底部安全区域
      }
    }
  } catch (e) {
    console.error('获取安全区域失败:', e);
  }
  // #endif

  // #ifdef MP-WEIXIN
  // 小程序胶囊按钮适配
  try {
    const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    insets.top = menuButtonInfo.top;
    insets.right = menuButtonInfo.right;
  }
  catch (e) {
    console.error('获取小程序胶囊按钮信息失败', e);
  }
  // #endif

  return insets;
}

/**
 * 应用平台特定行为
 * @param {string} action - 行为名称
 * @param {any} params - 行为参数
 */
export function applyPlatformBehavior(action: string, params?: any): void {
  const platform = getPlatformType();
  
  switch (action) {
    case 'hideKeyboard':
      // 隐藏键盘
      uni.hideKeyboard();
      // 处理特定平台的附加行为
      // #ifdef APP-PLUS
      try {
        // @ts-ignore
        if (platform === 'ios' && plus.os.name === 'iOS') {
          // iOS特殊处理
          // @ts-ignore
          plus.key.hideSoftKeybord();
        } else {
          // Android
          // @ts-ignore
          plus.key.hideSoftKeybord();
        }
      }
      catch (e) {
        console.error('隐藏软键盘失败', e);
      }
      // #endif
      break;

    case 'setStatusBarStyle':
      // 设置状态栏样式(亮色/暗色)
      // #ifdef APP-PLUS
      try {
        if (platform === 'ios') {
          // iOS特殊处理
          // @ts-ignore
          plus.navigator.setStatusBarStyle(params === 'light' ? 'light' : 'dark');
        } else {
          // Android
          // @ts-ignore
          plus.navigator.setStatusBarStyle(params === 'light' ? 'light' : 'dark');
        }
      }
      catch (e) {
        console.error('设置状态栏样式失败', e);
      }
      // #endif

      // #ifdef MP-WEIXIN
      uni.setNavigationBarColor({
        frontColor: params === 'light' ? '#ffffff' : '#000000',
        backgroundColor: '#FF6B35',
      });
      // #endif
      break;
      
    case 'adaptToIOSSimulator':
      // iOS模拟器适配特殊处理
      if (platform === 'ios' && isIOSSimulator()) {
        console.log('正在对iOS模拟器进行特殊适配...');
        // 在这里添加iOS模拟器特有的适配逻辑
      }
      break;

    // 更多特定行为...
  }
}

/**
 * 检查当前平台是否支持特定功能
 * @param {string} feature - 功能名称
 * @returns {boolean} 是否支持
 */
export function isFeatureSupported(feature: string): boolean {
  const platform = getPlatformType();
  
  // 针对不同平台的功能支持检查
  switch (feature) {
    case 'safeArea':
      return platform === 'ios' || platform === 'weapp';
    case 'darkMode':
      return platform === 'ios' || platform === 'h5' || platform === 'weapp';
    case 'vibration':
      return platform !== 'unknown';
    // iOS模拟器特定功能
    case 'iosSimulatorFeatures':
      return platform === 'ios' && isIOSSimulator();
    default:
      return config.platform.features.isFeatureSupported(feature);
  }
}

// 执行平台初始化
function initPlatform() {
  console.log('[platform.ts] 平台类型:', getPlatformType());
  
  // 特殊处理iOS模拟器
  if (isIOSSimulator()) {
    console.log('[platform.ts] 检测到iOS模拟器环境，应用特殊适配');
    applyPlatformBehavior('adaptToIOSSimulator');
  }
}

// 自动执行初始化
initPlatform();

export default {
  getPlatformType,
  getCurrentPlatform,
  getPlatformStyle,
  getSafeAreaInsets,
  applyPlatformBehavior,
  isFeatureSupported,
  isIOSSimulator,
  PLATFORM,
};
