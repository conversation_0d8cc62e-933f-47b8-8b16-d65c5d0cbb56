/**
 * 格式化金额，保留两位小数，添加正负号和货币符号
 * @param amount - 金额数值
 * @param showCurrencySymbol - 是否显示货币符号，默认为true
 * @returns 格式化后的金额字符串，例如：￥1,234.56 或 -￥50.00
 */
export function formatAmount(amount: number, showCurrencySymbol: boolean = true): string {
  if (amount === null || amount === undefined || isNaN(amount)) {
    return showCurrencySymbol ? '￥0.00' : '0.00';
  }
  const prefix = amount < 0 ? '-' : '';
  const absoluteAmount = Math.abs(amount);
  const formatted = absoluteAmount.toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
  return showCurrencySymbol ? `${prefix}￥${formatted}` : `${prefix}${formatted}`;
}

/**
 * 格式化用于显示的日期（如列表项副标题）
 * @param dateStr - 日期字符串 (YYYY-MM-DD)
 * @returns 格式化后的日期字符串，例如：今天, 昨天, 4月14日
 */
export function formatDisplayDate(dateStr: string): string {
  if (!dateStr) {
    return '';
  }
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    const txDate = new Date(dateStr);
    txDate.setHours(0, 0, 0, 0);

    if (txDate.getTime() === today.getTime()) {
      return '今天';
    }
    else if (txDate.getTime() === yesterday.getTime()) {
      return '昨天';
    }
    else {
      // 使用Intl.DateTimeFormat获得更本地化的格式
      return new Intl.DateTimeFormat('zh-CN', { month: 'long', day: 'numeric' }).format(txDate);
    }
  }
  catch (e) {
    console.error('Error formatting date:', e);
    return dateStr; // 出错时返回原始字符串
  }
}

/**
 * 格式化月份和年份显示
 * @param year - 年份
 * @param month - 月份 (1-12)
 * @returns 格式化后的字符串，例如：2025年 4月
 */
export function formatMonthYear(year: number, month: number): string {
  if (!year || !month)
    return '';
  return `${year}年 ${month}月`;
}
