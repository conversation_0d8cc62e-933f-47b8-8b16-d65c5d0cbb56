/**
 * uView Plus 图标工具
 * 仅保留字体检测功能，移除过时的加载方法
 */
import { uViewPlusIconConfig } from '@/config/icon.config';

/**
 * 检查字体是否已加载
 * @param fontFamilyName 字体名称
 * @returns 是否已加载
 */
export const checkFontLoaded = (fontFamilyName: string = 'uicon-iconfont'): Promise<boolean> => {
  // #ifdef H5
  if (typeof document === 'undefined') return Promise.resolve(false);
  
  return new Promise(resolve => {
    // 创建使用目标字体的元素
    const testSpan = document.createElement('span');
    testSpan.style.visibility = 'hidden';
    testSpan.style.position = 'absolute';
    testSpan.style.fontFamily = `${fontFamilyName}, monospace`;
    testSpan.style.fontSize = '24px';
    testSpan.innerHTML = '测试字体';
    document.body.appendChild(testSpan);
    
    // 获取元素宽度
    const width = testSpan.offsetWidth;
    
    // 修改为回退字体
    testSpan.style.fontFamily = 'monospace';
    const fallbackWidth = testSpan.offsetWidth;
    
    // 清理元素
    document.body.removeChild(testSpan);
    
    // 如果宽度不同，说明第一个字体已加载
    resolve(width !== fallbackWidth);
  });
  // #endif
  
  // #ifndef H5
  return Promise.resolve(true);
  // #endif
};

export default {
  checkFontLoaded
};