// src/utils/gesture.ts

interface Point { x: number, y: number }

/**
 * 计算两点之间的距离
 * @param p1 点1
 * @param p2 点2
 * @returns 两点距离
 */
export function getDistance(p1: Point, p2: Point): number {
  if (!p1 || !p2)
    return 0; // 防御性检查

  const dx = p1.x - p2.x;
  const dy = p1.y - p2.y;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * 获取触摸点相对于 Canvas 的坐标
 * uni-app 的 TouchEvent 可能包含 touches 或 changedTouches
 * @param e 触摸事件
 * @param canvasElement H5环境下 Canvas 的 DOM 元素信息 (可选)
 * @returns 触摸点坐标 { x, y } 或 null
 */
export function getTouchPosition(e: any, canvasElement?: any): Point | null {
  if (!e)
    return null; // 防御性检查

  let touch = null;

  try {
    if (e.touches && e.touches.length > 0) {
      touch = e.touches[0];
    }
    else if (e.changedTouches && e.changedTouches.length > 0) {
      touch = e.changedTouches[0];
    }

    if (!touch) {
      console.warn('无法获取触摸点信息');
      return null;
    }

    // 不同平台的处理方式
    // #ifdef APP-PLUS || MP-WEIXIN
    // 小程序和APP环境可以直接使用touch.x, touch.y (相对于Canvas)
    if (touch.x !== undefined && touch.y !== undefined) {
      return { x: touch.x, y: touch.y };
    }
    // #endif

    // H5环境或回退机制
    if (canvasElement) {
      // 使用boundingClientRect信息
      if (canvasElement.left !== undefined && canvasElement.top !== undefined) {
        return {
          x: touch.clientX - canvasElement.left,
          y: touch.clientY - canvasElement.top,
        };
      }
    }

    // 尝试从当前事件目标获取位置
    if (e.currentTarget && typeof e.currentTarget.getBoundingClientRect === 'function') {
      try {
        const rect = e.currentTarget.getBoundingClientRect();
        return {
          x: touch.clientX - rect.left,
          y: touch.clientY - rect.top,
        };
      }
      catch (err) {
        console.warn('获取元素位置失败:', err);
      }
    }

    // 回退方案
    console.warn('使用回退方案计算触摸坐标，可能不精确');
    return {
      x: touch.pageX - (e.currentTarget?.offsetLeft || 0),
      y: touch.pageY - (e.currentTarget?.offsetTop || 0),
    };
  }
  catch (error) {
    console.error('getTouchPosition发生错误:', error);
    return null;
  }
}

/**
 * 判断一个点是否在指定圆圈内
 * @param touchPos 触摸点坐标
 * @param circleCenter 圆心坐标
 * @param radius 圆半径
 * @returns 是否在圆内
 */
export function isPointInCircle(touchPos: Point, circleCenter: Point, radius: number): boolean {
  if (!touchPos || !circleCenter)
    return false; // 防御性检查
  return getDistance(touchPos, circleCenter) <= radius;
}

/**
 * 计算两点连线的角度 (相对于水平方向, 弧度)
 * @param p1 点1
 * @param p2 点2
 * @returns 角度 (弧度)
 */
export function getAngle(p1: Point, p2: Point): number {
  if (!p1 || !p2)
    return 0; // 防御性检查
  return Math.atan2(p2.y - p1.y, p2.x - p1.x);
}
