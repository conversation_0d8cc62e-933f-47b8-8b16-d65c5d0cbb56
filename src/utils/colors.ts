/**
 * 颜色工具函数
 * 提供获取CSS变量计算值等功能
 */

/**
 * 获取 CSS 变量的计算值
 * 注意：此函数依赖于浏览器环境来计算样式，
 * 在 Node.js 环境或非浏览器上下文中无法使用。
 * 在 uni-app 中，确保在页面或组件挂载后，DOM 元素实际存在时调用。
 * 
 * @param variableName CSS 变量名 (例如 '--color-primary')
 * @param fallbackColor 如果获取失败，返回的回退颜色 (可选)
 * @returns 计算后的颜色字符串 (如 '#FF6B35') 或回退颜色
 */
export function getCssVariableValue(variableName: string, fallbackColor: string = ''): string {
  if (!variableName) {
    console.warn('getCssVariableValue: 变量名不能为空');
    return fallbackColor;
  }
  
  // 处理非浏览器环境
  // #ifdef H5
  if (typeof window === 'undefined' || typeof document === 'undefined') {
    console.warn('getCssVariableValue: 非浏览器环境无法获取CSS变量值');
    return fallbackColor;
  }
  
  try {
    // 确保变量名格式正确
    const normalizedName = variableName.startsWith('--') ? variableName : `--${variableName}`;
    
    // 尝试从 :root 获取，如果不行，可以尝试从 document.body
    let value = getComputedStyle(document.documentElement)
      .getPropertyValue(normalizedName)
      .trim();
      
    // 如果从根元素获取失败，尝试从body获取
    if (!value && document.body) {
      value = getComputedStyle(document.body)
        .getPropertyValue(normalizedName)
        .trim();
    }
    
    // 返回计算值或后备值
    return value || fallbackColor;
  } catch (error) {
    console.warn(`getCssVariableValue: 获取CSS变量 ${variableName} 失败:`, error);
    return fallbackColor;
  }
  // #endif
  
  // 非H5环境直接返回回退颜色
  // #ifndef H5
  return fallbackColor;
  // #endif
}

/**
 * CSS颜色主题类型
 */
export type ChartColorType = 'bar' | 'pie' | 'line';

/**
 * 获取图表使用的颜色数组
 * 返回项目规范定义的主题颜色，按特定顺序排列
 * 
 * @param type 图表类型，如 'bar'(柱状图), 'pie'(饼图), 'line'(折线图)等
 * @returns 颜色数组
 */
export function getChartColors(type: ChartColorType = 'bar'): string[] {
  // 定义默认颜色，以防获取CSS变量失败
  const defaultColors = {
    bar: ['#4CAF50', '#FF6B35'],
    pie: ['#FF6B35', '#FFB74D', '#FFCC80', '#FFE0B2', '#BDBDBD'],
    line: ['#2196F3', '#FF6B35', '#4CAF50', '#FFC107']
  };
  
  try {
    // 如果是饼图，使用更多颜色变化
    if (type === 'pie') {
      return [
        getCssVariableValue('--color-primary', '#FF6B35'),       // 主题橙色 - 用于第一个分类
        getCssVariableValue('--color-primary-light-1', '#FFB74D'), // 浅橙色
        getCssVariableValue('--color-primary-light-2', '#FFCC80'), // 更浅橙色
        getCssVariableValue('--color-primary-light-3', '#FFE0B2'), // 最浅橙色
        getCssVariableValue('--color-gray', '#BDBDBD')           // 灰色 - 用于"其他"
      ];
    } else if (type === 'line') {
      // 折线图通常需要更多的颜色区分
      return [
        getCssVariableValue('--color-info', '#2196F3'),     // 蓝色 - 主要曲线
        getCssVariableValue('--color-primary', '#FF6B35'),  // 橙色 - 次要曲线
        getCssVariableValue('--color-success', '#4CAF50'),  // 绿色 - 第三曲线
        getCssVariableValue('--color-warning', '#FFC107')   // 黄色 - 第四曲线
      ];
    }
    
    // 柱状图/默认
    return [
      getCssVariableValue('--color-success', '#4CAF50'),  // 绿色 - 收入
      getCssVariableValue('--color-primary', '#FF6B35')   // 橙色 - 支出
    ];
  } catch (error) {
    console.error('获取图表颜色失败，使用默认颜色:', error);
    return defaultColors[type] || defaultColors.bar;
  }
}

/**
 * 将 rgba 颜色转换为带透明度的 hex 颜色
 * 
 * @param r 红色通道 (0-255)
 * @param g 绿色通道 (0-255)
 * @param b 蓝色通道 (0-255)
 * @param a 透明度 (0-1)
 * @returns 十六进制颜色代码，如 '#FF6B35CC'
 */
export function rgbaToHex(r: number, g: number, b: number, a: number = 1): string {
  try {
    // 验证输入值
    r = Math.min(255, Math.max(0, Math.round(r)));
    g = Math.min(255, Math.max(0, Math.round(g)));
    b = Math.min(255, Math.max(0, Math.round(b)));
    a = Math.min(1, Math.max(0, a));
    
    // 转换为十六进制
    const alphaHex = Math.round(a * 255).toString(16).padStart(2, '0');
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}${a < 1 ? alphaHex : ''}`;
  } catch (error) {
    console.error('颜色转换失败:', error);
    return '#000000';
  }
}

export default {
  getCssVariableValue,
  getChartColors,
  rgbaToHex
}; 