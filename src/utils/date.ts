/**
 * 获取指定年月的开始日期和结束日期
 * @param year 年份
 * @param month 月份(1-12)
 * @returns 包含startDate和endDate的对象，格式为YYYY-MM-DD
 */
export function getMonthStartAndEndDates(year: number, month: number): { startDate: string, endDate: string } {
  // 校验输入
  if (!year || !month || month < 1 || month > 12) {
    throw new Error('无效的年份或月份');
  }

  // 创建月份第一天的日期对象
  const startDateObj = new Date(year, month - 1, 1);
  
  // 创建下个月第一天的日期对象，然后减去1毫秒得到当月最后一天
  const endDateObj = new Date(year, month, 0);
  
  // 格式化为YYYY-MM-DD
  const startDate = formatDateToString(startDateObj);
  const endDate = formatDateToString(endDateObj);
  
  return { startDate, endDate };
}

/**
 * 将日期对象格式化为YYYY-MM-DD字符串
 * @param date 日期对象
 * @returns 格式化后的日期字符串
 */
function formatDateToString(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  
  return `${year}-${month}-${day}`;
} 