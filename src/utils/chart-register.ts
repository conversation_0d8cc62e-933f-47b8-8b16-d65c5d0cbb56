/**
 * uCharts组件注册工具
 * 用于注册qiun-data-charts组件
 */
import { defineAsyncComponent } from 'vue';
import type { App } from 'vue';

// 异步加载qiun-data-charts组件
const QiunDataCharts = defineAsyncComponent(() => 
  import('@/components/u-charts/qiun-data-charts.vue')
);

/**
 * 注册uCharts相关组件到Vue应用
 * @param app Vue应用实例
 */
export function registerUCharts(app: App) {
  // 注册qiun-data-charts组件
  app.component('qiun-data-charts', QiunDataCharts);
  
  console.log('[uCharts] 组件注册成功!');
}

/**
 * 初始化uCharts配置
 */
export function initUChartsConfig() {
  console.log('[uCharts] 初始化配置');
  
  // 在这里可以添加uCharts全局配置
  // 例如全局颜色、字体等
  
  console.log('[uCharts] 配置初始化完成');
} 