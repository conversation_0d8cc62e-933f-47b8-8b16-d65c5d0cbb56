/* global.scss - 全局样式文件 */

/* 全局重置样式 */
.page-container {
  min-height: 100vh;
  padding: var(--spacing-md, 16px);
  background-color: var(--bg-secondary, #f5f5f5);
}

/* ========== uView Plus 图标字体支持 ========== */
/* 全局字体声明 */
@font-face {
  font-family: 'uicon-iconfont';
  src: url('/static/fonts/uicon-iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: block; /* 改善性能和可见性 */
}

/* 图标基础样式 - 使用属性选择器增加特异性而非!important */
/* 这种选择器写法可以提高优先级而不需要!important */
.uicon[class],
[class^="uicon-"][class],
[class*=" uicon-"][class],
.u-icon[class],
[class^="u-icon-"][class],
[class*=" u-icon-"][class],
.app-icon[class] {
  /* 字体样式 */
  font-family: uicon-iconfont, sans-serif;
  font-weight: normal;
  font-style: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  
  /* 布局样式 */
  display: inline-flex;
  text-align: center;
  align-items: center;
  justify-content: center;
  
  /* 避免文本处理问题 */
  word-wrap: normal;
  white-space: nowrap;
  
  /* 确保一致的尺寸 */
  width: 1em;
  height: 1em;
}

/* 确保伪元素正确应用字体 - 使用属性选择器组合提高特异性 */
[class^="uicon-"]::before[class],
[class*=" uicon-"]::before[class],
[class^="u-icon-"]::before[class],
[class*=" u-icon-"]::before[class] {
  font-family: uicon-iconfont, sans-serif;
}

/* 仅在必要情况下使用!important - 处理第三方库冲突 */
/* 根据@UI界面规则文档.md 5.1节，特殊情况允许使用!important */
.third-party-override [class^="uicon-"]::before,
.third-party-override [class*=" uicon-"]::before,
.tailwind [class^="uicon-"]::before,
.tailwind [class*=" uicon-"]::before {
  font-family: uicon-iconfont !important;
}

/* 确保旋转图标动画正常工作 */
@keyframes app-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.app-icon--spin,
.u-icon--spin,
.uicon--spin {
  animation: app-icon-spin 2s infinite linear;
}

/* ========== 主题与颜色变量 ========== */
:root {
  /* 主题颜色 */
  --color-primary: #FF6B35;
  --color-primary-light: #FF8B60;
  --color-primary-dark: #F34213;
  --color-primary-disabled: #FFDBCC;
  
  /* 功能色 */
  --color-success: #4CAF50;
  --color-success-light: #A5D6A7;
  --color-warning: #FFC107;
  --color-warning-light: #FFE082;
  --color-error: #F44336;
  --color-error-light: #EF9A9A;
  --color-info: #2196F3;
  --color-info-light: #90CAF9;
  
  /* 背景色 */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F5F5F5;
  --bg-tertiary: #EEEEEE;
  --bg-disabled: #CCCCCC;
  
  /* 文字颜色 */
  --text-primary: #262626;
  --text-secondary: #757575;
  --text-tertiary: #9E9E9E;
  --text-disabled: #BDBDBD;
  --text-white: #FFFFFF;
  
  /* 边框颜色 */
  --border-color: #E0E0E0;
  --border-color-light: #F0F0F0;
  --border-color-dark: #BDBDBD;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 16px;
  --radius-circle: 50%;
  
  /* 字体大小 */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-base: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 24px;
  
  /* 字重 */
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-bold: 700;
  
  /* 阴影 */
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.1);
}

/* 暗黑模式颜色变量 */
[data-theme="dark"] {
  --color-primary: #FF8B60;
  --color-primary-light: #FFA280;
  --color-primary-dark: #FF6B35;
  --color-primary-disabled: #80443C;
  
  --bg-primary: #121212;
  --bg-secondary: #1E1E1E;
  --bg-tertiary: #2C2C2C;
  --bg-disabled: #4D4D4D;
  
  --text-primary: #FFFFFF;
  --text-secondary: #BDBDBD;
  --text-tertiary: #9E9E9E;
  --text-disabled: #757575;
  
  --border-color: #333333;
  --border-color-light: #444444;
  --border-color-dark: #666666;
  
  --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.5);
  --shadow-md: 0 4px 8px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 8px 16px rgba(0, 0, 0, 0.5);
}

/* 通用工具类 - 根据规范不使用!important */
.u-clearfix::after {
  content: "";
  display: table;
  clear: both;
}

.u-ellipsis {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 卡片通用样式 */
.card {
  background-color: var(--card-bg, #fff);
  box-shadow: var(--shadow-card, 0 2px 8px rgb(0 0 0 / 10%));
  border-radius: var(--radius-card, 12px);
  padding: var(--spacing-md, 16px);
  margin-bottom: var(--spacing-md, 16px);
}

/* 文本样式 */
.text-primary {
  color: var(--text-primary, #333);
}

.text-secondary {
  color: var(--text-secondary, #666);
}

.text-hint {
  color: var(--text-hint, #999);
}

.text-white {
  color: #fff;
}

.text-disabled {
  color: var(--text-disabled, #ccc);
}

.text-overflow-1 {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-overflow-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.text-bold {
  font-weight: bold;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* 按钮通用样式 */
.button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 8px 16px;
  border-radius: var(--radius-md, 8px);
  font-size: var(--font-size-md, 16px);
  font-weight: 500;
  transition: all var(--transition-normal, 0.3s);
  cursor: pointer;
}

/* 主要按钮 */
.button-primary {
  background-color: var(--color-primary, #ff6b35);
  color: var(--text-inverse, #fff);
}

/* 禁用状态 */
.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 间距工具类 */
.mt-1 {
  margin-top: var(--spacing-xs, 4px);
}

.mt-2 {
  margin-top: var(--spacing-sm, 8px);
}

.mt-3 {
  margin-top: var(--spacing-md, 16px);
}

.mt-4 {
  margin-top: var(--spacing-lg, 24px);
}

.mt-5 {
  margin-top: var(--spacing-xl, 32px);
}

.mb-1 {
  margin-bottom: var(--spacing-xs, 4px);
}

.mb-2 {
  margin-bottom: var(--spacing-sm, 8px);
}

.mb-3 {
  margin-bottom: var(--spacing-md, 16px);
}

.mb-4 {
  margin-bottom: var(--spacing-lg, 24px);
}

.mb-5 {
  margin-bottom: var(--spacing-xl, 32px);
}

.ml-1 {
  margin-left: var(--spacing-xs, 4px);
}

.ml-2 {
  margin-left: var(--spacing-sm, 8px);
}

.ml-3 {
  margin-left: var(--spacing-md, 16px);
}

.ml-4 {
  margin-left: var(--spacing-lg, 24px);
}

.ml-5 {
  margin-left: var(--spacing-xl, 32px);
}

.mr-1 {
  margin-right: var(--spacing-xs, 4px);
}

.mr-2 {
  margin-right: var(--spacing-sm, 8px);
}

.mr-3 {
  margin-right: var(--spacing-md, 16px);
}

.mr-4 {
  margin-right: var(--spacing-lg, 24px);
}

.mr-5 {
  margin-right: var(--spacing-xl, 32px);
}

/* 边框工具类 */
.border {
  border: 1px solid var(--border-color, #e5e5e5);
}

.border-top {
  border-top: 1px solid var(--border-color, #e5e5e5);
}

.border-bottom {
  border-bottom: 1px solid var(--border-color, #e5e5e5);
}

/* 字体工具类 */
.font-bold {
  font-weight: 700;
}

.font-medium {
  font-weight: 500;
}

/* 弹性布局工具类 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.items-center {
  align-items: center;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.flex-1 {
  flex: 1;
}

/* 图标通用样式 */
.icon {
  font-size: var(--font-size-md, 16px);
}

.icon-sm {
  font-size: var(--font-size-sm, 14px);
}

.icon-lg {
  font-size: var(--font-size-lg, 18px);
}

/* 全局过渡效果 */
.fade-transition {
  transition: opacity var(--transition-normal, 0.3s);
}

/* 多端适配辅助类 */
.h5-only,
.mp-only,
.app-only {
  display: none !important;
}

/* #ifdef H5 */
.h5-only { display: block !important; }
/* #endif */

/* #ifdef MP-WEIXIN */
.mp-only { display: block !important; }
/* #endif */

/* #ifdef APP-PLUS */
.app-only { display: block !important; }
/* #endif */

/* 导入基础变量设置 */
@import './variables';

/* 导入工具类 - 修复路径，改回使用别名 */
@import '@tools/style/scripts/_utilities';

/* 需要隐藏时请用.u-hide工具类 */
