/* Global CSS Variables */
:root {
  /* 主题色系 */
  --color-primary: #ff6b35;
  --color-primary-dark: #f34213; /* 添加主色调深色版本 */
  --color-success: #4caf50;
  --color-warning: #ffc107;
  --color-error: #f44336;
  --color-info: #2196f3;

  /* 文字色 */
  --text-primary: #333;
  --text-secondary: #666;
  --text-hint: #999;
  --text-inverse: #fff;

  /* 背景色 */
  --bg-primary: #fff;
  --bg-secondary: #f5f5f5;
  --card-bg: #fff;
  --border-color: #e5e5e5; /* Added light border color */

  /* 间距系统 (8px based) */
  --spacing-xs: 4px; /* Extra Small */
  --spacing-sm: 8px; /* Small */
  --spacing-md: 16px; /* Medium */
  --spacing-lg: 24px; /* Large */
  --spacing-xl: 32px; /* Extra Large */

  /* 圆角系统 */
  --radius-sm: 4px;
  --radius-md: 8px; /* Default for buttons, inputs */
  --radius-lg: 16px;
  --radius-card: 12px; /* Specific for cards */
  --radius-image: 8px;
  --radius-circle: 50%;
  --radius-button: 8px; /* 添加按钮圆角变量 */

  /* 阴影系统 */
  --shadow-card: 0 2px 8px rgb(0 0 0 / 10%);
  --shadow-raised: 0 4px 16px rgb(0 0 0 / 12%);
  --shadow-dialog: 0 8px 30px rgb(0 0 0 / 18%);

  /* 字体大小 (Example, can be refined later) */
  --font-size-xs: 12px;
  --font-size-sm: 14px;
  --font-size-md: 16px;
  --font-size-lg: 18px;
  --font-size-xl: 20px;
  --font-size-xxl: 22px;

  /* 过渡动画 */
  --transition-fast: 0.2s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;

  /* 分隔线颜色 */
  --divider-color: #eee;

  /* 多端适配相关变量 - 导航栏和安全区域 */

  /* 注意：这些变量仅作为CSS变量默认值，实际应用时优先使用platform.js中的getPlatformStyle函数 */
  --nav-height: 88px; /* 包含状态栏 */
  --status-bar-height: 44px;
  --safe-area-top: 0px; /* 默认值，仅iOS刘海屏需要特殊处理 */
  --safe-area-bottom: env(safe-area-inset-bottom, 0);
  --tab-bar-height: 60px;
  
  /* iOS特定变量 - 新增 */
  --ios-nav-height: 88px; /* iOS导航栏高度 */
  --ios-status-bar-height: 44px; /* iOS状态栏高度 */
  --ios-tab-bar-height: 60px; /* iOS底部标签栏高度 */
  --ios-safe-area-bottom: env(safe-area-inset-bottom, 0); /* iOS底部安全区域 */
  --ios-safe-area-top: env(safe-area-inset-top, 0); /* iOS顶部安全区域 */

  /* 分类背景颜色 - 使用cat-前缀（现有代码兼容，建议新代码统一使用category-前缀） */
  --cat-bg-orange: #fff5f2;
  --cat-bg-blue: #e0f2fe;
  --cat-bg-yellow: #fefce8;
  --cat-bg-purple: #f3e8ff;
  --cat-bg-indigo: #e0e7ff;
  --cat-bg-cyan: #dffeff;
  --cat-bg-red: #fee2e2;
  --cat-bg-green: #ecfdf5;
  --cat-bg-emerald: #d1fae5;
  --cat-bg-lime: #f7fee7;
  --cat-bg-gray: #f3f4f6;

  /* 分类文字颜色 - 使用cat-前缀（现有代码兼容，建议新代码统一使用category-前缀） */
  --cat-color-orange: #ff6b35;
  --cat-color-blue: #0ea5e9;
  --cat-color-yellow: #f59e0b;
  --cat-color-purple: #8b5cf6;
  --cat-color-indigo: #4f46e5;
  --cat-color-cyan: #0891b2;
  --cat-color-red: #ef4444;
  --cat-color-green: #10b981;
  --cat-color-emerald: #059669;
  --cat-color-lime: #84cc16;
  --cat-color-gray: #6b7280;

  /* 新增：分类图标背景色 - 使用category-前缀（推荐在新代码中统一使用此前缀） */
  --bg-category-food: #fff5f2;
  --bg-category-shopping: #e0f2fe;
  --bg-category-transport: #ffe4e6;
  --bg-category-entertainment: #fef3c7;
  --bg-category-income: #ecfdf5; /* 工资/收入 */
  --bg-category-housing: #e0f2fe; /* 住房（暂用购物蓝） */
  --bg-category-default: #f5f5f5; /* 默认灰色 */

  /* 新增：分类图标颜色 - 使用category-前缀（推荐在新代码中统一使用此前缀） */
  --color-category-food: #ff6b35;
  --color-category-shopping: #0ea5e9;
  --color-category-transport: #e11d48;
  --color-category-entertainment: #d97706;
  --color-category-income: #10b981;
  --color-category-housing: #0ea5e9;
  --color-category-default: #666;
}

@media (prefers-color-scheme: dark) {
  :root {
    /* 主题色系 - Dark */
    --color-primary: #ff8a65; /* Lighter primary for dark mode */
    --color-primary-dark: #ff6b35; /* 暗色模式下的深色主题色 */
    --color-success: #66bb6a;
    --color-warning: #ffca28;
    --color-error: #ef5350;
    --color-info: #42a5f5;

    /* 文字色 - Dark */
    --text-primary: #f5f5f5;
    --text-secondary: #aaa;
    --text-hint: #757575;
    --text-inverse: #121212;

    /* 背景色 - Dark */
    --bg-primary: #121212;
    --bg-secondary: #1e1e1e;
    --card-bg: #1e1e1e;
    --border-color: #333;
    --divider-color: #333;

    /* 分类背景颜色 - 暗色模式 - cat-前缀 */
    --cat-bg-orange: #2e1b13;
    --cat-bg-blue: #0f2942;
    --cat-bg-yellow: #332500;
    --cat-bg-purple: #22154a;
    --cat-bg-indigo: #1a1b4b;
    --cat-bg-cyan: #082f38;
    --cat-bg-red: #430d0d;
    --cat-bg-green: #0f3626;
    --cat-bg-emerald: #022c22;
    --cat-bg-lime: #243010;
    --cat-bg-gray: #1f2937;

    /* 分类文字颜色 - 暗色模式 (更亮的颜色) - cat-前缀 */
    --cat-color-orange: #ff8a65;
    --cat-color-blue: #38bdf8;
    --cat-color-yellow: #fcd34d;
    --cat-color-purple: #a78bfa;
    --cat-color-indigo: #818cf8;
    --cat-color-cyan: #22d3ee;
    --cat-color-red: #f87171;
    --cat-color-green: #34d399;
    --cat-color-emerald: #10b981;
    --cat-color-lime: #a3e635;
    --cat-color-gray: #9ca3af;

    /* 阴影系统 - Dark */
    --shadow-card: 0 2px 12px rgb(0 0 0 / 30%);
    --shadow-raised: 0 4px 20px rgb(0 0 0 / 35%);
    --shadow-dialog: 0 8px 35px rgb(0 0 0 / 40%);

    /* 新增：暗黑模式下的分类图标背景色 - category-前缀 */
    --bg-category-food: #5c2a1a;
    --bg-category-shopping: #1a3a5c;
    --bg-category-transport: #5c1a2e;
    --bg-category-entertainment: #5c4a1a;
    --bg-category-income: #1a5c4a;
    --bg-category-housing: #1a3a5c;
    --bg-category-default: #333;

    /* 新增：暗黑模式下的分类图标颜色 - category-前缀 */
    --color-category-food: #ff8a65;
    --color-category-shopping: #4fc3f7;
    --color-category-transport: #f06292;
    --color-category-entertainment: #ffd54f;
    --color-category-income: #4db6ac;
    --color-category-housing: #4fc3f7;
    --color-category-default: #aaa;
  }
}

/* 手动切换暗黑模式的类 (可选) */
.theme-dark {
  /* 主题色系 - Dark */
  --color-primary: #ff8a65;
  --color-primary-dark: #ff6b35; /* 暗色模式下的深色主题色 */
  --color-success: #66bb6a;
  --color-warning: #ffca28;
  --color-error: #ef5350;
  --color-info: #42a5f5;

  /* 文字色 - Dark */
  --text-primary: #f5f5f5;
  --text-secondary: #aaa;
  --text-hint: #757575;
  --text-inverse: #121212;

  /* 背景色 - Dark */
  --bg-primary: #121212;
  --bg-secondary: #1e1e1e;
  --card-bg: #1e1e1e;
  --border-color: #333;
  --divider-color: #333;

  /* 分类背景颜色 - 暗色模式 - cat-前缀 */
  --cat-bg-orange: #2e1b13;
  --cat-bg-blue: #0f2942;
  --cat-bg-yellow: #332500;
  --cat-bg-purple: #22154a;
  --cat-bg-indigo: #1a1b4b;
  --cat-bg-cyan: #082f38;
  --cat-bg-red: #430d0d;
  --cat-bg-green: #0f3626;
  --cat-bg-emerald: #022c22;
  --cat-bg-lime: #243010;
  --cat-bg-gray: #1f2937;

  /* 分类文字颜色 - 暗色模式 (更亮的颜色) - cat-前缀 */
  --cat-color-orange: #ff8a65;
  --cat-color-blue: #38bdf8;
  --cat-color-yellow: #fcd34d;
  --cat-color-purple: #a78bfa;
  --cat-color-indigo: #818cf8;
  --cat-color-cyan: #22d3ee;
  --cat-color-red: #f87171;
  --cat-color-green: #34d399;
  --cat-color-emerald: #10b981;
  --cat-color-lime: #a3e635;
  --cat-color-gray: #9ca3af;

  /* 阴影系统 - Dark */
  --shadow-card: 0 2px 12px rgb(0 0 0 / 30%);
  --shadow-raised: 0 4px 20px rgb(0 0 0 / 35%);
  --shadow-dialog: 0 8px 35px rgb(0 0 0 / 40%);

  /* 分类图标背景色 - 暗色模式 - category-前缀 */
  --bg-category-food: #5c2a1a;
  --bg-category-shopping: #1a3a5c;
  --bg-category-transport: #5c1a2e;
  --bg-category-entertainment: #5c4a1a;
  --bg-category-income: #1a5c4a;
  --bg-category-housing: #1a3a5c;
  --bg-category-default: #333;

  /* 分类图标颜色 - 暗色模式 - category-前缀 */
  --color-category-food: #ff8a65;
  --color-category-shopping: #4fc3f7;
  --color-category-transport: #f06292;
  --color-category-entertainment: #ffd54f;
  --color-category-income: #4db6ac;
  --color-category-housing: #4fc3f7;
  --color-category-default: #aaa;
}

/* 平台特定样式 - 保持在文件末尾，避免被其他样式覆盖 */

/* 这些变量不会实际生效，需要结合utils/platform.js使用，仅供参考 */
// iOS: --nav-height: 88px; --status-bar-height: 44px; --tab-bar-height: 60px;
// Android: --nav-height: 66px; --status-bar-height: 24px; --tab-bar-height: 56px;
// MP-WEIXIN: --nav-height: 90px; --status-bar-height: 25px; --tab-bar-height: 56px;
// H5: --nav-height: 60px; --status-bar-height: 0; --tab-bar-height: 50px;
