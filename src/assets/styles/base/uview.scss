/**
 * uView Plus 样式桥接文件
 * 提供组件内直接引用的功能
 */

/* 首先导入 uView Plus 基础变量 */
$uicon-font-family: "uicon-iconfont" !default;
$uicon-font-path: '/static/fonts' !default;
$uicon-font-file: 'uicon-iconfont.ttf' !default;

/* 强制声明字体引用路径 */
$uicon-font-url: '#{$uicon-font-path}/#{$uicon-font-file}' !default;

/* uView Plus 单位设置 */
$up-unit: rpx !default;

/* 确保图标字体正确声明 */
@font-face {
  font-family: $uicon-font-family;
  src: url($uicon-font-url) format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 图标类样式规则 */
.uicon,
.u-icon,
[class^="uicon-"], 
[class*=" uicon-"],
[class^="u-icon-"], 
[class*=" u-icon-"] {
  font-family: $uicon-font-family !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* 图标旋转动画 */
@keyframes u-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.u-icon--spin {
  animation: u-icon-spin 2s infinite linear;
}

/* 基础颜色变量 */
$u-main-color: #303133 !default;
$u-content-color: #606266 !default;
$u-tips-color: #909399 !default;
$u-light-color: #c0c4cc !default;
$u-border-color: #e4e7ed !default;
$u-bg-color: #f3f4f6 !default;
$u-disabled-color: #c8c9cc !default;

/* 主题色变量 */
$u-primary: #3c9cff !default;
$u-primary-dark: #398ade !default;
$u-primary-disabled: #9acafc !default;
$u-primary-light: #ecf5ff !default;

/* 警告色 */
$u-warning: #f9ae3d !default;
$u-warning-dark: #f1a532 !default;
$u-warning-disabled: #f9d39b !default;
$u-warning-light: #fdf6ec !default;

/* 成功色 */
$u-success: #5ac725 !default;
$u-success-dark: #53c21d !default;
$u-success-disabled: #a9e08f !default;
$u-success-light: #f5fff0 !default;

/* 错误色 */
$u-error: #f56c6c !default;
$u-error-dark: #e45656 !default;
$u-error-disabled: #f7b2b2 !default;
$u-error-light: #fef0f0 !default;

/* 信息色 */
$u-info: #909399 !default;
$u-info-dark: #767a82 !default;
$u-info-disabled: #c4c6c9 !default;
$u-info-light: #f4f4f5 !default;