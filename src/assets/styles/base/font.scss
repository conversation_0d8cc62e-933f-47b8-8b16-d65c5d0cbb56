/**
 * 图标字体定义文件
 * 统一管理项目中使用的字体
 */

/* 定义字体路径变量 */
$icon-font-path: '/static/fonts';
$icon-font-file: 'uicon-iconfont.ttf';

/* 定义多个可能的路径以增加兼容性 */
@font-face {
  font-family: 'uicon-iconfont';
  src: url('#{$icon-font-path}/#{$icon-font-file}') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 基础图标样式 */
[class^="uicon-"], 
[class*=" uicon-"] {
  font-family: 'uicon-iconfont' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  -webkit-font-smoothing: antialiased;
}

/* 旋转动画 */
@keyframes u-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.u-icon--spin {
  animation: u-spin 2s infinite linear;
} 