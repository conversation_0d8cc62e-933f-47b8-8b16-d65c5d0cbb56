/**
 * uView Plus 图标样式专用文件
 * 为确保图标在所有场景下均可正常显示
 */

/* 字体定义 */
@font-face {
  font-family: 'uicon-iconfont';
  src: url('/static/fonts/uicon-iconfont.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

/* 基础图标样式 */
[class^="uicon-"], 
[class*=" uicon-"],
[class^="u-icon-"], 
[class*=" u-icon-"] {
  /* 字体设置 */
  font-family: 'uicon-iconfont' !important;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  
  /* 布局控制 */
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-rendering: auto;
  -webkit-font-smoothing: antialiased;
}

/* 关键图标Unicode映射 */
.uicon-home:before,
.u-icon-home:before { content: "\e600"; }

.uicon-photo:before,
.u-icon-photo:before { content: "\e60b"; }

.uicon-star:before,
.u-icon-star:before { content: "\e688"; }

.uicon-setting:before,
.u-icon-setting:before { content: "\e61f"; }

.uicon-search:before,
.u-icon-search:before { content: "\e62a"; }

.uicon-camera:before,
.u-icon-camera:before { content: "\e62d"; }

.uicon-plus:before,
.u-icon-plus:before { content: "\e620"; }

.uicon-trash:before,
.u-icon-trash:before { content: "\e623"; }

.uicon-question-circle:before,
.u-icon-question-circle:before { content: "\e607"; }

.uicon-email-fill:before,
.u-icon-email-fill:before { content: "\e629"; }

.uicon-bell:before,
.u-icon-bell:before { content: "\e609"; }

.uicon-lock:before,
.u-icon-lock:before { content: "\e61a"; }

.uicon-heart-fill:before,
.u-icon-heart-fill:before { content: "\e64b"; }

.uicon-thumb-up-fill:before,
.u-icon-thumb-up-fill:before { content: "\e662"; }

.uicon-map:before,
.u-icon-map:before { content: "\e616"; }

/* 添加新增图标映射 */
.uicon-mic:before,
.u-icon-mic:before { content: "\e64d"; }

.uicon-file-text:before,
.u-icon-file-text:before { content: "\e619"; }

.uicon-pie-chart:before,
.u-icon-pie-chart:before { content: "\e612"; }

.uicon-info:before,
.u-icon-info:before { content: "\e653"; }

.uicon-line:before,
.u-icon-line:before { content: "\e668"; }

.uicon-reload:before,
.u-icon-reload:before { content: "\e60e"; }

/* 添加颜色变体图标 */
.uicon-home-fill:before,
.u-icon-home-fill:before { content: "\e600"; }

.uicon-star-fill:before,
.u-icon-star-fill:before { content: "\e688"; }

.uicon-plus-circle:before,
.u-icon-plus-circle:before { content: "\e621"; }

.uicon-question:before,
.u-icon-question:before { content: "\e607"; }

.uicon-arrow-right:before,
.u-icon-arrow-right:before { content: "\e62f"; }

.uicon-arrow-left:before,
.u-icon-arrow-left:before { content: "\e62f"; }

.uicon-checkmark-circle:before,
.u-icon-checkmark-circle:before { content: "\e645"; }

.uicon-close-circle:before,
.u-icon-close-circle:before { content: "\e646"; }

.uicon-info-circle:before,
.u-icon-info-circle:before { content: "\e654"; }

/* 旋转动画定义 */
@keyframes u-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.u-icon--spin {
  animation: u-icon-spin 2s infinite linear;
} 