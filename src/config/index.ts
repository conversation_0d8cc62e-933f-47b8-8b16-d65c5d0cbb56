/**
 * 应用配置中心
 * 统一导入和导出所有配置项，便于集中管理
 */

// 导入特定配置文件
import apiConfig from './api.config';
import platformConfig from './platform.config';

/**
 * 应用配置
 */
const config = {
  // API 配置
  api: apiConfig,

  // 平台差异配置
  platform: platformConfig,

  // 存储相关配置
  storage: {
    // 存储键前缀
    prefix: 'jizhang_',
    // 存储键名
    keys: {
      TOKEN: 'app_token',
      REFRESH_TOKEN: 'app_refresh_token',
      USER_INFO: 'user_info',
      THEME: 'app_theme',
      LANGUAGE: 'app_language',
      LAST_LOGIN: 'last_login_time',
      SEARCH_HISTORY: 'search_history',
      RECENT_BOOKS: 'recent_books',
      CURRENT_BOOK_ID: 'current_book_id',
      GESTURE_PASSWORD: 'has_set_gesture',
    },
  },

  // 默认设置
  defaults: {
    // 语言设置（当未设置时的默认值）
    language: 'zh-CN',
    // 默认主题
    theme: 'light',
    // 默认字体大小（用于字体缩放功能）
    fontSize: 'normal',
    // 默认预算月份长度
    budgetMonthLength: 30,
    // 默认交易排序方式
    transactionSort: 'date-desc',
    // 默认记账分类
    defaultCategory: '餐饮',
    // 默认起始页
    startPage: '/pages/home/<USER>',
    // 默认头像
    defaultAvatar: '/static/images/default-avatar.png',
  },

  // 环境配置
  env: {
    // 是否为开发环境
    isDev: import.meta.env.DEV,
    // 是否使用模拟数据
    useMockData: import.meta.env.VITE_USE_MOCK_DATA === 'true',
    // 应用版本
    appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0',
    // 应用名称
    appName: import.meta.env.VITE_APP_TITLE || '记账无忌',
  },

  // 表单验证规则
  validation: {
    // 手机号验证规则
    phoneRegex: /^1[3-9]\d{9}$/,
    // 密码长度要求
    passwordMinLength: 8,
    // 用户名长度限制
    usernameMaxLength: 20,
    // 评论长度限制
    commentMaxLength: 200,
  },

  // 应用限制
  limits: {
    // 最大上传图片大小 (MB)
    maxImageUploadSize: 5,
    // 最大分类数量
    maxCategoryCount: 50,
    // 最大账本数量（免费用户）
    maxBookCountFree: 5,
    // 搜索历史最大保存条数
    maxSearchHistoryCount: 20,
  },
};

export default config;
