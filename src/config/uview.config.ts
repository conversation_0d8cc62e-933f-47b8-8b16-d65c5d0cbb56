/**
 * uView Plus 全局配置
 */

export const uviewConfig = {
  // 全局配置
  config: {
    unit: 'rpx', // 默认单位，可以为 rpx 或 px
    vtimestamp: true, // 是否开启动态获取服务器时间
    compGutter: 20, // 组件与组件之间的间距
  },
  // 组件默认属性配置
  props: {
    // 图标组件
    icon: {
      size: 32, // 图标大小
    },
    // 按钮组件
    button: {
      plain: false,
      size: 'medium',
    },
    // 输入框组件
    input: {
      border: true,
    },
  }
};

/**
 * 初始化uView Plus配置
 */
export const initUviewConfig = (uni: any) => {
  try {
    console.log('[uView Plus] 开始初始化配置...');
    
    // 防御性检查
    if (!uni || typeof uni !== 'object') {
      console.warn('[uView Plus] uni对象不是一个有效对象');
      return false;
    }
    
    // 确保uni.$u存在
    if (!uni.$u) {
      console.log('[uView Plus] 创建uni.$u对象');
      uni.$u = {
        config: {},
        props: {},
        setConfig: function(config: any) {
          console.log('[uView Plus] 应用配置:', config);
          uni.$u.config = config.config || {};
          uni.$u.props = config.props || {};
        }
      };
    }
    
    // 如果setConfig不存在，创建一个空函数
    if (typeof uni.$u.setConfig !== 'function') {
      console.log('[uView Plus] 创建setConfig函数');
      uni.$u.setConfig = function(config: any) {
        console.log('[uView Plus] 应用配置:', config);
        uni.$u.config = config.config || {};
        uni.$u.props = config.props || {};
      };
    }
    
    // 设置全局配置
    console.log('[uView Plus] 应用全局配置');
    uni.$u.setConfig({
      ...uviewConfig
    });
    
    console.log('[uView Plus] 配置初始化完成');
    return true;
  } catch (error) {
    console.error('[uView Plus] 配置初始化失败', error);
    return false;
  }
};

export default {
  uviewConfig,
  initUviewConfig
};