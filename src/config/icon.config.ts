/**
 * 图标配置文件
 * 用于统一管理图标相关的配置和路径
 */

// 检测iOS模拟器环境
let isIOSSimulator = false;
// #ifdef APP-PLUS
try {
  // 简单检测iOS模拟器
  // @ts-ignore
  if (plus && plus.os && plus.os.name === 'iOS') {
    if (typeof plus.device !== 'undefined') {
      // @ts-ignore
      const model = plus.device.model || '';
      isIOSSimulator = model.toLowerCase().includes('simulator');
    }
  }
} catch (e) {
  console.error('[icon.config] 检测iOS模拟器环境失败:', e);
}
// #endif

/**
 * uView Plus 图标配置
 */
export const uViewPlusIconConfig = {
  // 图标字体文件路径 - 针对不同环境提供不同路径
  get fontPath() {
    // iOS模拟器环境特殊处理
    if (isIOSSimulator) {
      console.log('[icon.config] 使用iOS模拟器专用图标路径');
      return 'static/fonts/uicon-iconfont.ttf';
    }
    
    // #ifdef H5
    return '/static/fonts/uicon-iconfont.ttf';
    // #endif
    
    // #ifdef APP-PLUS
    return 'static/fonts/uicon-iconfont.ttf';
    // #endif
    
    // #ifdef MP
    return '~@/static/fonts/uicon-iconfont.ttf';
    // #endif
    
    // 兜底路径
    return '/static/fonts/uicon-iconfont.ttf';
  },
  
  // 图标字体名称
  fontFamily: 'uicon-iconfont',
  
  // 图标前缀
  prefix: 'uicon',
  
  // 图标默认尺寸
  defaultSize: 32,
  
  // 图标默认颜色
  defaultColor: 'var(--color-text-primary, #262626)',
  
  // 字体显示策略
  fontDisplay: 'swap', // 设置为swap以便在字体加载前显示后备字体
  
  // 支持的图标加载方式
  loadingMode: 'preload', // 可选值: preload（预加载）, lazy（延迟加载）, auto（自动）
  
  // iOS模拟器兼容性标志
  isIOSSimulator,
  
  // 获取适合当前平台的字体路径
  getPlatformFontPath(platform: string): string {
    switch (platform) {
      case 'ios':
        return isIOSSimulator 
          ? 'static/fonts/uicon-iconfont.ttf' 
          : 'static/fonts/uicon-iconfont.ttf';
      case 'android':
        return 'static/fonts/uicon-iconfont.ttf';
      case 'h5':
        return '/static/fonts/uicon-iconfont.ttf';
      case 'weapp':
        return '~@/static/fonts/uicon-iconfont.ttf';
      default:
        return '/static/fonts/uicon-iconfont.ttf';
    }
  }
};

/**
 * 图标模式枚举
 */
export enum IconMode {
  UViewPlus = 'uview-plus', // 使用uView Plus图标库
  Custom = 'custom'         // 使用自定义图标库
}

/**
 * 当前项目使用的图标模式
 */
export const currentIconMode = IconMode.UViewPlus;

export default {
  uViewPlusIconConfig,
  IconMode,
  currentIconMode,
  isIOSSimulator
};