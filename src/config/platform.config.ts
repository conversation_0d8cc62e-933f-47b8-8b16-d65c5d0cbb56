/**
 * 平台差异配置
 * 用于处理多端差异，包括样式尺寸、行为、权限等
 */

// 平台样式配置
export const PLATFORM_STYLE = {
  // 样式映射表，用于统一不同平台的尺寸、间距等样式变量
  styleMap: {
    'nav-height': {
      ios: '88px', // iOS标准导航栏高度(含状态栏)
      android: '56px', // 调整Android值
      h5: '50px', // 调整Web值
      weapp: '70px', // 调整小程序值
    },
    'safe-bottom': {
      ios: 'env(safe-area-inset-bottom, 0px)',
      android: '0px',
      h5: '0px',
      weapp: '0px',
    },
    'status-bar-height': {
      ios: '44px', // 标准iOS状态栏高度
      android: '20px', // 调整Android状态栏高度
      h5: '0px',
      weapp: '25px',
    },
    'tab-bar-height': {
      ios: '60px',
      android: '56px',
      h5: '50px',
      weapp: '56px',
    },
  },

  // 获取当前平台类型的函数
  getPlatformType: (): 'ios' | 'android' | 'h5' | 'weapp' | 'unknown' => {
    // #ifdef APP-PLUS
    try {
      const systemInfo = uni.getSystemInfoSync();
      if (systemInfo.platform === 'ios') {
        return 'ios';
      }
      return 'android';
    } catch (e) {
      console.error('获取平台类型失败', e);
      return 'unknown';
    }
    // #endif

    // #ifdef H5
    return 'h5';
    // #endif

    // #ifdef MP-WEIXIN
    return 'weapp';
    // #endif

    return 'unknown';
  },

  // 获取平台特定样式的函数
  getPlatformStyle: (key: string): string => {
    const platform = PLATFORM_STYLE.getPlatformType();
    // @ts-ignore - 类型可能问题，但实际运行正常
    return PLATFORM_STYLE.styleMap[key]?.[platform] || PLATFORM_STYLE.styleMap[key]?.ios || '';
  },
};

// 平台功能差异配置
export const PLATFORM_FEATURES = {
  // 平台功能支持情况
  supports: {
    geolocation: {
      ios: true,
      android: true,
      h5: true,
      weapp: true,
    },
    biometrics: {
      ios: true,
      android: true,
      h5: false,
      weapp: false,
    },
    clipboard: {
      ios: true,
      android: true,
      h5: true,
      weapp: true,
    },
    shareApp: {
      ios: true,
      android: true,
      h5: false,
      weapp: true,
    },
  },

  // 检查当前平台是否支持某功能
  isFeatureSupported: (feature: string): boolean => {
    const platform = PLATFORM_STYLE.getPlatformType();
    // @ts-ignore - 类型可能问题，但实际运行正常
    return PLATFORM_FEATURES.supports[feature]?.[platform] || false;
  },
};

export default {
  style: PLATFORM_STYLE,
  features: PLATFORM_FEATURES,
};
