<template>
  <view class="pie-chart" :style="{ height: props.height }">
    <view v-if="props.loading" class="chart-loading">加载中...</view>
    <view v-else-if="isEmpty" class="chart-empty">暂无数据</view>
    <canvas 
      v-else
      :id="canvasId" 
      :canvas-id="canvasId"
      class="charts-canvas" 
      :style="{ width: '100%', height: props.height }"
    ></canvas>
  </view>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';

const props = defineProps({
  chartData: { type: Object, required: true },
  height: { type: String, default: '200px' },
  loading: { type: Boolean, default: false }
});

// uCharts 组件注册
import uCharts from '@qiun/ucharts/u-charts.js';

const chartType = 'ring'; // 圆环图
const canvasId = 'pie-chart-' + Math.random().toString(36).slice(-8);
const canvasDOM = ref(null);
const chartInstance = ref(null);
const isEmpty = computed(() => {
  return props.chartData?.series?.[0]?.data?.[0]?.name === '无数据' || 
    (props.chartData?.series?.[0]?.data?.every(d => d.value === 0));
});

// 颜色方案：主色橙色，其他用不同橙色/灰色
const defaultColors = [
  '#FF6B35', // 主色
  '#FFB74D', // orange-300
  '#FFCC80', // orange-200
  '#FFE0B2', // orange-100
  '#BDBDBD', // gray-300
];

// 适配props.chartData为uCharts格式
const uChartsOpts = computed(() => {
  const opts = {
    type: chartType,
    series: [],
    categories: [],
    animation: true,
    background: '#FFFFFF',
    padding: [15, 15, 15, 15],
    enableScroll: false,
    legend: {
      show: true,
      position: 'right',
      float: 'right',
      padding: 5,
      margin: 5,
      backgroundColor: 'rgba(0,0,0,0)',
      borderColor: 'rgba(0,0,0,0)',
      itemGap: 10,
      fontSize: 12,
      lineHeight: 10,
      fontColor: '#666666',
      format: '{name}: {value}%'
    },
    extra: {
      ring: {
        ringWidth: 30,
        centerColor: '#FFFFFF',
        activeOpacity: 0.5,
        activeRadius: 10,
        offsetAngle: 0,
        labelWidth: 15,
        border: false,
        borderWidth: 3,
        borderColor: '#FFFFFF'
      }
    }
  };

  // 设置数据和颜色
  if (props.chartData?.series?.[0]?.data?.length) {
    const data = props.chartData.series[0].data;
    const colorMap = {};

    // 准备图表需要的系列数据格式
    opts.series = data.map((item, index) => {
      const color = item.itemStyle?.color || defaultColors[index % defaultColors.length];
      colorMap[item.name] = color;
      return {
        name: item.name,
        data: item.value,
        color
      };
    });

    // 添加到颜色映射到自定义选项
    opts.customColor = colorMap;
  }

  return opts;
});

// 监听数据变化
watch(() => props.chartData, () => {
  if (!isEmpty.value && chartInstance.value) {
    updateChart();
  }
}, { deep: true });

// 初始化图表 - 修复版本
function initChart() {
  try {
    console.log('开始初始化PieChart图表...');
    
    // 获取Canvas上下文
    const ctx = uni.createCanvasContext(canvasId);
    
    if (!ctx) {
      console.error('PieChart - 获取Canvas上下文失败');
      return;
    }
    
    console.log('PieChart - 成功获取Canvas上下文');
    
    // 创建图表实例
    chartInstance.value = new uCharts({
      canvasId: canvasId,
      context: ctx,
      width: 375,
      height: 250,
      series: uChartsOpts.value.series,
      categories: uChartsOpts.value.categories,
      animation: uChartsOpts.value.animation,
      background: uChartsOpts.value.background,
      padding: uChartsOpts.value.padding,
      legend: uChartsOpts.value.legend,
      extra: uChartsOpts.value.extra
    });
    
    // 重要：必须延迟执行draw()，并且必须调用draw()方法
    setTimeout(() => {
      ctx.draw(true);
      console.log('PieChart - draw完成');
    }, 200);
    
    console.log('PieChart - 图表实例创建成功');
  } catch (err) {
    console.error('PieChart - 初始化图表失败:', err);
  }
}

// 更新图表
function updateChart() {
  try {
    if (chartInstance.value) {
      chartInstance.value.updateData({
        series: uChartsOpts.value.series,
        categories: uChartsOpts.value.categories
      });
      
      // 确保draw()被调用
      setTimeout(() => {
        const ctx = uni.createCanvasContext(canvasId);
        if (ctx) ctx.draw(true);
      }, 100);
    } else {
      console.log('PieChart - 实例不存在，重新初始化');
      initChart();
    }
  } catch (err) {
    console.error('PieChart - 更新图表失败:', err);
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  console.log('PieChart组件已挂载');
  if (!isEmpty.value) {
    // 延长延时时间，确保DOM已完全渲染
    setTimeout(() => {
      initChart();
    }, 500);
  }
});
</script>

<style lang="scss" scoped>
.pie-chart {
  position: relative;
  width: 100%;
  
  .charts-canvas {
    width: 100%;
    height: 100%;
  }
  
  .chart-loading,
  .chart-empty {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-hint, #999999);
    font-size: var(--font-size-md, 14px);
    background-color: var(--bg-secondary, #f6f6f6);
    border-radius: var(--radius-md, 8px);
  }
}
</style>