<template>
  <view 
    class="chat-bubble"
    :class="[
      `chat-bubble--${sender}`,
      { 'chat-bubble--loading': isLoading }
    ]"
    :id="id"
  >
    <!-- 头像 -->
    <view v-if="sender !== 'system'" class="chat-bubble__avatar">
      <image 
        class="chat-bubble__avatar-img" 
        :src="computedAvatarUrl" 
        mode="aspectFill"
      />
    </view>
    
    <!-- 气泡内容 -->
    <view class="chat-bubble__content">
      <!-- 加载中状态 -->
      <view v-if="isLoading" class="chat-bubble__loading">
        <view class="chat-bubble__loading-dot"></view>
        <view class="chat-bubble__loading-dot"></view>
        <view class="chat-bubble__loading-dot"></view>
      </view>
      
      <!-- 插槽内容 -->
      <slot v-else></slot>
      
      <!-- 时间戳 -->
      <view v-if="timestamp && !isLoading" class="chat-bubble__time">
        {{ formatTime(timestamp) }}
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 默认头像 - 使用配置变量，避免硬编码路径
const DEFAULT_AVATARS = {
  user: '/static/avatar/user-default.png',
  assistant: '/static/images/2LOGO.png',
  system: '/static/avatar/system.png'
};

interface Props {
  /** 发送者类型: 'user' | 'assistant' | 'system' */
  sender: 'user' | 'assistant' | 'system'
  /** 头像URL，默认根据sender类型选择 */
  avatarUrl?: string
  /** 消息时间戳 */
  timestamp?: number | string
  /** 是否显示为加载中状态 */
  isLoading?: boolean
  /** 消息ID，用于操作引用 */
  messageId?: string
  /** DOM元素ID，用于滚动定位 */
  id?: string
}

const props = withDefaults(defineProps<Props>(), {
  sender: 'assistant',
  avatarUrl: '',
  timestamp: undefined,
  isLoading: false,
  messageId: '',
  id: ''
});

// 计算头像URL
const computedAvatarUrl = computed(() => {
  if (props.avatarUrl) return props.avatarUrl;
  return DEFAULT_AVATARS[props.sender] || DEFAULT_AVATARS.assistant;
});

// 格式化时间
const formatTime = (timestamp: number | string): string => {
  if (!timestamp) return '';
  
  const date = new Date(typeof timestamp === 'string' ? parseInt(timestamp) : timestamp);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) return '';
  
  // 小于当天的显示日期+时间，当天的只显示时间
  const today = new Date();
  const isToday = date.getDate() === today.getDate() && 
                 date.getMonth() === today.getMonth() && 
                 date.getFullYear() === today.getFullYear();
  
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  
  if (isToday) {
    return `${hours}:${minutes}`;
  } else {
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    return `${month}-${day} ${hours}:${minutes}`;
  }
};

// 定义事件
const emit = defineEmits<{
  (e: 'imageTap', url: string): void;
  (e: 'imageLoaded', data: { messageId: string, id: string }): void;
}>();

// 计算元素ID
const elementId = computed(() => props.id || `msg-${props.messageId}`);

// 检查是否含有图片
const hasImage = computed(() => {
  // 在实际应用中，这里会检查slot内容，但Vue组合式API中这很复杂
  // 通常会通过父组件传递标志来判断
  return false;
});

// 获取图片
const getImage = (): string => {
  // 在实际应用中，这里会获取slot中的图片URL
  return '';
};

// 处理图片点击
const handleImageTap = (url: string): void => {
  emit('imageTap', url);
};

// 处理图片加载完成
const handleImageLoaded = (data: { messageId: string, id: string }): void => {
  // 通知父组件图片已加载完成，可以更新滚动
  emit('imageLoaded', {
    messageId: data.messageId || props.messageId,
    id: data.id || elementId.value
  });
};
</script>

<style lang="scss" scoped>
.chat-bubble {
  display: flex;
  margin-bottom: var(--spacing-sm, 10px);
  
  // 用户消息（靠右）
  &--user {
    flex-direction: row-reverse;
    
    .chat-bubble__content {
      background-color: var(--color-primary, #FF6B35);
      color: var(--text-inverse, #FFFFFF);
      border-radius: var(--radius-lg, 16px) var(--radius-sm, 2px) var(--radius-lg, 16px) var(--radius-lg, 16px);
      margin-right: var(--spacing-sm, 12px);
    }
    
    .chat-bubble__time {
      text-align: right;
      color: rgba(255, 255, 255, 0.7);
    }
  }
  
  // AI消息（靠左）
  &--assistant {
    flex-direction: row;
    
    .chat-bubble__content {
      background-color: var(--bg-secondary, #f0f0f0);
      color: var(--text-primary, #333333);
      border-radius: var(--radius-sm, 2px) var(--radius-lg, 16px) var(--radius-lg, 16px) var(--radius-lg, 16px);
      margin-left: var(--spacing-sm, 12px);
    }
  }
  
  // 系统消息（居中）
  &--system {
    justify-content: center;
    
    .chat-bubble__content {
      background-color: var(--bg-secondary, #f0f0f0);
      color: var(--text-secondary, #666666);
      border-radius: var(--radius-lg, 16px);
      font-size: var(--font-size-sm, 14px);
      max-width: 90%;
      text-align: center;
    }
  }
  
  // 头像
  &__avatar {
    width: 40px;
    height: 40px;
    flex-shrink: 0;
    
    &-img {
      width: 100%;
      height: 100%;
      border-radius: var(--radius-circle, 50%);
      border: 1px solid rgba(0, 0, 0, 0.05);
      object-fit: cover;
    }
  }
  
  // 内容
  &__content {
    padding: var(--spacing-sm, 12px) var(--spacing-md, 16px);
    max-width: 70%;
    word-break: break-word;
    position: relative;
  }
  
  // 时间戳
  &__time {
    font-size: var(--font-size-xs, 12px);
    color: var(--text-tertiary, #999999);
    margin-top: var(--spacing-xs, 4px);
  }
  
  // 加载中状态
  &--loading {
    .chat-bubble__content {
      min-width: 60px;
      min-height: 30px;
    }
  }
  
  &__loading {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs, 4px);
    
    &-dot {
      width: 8px;
      height: 8px;
      border-radius: var(--radius-circle, 50%);
      background-color: var(--text-tertiary, #999999);
      animation: loading 1.4s infinite ease-in-out both;
      
      &:nth-child(1) {
        animation-delay: -0.32s;
      }
      
      &:nth-child(2) {
        animation-delay: -0.16s;
      }
    }
  }
}

@keyframes loading {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 多端适配 */
/* #ifdef MP-WEIXIN */
.chat-bubble {
  &__avatar {
    width: 36px;
    height: 36px;
  }
}
/* #endif */
</style> 