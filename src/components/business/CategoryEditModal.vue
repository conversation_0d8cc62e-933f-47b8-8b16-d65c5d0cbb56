<script setup lang="ts">
import type { Category } from '@/stores/category.store';
import { ref, watch } from 'vue';
import AppModal from '@/components/common/AppModal.vue';
import AppInput from '@/components/common/AppInput.vue';
import AppButton from '@/components/common/AppButton.vue';
import AppIcon from '@/components/common/AppIcon.vue';

// 预设图标
const presetIcons = [
  'utensils', 'shopping-cart', 'home', 'car', 'plane', 
  'coffee', 'mobile-alt', 'money-bill-wave', 'gift', 
  'heartbeat', 'graduation-cap', 'paw', 'gamepad', 
  'dumbbell', 'cart-shopping', 'briefcase', 'book',
  'wallet', 'chart-line', 'credit-card', 'suitcase',
  'child', 'shirt', 'wine-glass', 'basket-shopping'
];

// 预设颜色
const presetColors = [
  '#FF6B35', // 主题色（橙色）
  '#4CAF50', // 绿色
  '#2196F3', // 蓝色
  '#9C27B0', // 紫色
  '#8BC34A', // 浅绿色
  '#FF9800', // 橙色
  '#F44336', // 红色
  '#03A9F4', // 浅蓝色
  '#E91E63', // 粉色
  '#CDDC39', // 黄绿色
  '#795548', // 棕色
  '#607D8B', // 蓝灰色
  '#9575CD', // 浅紫色
  '#00BCD4', // 青色
];

interface Props {
  visible: boolean;
  category?: Partial<Category>;
  mode: 'add' | 'edit';
}

const props = defineProps<Props>();
const emit = defineEmits(['update:visible', 'save', 'cancel']);

// 编辑状态
const name = ref('');
const icon = ref('');
const bgColor = ref('');
const type = ref<'income' | 'expense'>('expense');

// 重置表单
const resetForm = () => {
  if (props.mode === 'edit' && props.category) {
    name.value = props.category.name || '';
    icon.value = props.category.icon || '';
    bgColor.value = props.category.bgColor || '';
    type.value = props.category.type || 'expense';
  } else {
    name.value = '';
    icon.value = '';
    bgColor.value = presetColors[0]; // 默认第一个颜色
    type.value = 'expense';
  }
};

// 监听 category 或 visible 变化
watch(() => props.category, resetForm, { immediate: true });
watch(() => props.visible, (val) => {
  if (val) resetForm();
}, { immediate: true });

// 关闭模态框
const handleClose = () => {
  emit('update:visible', false);
  emit('cancel');
};

// 保存分类
const handleSave = () => {
  if (!name.value || !icon.value || !bgColor.value) {
    uni.showToast({
      title: '请填写完整的分类信息',
      icon: 'none',
    });
    return;
  }

  const categoryData: Partial<Category> = {
    name: name.value,
    icon: icon.value,
    bgColor: bgColor.value,
    type: type.value,
  };

  if (props.mode === 'edit' && props.category?.id) {
    categoryData.id = props.category.id;
  }

  emit('save', categoryData);
  emit('update:visible', false);
};

// 选择图标
const selectIcon = (selectedIcon: string) => {
  icon.value = selectedIcon;
};

// 选择颜色
const selectColor = (selectedColor: string) => {
  bgColor.value = selectedColor;
};

// 切换类型
const toggleType = (selectedType: 'income' | 'expense') => {
  type.value = selectedType;
};
</script>

<template>
  <AppModal :visible="visible" @close="handleClose">
    <view class="category-edit-modal">
      <!-- 标题 -->
      <view class="category-edit-modal__header">
        <text class="category-edit-modal__title">{{ mode === 'add' ? '添加分类' : '编辑分类' }}</text>
      </view>

      <!-- 表单内容 -->
      <view class="category-edit-modal__content">
        <!-- 分类名称 -->
        <view class="category-edit-modal__form-item">
          <text class="category-edit-modal__label">分类名称</text>
          <AppInput
            v-model="name"
            placeholder="请输入分类名称（最多10个字符）"
            maxlength="10"
            class="category-edit-modal__input"
          />
        </view>

        <!-- 分类类型 -->
        <view class="category-edit-modal__form-item">
          <text class="category-edit-modal__label">分类类型</text>
          <view class="category-edit-modal__type-switch">
            <view
              class="type-switch__item"
              :class="{ 'type-switch__item--active': type === 'expense' }"
              @click="toggleType('expense')"
            >
              支出
            </view>
            <view
              class="type-switch__item"
              :class="{ 'type-switch__item--active': type === 'income' }"
              @click="toggleType('income')"
            >
              收入
            </view>
          </view>
        </view>

        <!-- 图标选择 -->
        <view class="category-edit-modal__form-item">
          <text class="category-edit-modal__label">选择图标</text>
          <view class="category-edit-modal__icon-grid">
            <view
              v-for="presetIcon in presetIcons"
              :key="presetIcon"
              class="icon-item"
              :class="{ 'icon-item--active': icon === presetIcon }"
              :style="{ backgroundColor: icon === presetIcon ? bgColor : 'var(--color-gray-100, #f0f0f0)' }"
              @click="selectIcon(presetIcon)"
            >
              <AppIcon
                :icon="presetIcon"
                :color="icon === presetIcon ? 'white' : 'var(--color-gray-500, #999)'"
              />
            </view>
          </view>
        </view>

        <!-- 颜色选择 -->
        <view class="category-edit-modal__form-item">
          <text class="category-edit-modal__label">选择颜色</text>
          <view class="category-edit-modal__color-grid">
            <view
              v-for="presetColor in presetColors"
              :key="presetColor"
              class="color-item"
              :class="{ 'color-item--active': bgColor === presetColor }"
              :style="{ backgroundColor: presetColor }"
              @click="selectColor(presetColor)"
            >
              <AppIcon
                v-if="bgColor === presetColor"
                icon="check"
                color="white"
                size="xs"
              />
            </view>
          </view>
        </view>

        <!-- 当前预览 -->
        <view class="category-edit-modal__form-item">
          <text class="category-edit-modal__label">预览效果</text>
          <view class="category-edit-modal__preview">
            <view
              class="preview-icon"
              :style="{ backgroundColor: bgColor || 'var(--color-gray-100, #f0f0f0)' }"
            >
              <AppIcon
                :icon="icon || 'question-circle'"
                color="white"
              />
            </view>
            <text class="preview-text">{{ name || '分类名称' }}</text>
          </view>
        </view>
      </view>

      <!-- 按钮 -->
      <view class="category-edit-modal__footer">
        <AppButton
          type="outline"
          size="md"
          class="category-edit-modal__btn category-edit-modal__btn--cancel"
          @click="handleClose"
        >
          取消
        </AppButton>
        <AppButton
          type="primary"
          size="md"
          class="category-edit-modal__btn"
          @click="handleSave"
        >
          保存
        </AppButton>
      </view>
    </view>
  </AppModal>
</template>

<style lang="scss" scoped>
.category-edit-modal {
  width: 100%;
  padding: var(--space-md, 16px);

  &__header {
    margin-bottom: var(--space-md, 16px);
    text-align: center;
  }

  &__title {
    font-size: var(--font-size-large, 18px);
    font-weight: 500;
    color: var(--text-primary, #333);
  }

  &__content {
    margin-bottom: var(--space-md, 16px);
  }

  &__form-item {
    margin-bottom: var(--space-md, 16px);
  }

  &__label {
    display: block;
    font-size: var(--font-size-small, 14px);
    color: var(--text-secondary, #666);
    margin-bottom: var(--space-xs, 8px);
  }

  &__input {
    width: 100%;
  }

  &__type-switch {
    display: flex;
    border-radius: var(--radius-pill, 999px);
    overflow: hidden;
    border: 1px solid var(--color-primary, #ff6b35);
    height: 40px;
  }

  &__icon-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
  }

  &__color-grid {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -8px;
  }

  &__preview {
    display: flex;
    align-items: center;
    padding: var(--space-md, 16px);
    background-color: var(--color-gray-100, #f5f5f5);
    border-radius: var(--radius-md, 8px);
  }

  &__footer {
    display: flex;
    justify-content: space-between;
  }

  &__btn {
    flex: 1;

    &--cancel {
      margin-right: var(--space-sm, 8px);
    }
  }
}

.type-switch__item {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-small, 14px);
  cursor: pointer;
  transition: all 0.3s;

  &--active {
    background-color: var(--color-primary, #ff6b35);
    color: white;
  }
}

.icon-item {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-md, 8px);
  margin: 8px;
  cursor: pointer;
  transition: all 0.3s;

  &--active {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }
}

.color-item {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  margin: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: all 0.3s;

  &--active {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  &::after {
    content: "";
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    border-radius: 50%;
    border: 2px solid transparent;
    transition: all 0.3s;
  }

  &--active::after {
    border-color: var(--color-primary, #ff6b35);
  }
}

.preview-icon {
  width: 48px;
  height: 48px;
  border-radius: var(--radius-md, 8px);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: var(--space-md, 16px);
}

.preview-text {
  font-size: var(--font-size-medium, 16px);
  color: var(--text-primary, #333);
}
</style> 