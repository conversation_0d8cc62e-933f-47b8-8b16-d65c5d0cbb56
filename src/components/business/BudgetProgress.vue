<script setup lang="ts">
import { computed } from 'vue';

// 定义组件的props
interface Props {
  // 预算标题
  title: string
  // 已使用金额
  spent: number
  // 预算总额
  budget: number
  // 货币符号
  currency?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '本月预算',
  spent: 0,
  budget: 1000,
  currency: '¥',
});

// 计算进度百分比
const progressValue = computed(() => {
  // 边界处理：防止NaN和无限值
  if (props.budget <= 0)
    return 0;

  // 计算进度比例，最大为100%
  const percentage = Math.min(100, (props.spent / props.budget) * 100);
  return Math.round(percentage * 10) / 10; // 保留一位小数
});

// 格式化进度百分比显示
const displayPercentage = computed(() => `${progressValue.value}%`);

// 计算进度条宽度样式
const progressPercentage = computed(() => `${progressValue.value}%`);

// 根据进度计算颜色
const progressColor = computed(() => {
  // 根据进度设置不同的颜色 - 使用CSS变量确保主题一致性
  if (progressValue.value < 70) {
    return 'var(--color-success, #4CAF50)'; // 安全范围 - 绿色
  }
  else if (progressValue.value < 90) {
    return 'var(--color-warning, #FFC107)'; // 警告范围 - 黄色
  }
  else {
    return 'var(--color-error, #F44336)'; // 危险范围 - 红色
  }
});

// 格式化预算和已使用金额
const formattedSpent = computed(() => `${props.currency}${props.spent.toLocaleString()}`);

const formattedBudget = computed(() => `${props.currency}${props.budget.toLocaleString()}`);

// 根据使用情况显示状态消息
const statusMessage = computed(() => {
  const remaining = props.budget - props.spent;

  if (remaining < 0) {
    return `超出预算 ${props.currency}${Math.abs(remaining).toLocaleString()}`;
  }
  else if (progressValue.value >= 90) {
    return `预算即将用完，剩余 ${props.currency}${remaining.toLocaleString()}`;
  }
  else if (progressValue.value >= 70) {
    return `预算使用良好，剩余 ${props.currency}${remaining.toLocaleString()}`;
  }
  else {
    return `预算充足，剩余 ${props.currency}${remaining.toLocaleString()}`;
  }
});
</script>

<template>
  <view class="budget-progress">
    <view class="budget-progress__header">
      <view class="budget-progress__title">
        {{ title }}
      </view>
      <view class="budget-progress__values">
        <text class="budget-progress__spent">
          {{ formattedSpent }}
        </text>
        <text class="budget-progress__separator">
          /
        </text>
        <text class="budget-progress__budget">
          {{ formattedBudget }}
        </text>
      </view>
    </view>

    <view class="budget-progress__bar-container">
      <view
        class="budget-progress__bar"
        :style="{ width: progressPercentage, backgroundColor: progressColor }"
      />
    </view>

    <view class="budget-progress__footer">
      <text class="budget-progress__percentage">
        {{ displayPercentage }}
      </text>
      <text class="budget-progress__message">
        {{ statusMessage }}
      </text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .budget-progress {
    width: 100%;
    padding: var(--spacing-md, 16px);
    background-color: var(--bg-primary, #fff);
    border-radius: var(--radius-card, 12px);
    box-shadow: var(--shadow-card, 0 2px 8px rgb(0 0 0 / 10%));
    margin-bottom: var(--spacing-md, 16px);

    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-sm, 8px);
    }

    &__title {
      font-size: var(--font-size-md, 16px);
      font-weight: 500;
      color: var(--text-primary, #333);
    }

    &__values {
      display: flex;
      align-items: baseline;
    }

    &__spent {
      font-size: var(--font-size-md, 16px);
      font-weight: 600;
      color: var(--text-primary, #333);
    }

    &__separator {
      margin: 0 var(--spacing-xs, 4px);
      color: var(--text-secondary, #666);
      font-size: var(--font-size-sm, 14px);
    }

    &__budget {
      font-size: var(--font-size-sm, 14px);
      color: var(--text-secondary, #666);
    }

    &__bar-container {
      height: 8px;
      width: 100%;
      background-color: var(--bg-secondary, #f5f5f5);
      border-radius: var(--radius-pill, 50px);
      overflow: hidden;
      margin: var(--spacing-sm, 8px) 0;
    }

    &__bar {
      height: 100%;
      border-radius: var(--radius-pill, 50px);
      transition:
        width 0.3s ease,
        background-color 0.3s ease;
    }

    &__footer {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-top: var(--spacing-xs, 4px);
    }

    &__percentage {
      font-size: var(--font-size-sm, 14px);
      font-weight: 500;
      color: var(--text-primary, #333);
    }

    &__message {
      font-size: var(--font-size-sm, 14px);
      color: var(--text-secondary, #666);
    }
  }

  // 多端适配

  /* #ifdef MP-WEIXIN */
  .budget-progress {
    margin: 0 var(--spacing-md, 16px) var(--spacing-md, 16px);
    width: auto;
  }

  /* #endif */
</style>
