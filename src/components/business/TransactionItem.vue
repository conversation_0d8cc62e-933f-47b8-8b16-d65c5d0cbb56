<script setup lang="ts">
import type { Transaction } from '@/types/transaction';
import type { PropType } from 'vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppSwipeAction from '@/components/common/AppSwipeAction.vue';
import AppSwipeActionButton from '@/components/common/AppSwipeActionButton.vue';
import { useCategoryStore } from '@/stores/category.store';
import { useTransactionStore } from '@/stores/transaction.store';
import dayjs from 'dayjs';
import { computed, ref } from 'vue';

const props = defineProps({
  transaction: {
    type: Object as PropType<Transaction>,
    required: true,
  },
});

const emit = defineEmits(['click']);

const categoryStore = useCategoryStore();
const transactionStore = useTransactionStore();
const swipeActionRef = ref<InstanceType<typeof AppSwipeAction> | null>(null);

// 获取默认背景颜色的辅助函数
const getDefaultBgColor = (type: string) => {
  if (type === 'income') {
    return 'var(--color-success, #4CAF50)';
  } else if (type === 'expense') {
    return 'var(--color-primary, #FF6B35)';
  } else {
    return 'var(--bg-secondary, #f5f5f5)';
  }
};

const categoryInfo = computed(() => {
  // 获取交易类型
  const txType = props.transaction.type || 'expense';
  
  // 1. 首先，检查transaction.category是否存在且是对象类型（API有时会直接传入完整的category对象）
  if (props.transaction.category && typeof props.transaction.category === 'object') {
    const category = props.transaction.category;
    // 如果category对象包含完整信息，直接使用
    if (category.id && category.name && category.icon) {
      console.log('使用交易自带的完整category对象:', category);
      return {
        id: category.id,
        name: category.name,
        icon: category.icon,
        bgColor: category.bgColor || getDefaultBgColor(txType),
        type: txType,
      };
    }
  }
  
  // 2. 尝试从categoryId获取分类信息
  const categoryId = props.transaction.categoryId || 
                    (typeof props.transaction.category === 'string' ? props.transaction.category : undefined);
                    
  if (categoryId) {
    console.log(`通过categoryId(${categoryId})获取分类信息`);
    const category = categoryStore.getCategoryById(categoryId);
    if (category) {
      console.log('成功从categoryStore获取分类:', category);
      return {
        id: category.id,
        name: category.name,
        icon: category.icon || (txType === 'income' ? 'wallet' : 'shopping-cart'),
        bgColor: category.bgColor || getDefaultBgColor(txType),
        type: category.type || txType,
      };
    }
  }
  
  // 3. 如果上述方法都失败，使用默认分类
  const defaultCategory = txType === 'income' 
    ? categoryStore.getDefaultIncomeCategory() 
    : categoryStore.getDefaultExpenseCategory();
  
  if (defaultCategory) {
    console.log(`无法找到categoryId(${props.transaction.categoryId})的分类，使用默认${txType}分类:`, defaultCategory);
    return defaultCategory;
  }
  
  // 4. 最终后备方案
  console.log('无法获取任何分类信息，使用基础未知分类');
  return {
    id: 'unknown',
    name: txType === 'income' ? '未知收入' : '未知支出',
    icon: txType === 'income' ? 'wallet' : 'question-circle',
    bgColor: getDefaultBgColor(txType),
    type: txType,
  };
});

const formatAccount = computed(() => {
  const account = props.transaction.account;
  if (!account)
    return '现金账户';
  if (typeof account === 'object' && account !== null && 'name' in account) {
    return account.name;
  }
  if (typeof account === 'string') {
    return account;
  }
  return String(account);
});

const formattedAmount = computed(() => {
  const amount = props.transaction.amount || 0;
  const prefix = props.transaction.type === 'income' ? '+' : '-';
  return `${prefix} ${Math.abs(amount).toFixed(2)}`;
});

const formattedTime = computed(() => {
  const dateStr = props.transaction.date;
  if (!dateStr) return '--:--';
  // 检查是否包含时间部分（T后有非零时间）
  const hasTime = /T\d{2}:\d{2}/.test(dateStr);
  if (hasTime) {
    try {
      return dayjs(dateStr).format('HH:mm');
    } catch (e) {
      return '--:--';
    }
  }
  // 没有时间部分则不显示
  return '';
});

// 处理点击事件
function handleClick() {
  if (swipeActionRef.value?.isOpened) {
    swipeActionRef.value.close();
    return;
  }
  emit('click', props.transaction);
}

// 处理编辑按钮点击事件
function handleEdit() {
  // 获取事务的模式（income或expense）
  const mode = props.transaction.type;
  
  // 计算绝对金额值（去掉负号）
  const absoluteAmount = Math.abs(props.transaction.amount);
  
  // 编辑时导航到记录页面，并传递ID、金额和模式
  // 这三个参数让mock API可以返回一致的数据
  uni.navigateTo({
    url: `/pages/transaction/record?id=${props.transaction.id}&amount=${absoluteAmount}&mode=${mode}`,
    fail: (error) => {
      console.error('Navigate to record page failed:', error);
      uni.showToast({ title: '页面跳转失败', icon: 'none' });
    },
  });
}

// 处理删除按钮点击事件
async function handleDelete() {
  // 关闭滑动操作
  if (swipeActionRef.value) {
    swipeActionRef.value.close();
  }
  
  // 确认是否删除
  uni.showModal({
    title: '删除确认',
    content: '确定要删除该笔交易记录吗？',
    confirmColor: 'var(--color-error, #F44336)',
    success: async (res) => {
      if (res.confirm) {
        try {
          const txId = props.transaction.id;
          if (!txId) {
            uni.showToast({ title: '交易ID无效', icon: 'none' });
            return;
          }
          
          // 调用Store删除交易
          await transactionStore.removeTransaction(txId.toString());
          uni.showToast({ title: '删除成功', icon: 'success' });
        } catch (error) {
          console.error('Failed to delete transaction:', error);
          uni.showToast({ title: '删除失败', icon: 'none' });
        }
      }
    },
  });
}
</script>

<template>
  <AppSwipeAction ref="swipeActionRef" :buttons-width="160">
    <template #default>
      <view class="transaction-item" @tap="handleClick">
        <view class="transaction-item__icon" :style="{ backgroundColor: categoryInfo.bgColor }">
          <AppIcon :icon="categoryInfo.icon" color="white" size="sm" />
        </view>
        <view class="transaction-item__content">
          <view class="transaction-item__main">
            <text class="transaction-item__name">
              {{ categoryInfo.name }}
            </text>
            <text class="transaction-item__amount" :class="transaction.type">
              {{ formattedAmount }}
            </text>
          </view>
          <view class="transaction-item__details">
            <text class="transaction-item__account">
              {{ formatAccount }}
            </text>
            <text class="transaction-item__time">
              {{ formattedTime }}
            </text>
          </view>
        </view>
      </view>
    </template>
    
    <template #actions>
      <view class="transaction-item__actions">
      <AppSwipeActionButton
        type="primary"
        text="编辑"
        icon="edit"
          customClass="edit-btn"
        @click="handleEdit"
      />
      <AppSwipeActionButton
        type="danger"
        text="删除"
        icon="trash"
          customClass="delete-btn"
        @click="handleDelete"
      />
      </view>
    </template>
  </AppSwipeAction>
</template>

<style lang="scss" scoped>
  .transaction-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-sm, 12px) 0; // 只保留上下padding
    background-color: var(--bg-primary, #fff);
    cursor: pointer;
    transition: background-color 0.2s ease;
    position: relative;
    margin: 0;
    &::after {
      content: '';
      position: absolute;
      left: 0;
      right: 0;
      bottom: 0;
      height: 1px;
      background-color: var(--border-color-light, rgba(0, 0, 0, 0.05));
      transform: scaleY(0.5);
    }
    &:active {
      background-color: var(--bg-secondary, #f5f5f5);
    }
    &:last-child::after {
      display: none;
    }
    &__icon {
      width: 36px;
      height: 36px;
      border-radius: var(--radius-circle, 50%);
      display: flex;
      justify-content: center;
      align-items: center;
      flex-shrink: 0;
      margin-right: 12px;
    }
    &__content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      min-width: 0;
      gap: 4px;
    }
    &__main {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
    &__name {
      font-size: var(--font-size-base, 15px);
      color: var(--text-primary, #333);
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 70%;
      padding-right: 8px;
      flex-shrink: 1;
    }
    &__amount {
      font-size: var(--font-size-base, 15px);
      font-weight: 600;
      text-align: right;
      flex-shrink: 0;
      min-width: 80px;
      &.income {
        color: var(--color-success, #4caf50);
      }
      &.expense {
        color: var(--color-error, #f44336);
      }
      &.transfer {
        color: var(--text-primary, #333);
      }
    }
    &__details {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;
    }
    &__account {
      font-size: var(--font-size-xs, 12px);
      color: var(--text-secondary, #666);
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 70%;
      flex-shrink: 1;
      line-height: 1.2;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    }
    &__time {
      font-size: var(--font-size-xs, 12px);
      color: var(--text-hint, #999);
      flex-shrink: 0;
      min-width: 60px;
      text-align: right;
    }
  }
  .transaction-item__actions {
    display: flex;
    flex-direction: row;
    align-items: center;
    height: 100%;
    width: 100%;
  }
  .edit-btn, .delete-btn {
    flex: 1 1 0;
    min-width: 0;
    max-width: 100%;
    justify-content: center;
  }
</style>
