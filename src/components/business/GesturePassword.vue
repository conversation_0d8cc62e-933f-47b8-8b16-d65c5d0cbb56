<script setup lang="ts">
import { getTouchPosition, isPointInCircle } from '@/utils/gesture';
import { computed, nextTick, onMounted, ref, watch } from 'vue';

// --- 定义接口和类型 ---
interface Point { x: number, y: number, index: number, active: boolean }
type Mode = 'set' | 'verify'
interface GestureFailData { message: string, points: Point[] }

// --- Props --- 定义组件接收的属性
const props = defineProps({
  // Canvas 容器尺寸 (px)
  size: { type: Number, default: 280 },
  // 密码点半径 (px)
  pointRadius: { type: Number, default: 30 }, // 外圈用于触摸判断
  // 内部实心点半径 (px)
  innerPointRadius: { type: Number, default: 8 },
  // 线条宽度 (px)
  lineWidth: { type: Number, default: 2 },
  // 正常状态颜色 - 使用CSS变量
  normalColor: { type: String, default: 'var(--color-secondary, #CCCCCC)' }, 
  // 激活状态颜色 - 使用CSS变量
  activeColor: { type: String, default: 'var(--color-primary, #FF6B35)' }, 
  // 错误状态颜色 - 使用CSS变量
  errorColor: { type: String, default: 'var(--color-error, #F44336)' }, 
  // Canvas ID (确保唯一性，特别是在同一页面多次使用时)
  canvasId: { type: String, default: 'gestureCanvas' },
  // 模式: 'set' (设置密码) 或 'verify' (验证密码)
  mode: { type: String as () => Mode, default: 'set' },
  // 最小连接点数
  minPoints: { type: Number, default: 4 },
  // 验证模式下需要比对的密码 (点索引数组)
  storedPassword: { type: Array as () => number[], default: () => [] },
  // 容器尺寸，默认等于 size
  containerSize: { type: Number, default: 280 },
  // 是否在组件内部显示状态消息
  showMessage: { type: Boolean, default: true },
  // 初始状态消息
  initialMessage: { type: String, default: '' },
});

// --- Emits --- 定义组件触发的事件
const emit = defineEmits<{
  (e: 'set', points: number[]): void // 设置模式下，密码设置完成时触发
  (e: 'success', points: number[]): void // 验证成功时触发
  (e: 'fail', data: GestureFailData): void // 验证失败时触发
  (e: 'error', message: string): void // 发生错误时触发 (如点数不足)
  (e: 'update:password', password: number[]): void // 密码更新时触发
  (e: 'complete', password: number[]): void // 手势绘制完成时触发
  (e: 'verified', success: boolean): void // 验证结果触发
}>();

// --- Refs --- 组件内部状态
const ctx = ref<UniApp.CanvasContext | null>(null);
const points = ref<Point[]>([]); // 九个点的信息
const selectedPoints = ref<Point[]>([]); // 当前选中的点
const currentTouchPos = ref<{ x: number, y: number } | null>(null); // 当前触摸点位置
const currentPosition = ref<{ x: number, y: number } | null>(null); // 当前触摸位置（用于绘制跟随线）
const isDrawing = ref(false); // 是否正在绘制
const currentStateColor = ref(processColor(props.activeColor)); // 当前状态颜色 (用于绘制)
const canvasElement = ref<DOMRect | null>(null); // 用于 H5 获取 getBoundingClientRect
const canvasInfo = ref<{ width: number, height: number } | null>(null); // 用于存储 canvas 的尺寸信息
const initRetryCount = ref(0); // 初始化重试计数
const showError = ref(false); // 是否显示错误状态
const MAX_RETRY_COUNT = 3; // 最大重试次数

// 添加状态消息相关变量
const statusMessage = ref(props.initialMessage || (props.mode === 'set' ? '请绘制解锁图案' : '请输入手势密码'));
const isStatusError = ref(false);

// 处理 CSS 变量颜色，转换为实际颜色值
function processColor(colorValue: string): string {
  // 检查是否为 CSS 变量格式
  if (colorValue.startsWith('var(--')) {
    // 提取默认值，格式为 var(--variable-name, #fallback)
    const matches = colorValue.match(/var\(.*,\s*([^)]*)\)/);
    if (matches && matches[1]) {
      return matches[1].trim();
    }
  }
  return colorValue;
}

// --- 计算属性 ---
// 处理后的颜色值
const normalColorValue = computed(() => processColor(props.normalColor));
const activeColorValue = computed(() => processColor(props.activeColor));
const errorColorValue = computed(() => processColor(props.errorColor));

const pointSpacing = computed(() => 
  // 点心之间的距离
   props.containerSize / 4, // 将容器宽度分为4份，点在 1/4, 2/4, 3/4 位置
);

const computedSize = computed(() => `${props.containerSize}px`);

// 所有点的索引引用，用于绘制线条
const allPoints = computed(() => points.value);

// --- 监视器 ---
watch(
  () => props.mode,
  () => {
    reset(); // 模式切换时重置
  },
);

// 监听activeColor变化，更新当前状态颜色
watch(
  () => props.activeColor,
  (newColor) => {
    if (!showError.value) {
      currentStateColor.value = processColor(newColor);
      if (ctx.value)
        draw();
    }
  },
);

// --- 生命周期钩子 ---
onMounted(() => {
  // 使用nextTick确保DOM已更新
  nextTick(() => {
    setTimeout(() => {
      initCanvas();
    }, 50);
  });
});

// --- 方法 ---

/** 初始化 Canvas 上下文 */
async function initCanvas() {
  try {
    // 先创建画布上下文
    ctx.value = uni.createCanvasContext(props.canvasId);

    if (!ctx.value) {
      if (initRetryCount.value < MAX_RETRY_COUNT) {
        initRetryCount.value++;
        setTimeout(() => {
          initCanvas();
        }, 300 * initRetryCount.value); // 递增延迟重试
        return;
      }
      emit('error', '创建画布上下文失败');
      return;
    }

    // 获取Canvas元素尺寸信息
    const query = uni.createSelectorQuery();

    // 获取canvas节点的信息
    query
      .select(`#${props.canvasId}`)
      .boundingClientRect()
      .exec((res) => {
        if (!res?.[0]) {
          // 假设Canvas尺寸与容器尺寸相同
          canvasInfo.value = {
            width: props.containerSize,
            height: props.containerSize,
          };
        }
        else {
          const { width, height } = res[0];

          // 画布宽高设置
          canvasInfo.value = {
            width: width || props.containerSize,
            height: height || props.containerSize,
          };

          // #ifdef H5
          canvasElement.value = res[0]; // H5: 将 BoundingClientRect 赋值给 canvasElement
          // #endif
        }

        // 初始化圆点并绘制
        initPoints();
        draw();
      });
  }
  catch (error: any) {
    if (initRetryCount.value < MAX_RETRY_COUNT) {
      initRetryCount.value++;
      setTimeout(() => {
        initCanvas();
      }, 300 * initRetryCount.value); // 递增延迟重试
    }
    else {
      emit('error', `画布初始化失败: ${error?.message || '未知错误'}`);
    }
  }
}

/** 初始化九个点的位置和状态 */
function initPoints() {
  if (points.value.length > 0)
    return; // 防止重复初始化

  const spacing = pointSpacing.value;
  const tempPoints: Point[] = [];
  for (let i = 0; i < 3; i++) {
    // row
    for (let j = 0; j < 3; j++) {
      // col
      tempPoints.push({
        x: spacing * (j + 1),
        y: spacing * (i + 1),
        index: i * 3 + j,
        active: false,
      });
    }
  }
  points.value = tempPoints;
}

/** 绘制函数 */
function draw() {
  if (!ctx.value)
    return;

  const pointRadius = Number.parseInt(props.pointRadius.toString());
  const innerPointRadius = Number.parseInt(props.innerPointRadius.toString());
  const lineWidth = Number.parseInt(props.lineWidth.toString());

  // 清空画布
  ctx.value.clearRect(
    0,
    0,
    canvasInfo.value?.width || props.containerSize,
    canvasInfo.value?.height || props.containerSize,
  );

  // 设置线条样式
  ctx.value.lineWidth = lineWidth;
  ctx.value.strokeStyle = currentStateColor.value;
  ctx.value.fillStyle = currentStateColor.value;

  // 绘制所有点
  drawPoints();

  // 绘制已连接的线
  drawLines();

  // 绘制当前移动中的线
  if (isDrawing.value && selectedPoints.value.length > 0 && currentTouchPos.value) {
    const lastPoint = selectedPoints.value[selectedPoints.value.length - 1];
    ctx.value.beginPath();
    ctx.value.moveTo(lastPoint.x, lastPoint.y);
    ctx.value.lineTo(currentTouchPos.value.x, currentTouchPos.value.y);
    ctx.value.stroke();
  }

  // 执行绘制
  ctx.value.draw(false);
}

/** 绘制所有点 */
function drawPoints() {
  if (!ctx.value)
    return;

  const pointRadius = Number.parseInt(props.pointRadius.toString());
  const innerPointRadius = Number.parseInt(props.innerPointRadius.toString());

  points.value.forEach((point) => {
    // 判断该点是否已激活
    const isActive = point.active;

    // 绘制外圈
    ctx.value?.beginPath();
    ctx.value?.arc(point.x, point.y, pointRadius, 0, Math.PI * 2, false);
    ctx.value?.setLineWidth(1);
    ctx.value?.setStrokeStyle(isActive ? currentStateColor.value : normalColorValue.value);
    ctx.value?.stroke();

    // 绘制内圆（激活点的内圆填充）
    if (isActive) {
      ctx.value?.beginPath();
      ctx.value?.arc(point.x, point.y, innerPointRadius, 0, Math.PI * 2, false);
      ctx.value?.setFillStyle(currentStateColor.value);
      ctx.value?.fill();
    }
  });
}

/** 绘制连接线 */
function drawLines() {
  if (!ctx.value || selectedPoints.value.length <= 0)
    return;

  // 设置线条颜色
  ctx.value.setStrokeStyle(currentStateColor.value);

  ctx.value.beginPath();

  // 从第一个选中的点开始绘制
  const firstPoint = selectedPoints.value[0];
  ctx.value.moveTo(firstPoint.x, firstPoint.y);

  // 依次连接所有选中的点
  for (let i = 1; i < selectedPoints.value.length; i++) {
    const point = selectedPoints.value[i];
    ctx.value.lineTo(point.x, point.y);
  }

  ctx.value.stroke();
}

/** 触摸开始 */
function onTouchStart(e: any) {
  if (!ctx.value) {
    // 尝试再次初始化，以防万一
    initCanvas();
    if (!ctx.value)
      return; // 如果还是没有，则返回
  }
  resetState(); // 开始新的绘制前重置状态
  isDrawing.value = true;
  handleTouch(e);
}

/** 触摸移动 */
function onTouchMove(e: any) {
  if (!isDrawing.value || !ctx.value)
    return;
  handleTouch(e);
}

/** 触摸结束 */
function onTouchEnd() {
  if (!isDrawing.value || !ctx.value)
    return;
  isDrawing.value = false;
  currentTouchPos.value = null; // 清除当前触摸点

  // 处理手势完成逻辑
  handleGestureComplete();
  draw(); // 最后绘制一次，移除跟随线
}

/** 处理触摸事件 (start/move) */
function handleTouch(e: any) {
  const pos = getTouchPosition(e, canvasElement.value); // 获取触摸点相对 Canvas 的坐标
  if (!pos)
    return;

  currentTouchPos.value = pos;

  // 检查是否有新的点被选中
  points.value.forEach((p) => {
    if (!p.active && isPointInCircle(pos, p, Number.parseInt(props.pointRadius.toString()))) {
      // 确保不重复添加同一个点
      if (!selectedPoints.value.find(sp => sp.index === p.index)) {
        p.active = true;
        selectedPoints.value.push(p);
      }
    }
  });
  draw(); // 重新绘制
}

/** 验证密码是否匹配 */
function verifyPassword(inputPoints: Point[]): boolean {
  if (!props.storedPassword || props.storedPassword.length === 0) {
    return false;
  }

  if (inputPoints.length !== props.storedPassword.length) {
    return false;
  }

  // 对比输入点的索引与存储的密码是否匹配
  for (let i = 0; i < inputPoints.length; i++) {
    if (inputPoints[i].index !== props.storedPassword[i]) {
      return false;
    }
  }

  return true;
}

/** 处理手势完成 */
function handleGestureComplete() {
  if (selectedPoints.value.length === 0)
    return;

  // 提取所选点的索引数组
  const selectedIndices = selectedPoints.value.map(p => p.index);

  // 触发完成事件，发送选中的点的索引
  emit('complete', selectedIndices);

  // 检查是否满足最小点数要求
  if (selectedPoints.value.length < props.minPoints) {
    showError.value = true;
    currentStateColor.value = errorColorValue.value;
    
    // 更新状态消息
    if (props.showMessage) {
      statusMessage.value = `至少需要${props.minPoints}个点`;
      isStatusError.value = true;
      setTimeout(() => {
        isStatusError.value = false;
        statusMessage.value = props.mode === 'set' ? '请绘制解锁图案' : '请输入手势密码';
      }, 1500);
    }

    // 发出错误事件通知
    const errorMessage = `至少需要${props.minPoints}个点`;
    emit('error', errorMessage);
    emit('fail', {
      message: errorMessage,
      points: selectedPoints.value,
    });

    // 延迟重置
    setTimeout(() => reset(), 800);
    return;
  }

  // 根据不同模式处理
  if (props.mode === 'set') {
    // 设置模式：发送选择的点索引
    emit('set', selectedIndices);
    emit('update:password', selectedIndices);
  }
  else if (props.mode === 'verify') {
    // 验证模式：对比与保存的密码
    const match = verifyPassword(selectedPoints.value);

    // 触发验证结果事件
    emit('verified', match);

    if (match) {
      // 验证成功
      if (props.showMessage) {
        statusMessage.value = '验证成功';
        isStatusError.value = false;
      }
      emit('success', selectedIndices);
      setTimeout(() => reset(), 500);
    }
    else {
      // 验证失败
      showError.value = true;
      currentStateColor.value = errorColorValue.value;
      
      // 更新状态消息
      if (props.showMessage) {
        statusMessage.value = '手势密码不匹配';
        isStatusError.value = true;
        setTimeout(() => {
          isStatusError.value = false;
          statusMessage.value = '请输入手势密码';
        }, 1500);
      }

      // 发出事件通知
      const errorMessage = '手势密码不匹配';
      emit('error', errorMessage);
      emit('fail', {
        message: errorMessage,
        points: selectedPoints.value,
      });

      // 延迟重置
      setTimeout(() => reset(), 800);
    }
  }

  // 重新绘制显示结果状态
  draw();
}

/** 重置组件状态和绘图 */
function reset() {
  resetState();
  if (ctx.value) {
    draw();
  }
}

/** 仅重置状态变量 */
function resetState() {
  selectedPoints.value = [];
  points.value.forEach(p => (p.active = false));
  currentTouchPos.value = null;
  currentStateColor.value = activeColorValue.value; // 恢复默认激活色
  isDrawing.value = false;
  showError.value = false;
}

// 设置状态消息
function setMessage(message: string, isError: boolean = false) {
  if (props.showMessage) {
    statusMessage.value = message;
    isStatusError.value = isError;
  }
}

// --- 暴露方法 --- (如果需要父组件调用)
defineExpose({ 
  reset, 
  setMessage, // 新增：暴露设置消息的方法
});
</script>

<template>
  <view
    class="gesture-password-container"
    :style="{ width: `${containerSize}px`, height: `${containerSize}px` }"
  >
    <canvas
      :id="canvasId"
      :canvas-id="canvasId"
      :style="{ width: `${containerSize}px`, height: `${containerSize}px` }"
      @touchstart="onTouchStart"
      @touchmove.stop.prevent="onTouchMove"
      @touchend="onTouchEnd"
      @touchcancel="onTouchEnd"
    ></canvas>
    
    <!-- 添加状态消息显示区域 -->
    <text
      v-if="showMessage"
      class="gesture-status-message"
      :class="{ 'gesture-status-message--error': isStatusError }"
    >
      {{ statusMessage }}
    </text>
  </view>
</template>

<style lang="scss" scoped>
  .gesture-password-container {
    position: relative; // 确保 canvas 在容器内
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  // Canvas 默认是 inline 元素，但 style 设置了宽高，表现类似 block
  canvas {
    display: block; // 确保 canvas 表现一致
  }
  
  // 状态消息样式
  .gesture-status-message {
    margin-top: var(--space-md, 16px);
    font-size: var(--font-size-medium, 16px);
    color: var(--text-primary, #333);
    text-align: center;
    transition: color var(--transition-normal, 0.3s) ease;
    min-height: 22px; // 添加最小高度，避免消息出现/消失时布局跳动
    width: 100%; // 确保宽度填满容器
    padding: 0 var(--space-md, 16px); // 两侧添加边距
    
    &--error {
      color: var(--color-error, #F44336);
    }
  }
</style>
