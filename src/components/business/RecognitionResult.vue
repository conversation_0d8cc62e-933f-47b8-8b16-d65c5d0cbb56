<template>
  <view class="recognition-result">
    <!-- 交易信息卡片 -->
    <view class="recognition-result__card">
      <!-- 标题和金额 -->
      <view class="recognition-result__header">
        <view class="recognition-result__category">
          <view 
            class="recognition-result__category-icon" 
            :style="{ backgroundColor: categoryInfo?.bgColor || '#f0f0f0' }"
          >
            <AppIcon 
              :icon="categoryInfo?.icon || 'circle-question'" 
              color="white" 
            />
          </view>
          <text class="recognition-result__category-name">{{ categoryInfo?.name || '未分类' }}</text>
        </view>
        <view class="recognition-result__amount" :class="amountClass">
          {{ amountPrefix }}{{ formatAmount(transaction.amount) }}
        </view>
      </view>
      
      <!-- 描述和日期 -->
      <view class="recognition-result__info">
        <view class="recognition-result__desc">
          {{ transaction.description || '无备注' }}
        </view>
        <view class="recognition-result__date">
          {{ formatDate(transaction.date) }}
        </view>
      </view>
      
      <!-- 操作按钮 -->
      <view class="recognition-result__actions">
        <AppButton 
          v-if="!isConfirmed" 
          type="success" 
          size="mini" 
          icon="check"
          @click="handleConfirm"
        >
          确认
        </AppButton>
        <AppButton 
          type="primary" 
          size="mini" 
          icon="edit"
          @click="handleEdit"
        >
          编辑
        </AppButton>
        <AppButton 
          v-if="!isConfirmed"
          type="error" 
          size="mini" 
          icon="trash"
          @click="handleDelete"
        >
          删除
        </AppButton>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppButton from '@/components/common/AppButton.vue';
import { useCategoryStore } from '@/stores/category.store';
import type { Category } from '@/stores/category.store';

// 定义交易识别结果的接口
interface TransactionRecognized {
  id?: string | number
  type: 'income' | 'expense'
  amount: number
  categoryId?: string | number
  categoryName?: string
  date: string | number | Date
  description?: string
}

interface Props {
  /** 识别出的交易信息 */
  transaction: TransactionRecognized
  /** 是否已确认 */
  isConfirmed?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  isConfirmed: false
});

const emit = defineEmits<{
  (e: 'edit-transaction', transaction: TransactionRecognized): void
  (e: 'confirm-transaction', transaction: TransactionRecognized): void
  (e: 'delete-transaction', transaction: TransactionRecognized): void
}>();

// 获取分类Store
const categoryStore = useCategoryStore();

// 计算分类信息
const categoryInfo = computed<Category | undefined>(() => {
  // 如果有categoryId，优先使用categoryId查找
  if (props.transaction.categoryId) {
    const category = categoryStore.getCategoryById(props.transaction.categoryId);
    if (category) return category;
  }
  
  // 如果有categoryName，尝试根据名称和类型查找
  if (props.transaction.categoryName) {
    const categories = categoryStore.getCategoriesByType(props.transaction.type);
    const category = categories.find(cat => cat.name === props.transaction.categoryName);
    if (category) return category;
  }
  
  // 如果都找不到，返回默认分类
  return props.transaction.type === 'income' 
    ? categoryStore.getCategoriesByType('income')[0] 
    : categoryStore.getCategoriesByType('expense')[0];
});

// 金额前缀
const amountPrefix = computed(() => {
  return props.transaction.type === 'income' ? '+' : '-';
});

// 金额样式
const amountClass = computed(() => {
  return `recognition-result__amount--${props.transaction.type}`;
});

// 格式化金额
const formatAmount = (amount: number): string => {
  return Math.abs(amount).toFixed(2);
};

// 格式化日期
const formatDate = (date: string | number | Date): string => {
  const dateObj = new Date(date);
  const year = dateObj.getFullYear();
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const day = dateObj.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// 处理确认按钮
const handleConfirm = () => {
  emit('confirm-transaction', props.transaction);
};

// 处理编辑按钮
const handleEdit = () => {
  emit('edit-transaction', props.transaction);
};

// 处理删除按钮
const handleDelete = () => {
  emit('delete-transaction', props.transaction);
};
</script>

<style lang="scss" scoped>
.recognition-result {
  width: 100%;
  
  &__card {
    background-color: var(--bg-primary, #ffffff);
    border-radius: var(--radius-card, 12px);
    box-shadow: var(--shadow-card, 0 2px 8px rgba(0, 0, 0, 0.08));
    padding: var(--spacing-md, 16px);
    margin-top: var(--spacing-xs, 4px);
  }
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-sm, 12px);
  }
  
  &__category {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 8px);
    
    &-icon {
      width: 32px;
      height: 32px;
      border-radius: var(--radius-circle, 50%);
      display: flex;
      align-items: center;
      justify-content: center;
    }
    
    &-name {
      font-size: var(--font-size-md, 16px);
      font-weight: 500;
      color: var(--text-primary, #333333);
    }
  }
  
  &__amount {
    font-size: var(--font-size-lg, 18px);
    font-weight: 600;
    
    &--income {
      color: var(--color-success, #4CAF50);
    }
    
    &--expense {
      color: var(--color-error, #F44336);
    }
  }
  
  &__info {
    margin-bottom: var(--spacing-md, 16px);
  }
  
  &__desc {
    font-size: var(--font-size-sm, 14px);
    color: var(--text-primary, #333333);
    margin-bottom: var(--spacing-xs, 4px);
    word-break: break-word;
  }
  
  &__date {
    font-size: var(--font-size-xs, 12px);
    color: var(--text-tertiary, #999999);
  }
  
  &__actions {
    display: flex;
    gap: var(--spacing-xs, 8px);
    justify-content: flex-end;
  }
}
</style> 