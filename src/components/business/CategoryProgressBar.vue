<template>
  <div class="category-progress-bar">
    <!-- 分段进度条 -->
    <div class="category-progress-bar__segments">
      <div
        v-for="cat in categories"
        :key="cat.id"
        class="category-progress-bar__segment"
        :style="getSegmentStyle(cat)"
        :title="`${cat.name}: ${getDisplayPercentage(cat.percentage)}%`"
      ></div>
    </div>
    <!-- 图例 -->
    <div class="category-progress-bar__legend">
      <div
        v-for="cat in categories"
        :key="cat.id"
        class="category-progress-bar__legend-item"
      >
        <span class="category-progress-bar__legend-dot" :style="{ backgroundColor: cat.color }" ></span>
        <span class="category-progress-bar__legend-label">{{ cat.name }} {{ getDisplayPercentage(cat.percentage) }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">

interface CategoryBarItem {
  id: string
  name: string
  percentage: number // 0-1之间的小数或者是0-100的整数
  color: string
}

const props = defineProps<{ categories: CategoryBarItem[] }>();

/**
 * 获取分段样式，确保即使很小的百分比也能有最小宽度显示
 */
function getSegmentStyle(cat: CategoryBarItem) {
  const MIN_SEGMENT_WIDTH = '2%'; // 最小宽度为2%
  const percentage = cat.percentage <= 1 ? cat.percentage : cat.percentage / 100;
  
  // 创建样式对象
  const style: Record<string, string | number> = {
    backgroundColor: cat.color
  };
  
  // 如果百分比太小，使用最小宽度
  if (percentage < 0.02) {
    style.flex = `0 0 ${MIN_SEGMENT_WIDTH}`;
    style.minWidth = MIN_SEGMENT_WIDTH;
  } else {
    style.flex = percentage;
  }
  
  return style;
}

/**
 * 获取用于显示的百分比值
 * 处理百分比既可能是0-1之间的小数，也可能是0-100之间的数字
 */
function getDisplayPercentage(value: number): string {
  // 如果value小于等于1，认为是0-1之间的小数，需要乘以100
  // 否则认为已经是百分比值，直接格式化
  const percentage = value <= 1 ? value * 100 : value;
  
  // 处理非常小的数值，确保显示正确
  if (percentage < 0.1) {
    return '< 0.1'; // 小于0.1%显示为"< 0.1%"
  }
  
  return percentage.toFixed(1);
}
</script>

<style lang="scss" scoped>
.category-progress-bar {
  width: 100%;
  margin-bottom: var(--space-md, 16px);

  &__segments {
    display: flex;
    gap: 2px;
    margin-bottom: 15px;
    height: 10px;
    border-radius: 5px;
    overflow: hidden;
    background: var(--bg-secondary, #f8f9fa);
  }

  &__segment {
    height: 100%;
    transition: flex 0.3s, background-color 0.3s;
    min-width: 0;
  }

  &__legend {
    display: flex;
    flex-wrap: wrap;
    gap: 10px 15px;
    font-size: 12px;
    color: var(--text-secondary, #666);
  }

  &__legend-item {
    display: flex;
    align-items: center;
  }

  &__legend-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
    display: inline-block;
  }

  &__legend-label {
    white-space: nowrap;
  }
}
</style> 