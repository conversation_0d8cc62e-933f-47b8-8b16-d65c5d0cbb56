<script setup lang="ts">
import AppCalendar from '@/components/common/AppCalendar.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';
import dayjs from 'dayjs';

// 定义组件的props
interface Props {
  // 当前输入值/表达式
    modelValue: string;
  // 控制键盘是否可见
    visible?: boolean;
    showDatePicker: boolean;
    date: Date;
    placeholder: string;
    notes: string;
    isValid: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: '0',
  visible: false,
  showDatePicker: true,
  date: () => new Date(),
  placeholder: '请输入备注',
  notes: '',
  isValid: true,
});

// 定义emit事件
const emit = defineEmits<{
    'update:modelValue': [value: string];
    confirm: [value: number];
    'chat-record': [];
    'date-change': [date: Date];
}>();

// 日期选择相关
const initialDate = props.date instanceof Date ? props.date : new Date(props.date);
const selectedDate = ref(new Date(initialDate.getFullYear(), initialDate.getMonth(), initialDate.getDate(), 0, 0, 0));
const calendarVisible = ref(false);

// 重新引入 internalExpression 来管理键盘的直接输入
const internalExpression = ref(props.modelValue);

// 监听外部 modelValue 的变化，同步到 internalExpression
watch(() => props.modelValue, (newValue) => {
  if (newValue !== internalExpression.value) {
    internalExpression.value = newValue;
  }
}, { immediate: true });

// 日期显示格式化
const dateDisplay = computed(() => {
  const today = new Date();
  const selected = selectedDate.value;

  // 判断是否是今天
  if (
      selected.getFullYear() === today.getFullYear() &&
      selected.getMonth() === today.getMonth() &&
      selected.getDate() === today.getDate()
  ) {
    return '今天';
  }

  // 判断是否是昨天
  const yesterday = new Date(today);
  yesterday.setDate(today.getDate() - 1);
  if (
      selected.getFullYear() === yesterday.getFullYear() &&
      selected.getMonth() === yesterday.getMonth() &&
      selected.getDate() === yesterday.getDate()
  ) {
    return '昨天';
  }

  // 其他日期显示月日
  return `${selected.getMonth() + 1}月${selected.getDate()}日`;
});

// 处理日期选择器点击
function handleDatePickerClick(event: Event) {
  event.stopPropagation(); // 阻止事件冒泡
    calendarVisible.value = !calendarVisible.value; // 切换日历显示状态
}

// 当 AppCalendar 的 modelValue 更新时调用此函数
function handleCalendarModelUpdate(date: Date | null) {
  if (date) {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();
    const newDate = new Date(year, month, day, 0, 0, 0);
    
    selectedDate.value = newDate;
    emit('date-change', newDate); // 通知父组件日期已更改
  }
  calendarVisible.value = false; // 关闭日历
}

// 点击外部关闭日历
function handleClickOutside(event: MouseEvent) {
  const target = event.target as HTMLElement;
  if (
      calendarVisible.value &&
      !target.closest('.app-calendar__content') &&
      !target.closest('.remark-icon-button')
  ) {
    calendarVisible.value = false;
  }
}

// 组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 辅助函数：预处理表达式，处理因父组件 toFixed(2) 导致的 ".00" 后缀
function preprocessInputExpression(expression: string): string {
  if (expression.endsWith('.00')) {
    const basePart = expression.substring(0, expression.length - 3);
    if (basePart === '' || (!isNaN(parseFloat(basePart)) && basePart === parseFloat(basePart).toString())) {
      return basePart === '' ? '0' : basePart;
    }
  }
  return expression;
}

// 主键盘事件分发函数
function handleKeyPress(key: string) {
  switch (key) {
    case '0': case '1': case '2': case '3': case '4':
    case '5': case '6': case '7': case '8': case '9':
      handleNumberInput(key);
      break;
    case '.': handleDotInput(); break;
    case '+': case '-': handleOperatorInput(key); break;
    case 'backspace': handleBackspace(); break;
    default: break;
  }
}

// 处理数字输入
function handleNumberInput(num: string) {
  let currentExpr = preprocessInputExpression(internalExpression.value);
  const lastChar = currentExpr.slice(-1);
  const isLastCharOperator = lastChar === '+' || lastChar === '-';

  if (currentExpr === '0') {
    internalExpression.value = num;
  } else if (isLastCharOperator) {
    internalExpression.value = currentExpr + num;
  } else {
    const parts = currentExpr.split(/[+-]/);
    const currentOperand = parts[parts.length - 1];
    if (currentOperand.includes('.')) {
      const decimalPart = currentOperand.split('.')[1];
      if (decimalPart && decimalPart.length >= 2) return;
      }
    if (currentOperand.replace('.', '').length >= 10) return;
    internalExpression.value = currentExpr + num;
  }
  emit('update:modelValue', internalExpression.value);
}

// 处理小数点输入
function handleDotInput() {
  let currentExpr = preprocessInputExpression(internalExpression.value);
  const parts = currentExpr.split(/[+-]/);
  const currentOperand = parts[parts.length - 1];
  if (currentOperand.includes('.')) return;
  internalExpression.value = `${currentExpr}.`;
  emit('update:modelValue', internalExpression.value);
}

// 处理运算符输入
function handleOperatorInput(operator: string) {
  let currentExpr = preprocessInputExpression(internalExpression.value);
  if (currentExpr === '' || (currentExpr === '0' && operator === '-')) return;
  const lastChar = currentExpr.slice(-1);
  if (lastChar === '+' || lastChar === '-' || lastChar === '.') {
    internalExpression.value = currentExpr.slice(0, -1) + operator;
  } else {
    if (currentExpr.includes('+') || currentExpr.includes('-')) {
      const result = calculateExpression(currentExpr); 
      internalExpression.value = result.toString() + operator;
    } else {
      internalExpression.value = currentExpr + operator;
    }
  }
  emit('update:modelValue', internalExpression.value);
}

// 处理退格键
function handleBackspace() {
  let currentExpr = internalExpression.value; 
  if (currentExpr.length <= 1 || currentExpr === '0.00') {
    internalExpression.value = '0';
    } else {
    internalExpression.value = currentExpr.slice(0, -1);
  }
  emit('update:modelValue', internalExpression.value);
}

// 计算表达式的值
// 注意：此函数目前仅支持简单的两数运算 (例如 "10+5" 或 "100-20")。
// 不支持连续运算 (如 "1+2+3") 或更复杂的表达式。
function calculateExpression(expression: string): number {
  try {
    // 尝试解析表达式的左右操作数和操作符
    // 使用正则表达式匹配数字和操作符，更稳健地处理各种情况
    const match = expression.match(/^([-+]?\d*\.?\d+)([-+])(\d*\.?\d+)$/);

    if (match && match.length === 4) {
      const leftOperand = parseFloat(match[1]);
      const operator = match[2];
      const rightOperand = parseFloat(match[3]);

      if (isNaN(leftOperand) || isNaN(rightOperand)) {
        console.error('计算表达式出错: 操作数无效', expression);
        return parseFloat(expression) || 0; // 如果解析失败，尝试返回原始表达式的数值，或0
      }

      if (operator === '+') {
        return leftOperand + rightOperand;
      } else if (operator === '-') {
        return leftOperand - rightOperand;
    }
    } else {
      // 如果不匹配 "数字-操作符-数字" 模式，则直接尝试将整个表达式转为数字
      // 这可以处理单个数字输入 (例如 "123" 或 "12.3") 的情况
      const singleNumber = parseFloat(expression);
      if (!isNaN(singleNumber)) {
        return singleNumber;
      }
    }
    // 如果所有解析都失败，记录错误并返回0
    console.error('计算表达式出错: 无法解析的表达式格式', expression);
    return 0;
  } catch (error) {
    console.error('计算表达式时发生意外错误:', error, expression);
    return 0; // 发生任何错误都返回0，保证程序不崩溃
  }
  }

  // 处理确认按钮点击
function handleConfirm() {
    // 计算表达式的最终结果
    const result = calculateExpression(internalExpression.value);
    // 向父组件传递确认事件和结果
    emit('confirm', result);
}

// 恢复 handleChatRecord 函数
function handleChatRecord() {
  emit('chat-record');
}

  // 监听父组件传入的 date prop 变化，并更新本地 selectedDate
watch(
  () => props.date,
  (newDateFromProp) => {
    if (newDateFromProp) {
      const validDateFromProp = newDateFromProp instanceof Date ? newDateFromProp : new Date(newDateFromProp);
      const year = validDateFromProp.getFullYear();
      const month = validDateFromProp.getMonth();
      const day = validDateFromProp.getDate();
      const standardizedNewDate = new Date(year, month, day, 0, 0, 0);

      if (selectedDate.value.getTime() !== standardizedNewDate.getTime()) {
        selectedDate.value = standardizedNewDate;
        console.log('AmountInput: props.date 变化，selectedDate 更新为:', standardizedNewDate);
      }
      }
    },
  { immediate: true, deep: true },
  );
</script>

<template>
  <view class="amount-input" :class="{ 'amount-input--visible': props.visible }">
    <view class="amount-input__top-section">
      <view class="account-book-button" @click="$emit('chat-record')">
        <AppIcon icon="book" size="xs" />
        <text class="account-book-button__text">聊天记</text>
      </view>

      <view class="keyboard-details-area">
        <view class="remark-with-actions">
          <input type="text" :placeholder="props.placeholder" class="remark-input" :value="props.notes" />

          <view class="remark-action-buttons">
            <view
              v-if="props.showDatePicker"
              class="remark-icon-button"
              @click="handleDatePickerClick"
            >
              <AppIcon icon="calendar" size="xs" />
              <text class="remark-icon-button__text">{{ dateDisplay }}</text>
            </view>

            <view class="remark-icon-button camera-only">
              <AppIcon icon="camera" size="xs" />
            </view>
          </view>
        </view>

        <view class="amount-display-container">
          <view class="amount-display">{{ internalExpression }}</view>
        </view>
      </view>
    </view>

    <view class="keyboard-grid-background">
      <view class="keyboard-grid">
        <!-- 数字键 1-9 -->
        <view
          v-for="num in ['1', '2', '3', '4', '5', '6', '7', '8', '9']"
          :key="num"
          class="keyboard-key keyboard-key--number"
          @click="handleKeyPress(num)"
        >
          {{ num }}
        </view>

        <!-- 小数点 -->
        <view
          class="keyboard-key keyboard-key--dot keyboard-key--function"
          @click="handleKeyPress('.')"
        >
          .
        </view>

        <!-- 数字0 -->
        <view class="keyboard-key keyboard-key--number" @click="handleKeyPress('0')"> 0 </view>

        <!-- 聊天记录 -->
        <view
          class="keyboard-key keyboard-key--chat keyboard-key--function"
          @click="handleChatRecord"
        >
          <AppIcon icon="comment" size="xs" /> 聊天记账
        </view>

        <!-- 退格键 -->
        <view
          class="keyboard-key keyboard-key--backspace keyboard-key--function"
          @click="handleKeyPress('backspace')"
        >
          <AppIcon icon="backspace" size="lg" />
        </view>

        <!-- 加号 -->
        <view
          class="keyboard-key keyboard-key--plus keyboard-key--operator"
          @click="handleKeyPress('+')"
        >
          +
        </view>

        <!-- 减号 -->
        <view
          class="keyboard-key keyboard-key--minus keyboard-key--operator"
          @click="handleKeyPress('-')"
        >
          -
        </view>

        <!-- 确认 -->
        <view class="keyboard-key keyboard-key--confirm" @click="handleConfirm"> 确定 </view>
      </view>
    </view>

    <!-- 日期选择器 -->
    <AppCalendar
      v-if="calendarVisible"
      :visible="calendarVisible"
      :modelValue="selectedDate"
      class="date-picker-calendar"
      @update:modelValue="handleCalendarModelUpdate"
      @close="calendarVisible = false"
      @update:visible="(val) => calendarVisible = val"
    />
  </view>
</template>

<style lang="scss" scoped>
  // 基础样式
  .amount-input {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: transparent;
    transform: translateY(100%);
    transition: transform var(--transition-normal, 0.3s) ease;
    z-index: var(--z-index-popup, 1000);
    display: flex;
    flex-direction: column;
    pointer-events: none;

    &--visible {
      transform: translateY(0);
      pointer-events: auto;
    }
    }

  // 顶部区域样式
    .amount-input__top-section {
      padding: 0 var(--spacing-md, 16px) var(--spacing-sm, 8px);
      display: flex;
      flex-direction: column;
      gap: var(--spacing-sm, 8px);
      background-color: var(--bg-primary, #fff);
    pointer-events: auto;
    flex-shrink: 0;

    /* #ifdef APP-PLUS-IOS */
    padding-bottom: env(safe-area-inset-bottom, 8px);

    /* #endif */
  }

  // 账本按钮
  .account-book-button {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs, 4px) var(--spacing-sm, 10px);
    background-color: var(--color-primary, #ff6b35);
    color: var(--text-inverse, #fff);
    border-radius: var(--radius-pill, 50px);
    box-shadow: var(--shadow-card, 0 2px 8px rgb(0 0 0 / 10%));
    cursor: pointer;
    align-self: flex-start;
    font-size: var(--font-size-xs, 12px);
    font-weight: 500;

    &__text {
      margin-left: var(--spacing-xs, 4px);
    }
  }

  // 键盘详情区域
  .keyboard-details-area {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs, 4px);
    flex-shrink: 0;
  }

  // 备注区域
  .remark-with-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 8px);
    background-color: var(--bg-secondary, #f5f5f5);
    padding: var(--spacing-xs, 8px) var(--spacing-sm, 10px);
    border-radius: var(--radius-md, 8px);
  }

  // 备注输入框
  .remark-input {
    flex-grow: 1;
    border: none;
    outline: none;
    font-size: var(--font-size-sm, 14px);
    color: var(--text-primary, #333);
    background: transparent;
    padding: 0;
    height: var(--spacing-lg, 24px);
    line-height: var(--spacing-lg, 24px);
  }

  // 备注操作按钮容器
  .remark-action-buttons {
    display: flex;
    gap: var(--spacing-sm, 8px);
    flex-shrink: 0;
  }

  // 备注图标按钮
  .remark-icon-button {
    background: var(--bg-secondary, #f5f5f5);
    border: 1px solid var(--border-color, #e5e5e5);
    width: auto;
    height: var(--spacing-lg, 24px);
    border-radius: var(--radius-sm, 4px);
    padding: 0 var(--spacing-sm, 8px);
    font-size: var(--font-size-xs, 12px);
    color: var(--text-secondary, #666);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-xs, 4px);
    cursor: pointer;
    white-space: nowrap;
    transition: background-color var(--transition-fast, 0.2s) ease;

    &.camera-only {
      width: var(--spacing-lg, 24px);
      padding: 0;
    }

    &__text {
      font-size: var(--font-size-xs, 11px);
      font-weight: 500;
      margin-left: 2px;
    }

    &:active {
      background-color: var(--border-color, #e5e5e5);
    }
  }

  // 金额显示容器
  .amount-display-container {
    padding: var(--spacing-xs, 4px) var(--spacing-xs, 4px) 0;
    text-align: right;
    min-height: 36px;
  }

  // 金额显示
  .amount-display {
    font-size: var(--font-size-xxl, 28px);
    color: var(--text-primary, #333);
    font-weight: 500;
    line-height: 1.2;
    transition: font-size var(--transition-fast, 0.2s) ease;
    word-break: break-all;
  }

  // 键盘背景
    .keyboard-grid-background {
      background-color: var(--bg-secondary, #f5f5f5);
      border-radius: var(--radius-lg, 16px) var(--radius-lg, 16px) 0 0;
      padding: var(--spacing-sm, 10px);
      box-shadow: var(--shadow-overlay, 0 -5px 20px rgb(0 0 0 / 10%));
      border-top: 1px solid var(--border-color, #e5e5e5);
      pointer-events: auto;
      flex-shrink: 0;

    /* #ifdef APP-PLUS-IOS */
    padding-bottom: calc(var(--spacing-sm, 10px) + env(safe-area-inset-bottom, 0));

    /* #endif */
    }

  // 键盘网格
  .keyboard-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: repeat(4, auto);
    gap: var(--spacing-sm, 8px);

    /* #ifdef MP-WEIXIN */
    padding-bottom: 8px; // 小程序底部额外间距

    /* #endif */
  }

  // 键盘按键
  .keyboard-key {
    height: 48px;
    background-color: var(--bg-primary, #fff);
    border-radius: var(--radius-md, 8px);
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: var(--font-size-xl, 20px);
    font-weight: 400;
    color: var(--text-primary, #333);
    user-select: none;
    box-shadow: var(--shadow-card, 0 1px 3px rgb(0 0 0 / 5%));
    cursor: pointer;
    transition:
      background-color var(--transition-fast, 0.1s) ease,
      transform var(--transition-fast, 0.1s) ease;

    &:active {
      background-color: var(--bg-secondary, #f5f5f5);
      transform: scale(0.97);
    }

    // 修饰符：功能键和操作符键
    &--function,
    &--operator {
      background-color: var(--bg-secondary, #f5f5f5);
      font-size: var(--font-size-lg, 18px);
      color: var(--text-secondary, #666);

      &:active {
        background-color: var(--border-color, #e5e5e5);
      }
    }

    // 特定键类型修饰符和网格位置
    &--backspace {
      grid-column: 4 / 5;
      grid-row: 1 / 2;
      font-size: var(--font-size-xl, 20px);
    }

    &--plus {
      grid-column: 4 / 5;
      grid-row: 2 / 3;
      font-size: var(--font-size-xxl, 22px);
    }

    &--minus {
      grid-column: 4 / 5;
      grid-row: 3 / 4;
      font-size: var(--font-size-xxl, 22px);
    }

    &--dot {
      font-size: var(--font-size-xl, 20px);
    }

    &--chat {
      grid-column: 3 / 4;
      grid-row: 4 / 5;
      font-size: var(--font-size-sm, 14px);
      padding: 0 var(--spacing-xs, 4px);
    }

    &--confirm {
      background-color: var(--color-primary, #ff6b35);
      color: var(--text-inverse, #fff);
      grid-column: 4 / 5;
      grid-row: 4 / 5;
      font-size: var(--font-size-md, 16px);
      font-weight: 500;

      &:active {
        filter: brightness(0.9);
      }
    }

    &--number {
      // 继承基础 .keyboard-key 样式
    }
  }

  // 日期选择器样式
  .date-picker-calendar {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 300px;
    z-index: var(--z-index-popup, 2000);
    background-color: var(--bg-primary, #fff);
    border-radius: var(--radius-lg, 16px);
    box-shadow: var(--shadow-overlay, 0 -5px 20px rgb(0 0 0 / 15%));
    max-height: 60vh;
    overflow-y: auto;
  }
</style>
