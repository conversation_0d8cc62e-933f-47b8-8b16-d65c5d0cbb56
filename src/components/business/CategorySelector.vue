<script setup lang="ts">
import type { Category } from '@/stores/category.store';
import AppIcon from '@/components/common/AppIcon.vue';
import { useCategoryStore } from '@/stores/category.store';
import { computed, defineEmits, defineProps, onMounted, ref, watch } from 'vue';

// 如果需要从外部传入分类，可以保留这个类型
interface ExternalCategory {
  id: string | number
  name: string
  icon: string
  bgColor?: string
  type: 'income' | 'expense'
}

// 定义 props
const props = defineProps({
  // 允许从外部传入分类，适用于特殊场景
  categories: {
    type: Array as () => Category[],
    default: () => [],
  },
  title: {
    type: String,
    default: '',
  },
  modelValue: {
    type: [String, Number],
    default: null,
  },
  // 默认类型（income 或 expense）
  type: {
    type: String as () => 'income' | 'expense',
    default: 'expense',
  },
  // 是否显示类型切换
  showTypeSwitch: {
    type: Boolean,
    default: false,
  },
  // 是否使用store (false表示完全使用props传入的categories)
  useStore: {
    type: Boolean,
    default: true,
  },
});

// 定义事件
const emit = defineEmits(['update:modelValue', 'select', 'type-change']);

// 获取分类store
const categoryStore = useCategoryStore();

// 内部状态
const selectedId = ref<string | number | null>(props.modelValue);
const currentType = ref<'income' | 'expense'>(props.type);

// 添加几个辅助方法来控制点击动画
const clickedCategoryId = ref<string | number | null>(null);

// 监听props变化
watch(
  () => props.modelValue,
  (newValue) => {
    selectedId.value = newValue;
  },
);

watch(
  () => props.type,
  (newValue) => {
    if (currentType.value !== newValue) {
      currentType.value = newValue;
      // 如果使用store，则同步到store
      if (props.useStore) {
        categoryStore.setCurrentType(newValue);
      }
    }
  },
);

// 计算属性：显示的分类列表
const displayCategories = computed(() => {
  if (props.useStore) {
    return props.categories.length > 0
      ? props.categories
      : categoryStore.currentType === currentType.value
        ? categoryStore.currentCategories
        : categoryStore.getCategoriesByType(currentType.value);
  }
  else {
    // 如果不使用 store，仅使用 props 传入的分类
    return props.categories.filter(cat => cat.type === currentType.value);
  }
});

// 初始化：同步当前类型到store
onMounted(() => {
  if (props.useStore) {
    categoryStore.setCurrentType(currentType.value);
  }
});

// 处理类型切换
function handleTypeChange(type: 'income' | 'expense'): void {
  currentType.value = type;

  if (props.useStore) {
    categoryStore.setCurrentType(type);
  }

  // 重置选中状态
  selectedId.value = null;
  emit('update:modelValue', null);

  // 触发类型变更事件
  emit('type-change', type);
}

// 选择分类
function selectCategory(category: Category | ExternalCategory): void {
  // 增强防御性编程，确保category存在
  if (!category || !category.id) {
    console.warn('Attempted to select invalid category', category);
    return;
  }

  selectedId.value = category.id;

  if (props.useStore) {
    categoryStore.selectCategory(category.id);
  }

  emit('update:modelValue', category.id);
  emit('select', category);
}

// 获取文本颜色
function getTextColor(isActive: boolean): string {
  return isActive 
    ? 'var(--color-primary, #FF6B35)'
    : 'var(--text-primary, #333333)';
}

// 修改获取图标颜色的方法，确保使用正确的分类颜色
function getCategoryIconColor(category: Category | ExternalCategory, isActive: boolean): string {
  // 如果选中状态，使用主题色
  if (isActive) {
    return 'var(--color-primary, #FF6B35)';
  }
  
  // 如果分类有color属性，优先使用
  if (category && typeof category === 'object' && 'color' in category && category.color) {
    return category.color;
  }
  
  // 使用bgColor作为图标颜色
  if (category && typeof category === 'object' && category.bgColor) {
    // 返回分类本身的背景色
    return category.bgColor;
  }
  
  // 根据分类类型返回默认颜色
  if (category && category.type === 'income') {
    return 'var(--color-success, #4CAF50)';
  } else {
    return 'var(--text-secondary, #666666)';
  }
}

// 处理图标点击动画
function handleCategoryClick(category: Category | ExternalCategory) {
  // 防御性编程，确保category存在
  if (!category || !category.id) return;
  
  // 记录点击的分类ID，用于触发动画
  clickedCategoryId.value = category.id;
  
  // 300ms后重置，与CSS动画时长匹配
  setTimeout(() => {
    clickedCategoryId.value = null;
  }, 300);
  
  // 调用选择分类方法
  selectCategory(category);
}
</script>

<template>
  <div class="category-selector">
    <div v-if="title" class="category-selector__header">
      <div class="category-selector__title">
        {{ title }}
      </div>

      <div v-if="showTypeSwitch" class="category-selector__type-switch">
        <div
          class="type-switch__item"
          :class="{ 'type-switch__item--active': currentType === 'expense' }"
          @click="handleTypeChange('expense')"
        >
          支出
        </div>
        <div
          class="type-switch__item"
          :class="{ 'type-switch__item--active': currentType === 'income' }"
          @click="handleTypeChange('income')"
        >
          收入
        </div>
      </div>
    </div>

    <div class="category-selector__content">
      <div
        v-for="category in displayCategories"
        :key="category?.id || 'unknown'"
        class="category-item"
        :class="{ 
          'category-item--active': selectedId === category?.id,
          'category-item--clicked': clickedCategoryId === category?.id
        }"
        @click="category && handleCategoryClick(category)"
      >
        <div
          class="category-item__icon-wrapper"
          :class="{ 'category-item__icon-wrapper--active': selectedId === category?.id }"
        >
          <AppIcon 
            :icon="category?.icon || 'question'" 
            :color="getCategoryIconColor(category, selectedId === category?.id)"
            size="24"
          />
        </div>
        <div 
          class="category-item__name"
          :style="{ color: getTextColor(selectedId === category?.id) }"
        >
          {{ category?.name || '未知分类' }}
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
  /* stylelint-disable no-descending-specificity */
  .category-selector {
    width: 100%;
    margin-top: 0;
    padding-top: 0;

    &__header {
      padding: 0 0 var(--space-xs, 4px) 0;
      margin-bottom: 0;
      display: flex;
      justify-content: space-between;
      align-items: center;

      &:empty {
        display: none;
        margin-bottom: 0;
        padding: 0;
      }
    }

    &__title {
      font-size: var(--font-size-medium, 16px);
      font-weight: 500;
      color: var(--text-primary, #333333);
    }

    &__type-switch {
      display: flex;
      border-radius: var(--radius-lg, 8px);
      overflow: hidden;
      background-color: var(--color-background-neutral, #f5f5f5);
    }

    &__content {
      display: flex;
      flex-wrap: wrap;
      gap: var(--space-md, 12px);
      margin-top: var(--space-md, 12px);
    }
  }

  .type-switch {
    &__item {
      padding: var(--space-xs, 4px) var(--space-md, 12px);
      font-size: var(--font-size-small, 14px);
    cursor: pointer;
      transition: all 0.3s ease;
      color: var(--text-secondary, #666666);

    &--active {
        background-color: var(--color-primary, #FF6B35);
        color: var(--color-white, white);
      }
    }
  }

  .category-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--space-xxs, 2px);
    width: calc(20% - var(--space-md, 12px));
    min-width: 60px;
    cursor: pointer;
    transition: transform 0.2s ease;

    @media (max-width: 360px) {
      width: calc(25% - var(--space-md, 12px));
    }

    &:active {
      transform: scale(0.95);
    }

    &__icon-wrapper {
      width: 48px;
      height: 48px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: var(--space-xxs, 2px);
      transition: all var(--transition-normal, 0.3s) ease;
      background-color: var(--bg-secondary, #F5F5F5);
      position: relative;
      
      /* 添加选中特效的边框 */
      &::after {
        content: '';
        position: absolute;
        top: -3px;
        left: -3px;
        right: -3px;
        bottom: -3px;
        border-radius: 50%;
        border: 2px solid transparent;
        opacity: 0;
        transition: all var(--transition-normal, 0.3s) ease;
      }
    }

    &__icon {
      font-size: 24px;
      transition: all var(--transition-normal, 0.3s) ease;
    }

    &__name {
      font-size: var(--font-size-small, 14px);
      text-align: center;
      color: var(--text-primary, #333333);
      transition: color var(--transition-normal, 0.3s) ease;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 100%;
    }

    &--active {
      .category-item__icon-wrapper {
        background-color: var(--color-primary-light, #ffeee7);
        transform: scale(1.08); /* 添加放大效果 */
        
        /* 激活边框特效 */
        &::after {
          border-color: var(--color-primary, #FF6B35);
          opacity: 1;
        }
      }

      .category-item__icon,
      .category-item__name {
        color: var(--color-primary, #FF6B35);
      }
    }

    &--clicked {
      .category-item__icon-wrapper {
        /* 点击时的动画 */
        animation: pulse-effect var(--transition-fast, 0.2s) ease-out;
        
        /* 添加波纹效果 */
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: var(--color-primary, #FF6B35);
          border-radius: 50%;
          opacity: 0.2;
          transform: scale(0);
          animation: ripple-effect var(--transition-normal, 0.3s) ease-out;
      }
    }
  }
  }

  /* 添加动画关键帧 */
  @keyframes pulse-effect {
    0% {
      transform: scale(1);
    }
    50% {
      transform: scale(0.92);
    }
    100% {
      transform: scale(1);
    }
  }

  @keyframes ripple-effect {
    0% {
      transform: scale(0.6);
      opacity: 0.2;
    }
    100% {
      transform: scale(1.5);
      opacity: 0;
    }
  }
</style>
