<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts/core';
import { PieChart, PieSeriesOption } from 'echarts/charts';
import {
  TitleComponent, 
  TitleComponentOption,
  TooltipComponent, 
  TooltipComponentOption,
  LegendComponent,
  LegendComponentOption,
} from 'echarts/components';
import { LabelLayout } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  LegendComponent,
  PieChart,
  CanvasRenderer,
  LabelLayout,
]);

// 定义组合类型
type ECOption = echarts.ComposeOption<
  PieSeriesOption | 
  TitleComponentOption | 
  TooltipComponentOption |
  LegendComponentOption
>;

// 定义饼图数据结构接口
interface PieChartDataItem {
  name: string;
  value: number;
  itemStyle?: {
    color?: string;
  };
}

interface Props {
  // 修改：使用统一的chartData接口
  chartData: {
    series: [{
      name: string;
  data: PieChartDataItem[];
    }];
    radius?: string | string[];
    legendPosition?: 'bottom' | 'right';
  };
  height?: string;
  loading?: boolean;
  showLegend?: boolean;
  chartType?: 'normal' | 'rose' | 'ring';
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px',
  loading: false,
  showLegend: true,
  chartType: 'normal',
});

const chartContainer = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 初始化ECharts实例
const initChart = () => {
  if (!chartContainer.value) {
    console.error('图表容器元素不存在');
    return;
  }
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  try {
    // 确保DOM已经渲染完成
    setTimeout(() => {
  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);
  
  // 监听窗口大小变化，调整图表大小
  const resizeHandler = () => {
    chartInstance?.resize();
  };
  window.addEventListener('resize', resizeHandler);
  
  // 确保在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler);
    chartInstance?.dispose();
    chartInstance = null;
  });
  
  // 更新图表
  updateChart();
    }, 100); // 增加延迟确保DOM已渲染
  } catch (error) {
    console.error('初始化图表失败:', error);
  }
};

// 更新图表数据和配置
const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新');
    return;
  }
  
  try {
  // 设置加载状态
  if (props.loading) {
    chartInstance.showLoading({
      text: '数据加载中...',
      textColor: 'var(--text-primary, #333)',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      fontSize: 14,
    });
    return;
  } else {
    chartInstance.hideLoading();
  }

    if (!props.chartData || !props.chartData.series || !props.chartData.series[0]) {
      console.warn('饼图数据不完整');
      return;
    }

    // 提取数据
    const { series, radius = '60%', legendPosition = 'bottom' } = props.chartData;
    const data = series[0].data;

  // 确定饼图类型配置
    let chartRadius = radius;
  let roseType = undefined;
  
  if (props.chartType === 'ring') {
    // 环形图
      chartRadius = Array.isArray(chartRadius) ? chartRadius : ['50%', '70%'];
  } else if (props.chartType === 'rose') {
    // 南丁格尔玫瑰图
    roseType = 'radius';
  }
  
  // 设置图表选项
  const option: ECOption = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c} ({d}%)',
      backgroundColor: 'var(--bg-card, #fff)',
      borderColor: 'var(--border-color, #eee)',
      textStyle: {
        color: 'var(--text-primary, #333)',
      },
    },
    legend: {
        show: props.showLegend,
        orient: legendPosition === 'right' ? 'vertical' : 'horizontal',
        [legendPosition]: 0,
        left: legendPosition === 'right' ? 'auto' : 'center',
      textStyle: {
        color: 'var(--text-primary, #333)',
      },
    },
    series: [
      {
          name: series[0].name,
        type: 'pie',
          radius: chartRadius,
        roseType,
        center: ['50%', '50%'],
          data,
        label: {
            show: true,
          formatter: '{b}: {d}%',
          color: 'var(--text-primary, #333)',
        },
        labelLine: {
            show: true,
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)',
          },
        },
      },
    ],
  };
  
  // 应用选项
  chartInstance.setOption(option, true);
    console.log('饼图更新完成');
  } catch (error) {
    console.error('更新图表出错:', error);
  }
};

// 监听数据变化并更新图表
watch(() => props.chartData, updateChart, { deep: true });
watch(() => props.loading, updateChart);
watch(() => props.chartType, updateChart);
watch(() => props.showLegend, updateChart);

// 组件挂载后初始化图表
onMounted(() => {
  console.log('饼图组件已挂载，开始初始化');
  initChart();
});

// 在组件销毁时清理echarts实例
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 对外暴露方法
defineExpose({
  initChart,
  chartInstance
});
</script>

<template>
  <div class="pie-chart-container">
    <div ref="chartContainer" class="chart-canvas" :style="{ height }"></div>
    <div v-if="!data.length && !loading" class="no-data">
      <div class="no-data-text">暂无数据</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.pie-chart-container {
  width: 100%;
  position: relative;
  border-radius: var(--border-radius-card, 12px);
  background-color: var(--bg-card, #fff);
  box-shadow: var(--shadow-card, 0 1px 3px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.chart-canvas {
  width: 100%;
  min-height: 200px;
}

.no-data {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-card, #fff);
}

.no-data-text {
  color: var(--text-hint, #999);
  font-size: 14px;
}
</style> 