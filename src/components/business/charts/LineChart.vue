<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts/core';
import { LineChart, LineSeriesOption } from 'echarts/charts';
import {
  TitleComponent, 
  TitleComponentOption,
  TooltipComponent, 
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
} from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
]);

// 定义组合类型
type ECOption = echarts.ComposeOption<
  LineSeriesOption | 
  TitleComponentOption | 
  TooltipComponentOption |
  GridComponentOption |
  LegendComponentOption
>;

// 定义折线图数据结构接口
interface LineChartDataItem {
  name: string;
  value: number;
}

interface LineChartSeries {
  name: string;
  data: LineChartDataItem[];
  color?: string;
}

interface Props {
  chartTitle?: string;
  xAxisData: string[];
  series: LineChartSeries[];
  height?: string;
  loading?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  chartTitle: '',
  height: '300px',
  loading: false,
});

const chartContainer = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 初始化ECharts实例
const initChart = () => {
  if (!chartContainer.value) return;
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);
  
  // 监听窗口大小变化，调整图表大小
  const resizeHandler = () => {
    chartInstance?.resize();
  };
  window.addEventListener('resize', resizeHandler);
  
  // 确保在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler);
    chartInstance?.dispose();
    chartInstance = null;
  });
  
  // 更新图表
  updateChart();
};

// 更新图表数据和配置
const updateChart = () => {
  if (!chartInstance) return;
  
  // 设置加载状态
  if (props.loading) {
    chartInstance.showLoading({
      text: '数据加载中...',
      textColor: 'var(--text-primary, #333)',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      fontSize: 14,
    });
    return;
  } else {
    chartInstance.hideLoading();
  }

  // 准备数据
  const series = props.series.map(serie => ({
      name: serie.name,
      type: 'line',
      data: serie.data.map(item => item.value),
      color: serie.color,
      smooth: true,
      areaStyle: {
        opacity: 0.1,
      },
    }));
  
  // 设置图表选项
  const option: ECOption = {
    title: {
      text: props.chartTitle,
      left: 'center',
      textStyle: {
        color: 'var(--text-primary, #333)',
        fontWeight: 'normal',
        fontSize: 16,
      },
    },
    tooltip: {
      trigger: 'axis',
      backgroundColor: 'var(--bg-card, #fff)',
      borderColor: 'var(--border-color, #eee)',
      textStyle: {
        color: 'var(--text-primary, #333)',
      },
    },
    legend: {
      data: props.series.map(serie => serie.name),
      bottom: 0,
      textStyle: {
        color: 'var(--text-primary, #333)',
      },
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '13%',
      top: props.chartTitle ? '15%' : '5%',
      containLabel: true,
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: props.xAxisData,
      axisLine: {
        lineStyle: {
          color: 'var(--border-color, #eee)',
        },
      },
      axisLabel: {
        color: 'var(--text-hint, #999)',
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: 'var(--text-hint, #999)',
      },
      splitLine: {
        lineStyle: {
          color: 'var(--border-color, #eee)',
          type: 'dashed',
        },
      },
    },
    series,
  };
  
  // 应用选项
  chartInstance.setOption(option, true);
};

// 监听数据变化并更新图表
watch(() => props.series, updateChart, { deep: true });
watch(() => props.xAxisData, updateChart, { deep: true });
watch(() => props.loading, updateChart);

// 组件挂载后初始化图表
onMounted(() => {
  initChart();
});

// 在组件销毁时清理echarts实例
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});
</script>

<template>
  <div class="line-chart-container">
    <div ref="chartContainer" class="chart-canvas" :style="{ height }"></div>
    <div v-if="!series.length && !loading" class="no-data">
      <div class="no-data-text">暂无数据</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.line-chart-container {
  width: 100%;
  position: relative;
  border-radius: var(--border-radius-card, 12px);
  background-color: var(--bg-card, #fff);
  box-shadow: var(--shadow-card, 0 1px 3px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.chart-canvas {
  width: 100%;
  min-height: 200px;
}

.no-data {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-card, #fff);
}

.no-data-text {
  color: var(--text-hint, #999);
  font-size: 14px;
}
</style> 