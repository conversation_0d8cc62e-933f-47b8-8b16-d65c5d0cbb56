<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue';
import * as echarts from 'echarts/core';
import { BarChart, BarSeriesOption } from 'echarts/charts';
import {
  TitleComponent, 
  TitleComponentOption,
  TooltipComponent, 
  TooltipComponentOption,
  GridComponent,
  GridComponentOption,
  LegendComponent,
  LegendComponentOption,
} from 'echarts/components';
import { UniversalTransition } from 'echarts/features';
import { CanvasRenderer } from 'echarts/renderers';

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  BarChart,
  CanvasRenderer,
  UniversalTransition,
]);

// 定义组合类型
type ECOption = echarts.ComposeOption<
  BarSeriesOption | 
  TitleComponentOption | 
  TooltipComponentOption |
  GridComponentOption |
  LegendComponentOption
>;

// 定义柱状图数据结构接口
interface BarChartDataItem {
  name?: string;
  value: number;
  itemStyle?: {
    color?: string;
  };
}

interface BarChartSeries {
  name: string;
  data: BarChartDataItem[];
  color?: string;
}

interface Props {
  // 修改：接收统一的chartData属性
  chartData: {
  xAxisData: string[];
  series: BarChartSeries[];
  };
  height?: string;
  loading?: boolean;
  horizontal?: boolean;
}

const props = withDefaults(defineProps<Props>(), {
  height: '300px',
  loading: false,
  horizontal: false,
});

const chartContainer = ref<HTMLElement | null>(null);
let chartInstance: echarts.ECharts | null = null;

// 初始化ECharts实例
const initChart = () => {
  if (!chartContainer.value) {
    console.error('图表容器元素不存在');
    return;
  }
  
  // 销毁已存在的实例
  if (chartInstance) {
    chartInstance.dispose();
  }
  
  try {
    // 确保DOM已经渲染完成
    setTimeout(() => {
  // 创建新实例
  chartInstance = echarts.init(chartContainer.value);
  
  // 监听窗口大小变化，调整图表大小
  const resizeHandler = () => {
    chartInstance?.resize();
  };
  window.addEventListener('resize', resizeHandler);
  
  // 确保在组件卸载时移除事件监听
  onUnmounted(() => {
    window.removeEventListener('resize', resizeHandler);
    chartInstance?.dispose();
    chartInstance = null;
  });
  
  // 更新图表
  updateChart();
    }, 100); // 增加延迟确保DOM已渲染
  } catch (error) {
    console.error('初始化图表失败:', error);
  }
};

// 更新图表数据和配置
const updateChart = () => {
  if (!chartInstance) {
    console.warn('图表实例不存在，无法更新');
    return;
  }
  
  try {
  // 设置加载状态
  if (props.loading) {
    chartInstance.showLoading({
      text: '数据加载中...',
      textColor: 'var(--text-primary, #333)',
      maskColor: 'rgba(255, 255, 255, 0.8)',
      fontSize: 14,
    });
    return;
  } else {
    chartInstance.hideLoading();
  }

    // 提取数据
    const { xAxisData, series } = props.chartData;

  // 准备数据
    const chartSeries = series.map(serie => ({
      name: serie.name,
      type: 'bar',
      data: serie.data.map(item => ({
        value: item.value,
        // 使用itemStyle或fallback到serie.color
        itemStyle: item.itemStyle || (serie.color ? { color: serie.color } : undefined)
      })),
      // 对水平柱状图特别调整
      ...(props.horizontal ? {
        // 增加水平柱状图的特有配置
        label: {
          show: true,
          position: 'right',
          formatter: '{c}',
          color: 'var(--text-primary, #333)',
        },
      } : {
        // 垂直柱状图特有配置
        label: {
          show: false,
        },
      }),
    }));
  
  // 设置图表选项
  const option: ECOption = {
    title: {
      left: 'center',
      textStyle: {
        color: 'var(--text-primary, #333)',
        fontWeight: 'normal',
        fontSize: 16,
      },
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow',
      },
      backgroundColor: 'var(--bg-card, #fff)',
      borderColor: 'var(--border-color, #eee)',
      textStyle: {
        color: 'var(--text-primary, #333)',
      },
    },
    legend: {
        data: series.map(serie => serie.name),
      bottom: 0,
      textStyle: {
        color: 'var(--text-primary, #333)',
      },
    },
    grid: {
      left: props.horizontal ? '15%' : '3%',
      right: props.horizontal ? '10%' : '4%',
      bottom: '13%',
        top: '5%',
      containLabel: true,
    },
    [props.horizontal ? 'yAxis' : 'xAxis']: {
      type: 'category',
        data: xAxisData,
      axisLine: {
        lineStyle: {
          color: 'var(--border-color, #eee)',
        },
      },
      axisLabel: {
        color: 'var(--text-hint, #999)',
        rotate: props.horizontal ? 0 : 30, // 水平显示时不旋转，垂直显示时旋转30度
        ...(props.horizontal ? {
          width: 70,
          overflow: 'truncate',
        } : {}),
      },
    },
    [props.horizontal ? 'xAxis' : 'yAxis']: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisLabel: {
        color: 'var(--text-hint, #999)',
      },
      splitLine: {
        lineStyle: {
          color: 'var(--border-color, #eee)',
          type: 'dashed',
        },
      },
    },
      series: chartSeries,
  };
  
  // 应用选项
  chartInstance.setOption(option, true);
    console.log('柱状图更新完成');
  } catch (error) {
    console.error('更新图表出错:', error);
  }
};

// 监听数据变化并更新图表
watch(() => props.chartData, updateChart, { deep: true });
watch(() => props.loading, updateChart);
watch(() => props.horizontal, updateChart);

// 组件挂载后初始化图表
onMounted(() => {
  console.log('柱状图组件已挂载，开始初始化');
  initChart();
});

// 在组件销毁时清理echarts实例
onUnmounted(() => {
  if (chartInstance) {
    chartInstance.dispose();
    chartInstance = null;
  }
});

// 对外暴露方法
defineExpose({
  initChart,
  chartInstance
});
</script>

<template>
  <div class="bar-chart-container">
    <div ref="chartContainer" class="chart-canvas" :style="{ height }"></div>
    <div v-if="!series.length && !loading" class="no-data">
      <div class="no-data-text">暂无数据</div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.bar-chart-container {
  width: 100%;
  position: relative;
  border-radius: var(--border-radius-card, 12px);
  background-color: var(--bg-card, #fff);
  box-shadow: var(--shadow-card, 0 1px 3px rgba(0, 0, 0, 0.1));
  overflow: hidden;
}

.chart-canvas {
  width: 100%;
  min-height: 200px;
}

.no-data {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-card, #fff);
}

.no-data-text {
  color: var(--text-hint, #999);
  font-size: 14px;
}
</style> 