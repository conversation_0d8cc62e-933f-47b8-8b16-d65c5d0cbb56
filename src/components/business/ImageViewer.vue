<template>
  <view v-if="visible && isValidUrl" class="image-viewer" @click="handleClose">
    <image :src="imageUrl" mode="aspectFit" class="image-viewer__image" />
  </view>
</template>

<script setup lang="ts">
import { computed } from 'vue';

// 图片查看器组件定义
interface Props {
  /** 是否显示图片查看器 */
  visible: boolean;
  /** 要显示的图片URL */
  imageUrl: string;
}

const props = defineProps<Props>();
const emit = defineEmits(['close']);

// 检查图片URL是否有效
const isValidUrl = computed(() => {
  return props.imageUrl && typeof props.imageUrl === 'string' && props.imageUrl.trim() !== '';
});

// 处理关闭按钮点击事件
const handleClose = () => {
  // 触发关闭事件
  emit('close');
};
</script>

<style lang="scss" scoped>
// 图片查看器样式
.image-viewer {
  // 定位为全屏覆盖
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bg-dialog-overlay, rgba(0, 0, 0, 0.9));
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  
  // 多端适配
  /* #ifdef H5 */
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
  /* #endif */
  
  /* #ifdef APP-PLUS */
  padding-bottom: var(--safe-area-bottom, 0);
  /* #endif */
  
  // 图片样式
  &__image {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}
</style> 