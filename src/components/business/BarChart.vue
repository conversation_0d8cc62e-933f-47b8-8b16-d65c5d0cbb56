<script setup lang="ts">
import { ref, watch, onMounted, computed } from 'vue';

const props = defineProps({
  chartData: { type: Object, required: true },
  height: { type: String, default: '200px' },
  loading: { type: Boolean, default: false }
});

// uCharts 组件注册
import uCharts from '@qiun/ucharts/u-charts.js';

const chartType = 'column'; // 柱状图
const canvasId = 'bar-chart-' + Math.random().toString(36).slice(-8);
const canvasDOM = ref(null);
const chartInstance = ref(null);
const isEmpty = computed(() => {
  return props.chartData?.xAxisData?.[0] === '无数据' || 
    (props.chartData?.series?.every(s => s.data?.every(d => d.value === 0)));
});

// 适配props.chartData为uCharts格式
const uChartsOpts = computed(() => {
  const opts = {
    type: chartType,
    categories: props.chartData?.xAxisData || [],
    series: [],
    animation: true,
    background: '#FFFFFF',
    padding: [15, 15, 15, 15],
    enableScroll: false,
    legend: {
      show: true,
      position: 'top',
      float: 'center',
      padding: 5,
      margin: 5,
      backgroundColor: 'rgba(0,0,0,0)',
      borderColor: 'rgba(0,0,0,0)',
      itemGap: 10,
      fontSize: 12,
      lineHeight: 10,
      fontColor: '#666666',
      format: '{name}'
    },
    xAxis: {
      disableGrid: true,
      gridType: 'dash',
      dashLength: 2,
      fontColor: '#666666',
      boundaryGap: 'center'
    },
    yAxis: {
      gridType: 'dash',
      dashLength: 2,
      data: [{ 
        min: 0,
        fontColor: '#666666',
        format: (val) => val.toFixed(0)
      }]
    },
    extra: {
      column: {
        type: 'group',
        width: 30,
        meter: {
          border: 1,
          fillColor: '#E5FBF8'
        }
      }
    }
  };

  // 转换系列数据
  if (props.chartData?.series?.length) {
    opts.series = props.chartData.series.map(series => ({
      name: series.name,
      data: series.data.map(item => item.value),
      color: series.data[0]?.itemStyle?.color || '#FF6B35'
    }));
  }

  return opts;
});

// 监听数据变化
watch(() => props.chartData, () => {
  if (!isEmpty.value && chartInstance.value) {
    updateChart();
  }
}, { deep: true });

// 初始化图表 - 修复版本
function initChart() {
  try {
    console.log('开始初始化BarChart图表...');
    // 统一使用createCanvasContext获取上下文
    const ctx = uni.createCanvasContext(canvasId);
    
    if (!ctx) {
      console.error('BarChart - 获取Canvas上下文失败');
      return;
    }
    
    console.log('BarChart - 成功获取Canvas上下文');
    
    // 初始化图表
    chartInstance.value = new uCharts({
      context: ctx,
      width: 375,
      height: 200,
      canvasId: canvasId,
      categories: uChartsOpts.value.categories,
      series: uChartsOpts.value.series,
      animation: uChartsOpts.value.animation,
      background: uChartsOpts.value.background,
      padding: uChartsOpts.value.padding,
      xAxis: uChartsOpts.value.xAxis, 
      yAxis: uChartsOpts.value.yAxis,
      legend: uChartsOpts.value.legend,
      extra: uChartsOpts.value.extra
    });
    
    // 重要：必须延迟执行draw()，并且必须调用draw()方法
    setTimeout(() => {
      ctx.draw(true);
      console.log('BarChart - draw完成');
    }, 200);
    
    // 绘制图表
    chartInstance.value.addEventListener('renderComplete', () => {
      console.log('BarChart渲染完成');
    });
    chartInstance.value.addEventListener('scrollLeft', () => {
      console.log('BarChart向左滚动');
    });
    chartInstance.value.addEventListener('scrollRight', () => {
      console.log('BarChart向右滚动');
    });
  } catch (err) {
    console.error('BarChart初始化出错:', err);
  }
}

// 更新图表
function updateChart() {
  try {
    if (chartInstance.value) {
      chartInstance.value.updateData({
        categories: uChartsOpts.value.categories,
        series: uChartsOpts.value.series
      });
      
      // 确保draw()被调用
      setTimeout(() => {
        const ctx = uni.createCanvasContext(canvasId);
        if (ctx) ctx.draw(true);
      }, 100);
    } else {
      initChart();
    }
  } catch (err) {
    console.error('BarChart - 更新图表失败:', err);
  }
}

// 组件挂载时初始化图表
onMounted(() => {
  console.log('BarChart组件已挂载');
  if (!isEmpty.value) {
    // 延长延时时间，确保DOM已完全渲染
    setTimeout(() => {
      initChart();
    }, 500);
  }
});
</script>

<template>
  <view class="bar-chart" :style="{ height: props.height }">
    <view v-if="props.loading" class="chart-loading">加载中...</view>
    <view v-else-if="isEmpty" class="chart-empty">暂无数据</view>
    <canvas 
      v-else
      :id="canvasId" 
      :canvas-id="canvasId"
      class="charts-canvas" 
      :style="{ width: '100%', height: props.height }"
    ></canvas>
  </view>
</template>

<style lang="scss" scoped>
.bar-chart {
  position: relative;
  width: 100%;
  
  .charts-canvas {
    width: 100%;
    height: 100%;
  }
  
  .chart-loading,
  .chart-empty {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    color: var(--text-hint, #999999);
    font-size: var(--font-size-md, 14px);
    background-color: var(--bg-secondary, #f6f6f6);
    border-radius: var(--radius-md, 8px);
  }
}
</style> 