<template>
  <view class="qiun-data-charts-container" :style="{ width: width, height: height }">
    <canvas
      :id="canvasId"
      :canvas-id="canvasId"
      class="charts-canvas"
      style="width: 100%; height: 100%;"
      @error="onCanvasError"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, getCurrentInstance, nextTick } from 'vue';
import uCharts from '@/components/u-charts/u-charts.js';

// 定义组件属性
const props = defineProps({
    canvasId: {
      type: String,
      required: true
    },
  type: {
    type: String,
      required: true
    },
  chartData: {
      type: Object,
      default: () => ({})
    },
  opts: {
    type: Object,
    default: () => ({})
  },
  width: {
    type: String,
    default: '100%'
  },
  height: {
    type: String,
    default: '300px'
  },
  useStaticData: {
    type: Boolean,
    default: false
  }
});

// 定义事件
const emit = defineEmits(['complete', 'error']);
      
// 图表实例
let chartInstance = null;
let canvasNode = null;
let ctx = null;

// 日志函数
function log(message) {
  console.log(`[uCharts-${props.canvasId}] ${message}`);
        }

function error(message) {
  console.error(`[uCharts-${props.canvasId}] ${message}`);
  emit('error', { message });
}

// Canvas错误处理
function onCanvasError(e) {
  error(`Canvas error: ${e.detail.errMsg}`);
}

// 获取静态测试数据
function getTestData(type) {
  if (type === 'column') {
    return {
      categories: ['A', 'B', 'C', 'D', 'E'],
      series: [
        {
          name: '系列1',
          data: [35, 20, 30, 37, 42],
          color: '#1890FF'
        },
        {
          name: '系列2',
          data: [28, 15, 25, 32, 38],
          color: '#91CB74'
        }
      ]
    };
  } else if (type === 'ring') {
    return {
      series: [
        {
          data: [
            { name: '分类1', value: 50, color: '#1890FF' },
            { name: '分类2', value: 30, color: '#91CB74' },
            { name: '分类3', value: 20, color: '#FAC858' }
          ]
        }
      ]
    };
  } else {
    return {
      categories: ['A', 'B', 'C'],
      series: [
        {
          name: '测试',
          data: [15, 20, 25]
        }
      ]
    };
          }
        }
        
// 获取简化配置
function getBaseConfig(type) {
  const config = {
    type: type,
    padding: [15, 15, 15, 15],
    background: '#FFFFFF',
    enableScroll: false,
    fontSize: 12,
    fontColor: '#666666',
    dataLabel: true
  };
  
  if (type === 'column') {
    config.xAxis = {
      disableGrid: true
    };
    config.yAxis = {
      data: [{ min: 0 }],
      gridType: 'dash'
    };
    config.extra = {
      column: {
        width: 20
      }
    };
  } else if (type === 'ring') {
    config.title = {
      name: '70%',
      fontSize: 20
    };
    config.subtitle = {
      name: '占比',
      fontSize: 14
    };
    config.extra = {
      ring: {
        ringWidth: 30,
        labelWidth: 15,
        centerColor: '#FFFFFF'
      }
    };
  }
  
  return config;
              }
              
// 准备图表配置和数据
function prepareChartOptions() {
  // 使用静态数据或传入的数据
  const chartData = props.useStaticData ? getTestData(props.type) : props.chartData;
  
                // 基础配置
  const baseConfig = getBaseConfig(props.type);
                
  // 合并用户配置
  const chartOptions = {
                  ...baseConfig,
    ...props.opts,
    // 强制设置必要属性
    type: props.type,
    context: ctx,
    canvas2d: false,
    canvasId: props.canvasId,
    pixelRatio: 1
                };
                
  // 根据图表类型设置数据
  if (props.type === 'column' || props.type === 'line' || props.type === 'area') {
    chartOptions.categories = chartData.categories || [];
    chartOptions.series = chartData.series || [];
  } else if (props.type === 'ring' || props.type === 'pie') {
    chartOptions.series = chartData.series || [];
  }
  
  return chartOptions;
}

// 初始化图表
async function initChart() {
  log('Initializing chart...');
  
  try {
    // 等待下一个渲染周期
    await nextTick();
    
    // 获取Canvas上下文
    try {
      // 尝试获取canvas节点
      const query = uni.createSelectorQuery();
      const instance = getCurrentInstance();
      
      if (instance && instance.proxy) {
        query.in(instance.proxy);
                }
      
      canvasNode = await new Promise((resolve) => {
        query.select(`#${props.canvasId}`)
          .boundingClientRect(data => {
            if (!data) {
              error('Canvas element not found.');
              resolve(null);
              return;
            }
            resolve(data);
          }).exec();
      });
      
      if (!canvasNode) {
        error('Failed to get canvas node');
        return;
      }
      
      log(`Canvas size: ${canvasNode.width}x${canvasNode.height}`);
        
      // 创建Canvas上下文
      const instance2 = getCurrentInstance();
      ctx = uni.createCanvasContext(props.canvasId, instance2 && instance2.proxy);
      
      if (!ctx) {
        error('Failed to create canvas context');
        return;
      }
      
      log('Canvas context created');
          
      // 准备配置
      const options = prepareChartOptions();
      
      // 添加尺寸
      options.width = canvasNode.width || parseInt(props.width);
      options.height = canvasNode.height || parseInt(props.height);
      
      // 特殊处理环形图和饼图，确保是圆形的
      if (props.type === 'ring' || props.type === 'pie') {
        // 使用最小的一边确保饼图是圆形的
        const minSize = Math.min(options.width, options.height);
        options.width = minSize;
        options.height = minSize;
      }
      
      log('Creating uCharts instance with config: ' + JSON.stringify({
        type: options.type,
        width: options.width,
        height: options.height,
        canvasId: options.canvasId
      }));
      
      // 创建图表实例
      chartInstance = new uCharts(options);
      
      log('uCharts instance created. Rendering...');
      
      // 强制绘制图表
      if (chartInstance && typeof chartInstance.draw === 'function') {
        chartInstance.draw(true); // 强制绘制
      }
      
      // 触发完成事件
      emit('complete', {
        canvasId: props.canvasId,
        type: props.type,
        width: canvasNode.width,
        height: canvasNode.height
      });
      
    } catch (e) {
      error(`Failed to initialize chart: ${e.message || e}`);
    }
  } catch (e) {
    error(`Error in initChart: ${e.message || e}`);
  }
}

// 销毁图表
function disposeChart() {
  if (chartInstance) {
    log('Disposing chart instance');
    // v2.x没有dispose方法，直接置空
    chartInstance = null;
        }
}

// 监听属性变化
watch(
  [() => props.chartData, () => props.opts],
  () => {
    log('Chart data or options changed');
    // 先销毁旧图表
    disposeChart();
    // 延迟重新初始化
    setTimeout(() => {
      initChart();
    }, 200);
  },
  { deep: true }
);

// 组件挂载
onMounted(() => {
  log('Component mounted');
  // 延迟初始化，确保DOM已渲染
  setTimeout(() => {
    initChart();
  }, 300);
});

// 组件销毁
onUnmounted(() => {
  log('Component unmounted');
  disposeChart();
});
</script>

<style lang="scss" scoped>
.qiun-data-charts-container {
  position: relative;
  box-sizing: border-box;
}

.charts-canvas {
  width: 100%;
  height: 100%;
}
</style> 