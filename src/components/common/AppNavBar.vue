<script setup lang="ts">
// 引入平台适配器，用于获取不同端的导航栏高度
import { getPlatformStyle } from '@/utils/platform';
import { computed } from 'vue';
// 不需要导入 AppIcon，因为已经通过 easycom 全局注册

// 定义Props，包含标题、返回按钮、主题、透明、样式等
interface Props {
  /** 页面标题 */
  title?: string
  /** 是否显示返回按钮 */
  showBack?: boolean
  /** 导航栏主题: default, primary */
  theme?: 'default' | 'primary'
  /** 是否透明背景 */
  transparent?: boolean
  /** 自定义样式 */
  customStyle?: Record<string, string>
}

// 设置props默认值，保证组件易用性
const props = withDefaults(defineProps<Props>(), {
  title: '',
  showBack: true,
  theme: 'default',
  transparent: false,
  customStyle: () => ({}),
});

// 使用平台适配器获取平台特定高度（如iOS/安卓/H5/小程序）
const navHeight = computed(() => getPlatformStyle('nav-height'));
const rawStatusBarHeight = getPlatformStyle('status-bar-height');
// 兜底处理，h5 或 0 时 fallback 到 26px，保证导航栏不会塌陷
const statusBarHeight = computed(() => {
  if (!rawStatusBarHeight || rawStatusBarHeight === '0' || rawStatusBarHeight === '0px') {
    return '26px';
  }
  return rawStatusBarHeight;
});

// 返回上一页的处理方法，若失败则跳转首页
function handleBack() {
  // 调用uni-app的返回方法，delta=1表示返回上一页
  uni.navigateBack({
    delta: 1,
    fail: () => {
      // 如果没有上一页（如直接打开的页面），则跳转到首页
      uni.switchTab({
        url: '/pages/home/<USER>',
      });
    },
  });
}
</script>

<template>
  <view
    class="app-nav-bar"
    :style="{
      height: navHeight,
    }"
    :class="[`app-nav-bar--${theme}`, { 'app-nav-bar--transparent': transparent }]"
  >
    <view class="app-nav-bar__content">
      <view class="app-nav-bar__left">
        <slot name="left">
          <!-- 返回按钮，使用AppIcon统一图标组件，icon大小和颜色通过props和样式控制 -->
          <AppIcon
            v-if="showBack"
            icon="arrow-left"
            size="sm"
            :custom-style="{
              width: '16px',
              height: '16px',
              display: 'block'
            }"
            class="app-nav-bar__back-icon"
            :color="
              theme === 'primary' ? 'var(--text-inverse, #FFFFFF)' : 'var(--text-primary, #333333)'
            "
            @click="handleBack"
          />
        </slot>
      </view>
      <view class="app-nav-bar__title">
        <slot name="title">
          {{ title }}
        </slot>
      </view>
      <view class="app-nav-bar__right">
        <slot name="right" ></slot>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-nav-bar {
  position: relative;
  width: 100%;
  box-sizing: border-box;
  z-index: 100;
  /* 只设置高度，不加paddingTop */

  &__content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 100%;
    /* 内容区在整个导航栏高度内垂直居中 */
  }

    &__left,
    &__right {
      flex: 0 0 80px;
      display: flex;
      align-items: center;
      height: 100%;
    }

    &__left {
    padding-left: var(--spacing-md, 16px);
      justify-content: flex-start;
    }

    &__right {
      justify-content: flex-end;
    padding-right: var(--spacing-md, 16px);
    }

    &__title {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      min-width: 120px;
      text-align: center;
      font-size: var(--font-size-lg, 18px);
      font-weight: 600;
      color: var(--text-primary, #333);
      padding: 0 var(--spacing-sm, 8px);
      white-space: nowrap;
      z-index: 2;
      &::after {
        content: '';
        display: block;
        width: 40px;
        height: 3px;
        background: var(--color-primary, #ff6b35);
        position: absolute;
        left: 50%;
      bottom: -6px;
        transform: translateX(-50%);
        border-radius: 2px;
        z-index: 1;
      }
    }

    &__back-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    /*
      确保图标尺寸统一
    */
    &:deep(.u-icon__icon) {
      width: 16px;
      height: 16px;
      font-size: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    }

    /* 主题样式 */
    &--default {
      background-color: var(--bg-primary, #fff);
      color: var(--text-primary, #333);
      border-bottom: 1px solid var(--border-color, #e5e5e5);
      .app-nav-bar__title {
        color: var(--text-primary, #333);
      }
      .app-nav-bar__back-icon {
        color: var(--text-primary, #333);
      }
    }
    &--primary {
      background-color: var(--color-primary, #ff6b35);
      color: var(--text-inverse, #fff);
      border-bottom: none;
      .app-nav-bar__title {
        color: var(--text-inverse, #fff);
        &::after {
        background: var(--text-inverse, #fff);
      }
    }
      .app-nav-bar__back-icon {
        color: var(--text-inverse, #fff);
      }
    }
    &--transparent {
      background-color: transparent;
      border-bottom: none;
      &.app-nav-bar--default {
        .app-nav-bar__title {
          color: var(--text-primary, #333);
        }
      }
      &.app-nav-bar--primary {
        .app-nav-bar__title {
          color: var(--text-inverse, #fff);
        }
      }
    }
    /* 小程序胶囊按钮适配 */
    /* #ifdef MP-WEIXIN */
  padding-right: 90px;
    .app-nav-bar__title {
    margin-right: 30px;
    }
    /* #endif */
  }
</style>
