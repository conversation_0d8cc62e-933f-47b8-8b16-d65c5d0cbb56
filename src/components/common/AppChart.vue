<template>
  <div
    class="chart-container"
    :style="{
      width: typeof width === 'number' ? `${width}px` : width,
      height: typeof height === 'number' ? `${height}px` : height,
    }"
  >
    <qiun-data-charts
      ref="qiunChartRef"
      v-if="showChart"
      :canvasId="chartId"
      :type="type"
      :chartData="transformedChartData"
      :width="parsedWidth"
      :height="parsedHeight"
      :canvas2d="canvas2d"
      :animation="animation"
      :dataLabel="dataLabel"
      :background="background"
      @complete="handleChartComplete"
      @error="handleChartError"
    />

    <!-- 错误提示或加载中显示 -->
    <div v-if="chartError" class="chart-error">
      <AppIcon icon="exclamation-triangle" size="md" color="var(--color-error, #F44336)" />
      <p>{{ chartError }}</p>
    </div>

    <!-- 环形图中心自定义slot -->
    <div v-if="type === 'ring' && $slots.center" class="chart-center-slot">
      <slot name="center" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount, nextTick, defineExpose } from 'vue';
import QiunDataCharts from '@/components/u-charts/qiun-data-charts.vue';
import AppIcon from '@/components/common/AppIcon.vue';

// Props定义
interface Props {
  chartData: any; // 图表数据
  options?: any; // 图表配置选项
  type: string; // 图表类型
  chartId: string; // 画布ID
  width?: string | number; // 图表宽度
  height?: string | number; // 图表高度
  background?: string; // 背景色
  animation?: boolean; // 是否启用动画
  canvas2d?: boolean; // 是否使用canvas2d
  dataLabel?: boolean; // 是否显示数据标签
}

const props = withDefaults(defineProps<Props>(), {
  width: '100%',
  height: '300px',
  background: '#FFFFFF',
  animation: true,
  canvas2d: true,
  dataLabel: true,
  options: () => ({})
});

// 事件
const emit = defineEmits(['chartInit', 'chartError']);

// 图表状态
const showChart = ref(true);
const chartError = ref('');
const retryCount = ref(0);
const maxRetries = 3;
const resizeObserver = ref<ResizeObserver | null>(null);
const chartContainer = ref<HTMLElement | null>(null);

// 新增：引用底层qiun-data-charts组件
const qiunChartRef = ref<any>(null);

// 解析尺寸，移除单位以便传给图表组件
const parsedWidth = computed(() => {
  console.log('[AppChart.vue] Parsing width:', props.width);
  try {
    if (typeof props.width === 'number') return props.width > 0 ? props.width : 320;
    if (typeof props.width === 'string') {
      if (props.width.endsWith('px')) {
        const numWidth = parseFloat(props.width.replace('px', ''));
        return isNaN(numWidth) || numWidth <= 0 ? 320 : numWidth;
      }
      if (chartContainer.value) {
        const w = chartContainer.value.clientWidth;
        return w > 0 ? w : 320;
      }
    }
    return 320;
  } catch (err) {
    console.error('[AppChart.vue] Error parsing width:', err);
    return 320;
  }
});

const parsedHeight = computed(() => {
  console.log('[AppChart.vue] Parsing height:', props.height);
  try {
    if (typeof props.height === 'number') return props.height > 0 ? props.height : 200;
    if (typeof props.height === 'string') {
      if (props.height.endsWith('px')) {
        const numHeight = parseFloat(props.height.replace('px', ''));
        return isNaN(numHeight) || numHeight <= 0 ? 200 : numHeight;
      }
      if (chartContainer.value) {
        const h = chartContainer.value.clientHeight;
        return h > 0 ? h : 200;
      }
    }
    return 200;
  } catch (err) {
    console.error('[AppChart.vue] Error parsing height:', err);
    return 200;
  }
});

// 预处理图表数据
const transformedChartData = computed(() => {
  try {
    console.log('[AppChart.vue] Transforming chartData for', props.chartId);
    const result = { ...props.chartData };
    // 合并options中的配置
    if (props.options) {
      Object.keys(props.options).forEach(key => {
        if (key !== 'series' && key !== 'categories') {
          result[key] = props.options[key];
        }
      });
      // 为环形图添加额外的圆环配置
      if ((props.type === 'ring' || props.type === 'pie') && !result.extra) {
        result.extra = {};
      }
      if (props.type === 'ring') {
        result.extra = {
          ...(result.extra || {}),
          ring: {
            ringWidth: 24,
            labelWidth: 12,
            linearType: 'custom',
            ...(result.extra?.ring || {}),
          },
        };
      }
      if (props.type === 'pie') {
        result.extra = {
          ...(result.extra || {}),
          pie: {
            labelWidth: 15,
            offsetAngle: 0,
            linearType: 'custom',
            ...(result.extra?.pie || {}),
          },
        };
      }
      if (props.type === 'column' || props.type === 'bar') {
        result.extra = {
          ...(result.extra || {}),
          column: {
            width: 18,
            barBorderRadius: [4, 4, 0, 0],
            seriesGap: 2,
            ...(result.extra?.column || {}),
          },
        };
        // 防御性兜底，确保seriesGap始终存在
        if (!result.extra.column.seriesGap) {
          result.extra.column.seriesGap = 2;
        }
      }
    }
    // 防御性：series必须为数组
    if (!Array.isArray(result.series)) {
      result.series = [];
    }
    // 防御性：每个series的data必须为数组
    result.series = result.series.map((s: any) => ({
      ...s,
      data: Array.isArray(s.data) ? s.data : [],
    }));
    console.log('[AppChart.vue] Transformed chartData:', result);
    return result;
  } catch (err) {
    console.error('[AppChart.vue] Error transforming chartData:', err);
    chartError.value = '图表数据处理错误';
    return props.chartData;
  }
});

// 图表初始化完成处理
const handleChartComplete = (res: any) => {
  console.log('[AppChart.vue] Chart complete:', res);
  chartError.value = '';
  emit('chartInit', res);
  retryCount.value = 0;
};

// 图表错误处理
const handleChartError = (err: any) => {
  console.error('[AppChart.vue] Chart error:', err);
  chartError.value = err.message || '图表渲染失败';
  emit('chartError', err);
  
  // 尝试重新渲染（最多3次）
  if (retryCount.value < maxRetries) {
    retryCount.value++;
    console.log(`[AppChart.vue] Retrying chart init (${retryCount.value}/${maxRetries})...`);
    
    setTimeout(() => {
      refreshChart();
    }, 300 * retryCount.value); // 每次重试间隔增加
  }
};

// 监听数据变化或尺寸变化，刷新图表
watch(() => props.chartData, () => {
  console.log('[AppChart.vue] Chart data changed, refreshing chart');
  nextTick(() => refreshChart());
}, { deep: true });

watch(() => props.options, () => {
  console.log('[AppChart.vue] Chart options changed, refreshing chart');
  nextTick(() => refreshChart());
}, { deep: true });

watch([parsedWidth, parsedHeight], () => {
  console.log('[AppChart.vue] Chart dimensions changed:', { width: parsedWidth.value, height: parsedHeight.value });
  nextTick(() => refreshChart());
});

// 刷新图表的方法
const refreshChart = () => {
  console.log('[AppChart.vue] Refreshing chart:', props.chartId);
  showChart.value = false;
  
  nextTick(() => {
    showChart.value = true;
  });
};

// 设置容器大小监听
const setupResizeObserver = () => {
  try {
    chartContainer.value = document.querySelector(`.chart-container`);
    if (!chartContainer.value) {
      console.warn('[AppChart.vue] Chart container not found for ResizeObserver');
      return;
    }
    
    if (typeof ResizeObserver === 'undefined') {
      console.warn('[AppChart.vue] ResizeObserver not supported in this environment');
      return;
    }
    
    resizeObserver.value = new ResizeObserver((entries) => {
      for (const entry of entries) {
        const { width, height } = entry.contentRect;
        console.log('[AppChart.vue] Container resized:', { width, height });
        nextTick(() => refreshChart());
      }
    });
    
    resizeObserver.value.observe(chartContainer.value);
  } catch (err) {
    console.error('[AppChart.vue] Error setting up ResizeObserver:', err);
  }
};

// 生命周期钩子
onMounted(() => {
  console.log('[AppChart.vue] Component mounted:', props.chartId);
  
  // 延迟处理，确保DOM已渲染
  nextTick(() => {
    setupResizeObserver();
  });
});

onBeforeUnmount(() => {
  // 清理工作
  if (resizeObserver.value) {
    resizeObserver.value.disconnect();
  }
});

// defineExpose暴露方法和属性，供父组件ref调用
// 这样父组件就能直接调用refresh/disposeChart/chartCanvasId等

defineExpose({
  // 刷新图表，force参数可选
  refresh: (force = false) => {
    // 调试点：刷新AppChart
    if (qiunChartRef.value && typeof qiunChartRef.value.refresh === 'function') {
      return qiunChartRef.value.refresh(force);
    }
    return false;
  },
  // 销毁/清空图表
  disposeChart: () => {
    // 调试点：销毁AppChart
    if (qiunChartRef.value && typeof qiunChartRef.value.clear === 'function') {
      qiunChartRef.value.clear();
    }
  },
  // 提供chartId，便于调试
  chartCanvasId: props.chartId
});
</script>

<style lang="scss" scoped>
.chart-container {
  position: relative;
  width: 100%;
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.chart-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.9);
  z-index: 10;
  
  p {
    margin-top: 8px;
    color: var(--color-error, #F44336);
    font-size: 12px;
    text-align: center;
  }
}

.chart-center-slot {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
</style> 