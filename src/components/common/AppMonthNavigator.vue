<template>
  <view
    class="app-month-navigator"
  >
    <view class="month-nav-arrow" @click="$emit('prev')">
      <AppIcon icon="chevron-left" size="xs" />
    </view>
    <text class="month-nav-date" @click="$emit('month-click')">
      {{ year }}年{{ month }}月
    </text>
    <view class="month-nav-arrow" @click="$emit('next')">
      <AppIcon icon="chevron-right" size="xs" />
    </view>
  </view>
</template>

<script setup lang="ts">
import AppIcon from '@/components/common/AppIcon.vue';
import { defineProps } from 'vue';
const props = defineProps<{ year: number, month: number }>();
</script>

<style lang="scss" scoped>
.app-month-navigator {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-primary, #fff);
  height: 38px;
  padding: 0 10px;
  border-radius: 19px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--border-color, #f0f0f0);
  box-sizing: border-box;
  width: 100%; /* 使其适应父容器宽度 */
  transition: all 0.2s;
}

.month-nav-arrow {
  font-size: 15px;
  color: var(--color-primary, #ff6b35);
  height: 22px;
  width: 22px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: all 0.2s ease;
  
  &:active {
    background-color: rgba(255, 107, 53, 0.1);
    transform: scale(0.95);
  }
}

.month-nav-date {
  font-size: 15px;
  font-weight: 600;
  margin: 0 2px;
  color: var(--text-primary, #333);
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 8px;
  transition: all 0.2s ease;
  white-space: nowrap;
  height: 28px;
  line-height: 28px;
  display: flex;
  align-items: center;
  
  &:active {
    background-color: rgba(255, 107, 53, 0.08);
    transform: scale(0.98);
  }
}
</style> 