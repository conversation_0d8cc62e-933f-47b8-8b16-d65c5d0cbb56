<script setup lang="ts">
import { computed, onMounted, ref, watch } from 'vue';
import AppButton from './AppButton.vue';
import AppModal from './AppModal.vue';
// AppIcon is globally registered via easycom

interface DatePickerProps {
  /** 当前选中的日期值 (YYYY-MM-DD 或 YYYY-MM) */
  modelValue?: string | Date
  /** 选择器类型: date(年月日), month(年月), year(年) */
  type?: 'date' | 'month' | 'year'
  /** 最小可选日期 */
  minDate?: string | Date
  /** 最大可选日期 */
  maxDate?: string | Date
  /** 是否禁用 */
  disabled?: boolean
  /** 是否显示选择器 */
  visible?: boolean
  /** 是否有错误 */
  error?: boolean
  /** 错误提示消息 */
  errorMessage?: string
  /** 标题 */
  title?: string
  /** 年份后缀 */
  yearSuffix?: string
  /** 月份后缀 */
  monthSuffix?: string
  /** 日期后缀 */
  daySuffix?: string
  /** 禁用今天之前的日期 */
  disablePastDates?: boolean
  /** 自定义格式化函数 */
  formatter?: (type: string, value: number) => string
  /** 是否隐藏触发器 */
  hideTrigger?: boolean
}

const props = withDefaults(defineProps<DatePickerProps>(), {
  modelValue: () => new Date(),
  type: 'date',
  minDate: '1900-01-01',
  maxDate: '2100-12-31',
  disabled: false,
  visible: false,
  error: false,
  errorMessage: '请选择有效日期',
  title: '选择日期',
  yearSuffix: '年',
  monthSuffix: '月',
  daySuffix: '日',
  disablePastDates: false,
  formatter: undefined,
  hideTrigger: false,
});

const emit = defineEmits<{
  (e: 'update:modelValue', value: string): void
  (e: 'update:visible', visible: boolean): void
  (e: 'confirm', value: string): void
  (e: 'cancel'): void
  (e: 'change', detail: { year?: number, month?: number, day?: number }): void
}>();

// 内部状态
const internalVisible = ref(props.visible);
const currentDate = ref(new Date());
const tempSelectedDate = ref(new Date());
const pickerValue = ref<number[]>([]);

// 处理props.modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      currentDate.value = typeof newVal === 'string' ? new Date(newVal) : newVal;
      tempSelectedDate.value = new Date(currentDate.value);
      updatePickerValue();
    }
  },
  { immediate: true },
);

// 处理props.visible变化
watch(
  () => props.visible,
  (newVal) => {
    internalVisible.value = newVal;
    if (newVal) {
      tempSelectedDate.value = new Date(currentDate.value);
      updatePickerValue();
    }
  },
  { immediate: true },
);

// 计算属性：日期选择范围
const minDateObj = computed(() => new Date(props.minDate));
const maxDateObj = computed(() => new Date(props.maxDate));

// 当前日期，用于禁用过去日期
const today = computed(() => {
  const date = new Date();
  date.setHours(0, 0, 0, 0);
  return date;
});

// 计算属性：显示哪些列
const showYear = computed(() => ['date', 'month', 'year'].includes(props.type));
const showMonth = computed(() => ['date', 'month'].includes(props.type));
const showDay = computed(() => props.type === 'date');

// 计算年份范围
const years = computed(() => {
  const minYear = minDateObj.value.getFullYear();
  const maxYear = maxDateObj.value.getFullYear();
  const years: number[] = [];

  for (let i = minYear; i <= maxYear; i++) {
    // 如果禁用过去日期，则只显示当前年份及以后
    if (props.disablePastDates && i < today.value.getFullYear()) {
      continue;
    }
    years.push(i);
  }

  return years;
});

// 计算月份范围
const months = computed(() => {
  const currentYear = tempSelectedDate.value.getFullYear();
  const minYear = minDateObj.value.getFullYear();
  const maxYear = maxDateObj.value.getFullYear();
  const months: number[] = [];

  let minMonth = 1;
  let maxMonth = 12;

  // 如果是最小年份，从最小月份开始
  if (currentYear === minYear) {
    minMonth = minDateObj.value.getMonth() + 1;
  }

  // 如果是最大年份，到最大月份结束
  if (currentYear === maxYear) {
    maxMonth = maxDateObj.value.getMonth() + 1;
  }

  // 如果禁用过去日期，且是当前年份，则只显示当前月份及以后
  if (props.disablePastDates && currentYear === today.value.getFullYear()) {
    minMonth = Math.max(minMonth, today.value.getMonth() + 1);
  }

  for (let i = minMonth; i <= maxMonth; i++) {
    months.push(i);
  }

  return months;
});

// 计算天数范围
const days = computed(() => {
  const currentYear = tempSelectedDate.value.getFullYear();
  const currentMonth = tempSelectedDate.value.getMonth() + 1;
  const daysInMonth = new Date(currentYear, currentMonth, 0).getDate();
  const days: number[] = [];

  let minDay = 1;
  let maxDay = daysInMonth;

  // 如果是最小年月，从最小日开始
  if (
    currentYear === minDateObj.value.getFullYear()
    && currentMonth === minDateObj.value.getMonth() + 1
  ) {
    minDay = minDateObj.value.getDate();
  }

  // 如果是最大年月，到最大日结束
  if (
    currentYear === maxDateObj.value.getFullYear()
    && currentMonth === maxDateObj.value.getMonth() + 1
  ) {
    maxDay = maxDateObj.value.getDate();
  }

  // 如果禁用过去日期，且是当前年月，则只显示当前日期及以后
  if (
    props.disablePastDates
    && currentYear === today.value.getFullYear()
    && currentMonth === today.value.getMonth() + 1
  ) {
    minDay = Math.max(minDay, today.value.getDate());
  }

  for (let i = minDay; i <= maxDay; i++) {
    days.push(i);
  }

  return days;
});

// 计算显示值
const displayValue = computed(() => {
  if (props.disabled) {
    return '请选择';
  }

  const date = currentDate.value;
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();

  switch (props.type) {
    case 'year':
      return `${year}${props.yearSuffix}`;
    case 'month':
      return `${year}${props.yearSuffix}${month}${props.monthSuffix}`;
    case 'date':
    default:
      return `${year}${props.yearSuffix}${month}${props.monthSuffix}${day}${props.daySuffix}`;
  }
});

// 计算样式
const indicatorStyle = computed(() => 'height: 40px; line-height: 40px; color: var(--color-primary, #FF6B35);');

const maskStyle = computed(() => `background-size: 100% 80px; background-image: linear-gradient(180deg, 
    rgba(255, 255, 255, 0.9), 
    rgba(255, 255, 255, 0.6)), 
    linear-gradient(0deg, 
    rgba(255, 255, 255, 0.9), 
    rgba(255, 255, 255, 0.6));`);

// 方法：点击触发器
function handleTriggerClick() {
  if (props.disabled)
    return;

  internalVisible.value = true;
  emit('update:visible', true);
}

// 方法：取消选择
function handleCancel() {
  internalVisible.value = false;
  emit('update:visible', false);
  emit('cancel');
}

// 方法：确认选择
function handleConfirm() {
  // 更新当前选中日期
  currentDate.value = new Date(tempSelectedDate.value);

  // 格式化日期为字符串输出
  let dateStr = '';
  const year = currentDate.value.getFullYear();
  const month = currentDate.value.getMonth() + 1;
  const day = currentDate.value.getDate();

  switch (props.type) {
    case 'year':
      dateStr = `${year}`;
      break;
    case 'month':
      dateStr = `${year}-${month.toString().padStart(2, '0')}`;
      break;
    case 'date':
    default:
      dateStr = `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
      break;
  }

  // 提交结果
  emit('update:modelValue', dateStr);
  emit('confirm', dateStr);

  // 关闭选择器
  internalVisible.value = false;
  emit('update:visible', false);
}

// 方法：处理选择变化
function handleChange(e: any) {
  const values = e.detail.value;
  let yearIndex = 0;
  let monthIndex = 0;
  let dayIndex = 0;

  // 根据显示的列来确定索引位置
  if (showYear.value) {
    yearIndex = 0;
  }

  if (showMonth.value) {
    monthIndex = showYear.value ? 1 : 0;
  }

  if (showDay.value) {
    dayIndex = (showYear.value ? 1 : 0) + (showMonth.value ? 1 : 0);
  }

  // 更新临时选中日期
  const newDate = new Date(tempSelectedDate.value);

  if (showYear.value && years.value.length > 0) {
    newDate.setFullYear(years.value[values[yearIndex]]);
  }

  if (showMonth.value && months.value.length > 0) {
    newDate.setMonth(months.value[values[monthIndex]] - 1);
  }

  if (showDay.value && days.value.length > 0) {
    // 先设置月底最后一天，防止月份天数不同导致日期溢出
    newDate.setDate(1);
    if (values[dayIndex] < days.value.length) {
      newDate.setDate(days.value[values[dayIndex]]);
    }
  }

  tempSelectedDate.value = newDate;

  // 更新选择器值
  pickerValue.value = values;

  // 触发change事件
  emit('change', {
    year: showYear.value ? years.value[values[yearIndex]] : undefined,
    month: showMonth.value ? months.value[values[monthIndex]] : undefined,
    day: showDay.value ? days.value[values[dayIndex]] : undefined,
  });
}

// 方法：更新选择器值（完全重构，彻底解决初始化问题）
function updatePickerValue() {
  // 使用更强健的延迟执行和防错机制
  try {
    // 使用nextTick确保Vue已完成渲染周期
    setTimeout(() => {
      try {
        // 严格的类型和可用性检查
        if (typeof years === 'undefined' || typeof months === 'undefined' || 
            typeof days === 'undefined' || typeof showYear === 'undefined' || 
            typeof showMonth === 'undefined' || typeof showDay === 'undefined') {
          console.warn('AppDatePicker: 组件计算属性尚未初始化，已安排下次更新');
          // 再次尝试延迟更新
          setTimeout(updatePickerValue, 50);
          return;
        }
        
        // 更严格的数组检查，确保都是实例化的计算属性，并且有正确的值
        if (!years.value || !Array.isArray(years.value) || years.value.length === 0 ||
            !months.value || !Array.isArray(months.value) || months.value.length === 0 ||
            !days.value || !Array.isArray(days.value) || days.value.length === 0) {
          console.warn('AppDatePicker: 日期数组不完整或为空，已安排下次更新');
          // 短延迟后再次尝试
          setTimeout(updatePickerValue, 50);
          return;
        }
        
        // 非常严格的日期有效性检查
        if (!tempSelectedDate.value || 
            !(tempSelectedDate.value instanceof Date) || 
            isNaN(tempSelectedDate.value.getTime())) {
          console.warn('AppDatePicker: 日期对象无效，使用今天日期');
          // 重置为今天日期
          tempSelectedDate.value = new Date();
        }
        
        // 使用安全的访问模式查找索引
        let yearIndex = -1;
        let monthIndex = -1;
        let dayIndex = -1;
        
        try {
          yearIndex = years.value.findIndex(y => y === tempSelectedDate.value.getFullYear());
        } catch (e) {
          console.warn('AppDatePicker: 计算年份索引时出错', e);
        }
        
        try {
          monthIndex = months.value.findIndex(m => m === tempSelectedDate.value.getMonth() + 1);
        } catch (e) {
          console.warn('AppDatePicker: 计算月份索引时出错', e);
        }
        
        try {
          dayIndex = days.value.findIndex(d => d === tempSelectedDate.value.getDate());
        } catch (e) {
          console.warn('AppDatePicker: 计算日期索引时出错', e);
        }
        
        // 构建选择器值数组，每一步都用安全默认值
        const value: number[] = [];
        
        if (showYear.value) {
          value.push(yearIndex >= 0 ? yearIndex : 0);
        }
        
        if (showMonth.value) {
          value.push(monthIndex >= 0 ? monthIndex : 0);
        }
        
        if (showDay.value) {
          value.push(dayIndex >= 0 ? dayIndex : 0);
        }
        
        // 最终安全检查：确保数组长度符合预期
        if (value.length === 0) {
          console.warn('AppDatePicker: 生成的选择器值数组为空，使用默认值');
          // 根据type确定默认值数组
          if (props.type === 'date') value.push(0, 0, 0);
          else if (props.type === 'month') value.push(0, 0);
          else value.push(0);
        }
        
        // 设置选择器值
        pickerValue.value = value;
        console.log('AppDatePicker: 成功更新选择器值', value);
      } catch (innerError) {
        console.error('AppDatePicker: 更新选择器值过程中出错', innerError);
        // 设置安全的默认值，根据当前类型确定数组长度
        const defaultValue = [0, 0, 0].slice(0, 
          (showYear.value ? 1 : 0) + (showMonth.value ? 1 : 0) + (showDay.value ? 1 : 0)
        );
        pickerValue.value = defaultValue.length > 0 ? defaultValue : [0];
      }
    }, 50); // 增加延迟，确保计算属性都已初始化
  } catch (error) {
    console.error('AppDatePicker: 启动更新选择器值时发生严重错误', error);
    // 完全回退策略，设置最基本的默认值
    pickerValue.value = [0];
  }
}

// 增强初始化逻辑，确保组件正确渲染
onMounted(() => {
  // 使用多阶段初始化，确保各种情况下组件都能正确渲染
  console.log('AppDatePicker: 组件挂载完成，开始初始化');
  
  // 第一阶段：立即尝试初始化
  updatePickerValue();
  
  // 第二阶段：50ms后再次更新，确保计算属性完成
  setTimeout(() => {
    try {
      updatePickerValue();
    } catch (e) {
      console.warn('AppDatePicker: 第二阶段初始化出错', e);
    }
  }, 50);
  
  // 第三阶段：200ms后最终保险措施
  setTimeout(() => {
    try {
      // 如果pickerValue仍为空，尝试最后一次更新
      if (!pickerValue.value || pickerValue.value.length === 0) {
        console.warn('AppDatePicker: 前两阶段初始化失败，执行最终保险措施');
        updatePickerValue();
      }
    } catch (e) {
      console.error('AppDatePicker: 组件初始化全部失败', e);
      // 最终兜底：强制设置默认值
      pickerValue.value = props.type === 'date' ? [0, 0, 0] : 
                          props.type === 'month' ? [0, 0] : [0];
    }
  }, 200);
});
</script>

<template>
  <view class="app-date-picker">
    <!-- 日期选择器触发区域 -->
    <view class="app-date-picker__trigger" @click="showPicker">
      <slot name="trigger">
        <view class="app-date-picker__display">
          <text>{{ displayValue || placeholder }}</text>
          <AppIcon icon="calendar-alt" class="app-date-picker__icon" />
        </view>
      </slot>
    </view>

    <!-- 日期选择器弹窗 -->
    <AppModal v-model:visible="pickerVisible" title="选择日期" @close="handleClose">
      <view class="app-date-picker__content">
        <!-- 日历头部 - 年月切换 -->
        <view class="app-date-picker__header">
          <view class="app-date-picker__nav">
            <view class="app-date-picker__nav-btn" @click="prevYear">
              <AppIcon icon="chevron-double-left" />
            </view>
            <view class="app-date-picker__nav-btn" @click="prevMonth">
              <AppIcon icon="chevron-left" />
            </view>
          </view>
          
          <view class="app-date-picker__title" @click="showYearMonthSelector">
            {{ `${currentYear}年${currentMonth}月` }}
          </view>
          
          <view class="app-date-picker__nav">
            <view class="app-date-picker__nav-btn" @click="nextMonth">
              <AppIcon icon="chevron-right" />
            </view>
            <view class="app-date-picker__nav-btn" @click="nextYear">
              <AppIcon icon="chevron-double-right" />
            </view>
          </view>
        </view>
        
        <!-- 星期表头 -->
        <view class="app-date-picker__weekdays">
          <text v-for="(day, index) in weekdays" :key="index" class="app-date-picker__weekday">
            {{ day }}
          </text>
        </view>

        <!-- 日期网格 -->
        <view class="app-date-picker__days">
          <view 
            v-for="(day, index) in days" 
            :key="index" 
            class="app-date-picker__day" 
            :class="{
              'app-date-picker__day--out-of-month': !day.currentMonth,
              'app-date-picker__day--today': day.isToday,
              'app-date-picker__day--selected': isSelectedDate(day)
            }"
            @click="selectDate(day)"
          >
            {{ day.date }}
          </view>
              </view>
        
        <!-- 年月选择器 (条件渲染) -->
        <view v-if="showYearMonth" class="app-date-picker__year-month-selector">
          <!-- 年份选择 -->
          <view class="app-date-picker__selector">
            <view class="app-date-picker__selector-title">年份</view>
            <view class="app-date-picker__selector-items">
              <view 
                v-for="year in yearRange" 
                :key="year" 
                class="app-date-picker__selector-item"
                :class="{'app-date-picker__selector-item--active': year === currentYear}"
                @click="selectYear(year)"
              >
                {{ year }}
              </view>
            </view>
          </view>

          <!-- 月份选择 -->
          <view class="app-date-picker__selector">
            <view class="app-date-picker__selector-title">月份</view>
            <view class="app-date-picker__selector-items">
              <view 
                v-for="month in 12" 
                :key="month" 
                class="app-date-picker__selector-item"
                :class="{'app-date-picker__selector-item--active': month === currentMonth}"
                @click="selectMonth(month)"
              >
                {{ month }}
              </view>
            </view>
          </view>
        </view>

        <!-- 底部操作栏 -->
        <view class="app-date-picker__footer">
          <view class="app-date-picker__footer-btn" @click="setToday">
            <AppIcon icon="calendar-day" /> 今天
          </view>
          <view class="app-date-picker__footer-btn" @click="clearDate">
            <AppIcon icon="trash" /> 清除
          </view>
          <view class="app-date-picker__footer-btn app-date-picker__footer-btn--confirm" @click="confirmDate">
            <AppIcon icon="check" /> 确定
          </view>
        </view>
      </view>
    </AppModal>
  </view>
</template>

<style lang="scss" scoped>
  .app-date-picker {
    position: relative;
    width: 100%;
    
    // 当hideTrigger为true时，使组件不占用空间
    &:empty {
      display: contents;
    }

    // 触发器样式
    &__trigger {
      width: 100%;
      position: relative;

      &--disabled {
        opacity: 0.6;
        cursor: not-allowed;
      }

      &--error {
        .app-date-picker__display {
          border-color: var(--color-error, #f44336);
        }
      }
    }

    // 展示区域
    &__display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
      height: 40px;
      padding: 0 var(--spacing-md, 16px);
      border: 1px solid var(--border-color, #e5e5e5);
      border-radius: var(--radius-input, 8px);
      background-color: var(--bg-primary, #fff);
      transition: all 0.3s;

      &:active {
        opacity: 0.8;
      }
    }

    &__icon {
      margin-right: var(--spacing-xs, 8px);
    }

    &__value {
      flex: 1;
      font-size: var(--font-size-md, 16px);
      color: var(--text-primary, #333);
    }

    &__arrow {
      margin-left: var(--spacing-xs, 8px);
      transition: transform 0.3s;
    }

    &__error-text {
      position: absolute;
      font-size: var(--font-size-sm, 12px);
      color: var(--color-error, #f44336);
      margin-top: 4px;
    }

    // 遮罩层
    &__overlay {
      position: fixed;
      inset: 0;
      background-color: rgb(0 0 0 / 40%);
      z-index: 1000;
      display: flex;
      justify-content: center;

      /* #ifdef H5 */
      align-items: center;

      /* #endif */
    }

    // 弹出面板
    &__popup {
      background-color: var(--bg-primary, #fff);
      overflow: hidden;
      animation: slide-up 0.3s;

      /* #ifdef H5 */
      width: 90%;
      max-width: 400px;
      border-radius: var(--radius-lg, 16px);

      /* #endif */
    }

    // 头部
    &__header {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 50px;
      padding: 0 var(--spacing-md, 16px);
      border-bottom: 1px solid var(--border-color, #e5e5e5);
    }

    &__title {
      font-size: var(--font-size-lg, 18px);
      font-weight: 500;
      color: var(--text-primary, #333);
    }

    // 内容区
    &__body {
      padding: var(--spacing-md, 16px) 0;
    }

    &__picker-view {
      width: 100%;
      height: 200px;
    }

    &__picker-item {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 40px;
      font-size: var(--font-size-md, 16px);
      color: var(--text-primary, #333);
    }

    // 底部
    &__footer {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: var(--spacing-sm, 8px) var(--spacing-md, 16px);
      border-top: 1px solid var(--border-color, #e5e5e5);
    }

    &__btn {
      flex: 1;
      margin: 0 var(--spacing-xs, 8px);
    }
  }

  // 多端适配样式

  /* #ifdef MP-WEIXIN */
  .app-date-picker {
    &__popup {
      padding-bottom: env(safe-area-inset-bottom, 0);
    }
  }

  /* #endif */

  /* #ifdef APP-PLUS */
  .app-date-picker {
    &__popup {
      /* #ifdef APP-PLUS-IOS */
      padding-bottom: env(safe-area-inset-bottom, 0);

      /* #endif */
    }
  }

  /* #endif */

  @keyframes slide-up {
    from {
      transform: translateY(100%);
    }

    to {
      transform: translateY(0);
    }
  }

  /* 暗黑模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-date-picker {
      &__display {
        border-color: var(--border-color, #333);
        background-color: var(--bg-secondary, #1e1e1e);
      }

      &__value {
        color: var(--text-primary, #f5f5f5);
      }

      &__popup {
        background-color: var(--bg-secondary, #1e1e1e);
      }

      &__header {
        border-bottom-color: var(--border-color, #333);
      }

      &__title {
        color: var(--text-primary, #f5f5f5);
      }

      &__picker-item {
        color: var(--text-primary, #f5f5f5);
      }

      &__footer {
        border-top-color: var(--border-color, #333);
      }
    }
  }
</style>
