<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';

/**
 * 组件属性定义
 */
const props = defineProps({
  /**
   * 是否禁用滑动功能
   */
  disabled: {
    type: Boolean,
    default: false,
  },
  /**
   * 操作按钮的总宽度 (单位: px)
   */
  buttonsWidth: {
    type: Number,
    default: 160, // 默认值适合两个按钮
  },
  /**
   * 滑动阈值，超过多少比例时自动展开/收起 (0-1之间)
   */
  threshold: {
    type: Number,
    default: 0.3,
  },
  /**
   * 动画持续时间 (毫秒)
   */
  duration: {
    type: Number,
    default: 250,
  },
  /**
   * 是否允许跨组件关闭其他已打开的滑动项
   */
  autoClose: {
    type: Boolean,
    default: true,
  },
});

/**
 * 组件事件
 */
const emit = defineEmits(['open', 'close']);

// 组件状态
const startX = ref(0); // 开始触摸的X坐标
const startY = ref(0); // 开始触摸的Y坐标
const moveX = ref(0); // 水平移动的距离
const isMoving = ref(false); // 是否正在移动
const direction = ref<'left' | 'right' | 'vertical' | null>(null); // 滑动方向
const isOpened = ref(false); // 是否已打开
const container = ref<HTMLElement | null>(null); // 容器元素引用
const actionId = ref(`swipe-${Date.now()}-${Math.round(Math.random() * 1000)}`); // 唯一标识符
const directionLocked = ref(false); // 是否锁定滑动方向

/**
 * 计算属性
 */
// 计算当前位移样式
const transformStyle = computed(() => {
  const translateX = isOpened.value
    ? `-${props.buttonsWidth}px`
    : moveX.value < 0
      ? `${moveX.value}px`
      : '0px';
  
  return {
    transform: `translateX(${translateX})`,
    transitionDuration: isMoving.value ? '0ms' : `${props.duration}ms`,
  };
});

// 监听disabled属性变化，如果设为true则关闭
watch(() => props.disabled, (newValue) => {
  if (newValue && isOpened.value) {
    close();
  }
});

/**
 * 关闭所有其他打开的滑动项的自定义事件
 */
function closeOtherSwipeItems(id: string) {
  if (id !== actionId.value && isOpened.value) {
    close();
  }
}

/**
 * 触摸开始事件
 */
function handleTouchStart(e: TouchEvent) {
  if (props.disabled) return;
  
  // 记录起始触摸位置
  startX.value = e.touches[0].clientX;
  startY.value = e.touches[0].clientY;
  isMoving.value = true;
  direction.value = null;
  directionLocked.value = false;
}

/**
 * 触摸移动事件
 */
function handleTouchMove(e: TouchEvent) {
  if (props.disabled || !isMoving.value) return;
  
  const currentX = e.touches[0].clientX;
  const currentY = e.touches[0].clientY;
  
  // 计算水平和垂直移动距离
  const deltaX = currentX - startX.value;
  const deltaY = currentY - startY.value;
  
  // 如果方向未锁定，先确定滑动方向
  if (!directionLocked.value) {
    // 使用小阈值确定方向锁定
    const directionThreshold = 10;
    
    if (Math.abs(deltaX) > directionThreshold || Math.abs(deltaY) > directionThreshold) {
      // 如果主要是水平移动
      if (Math.abs(deltaX) >= Math.abs(deltaY)) {
        direction.value = deltaX > 0 ? 'right' : 'left';
        // 允许水平滑动事件继续
        if (direction.value === 'left' || isOpened.value) {
          e.preventDefault(); // 阻止页面滚动
        }
      } else {
        // 如果主要是垂直移动，标记为垂直
        direction.value = 'vertical';
        isMoving.value = false; // 停止跟踪移动
        return; // 允许页面正常滚动
      }
      
      directionLocked.value = true;
    }
    
    if (!directionLocked.value) return;
  }
  
  // 如果是垂直方向的滑动，则不处理
  if (direction.value === 'vertical') return;
  
  // 防止页面滚动
  if (direction.value === 'left' || isOpened.value) {
    e.preventDefault();
  }
  
  let moveDistance = deltaX;
  
  // 如果已经打开，滑动起点需要考虑已位移距离
  if (isOpened.value) {
    moveDistance -= props.buttonsWidth;
  }
  
  // 限制右滑（只允许关闭操作，不允许右滑超出）
  if (moveDistance > 0 && !isOpened.value) {
    moveDistance = 0;
  }
  
  // 限制左滑不超出按钮宽度
  if (moveDistance < -props.buttonsWidth) {
    moveDistance = -props.buttonsWidth;
  }
  
  moveX.value = moveDistance;
}

/**
 * 触摸结束事件
 */
function handleTouchEnd() {
  if (props.disabled || !isMoving.value) return;
  
  isMoving.value = false;
  
  // 如果不是水平方向的滑动，则不处理
  if (direction.value !== 'left' && direction.value !== 'right') return;
  
  const currentX = moveX.value;
  
  // 如果已经打开
  if (isOpened.value) {
    // 如果右滑超过阈值，则关闭
    if (currentX > -props.buttonsWidth * (1 - props.threshold)) {
      close();
    } else {
      // 否则保持打开
      open();
    }
  } else {
    // 如果左滑超过阈值，则打开
    if (currentX < -props.buttonsWidth * props.threshold) {
      open();
    } else {
      // 否则关闭
      close();
    }
  }
}

/**
 * 打开滑动操作
 */
function open() {
  if (props.disabled) return;
  
  isOpened.value = true;
  moveX.value = -props.buttonsWidth;
  emit('open');
  
  // 广播关闭其他滑动项
  if (props.autoClose) {
    uni.$emit('closeSwipeAction', actionId.value);
  }
}

/**
 * 关闭滑动操作
 */
function close() {
  isOpened.value = false;
  moveX.value = 0;
  emit('close');
}

/**
 * 点击内容区域事件
 */
function handleContentClick() {
  if (isOpened.value) {
    close();
  }
}

/**
 * 生命周期钩子
 */
onMounted(() => {
  // 监听关闭其他滑动项的事件
  if (props.autoClose) {
    uni.$on('closeSwipeAction', closeOtherSwipeItems);
  }
});

onUnmounted(() => {
  // 移除事件监听器
  if (props.autoClose) {
    uni.$off('closeSwipeAction', closeOtherSwipeItems);
  }
});

// 导出给父组件使用的方法
defineExpose({
  open,
  close,
  isOpened,
});
</script>

<template>
  <view ref="container" class="app-swipe-action">
    <!-- 内容区域 -->
    <view
      class="app-swipe-action__content"
      :style="transformStyle"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
      @touchcancel="handleTouchEnd"
      @click="handleContentClick"
    >
      <slot></slot>
    </view>
    
    <!-- 操作按钮区域 -->
    <view class="app-swipe-action__buttons" :style="{ width: `${buttonsWidth}px` }">
      <slot name="actions"></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.app-swipe-action {
  position: relative;
  overflow: hidden;
  /* 修复边界渲染问题 */
  width: 100%;
  border-radius: inherit;
  transform: translateZ(0); /* 启用硬件加速，避免亚像素渲染问题 */
  
  &__content {
    position: relative;
    z-index: 10;
    background-color: var(--bg-primary, #fff);
    transition-property: transform;
    transition-timing-function: ease-out;
    width: 100%;
    border-radius: inherit; /* 继承父元素的圆角 */
    overflow: hidden; /* 确保内容不溢出 */
  }
  
  &__buttons {
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    display: flex;
    z-index: 5;
    border-radius: 0; /* 重置可能继承的圆角 */
  }
}
</style> 