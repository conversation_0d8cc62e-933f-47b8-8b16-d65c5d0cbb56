<script setup lang="ts">
import { computed } from 'vue';
import AppIcon from './AppIcon.vue';

interface Props {
  /** 类型: primary, success, warning, error, info */
  type?: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'default'
  /** 文字 */
  text?: string
  /** 图标 */
  icon?: string
  /** 是否禁用 */
  disabled?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  text: '',
  disabled: false,
});

/**
 * 组件事件
 */
const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>();

/**
 * 计算属性
 */
// 计算按钮样式类名
const buttonClass = computed(() => [
    'app-swipe-action-button',
    `app-swipe-action-button--type-${props.type}`,
    {
      'app-swipe-action-button--disabled': props.disabled,
    },
  ]);

// 根据类型获取图标颜色
const iconColor = computed(() => {
  const colorMap: Record<string, string> = {
    default: 'var(--text-primary, #333333)',
    primary: 'var(--text-inverse, #ffffff)',
    success: 'var(--text-inverse, #ffffff)',
    warning: 'var(--text-inverse, #ffffff)',
    error: 'var(--text-inverse, #ffffff)',
    info: 'var(--text-inverse, #ffffff)',
  };
  return colorMap[props.type] || colorMap.default;
});

/**
 * 点击按钮事件
 */
function handleClick(event: MouseEvent) {
  if (props.disabled) return;
  emit('click', event);
}
</script>

<template>
  <view
    :class="buttonClass"
    @tap="handleClick"
  >
    <!-- 图标 -->
    <AppIcon
      v-if="icon"
      :icon="icon"
      :color="iconColor"
      class="app-swipe-action-button__icon"
    />
    
    <!-- 文本 -->
    <text
      v-if="text"
      class="app-swipe-action-button__text"
    >
      {{ text }}
    </text>
  </view>
</template>

<style lang="scss" scoped>
.app-swipe-action-button {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  height: 100%;
  flex-shrink: 0;
  padding: 0 16px;
  border-radius: var(--radius-button, 8px);
  gap: 8px;
  transition: background-color 0.2s;
  flex: 1 1 0;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  
  &__icon {
    font-size: 18px;
    flex-shrink: 0;
    margin-right: 0;
  }
  
  &__text {
    font-size: 16px;
    font-weight: 600;
    flex-shrink: 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
  }
  
  // 类型样式
  &--type-primary {
    background-color: var(--color-primary, #FF6B35);
    color: var(--text-inverse, #fff);
  }
  
  &--type-success {
    background-color: var(--color-success, #4CAF50);
    color: var(--text-inverse, #fff);
  }
  
  &--type-warning {
    background-color: var(--color-warning, #FFC107);
    color: var(--text-inverse, #fff);
  }
  
  &--type-error {
    background-color: var(--color-error, #F44336);
    color: var(--text-inverse, #fff);
  }
  
  &--type-info {
    background-color: var(--color-info, #2196f3);
    color: var(--text-inverse, #fff);
  }
  
  &--type-default {
    background-color: var(--bg-secondary, #f5f5f5);
    color: var(--text-primary, #333);
  }
  
  // 禁用状态
  &--disabled {
    opacity: 0.5;
    pointer-events: none;
  }
}
</style> 