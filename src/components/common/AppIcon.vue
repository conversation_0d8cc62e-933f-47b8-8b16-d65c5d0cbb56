<script setup lang="ts">
import { computed } from 'vue';
// 显式导入u-icon组件
import UIcon from 'uview-plus/components/u-icon/u-icon.vue';

/** 
 * AppIcon组件 props 类型定义
 */
interface Props {
  // 图标名称 (使用uView Plus图标名，可以不带uicon-前缀)
  icon?: string;
  // 图标大小 (数字或预设大小字符串)
  size?: string | number;
  // 图标颜色 (推荐使用CSS变量)
  color?: string;
  // 是否加粗
  bold?: boolean;
  // 图标右侧文字
  label?: string;
  // 文字位置 (right/bottom)
  labelPos?: string;
  // 文字大小
  labelSize?: string | number;
  // 文字颜色
  labelColor?: string;
  // 图标到顶部距离
  top?: string | number;
  // 是否阻止事件传递
  stop?: boolean;
  // 自定义样式对象
  customStyle?: object;
  // 是否旋转
  spin?: boolean;
  // 自定义字体图标前缀
  customPrefix?: string;
}

// 定义组件props
const props = withDefaults(defineProps<Props>(), {
  icon: 'question-circle', // 默认使用问号图标
  size: 20,
  color: 'var(--color-text-primary, #262626)',
  bold: false,
  label: '',
  labelPos: 'right',
  labelSize: '15',
  labelColor: '#606266',
  top: 0,
  stop: false,
  customStyle: () => ({}),
  spin: false,
  customPrefix: 'uicon'
});

// 定义事件
defineEmits(['click']);

// 计算最终图标名称
const finalIconName = computed(() => {
  // 防御性检查
  if (!props.icon) {
    return 'question-circle';
  }
  
  // 处理图标名称转换
  return props.icon;
});

// 不再需要在组件中单独加载字体图标
// 使用全局样式加载字体图标，符合uView Plus官方规范
</script>

<template>
  <!-- 使用uView Plus的u-icon组件 -->
  <UIcon
    :name="finalIconName"
    :size="size"
    :color="color"
    :bold="bold"
    :label="label"
    :label-pos="labelPos"
    :label-size="labelSize"
    :label-color="labelColor"
    :top="top"
    :stop="stop"
    :custom-style="customStyle"
    :custom-prefix="customPrefix"
    :class="{ 'u-icon--spin': spin }"
    @click="$emit('click', $event)"
  />
</template>

<style lang="scss">
/* 定义旋转动画 */
.u-icon--spin {
  animation: u-icon-spin 2s infinite linear;
}

@keyframes u-icon-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>