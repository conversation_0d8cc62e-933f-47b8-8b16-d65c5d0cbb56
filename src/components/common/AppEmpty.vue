<script setup lang="ts">
interface Props {
  /** 标题文字 */
  title?: string
  /** 描述文字 */
  description?: string
  /** 图标名称 */
  icon?: string
  /** 图标大小 */
  iconSize?: string | number
  /** 图标颜色 */
  iconColor?: string
  /** 图片区域大小 */
  imageSize?: string
  /** 是否全屏展示 */
  fullPage?: boolean
  /** 自定义样式 */
  customStyle?: string | object
}

const props = withDefaults(defineProps<Props>(), {
  title: '暂无数据',
  description: '',
  icon: 'inbox',
  iconSize: '64px',
  imageSize: '120px',
  fullPage: false,
});
</script>

<template>
  <view class="app-empty" :class="{ 'app-empty--fullpage': fullPage }" :style="customStyle">
    <!-- 空状态图标 -->
    <view class="app-empty__image" :style="{ height: imageSize }">
      <slot name="image">
        <AppIcon
          :icon="icon"
          :size="iconSize"
          :color="iconColor || 'var(--text-hint, #999999)'"
          class="app-empty__icon"
        />
      </slot>
    </view>

    <!-- 空状态文字 -->
    <view v-if="title || $slots.title" class="app-empty__title">
      <slot name="title">
        {{ title }}
      </slot>
    </view>

    <!-- 空状态描述 -->
    <view v-if="description || $slots.description" class="app-empty__description">
      <slot name="description">
        {{ description }}
      </slot>
    </view>

    <!-- 操作按钮区域 -->
    <view v-if="$slots.default" class="app-empty__actions">
      <slot ></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    padding: var(--spacing-lg, 24px);
    text-align: center;
    width: 100%;

    /* 全屏模式 */
    &--fullpage {
      min-height: 60vh;
    }

    &__image {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      margin-bottom: var(--spacing-md, 16px);
    }

    &__icon {
      color: var(--text-hint, #999);
    }

    &__title {
      font-size: var(--font-size-lg, 18px);
      color: var(--text-primary, #333);
      font-weight: 500;
      margin-bottom: var(--spacing-sm, 8px);
      line-height: 1.4;
    }

    &__description {
      font-size: var(--font-size-md, 16px);
      color: var(--text-secondary, #666);
      margin-bottom: var(--spacing-md, 16px);
      line-height: 1.5;
      max-width: 80%;
    }

    &__actions {
      margin-top: var(--spacing-md, 16px);

      :deep(.app-button) {
        margin: 0 var(--spacing-xs, 4px);
      }
    }
  }

  /* 暗黑模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-empty {
      &__title {
        color: var(--text-primary, #f5f5f5);
      }

      &__description {
        color: var(--text-secondary, #aaa);
      }
    }
  }
</style>
