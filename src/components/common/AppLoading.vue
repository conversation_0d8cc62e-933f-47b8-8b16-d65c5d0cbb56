<script setup lang="ts">
interface Props {
  /** 加载动画类型: circle, spinner, dots */
  type?: 'circle' | 'spinner' | 'dots'
  /** 加载尺寸: small, medium, large */
  size?: 'small' | 'medium' | 'large'
  /** 加载文本 */
  text?: string
  /** 动画颜色 */
  color?: string
  /** 文本颜色 */
  textColor?: string
  /** 是否垂直排列图标和文字 */
  vertical?: boolean
  /** 是否全屏显示 */
  fullscreen?: boolean
  /** 自定义样式 */
  customStyle?: string | object
}

const props = withDefaults(defineProps<Props>(), {
  type: 'spinner',
  size: 'medium',
  text: '加载中...',
  vertical: true,
  fullscreen: false,
});
</script>

<template>
  <view
    class="app-loading"
    :class="[
      `app-loading--type-${type}`,
      `app-loading--size-${size}`,
      {
        'app-loading--fullscreen': fullscreen,
        'app-loading--with-text': $slots.default || text,
        'app-loading--vertical': vertical,
      },
    ]"
    :style="customStyle"
  >
    <!-- 圆形加载动画 -->
    <view v-if="type === 'circle'" class="app-loading__circle">
      <view
        v-for="i in 12"
        :key="i"
        class="app-loading__circle-item"
        :style="{
          backgroundColor: color,
          animationDelay: `${(i - 1) * 0.1}s`,
        }"
      />
    </view>

    <!-- 加载中图标 -->
    <view v-else-if="type === 'spinner'" class="app-loading__spinner">
      <AppIcon icon="spinner" spin size="lg" :color="color || 'var(--color-primary, #FF6B35)'" />
    </view>

    <!-- 点动画 -->
    <view v-else-if="type === 'dots'" class="app-loading__dots">
      <view
        v-for="i in 3"
        :key="i"
        class="app-loading__dot"
        :style="{
          backgroundColor: color || 'var(--color-primary, #FF6B35)',
          animationDelay: `${(i - 1) * 0.2}s`,
        }"
      />
    </view>

    <!-- 加载文本 -->
    <view v-if="$slots.default || text" class="app-loading__text" :style="{ color: textColor }">
      <slot>{{ text }}</slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-loading {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    /* 垂直布局 */
    &--vertical {
      flex-direction: column;
    }

    /* 全屏显示 */
    &--fullscreen {
      position: fixed;
      inset: 0;
      z-index: 999;
      background-color: rgb(255 255 255 / 90%);
      display: flex;
    }

    /* 尺寸 */
    &--size-small {
      .app-loading__circle {
        width: 24px;
        height: 24px;
      }

      .app-loading__circle-item {
        width: 2px;
        height: 6px;
      }

      .app-loading__dots .app-loading__dot {
        width: 6px;
        height: 6px;
        margin: 0 2px;
      }

      .app-loading__text {
        font-size: var(--font-size-sm, 14px);
      }
    }

    &--size-medium {
      .app-loading__circle {
        width: 32px;
        height: 32px;
      }

      .app-loading__circle-item {
        width: 2.5px;
        height: 8px;
      }

      .app-loading__dots .app-loading__dot {
        width: 8px;
        height: 8px;
        margin: 0 3px;
      }

      .app-loading__text {
        font-size: var(--font-size-md, 16px);
      }
    }

    &--size-large {
      .app-loading__circle {
        width: 48px;
        height: 48px;
      }

      .app-loading__circle-item {
        width: 3px;
        height: 12px;
      }

      .app-loading__dots .app-loading__dot {
        width: 12px;
        height: 12px;
        margin: 0 4px;
      }

      .app-loading__text {
        font-size: var(--font-size-lg, 18px);
      }
    }

    /* 圆形加载动画 */
    &__circle {
      position: relative;
      width: 32px;
      height: 32px;
    }

    &__circle-item {
      position: absolute;
      top: 0;
      left: 50%;
      width: 2.5px;
      height: 8px;
      margin-left: -1.25px;
      border-radius: 999px;
      background-color: var(--color-primary, #ff6b35);
      transform-origin: center 16px; /* 半径为容器高度的一半 */
      animation: app-loading-circle 1.2s infinite ease;

      @for $i from 1 through 12 {
        &:nth-child(#{$i}) {
          transform: rotate(#{($i - 1) * 30}deg);
          opacity: 1 - ($i - 1) / 12;
        }
      }
    }

    /* 点动画 */
    &__dots {
      display: flex;
      align-items: center;
    }

    &__dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      margin: 0 3px;
      background-color: var(--color-primary, #ff6b35);
      animation: app-loading-dots 1.4s infinite ease-in-out;
    }

    /* 文本 */
    &__text {
      margin: var(--spacing-sm, 8px);
      color: var(--text-secondary, #666);
      font-size: var(--font-size-md, 16px);
      line-height: 1.4;
    }

    /* 调整水平布局时的间距 */
    &:not(.app-loading--vertical) {
      .app-loading__text {
        margin-left: var(--spacing-sm, 8px);
        margin-top: 0;
      }
    }
  }

  /* 圆形加载动画关键帧 */
  @keyframes app-loading-circle {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0.25;
    }
  }

  /* 点动画关键帧 */
  @keyframes app-loading-dots {
    0%,
    80%,
    100% {
      transform: scale(0.6);
      opacity: 0.5;
    }

    40% {
      transform: scale(1);
      opacity: 1;
    }
  }

  /* 暗黑模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-loading {
      &--fullscreen {
        background-color: rgb(18 18 18 / 90%);
      }

      &__text {
        color: var(--text-secondary, #aaa);
      }
    }
  }
</style>
