<script setup lang="ts">
import AppIcon from '@/components/common/AppIcon.vue';

// 定义组件属性
interface Props {
  title: string;
  icon: string;
  iconColor?: string;
  withSpacing?: boolean;
}

const props = defineProps<Props>();
</script>

<template>
  <div class="section-header" :class="{ 'section-header--with-spacing': withSpacing }">
    <AppIcon 
      :icon="icon" 
      class="section-header__icon" 
      :color="iconColor || 'var(--color-primary)'"
    />
    <h2 class="section-header__title">{{ title }}</h2>
  </div>
</template>

<style lang="scss" scoped>
/* 章节标题组件 */
.section-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm, 8px);
  padding-top: var(--spacing-xs, 4px);
  
  &--with-spacing {
    margin-bottom: var(--spacing-md, 16px);
  }
  
  &__icon {
    color: var(--color-primary, #FF6B35);
    font-size: var(--font-size-lg, 18px);
  }
  
  &__title {
    font-weight: 600;
    font-size: var(--font-size-lg, 18px);
    color: var(--text-primary, #333333);
  }
}
</style> 