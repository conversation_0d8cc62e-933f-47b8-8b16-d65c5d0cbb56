<script setup lang="ts">
import { computed, onMounted, onUnmounted, ref, watch } from 'vue';

interface Day {
  date: Date
  day: number
  month: number
  year: number
  isCurrentMonth: boolean
  isToday: boolean
  isSelected: boolean
  isDisabled: boolean
  hasEvent?: boolean
}

interface Props {
  modelValue?: Date | null // 当前选中的日期
  minDate?: Date // 最小可选日期
  maxDate?: Date // 最大可选日期
  events?: Date[] // 有事件的日期数组
  showControls?: boolean // 是否显示上下月切换控件
  firstDayOfWeek?: 0 | 1 // 每周第一天，0表示周日，1表示周一
  visible?: boolean // 是否显示日历
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: null,
  minDate: () => new Date(new Date().getFullYear() - 10, 0, 1), // 默认10年前
  maxDate: () => new Date(new Date().getFullYear() + 10, 11, 31), // 默认10年后
  events: () => [],
  showControls: true,
  firstDayOfWeek: 1, // 默认周一开始
  visible: false,
});

const emit = defineEmits([
  'update:modelValue',
  'month-change',
  'day-click',
  'update:visible',
  'close',
  'select',
]);

// 状态
const selectedDate = ref<Date | null>(props.modelValue || new Date());
const currentMonth = ref(props.modelValue?.getMonth() ?? new Date().getMonth());
const currentYear = ref(props.modelValue?.getFullYear() ?? new Date().getFullYear());
const showYearMonthSelector = ref(false);

// 计算属性
const selectedYear = computed(() => currentYear.value);
const selectedMonth = computed(() => currentMonth.value + 1); // 月份从1开始显示

const currentYearMonth = computed(() => `${currentYear.value}年${selectedMonth.value}月`);

const weekdays = computed(() => {
  const days
      = props.firstDayOfWeek === 1
        ? ['一', '二', '三', '四', '五', '六', '日']
        : ['日', '一', '二', '三', '四', '五', '六'];
  return days;
});

// 计算当前月份的日期网格
const days = computed((): Day[] => {
  const result: Day[] = [];

  // 当前月第一天
  const firstDayOfMonth = new Date(currentYear.value, currentMonth.value, 1);

  // 当前月最后一天
  const lastDayOfMonth = new Date(currentYear.value, currentMonth.value + 1, 0);

  // 计算当前月第一天是星期几
  let firstWeekday = firstDayOfMonth.getDay(); // 0 是周日

  // 根据 firstDayOfWeek 调整
  if (props.firstDayOfWeek === 1) {
    // 如果周一是一周第一天
    firstWeekday = firstWeekday === 0 ? 6 : firstWeekday - 1;
  }

  // 上个月的最后几天
  const prevMonthLastDay = new Date(currentYear.value, currentMonth.value, 0).getDate();

  // 填充上个月的日期
  for (let i = 0; i < firstWeekday; i++) {
    const day = prevMonthLastDay - firstWeekday + i + 1;
    const prevMonth = currentMonth.value - 1 < 0 ? 11 : currentMonth.value - 1;
    const prevYear = prevMonth === 11 ? currentYear.value - 1 : currentYear.value;
    const date = new Date(prevYear, prevMonth, day);

    result.push({
      date,
      day,
      month: prevMonth,
      year: prevYear,
      isCurrentMonth: false,
      isToday: isToday(date),
      isSelected: isSelected(date),
      isDisabled: isDisabled(date),
      hasEvent: hasEvent(date),
    });
  }

  // 填充当前月的日期
  for (let i = 1; i <= lastDayOfMonth.getDate(); i++) {
    const date = new Date(currentYear.value, currentMonth.value, i);

    result.push({
      date,
      day: i,
      month: currentMonth.value,
      year: currentYear.value,
      isCurrentMonth: true,
      isToday: isToday(date),
      isSelected: isSelected(date),
      isDisabled: isDisabled(date),
      hasEvent: hasEvent(date),
    });
  }

  // 计算需要填充的下个月天数（修改为5行显示，不再是6行）
  const totalDaysNeeded = 35; // 5行 x 7列 = 35，修改为5行而不是6行
  const daysNeeded = totalDaysNeeded - result.length;

  // 只在需要的情况下填充下个月的日期
  if (daysNeeded > 0) {
    for (let i = 1; i <= daysNeeded; i++) {
      const nextMonth = currentMonth.value + 1 > 11 ? 0 : currentMonth.value + 1;
      const nextYear = nextMonth === 0 ? currentYear.value + 1 : currentYear.value;
      const date = new Date(nextYear, nextMonth, i);

      result.push({
        date,
        day: i,
        month: nextMonth,
        year: nextYear,
        isCurrentMonth: false,
        isToday: isToday(date),
        isSelected: isSelected(date),
        isDisabled: isDisabled(date),
        hasEvent: hasEvent(date),
      });
    }
  }

  return result;
});

// 可选年份范围
const availableYears = computed(() => {
  const minYear = props.minDate.getFullYear();
  const maxYear = props.maxDate.getFullYear();
  const years = [];

  for (let year = minYear; year <= maxYear; year++) {
    years.push(year);
  }

  return years;
});

// 是否显示上下月切换控制
const showPrevNextControls = computed(() => props.showControls);

// 辅助函数
function isToday(date: Date): boolean {
  const today = new Date();
  return (
    date.getDate() === today.getDate()
    && date.getMonth() === today.getMonth()
    && date.getFullYear() === today.getFullYear()
  );
}

function isSelected(date: Date): boolean {
  if (!selectedDate.value)
    return false;

  return (
    date.getDate() === selectedDate.value.getDate()
    && date.getMonth() === selectedDate.value.getMonth()
    && date.getFullYear() === selectedDate.value.getFullYear()
  );
}

function isDisabled(date: Date): boolean {
  return date < props.minDate || date > props.maxDate;
}

function hasEvent(date: Date): boolean {
  return props.events.some(
    eventDate =>
      eventDate.getDate() === date.getDate()
      && eventDate.getMonth() === date.getMonth()
      && eventDate.getFullYear() === date.getFullYear(),
  );
}

// 获取日期CSS类
function getDayClasses(day: Day) {
  return {
    'app-calendar__day--not-current-month': !day.isCurrentMonth,
    'app-calendar__day--today': day.isToday,
    'app-calendar__day--selected': day.isSelected,
    'app-calendar__day--disabled': day.isDisabled,
    'app-calendar__day--has-event': day.hasEvent,
    'app-calendar__day--weekend': day.date.getDay() === 0 || day.date.getDay() === 6, // 周六周日
  };
}

// 处理点击日期
function handleDayClick(day: Day) {
  if (day.isDisabled)
    return;

  // 创建一个新的日期对象，明确设置年月日时分秒
  const newDate = new Date(day.year, day.month, day.day, 0, 0, 0);
  
  console.log('日历组件选择日期:', newDate, 'ISO:', newDate.toISOString());
  
  selectedDate.value = newDate;
  emit('update:modelValue', selectedDate.value);
  emit('day-click', selectedDate.value);
  emit('select', selectedDate.value);

  // 如果点击的是非当前月份，则切换月份视图
  if (!day.isCurrentMonth) {
    currentMonth.value = day.month;
    currentYear.value = day.year;
    emit('month-change', { month: day.month, year: day.year });
  }

  // 选择日期后自动关闭
  emit('update:visible', false);
  emit('close');
}

// 处理上个月点击
function handlePrevMonth() {
  if (currentMonth.value === 0) {
    currentMonth.value = 11;
    currentYear.value--;
  }
  else {
    currentMonth.value--;
  }

  emit('month-change', { month: currentMonth.value, year: currentYear.value });
}

// 处理下个月点击
function handleNextMonth() {
  if (currentMonth.value === 11) {
    currentMonth.value = 0;
    currentYear.value++;
  }
  else {
    currentMonth.value++;
  }

  emit('month-change', { month: currentMonth.value, year: currentYear.value });
}

// 处理年份选择
function handleYearSelect(year: number) {
  currentYear.value = year;
}

// 处理月份选择
function handleMonthSelect(month: number) {
  currentMonth.value = month - 1; // 转为0-11
  showYearMonthSelector.value = false;
}

// 处理点击外部区域关闭日历
function handleOverlayClick() {
  emit('update:visible', false);
  emit('close');
}

// 关闭年月选择器的点击外部事件处理
function handleClickOutside(event: MouseEvent) {
  const target = event.target as HTMLElement;
  if (
    showYearMonthSelector.value
    && !target.closest('.app-calendar__title')
    && !target.closest('.app-calendar__selector')
  ) {
    showYearMonthSelector.value = false;
  }

  // 如果点击的不是日历组件内的元素，关闭日历
  if (
    props.visible
    && !target.closest('.app-calendar__content')
    && !target.closest('.remark-icon-button')
  ) {
    emit('update:visible', false);
    emit('close');
  }
}

// 组件挂载时添加点击事件监听
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
});

// 组件卸载时移除监听
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

// 监听modelValue变化
watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      selectedDate.value = newVal;
      // 如果选择的日期不在当前月份，则自动切换到该月
      if (
        newVal.getMonth() !== currentMonth.value
        || newVal.getFullYear() !== currentYear.value
      ) {
        currentMonth.value = newVal.getMonth();
        currentYear.value = newVal.getFullYear();
      }
    }
    else {
      selectedDate.value = new Date(); // Default to today if null
    }
  },
  { immediate: true },
);

// 监听visible变化，当显示时重置到当前日期视图
watch(
  () => props.visible,
  (newVal) => {
    if (newVal) {
      // 如果没有选中日期，则默认显示今天
      if (!selectedDate.value) {
        const today = new Date();
        selectedDate.value = today;
        currentMonth.value = today.getMonth();
        currentYear.value = today.getFullYear();
      }
    }
  },
  { immediate: true },
);
</script>

<template>
  <view v-if="visible" class="app-calendar">
    <!-- 使用transition包裹整个组件，而不是只应用到内容区域 -->
    <transition name="fade">
      <!-- 遮罩层 -->
      <view class="app-calendar__overlay" @click="handleOverlayClick" />
    </transition>

    <!-- 内容区域使用单独的transition -->
    <transition name="slide-up">
      <view class="app-calendar__content">
        <!-- 日历头部 -->
        <view class="app-calendar__header">
          <view class="app-calendar__controls">
            <AppIcon
              v-if="showPrevNextControls"
              icon="chevron-left"
              class="app-calendar__arrow"
              @click="handlePrevMonth"
            />
            <view
              class="app-calendar__title"
              @click="showYearMonthSelector = !showYearMonthSelector"
            >
              {{ currentYearMonth }}
              <AppIcon icon="angle-down" size="sm" class="app-calendar__dropdown-icon" />
            </view>
            <AppIcon
              v-if="showPrevNextControls"
              icon="chevron-right"
              class="app-calendar__arrow"
              @click="handleNextMonth"
            />
          </view>

          <!-- 年月选择器 -->
          <transition name="slide-down">
            <view v-if="showYearMonthSelector" class="app-calendar__selector">
              <view class="app-calendar__selector-row">
                <view class="app-calendar__selector-label">
                  年份
                </view>
                <view class="app-calendar__selector-options">
                  <view
                    v-for="year in availableYears"
                    :key="year"
                    class="app-calendar__selector-option"
                    :class="{ 'app-calendar__selector-option--active': year === selectedYear }"
                    @click="handleYearSelect(year)"
                  >
                    {{ year }}
                  </view>
                </view>
              </view>
              <view class="app-calendar__selector-row">
                <view class="app-calendar__selector-label">
                  月份
                </view>
                <view class="app-calendar__selector-options">
                  <view
                    v-for="month in 12"
                    :key="month"
                    class="app-calendar__selector-option"
                    :class="{ 'app-calendar__selector-option--active': month === selectedMonth }"
                    @click="handleMonthSelect(month)"
                  >
                    {{ month }}月
                  </view>
                </view>
              </view>
            </view>
          </transition>
        </view>

        <!-- 星期标题 -->
        <view class="app-calendar__weekdays">
          <view
            v-for="(weekday, index) in weekdays"
            :key="index"
            class="app-calendar__weekday"
            :class="{ 'app-calendar__weekday--weekend': index === 0 || index === 6 }"
          >
            {{ weekday }}
          </view>
        </view>

        <!-- 日期网格 -->
        <view class="app-calendar__days">
          <view
            v-for="(day, index) in days"
            :key="index"
            class="app-calendar__day"
            :class="getDayClasses(day)"
            @click="handleDayClick(day)"
          >
            <view class="app-calendar__day-content">
              <view class="app-calendar__day-number">
                {{ day.isToday ? '今' : day.day }}
              </view>
              <view v-if="day.hasEvent" class="app-calendar__day-event-dot" />
            </view>
          </view>
        </view>

        <!-- 底部额外信息 -->
        <view v-if="$slots.footer" class="app-calendar__footer">
          <slot name="footer" ></slot>
        </view>
      </view>
    </transition>
  </view>
</template>

<style lang="scss" scoped>
  .app-calendar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: var(--z-index-popup, 1000);

    &__overlay {
      position: fixed;
      inset: 0;
      background-color: rgb(0 0 0 / 40%);
      z-index: -1;
    }

    &__content {
      width: 100%;
      background-color: var(--card-bg, #fff);
      border-radius: var(--radius-lg, 16px) var(--radius-lg, 16px) 0 0;
      box-shadow: var(--shadow-card, 0 2px 8px rgb(0 0 0 / 10%));
      overflow: hidden;
    }

    &__header {
      padding: 12px 16px; /* 减少垂直内边距，使标题栏更紧凑 */
      position: relative;
    }

    &__controls {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    &__title {
      font-size: var(--font-size-medium, 16px); /* 减小标题字体大小 */
      font-weight: 500;
      color: var(--text-primary, #333);
      display: flex;
      align-items: center;
      cursor: pointer;
    }

    &__dropdown-icon {
      margin-left: 4px; /* 减少图标与文本的间距 */
      color: var(--text-secondary, #666);
      font-size: 12px; /* 减小下拉图标的大小 */
    }

    &__arrow {
      width: 28px; /* 减小箭头按钮的大小 */
      height: 28px; /* 减小箭头按钮的大小 */
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      border-radius: 50%;
      color: var(--text-secondary, #666); /* 改变箭头颜色使其不那么突兀 */

      &:hover {
        background-color: var(--bg-secondary, #f5f5f5);
      }
    }

    &__selector {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: var(--card-bg, #fff);
      border-radius: 0 0 var(--radius-card, 12px) var(--radius-card, 12px);
      box-shadow: var(--shadow-card, 0 2px 8px rgb(0 0 0 / 10%));
      z-index: 10;
      padding: 16px;
    }

    &__selector-row {
      margin-bottom: 16px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    &__selector-label {
      font-size: var(--font-size-medium, 16px);
      font-weight: 500;
      color: var(--text-primary, #333);
      margin-bottom: 8px;
    }

    &__selector-options {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
    }

    &__selector-option {
      padding: 6px 12px;
      border-radius: var(--radius-input, 8px);
      background-color: var(--bg-secondary, #f5f5f5);
      font-size: var(--font-size-small, 14px);
      color: var(--text-primary, #333);
      cursor: pointer;

      &--active {
        background-color: var(--color-primary, #ff6b35);
        color: var(--text-inverse, #fff);
      }
    }

    &__weekdays {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      padding: 8px 0;
      border-bottom: 1px solid var(--border-color, #e5e5e5);
    }

    &__weekday {
      text-align: center;
      font-size: var(--font-size-small, 14px);
      color: var(--text-secondary, #666);
      font-weight: 500;

      &--weekend {
        color: var(--color-error, #f44336);
      }
    }

    &__days {
      display: grid;
      grid-template-columns: repeat(7, 1fr);
      grid-template-rows: repeat(5, 1fr); /* 修改为5行而不是6行 */
      gap: 2px;
      padding: 8px;
    }

    &__day-content {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      color: var(--text-primary, #333);
    }

    &__day-number {
      font-size: var(--font-size-small, 14px);
    }

    &__day-event-dot {
      width: 6px;
      height: 6px;
      border-radius: 50%;
      background-color: var(--color-primary, #ff6b35);
      margin-top: 4px;
      display: none;
    }

    &__day {
      position: relative;
      height: 0;
      padding-bottom: 100%; // 创建正方形
      cursor: pointer;
      border-radius: var(--radius-sm, 4px);
      overflow: hidden;

      &--not-current-month {
        opacity: 0.4;
      }

      &--selected {
        background-color: var(--color-primary, #ff6b35);
      }

      &--disabled {
        opacity: 0.3;
        cursor: not-allowed;
      }
      
      &:active {
        opacity: 0.7;
      }

      &:hover {
        background-color: var(--bg-secondary, #f5f5f5);
      }
      
      &--selected:hover,
      &--disabled:hover {
        background-color: initial;
      }

      &--today .app-calendar__day-content {
        color: var(--color-primary, #ff6b35);
        font-weight: 500;
      }

      &--selected .app-calendar__day-content {
        color: var(--text-inverse, #fff);
      }
      
      &--weekend .app-calendar__day-number {
        color: var(--color-error, #f44336);
      }

      &--weekend.app-calendar__day--selected .app-calendar__day-number {
        color: var(--text-inverse, #fff);
      }
      
      &--has-event .app-calendar__day-event-dot {
        display: block;
      }
    }

    &__footer {
      padding: 16px;
      border-top: 1px solid var(--border-color, #e5e5e5);
  }

    /* 多端适配 - 使用子选择器避免重复根选择器 */

  /* #ifdef MP-WEIXIN */
    .app-calendar__selector {
      top: 60px;
  }

  /* #endif */

  /* #ifdef APP-PLUS-IOS */
    .app-calendar__content {
      padding-bottom: env(safe-area-inset-bottom, 0);
  }

  /* #endif */

  /* 动画 */
  .fade-enter-active,
  .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter-from,
  .fade-leave-to {
    opacity: 0;
  }

  .slide-up-enter-active,
  .slide-up-leave-active {
    transition: transform 0.3s ease;
  }

  .slide-up-enter-from,
  .slide-up-leave-to {
    transform: translateY(100%);
  }

  .slide-down-enter-active,
  .slide-down-leave-active {
    transition: all 0.3s ease;
  }

  .slide-down-enter-from,
  .slide-down-leave-to {
    transform: translateY(-20px);
    opacity: 0;
    }
  }
</style>
