<script setup lang="ts">
interface Props {
  /** 分割线方向: horizontal, vertical */
  direction?: 'horizontal' | 'vertical'
  /** 分割线类型: normal, thick, hairline */
  type?: 'normal' | 'thick' | 'hairline'
  /** 文字位置: left, center, right */
  textPosition?: 'left' | 'center' | 'right'
  /** 是否为虚线 */
  dashed?: boolean
  /** 自定义样式 */
  customStyle?: string | object
}

const props = withDefaults(defineProps<Props>(), {
  direction: 'horizontal',
  type: 'normal',
  textPosition: 'center',
  dashed: false,
});
</script>

<template>
  <view
    class="app-divider"
    :class="[
      `app-divider--direction-${direction}`,
      `app-divider--type-${type}`,
      {
        'app-divider--with-text': $slots.default,
        'app-divider--dashed': dashed,
      },
    ]"
    :style="customStyle"
  >
    <view
      v-if="$slots.default && direction === 'horizontal'"
      class="app-divider__text"
      :class="`app-divider__text--${textPosition}`"
    >
      <slot ></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-divider {
    /* 基础样式 */
    position: relative;
    box-sizing: border-box;

    /* 方向: 水平 */
    &--direction-horizontal {
      display: flex;
      align-items: center;
      width: 100%;
      margin: var(--spacing-md, 16px) 0;
      height: 1px; /* 默认高度 */
      border-top: 0;
      border-left: 0;
      border-right: 0;
      border-bottom: 1px solid var(--border-color, #e5e5e5);

      /* 类型 */
      &.app-divider--type-normal {
        border-bottom-width: 1px;
      }

      &.app-divider--type-thick {
        height: 2px;
        border-bottom-width: 2px;
      }

      &.app-divider--type-hairline {
        height: 0.5px;
        border-bottom-width: 0.5px;
      }
    }

    /* 方向: 垂直 */
    &--direction-vertical {
      display: inline-block;
      height: 0.9em;
      margin: 0 var(--spacing-sm, 8px);
      vertical-align: middle;
      border-top: 0;
      border-bottom: 0;
      border-left: 1px solid var(--border-color, #e5e5e5);
      border-right: 0;

      /* 类型 */
      &.app-divider--type-normal {
        border-left-width: 1px;
      }

      &.app-divider--type-thick {
        border-left-width: 2px;
      }

      &.app-divider--type-hairline {
        border-left-width: 0.5px;
      }
    }

    /* 虚线 */
    &--dashed {
      &.app-divider--direction-horizontal {
        border-bottom-style: dashed;
      }

      &.app-divider--direction-vertical {
        border-left-style: dashed;
      }
    }

    /* 文字 */
    &--with-text {
      border-bottom: 0;

      &::before,
      &::after {
        content: '';
        position: absolute;
        top: 50%;
        width: 50%;
        border-bottom: 1px solid var(--border-color, #e5e5e5);
        transform: translateY(-50%);

        /* 应用虚线样式 */
        .app-divider--dashed & {
          border-bottom-style: dashed;
        }

        /* 应用类型样式 */
        .app-divider--type-thick & {
          border-bottom-width: 2px;
        }

        .app-divider--type-hairline & {
          border-bottom-width: 0.5px;
        }
      }

      &::before {
        left: 0;
      }

      &::after {
        right: 0;
      }
    }

    &__text {
      display: inline-block;
      padding: 0 var(--spacing-md, 16px);
      font-size: var(--font-size-sm, 14px);
      color: var(--text-secondary, #666);
      background-color: var(--bg-primary, #fff);
      position: relative;
      z-index: 1;

      /* 文本位置 */
      &--left {
        padding-left: 0;

        .app-divider--with-text::before {
          width: 5%;
        }

        .app-divider--with-text::after {
          width: 95%;
        }
      }

      &--center {
        /* 默认为中间位置 */
      }

      &--right {
        padding-right: 0;

        .app-divider--with-text::before {
          width: 95%;
        }

        .app-divider--with-text::after {
          width: 5%;
        }
      }
    }
  }

  /* 暗黑模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-divider {
      border-color: var(--border-color, #333);

      &--with-text::before,
      &--with-text::after {
        border-color: var(--border-color, #333);
      }

      &__text {
        background-color: var(--bg-primary, #121212);
      }
    }
  }
</style>
