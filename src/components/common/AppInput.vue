<script setup lang="ts">
import AppIcon from '@/components/common/AppIcon.vue';
import { computed, ref, useSlots } from 'vue';

// 定义Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: '',
  },
  type: {
    type: String,
    default: 'text', // text, number, password, digit, etc.
  },
  placeholder: {
    type: String,
    default: '请输入',
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  maxlength: {
    type: Number,
    default: 140,
  },
  clearable: {
    type: Boolean,
    default: false,
  },
  focus: {
    type: Boolean,
    default: false,
  },
  customClass: {
    type: String,
    default: '',
  },
  icon: {
    type: String,
    default: '',
  },
  prefix_text: {
    type: String,
    default: '',
  },
  label: {
    type: String,
    default: '',
  },
  name: {
    type: String,
    default: '',
  },
  readonly: {
    type: Boolean,
    default: false,
  },
  required: {
    type: Boolean,
    default: false,
  },
  password: {
    type: <PERSON>olean,
    default: false,
  },
  showPasswordToggle: {
    type: Boolean,
    default: false,
  },
  border: {
    type: Boolean,
    default: false,
  },
});

// 定义Emits
const emit = defineEmits(['update:modelValue', 'input', 'focus', 'blur', 'confirm', 'clear']);

// 内部状态
const isFocused = ref(props.focus);

// 获取插槽
const slots = useSlots();
const hasPrefixSlot = computed(() => !!slots.prefix);
const hasSuffixSlot = computed(() => !!slots.suffix);

// 处理输入事件
function handleInput(event: any) {
  const value = event.detail.value;
  emit('update:modelValue', value);
  emit('input', value);
}

// 处理聚焦事件
function handleFocus(event: any) {
  isFocused.value = true;
  emit('focus', event);
}

// 处理失焦事件
function handleBlur(event: any) {
  isFocused.value = false;
  emit('blur', event);
}

// 处理确认事件 (键盘确认按钮)
function handleConfirm(event: any) {
  emit('confirm', event.detail.value);
}

// 处理清除按钮点击
function handleClear() {
  emit('update:modelValue', '');
  emit('clear');
}

// 处理密码切换
function togglePasswordVisibility() {
  // Implementation of togglePasswordVisibility
}
</script>

<template>
  <view class="app-input" :class="{'app-input--border': border, 'app-input--disabled': disabled}">
    <!-- 标签 -->
    <text v-if="label" class="app-input__label">
      {{ label }}
      <text v-if="required" class="app-input__required">*</text>
        </text>
    
    <!-- 输入框主体 -->
    <view class="app-input__body">
      <!-- 显示图标 -->
      <AppIcon v-if="icon" :icon="icon" />
      
      <!-- 输入框 -->
    <input
        :value="modelValue"
      :type="type"
      :placeholder="placeholder"
      :disabled="disabled"
        :readonly="readonly"
      :maxlength="maxlength"
        :name="name"
        class="app-input__field"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @confirm="handleConfirm"
    />
      
      <!-- 清除按钮 -->
      <view 
        v-if="clearable && modelValue && !disabled && !readonly" 
        class="app-input__clear"
        @click="handleClear"
      >
        <AppIcon icon="close-circle" />
      </view>
      
      <!-- 密码切换按钮 -->
      <view 
        v-if="password && showPasswordToggle" 
        class="app-input__password-toggle"
        @click="togglePasswordVisibility"
      >
        <AppIcon :icon="showPassword ? 'eye' : 'eye-slash'" />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-input-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    height: 40px; /* 默认高度，可根据需要调整 */
    padding: 0 var(--spacing-sm, 12px);
    background-color: var(--bg-secondary, #f5f5f5);
    border: 1px solid var(--border-color, #e5e5e5);
    border-radius: var(--radius-input, 8px);
    transition:
      border-color var(--transition-fast, 0.2s) ease,
      background-color var(--transition-fast, 0.2s) ease;
    box-sizing: border-box;

    &--focused {
      border-color: var(--color-primary, #ff6b35);
      background-color: var(--bg-primary, #fff);
    }
  }

  .app-input {
    flex: 1;
    height: 100%;
    border: none;
    outline: none;
    padding: 0; /* 清除原生 input padding */
    font-size: var(--font-size-md, 16px);
    color: var(--text-primary, #333);
    background-color: transparent; /* 继承 wrapper 背景色 */

    &::placeholder {
      color: var(--text-hint, #999);
    }
  }

  .app-input__prefix,
  .app-input__suffix {
    display: flex;
    align-items: center;
    color: var(--text-secondary, #666);
  }

  .app-input__prefix {
    margin-right: var(--spacing-xs, 8px);
  }

  .app-input__prefix-text {
    color: var(--text-secondary, #666);
    font-size: var(--font-size-sm, 14px);
  }

  .app-input__suffix {
    margin-left: var(--spacing-xs, 8px);
  }

  .app-input__clear-icon {
    color: var(--text-hint, #999);
    cursor: pointer;
    padding: 4px; /* 增加点击区域 */

    &:active {
      color: var(--text-primary, #333);
    }
  }

  /* 禁用状态 */
  .app-input-wrapper--disabled {
    background-color: var(--bg-secondary, #f5f5f5);
    opacity: 0.7;
    cursor: not-allowed;

    .app-input {
      cursor: not-allowed;
    }
  }
</style>
