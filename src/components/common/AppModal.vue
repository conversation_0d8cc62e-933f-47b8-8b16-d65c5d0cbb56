<script setup lang="ts">
import AppButton from '@/components/common/AppButton.vue'; // Import AppButton if used in default footer
import AppIcon from '@/components/common/AppIcon.vue';
import { ref } from 'vue';

// --- Props ---
const props = defineProps({
  title: {
    type: String,
    default: '提示',
  },
  // If using v-model:
  // modelValue: {
  //   type: Boolean,
  //   default: false,
  // },
  closeOnClickOverlay: {
    type: Boolean,
    default: true, // Allow closing by clicking overlay
  },
  showFooter: {
    type: Boolean,
    default: false, // Default footer visibility
  },
});

// --- Emits ---
const emit = defineEmits(['close', 'confirm']); // Add 'update:modelValue' if using v-model

// --- State ---
const isVisible = ref(false);

// --- Methods ---
function open() {
  console.log('[DEBUG] AppModal opening');
  isVisible.value = true;
  // Optional: emit an open event
}

function close() {
  console.log('[DEBUG] AppModal closing');
  isVisible.value = false;
  emit('close');
  // If using v-model:
  // emit('update:modelValue', false);
}

function handleConfirm() {
  console.log('[DEBUG] AppModal confirm clicked');
  emit('confirm');
  // Typically close after confirm unless parent handles it
  // close();
}

function handleOverlayClick() {
  if (props.closeOnClickOverlay) {
    close();
  }
}

// --- Expose Methods ---
defineExpose({
  open,
  close,
});
</script>

<template>
  <view v-if="isVisible" class="app-modal-overlay" @click.self="handleOverlayClick">
    <view class="app-modal-content">
      <view class="app-modal-header">
        <AppIcon icon="times" size="lg" color="var(--color-text-hint)" />
        <text class="app-modal-title">
          {{ title }}
        </text>
        <view class="app-modal-close" @click="close">
          <AppIcon icon="times" size="lg" color="var(--color-text-hint)" />
        </view>
      </view>
      <scroll-view scroll-y class="app-modal-body">
        <slot ></slot>
        <!-- Default slot for modal content -->
      </scroll-view>
      <view v-if="showFooter" class="app-modal-footer">
        <slot name="footer">
          <!-- Default footer actions or leave empty -->
          <AppButton type="default" size="medium" @click="close">
            取消
          </AppButton>
          <AppButton type="primary" size="medium" @click="handleConfirm">
            确定
          </AppButton>
        </slot>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgb(0 0 0 / 50%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000; /* Ensure modal is on top */
  }

  .app-modal-content {
    width: 85%; /* Match prototype style */
    max-width: 400px; /* Max width for larger screens */
    max-height: 75vh; /* Max height */
    background-color: var(--color-bg-primary, #fff);
    border-radius: var(--radius-card, 18px); /* Use card radius */
    box-shadow: var(--shadow-dialog, 0 8px 30px rgb(0 0 0 / 18%));
    display: flex;
    flex-direction: column;
    overflow: hidden; /* Prevents content overflow */
  }

  .app-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-md, 16px) var(--spacing-md, 20px);
    border-bottom: 1px solid var(--color-border, rgb(0 0 0 / 8%));
    flex-shrink: 0;
  }

  .app-modal-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--color-text-primary, #333);
  }

  .app-modal-close {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;

    &:active {
      background-color: rgb(0 0 0 / 5%);
    }
  }

  .app-modal-body {
    flex: 1; /* Allows body to grow and scroll */
    padding: 5px 0 5px 5px; /* Match prototype padding for tag container */
    overflow-y: auto;

    /* Custom scrollbar (optional, webkit only) */
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #ccc;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #aaa;
    }
  }

  .app-modal-footer {
    padding: var(--spacing-md, 16px);
    border-top: 1px solid var(--color-border, rgb(0 0 0 / 8%));
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm, 8px);
    flex-shrink: 0;

    /* 使用标准:deep()写法处理按钮margin问题，避免使用!important */
    :deep(.app-button) {
      margin: 0;
    }
  }

  /* Ensure tag container styles are applied correctly when used in slot */
  // These styles might be better placed in the parent component or globally if tags are common
  :deep(.tag-container) {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md, 10px);
    padding: var(--spacing-md, 15px); // Padding applied inside the slot content
  }

  :deep(.tag) {
    padding: 8px 16px;
    border-radius: var(--radius-tag, 50px);
    background-color: var(--color-bg-secondary, #f8f9fa);
    color: var(--color-text-secondary, #666);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    user-select: none;
    border: 1px solid transparent;

    &.selected {
      background-color: var(--color-primary-light, #fff5f2);
      color: var(--color-primary, #ff6b35);
      border: 1px solid var(--color-primary-border, #ffcdb7);
      font-weight: 600;
    }

    &:active {
      transform: scale(0.95);
    }
  }
</style>
