<template>
  <view class="app-segmented-control">
    <button
      v-for="option in options"
      :key="option.value"
      class="segmented-btn"
      :class="{ active: modelValue === option.value }"
      type="button"
      @click="$emit('update:modelValue', option.value)"
    >
      {{ option.label }}
    </button>
  </view>
</template>

<script setup lang="ts">
import { defineProps, withDefaults } from 'vue';

interface Option {
  label: string
  value: string | number
}

const props = withDefaults(defineProps<{
  options: Option[]
  modelValue: string | number
}>(), {
  options: () => [],
  modelValue: '',
});
</script>

<style lang="scss" scoped>
.app-segmented-control {
  display: flex;
  background-color: var(--bg-secondary, #f5f5f5);
  border-radius: 8px;
  box-shadow: 0 1px 3px rgb(0 0 0 / 7%);
  overflow: hidden;
  padding: 2px;
}

.segmented-btn {
  flex: 1;
  border: none;
  background: transparent;
  color: var(--text-secondary, #666);
  font-size: 14px;
  font-weight: normal;
  padding: 6px 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 6px;
  min-width: 0;
  height: 32px;
  line-height: 1.2;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
}

.segmented-btn.active {
  background: var(--color-primary, #FF6B35);
  color: var(--text-inverse, #fff);
  font-weight: 500;
  box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}
</style> 