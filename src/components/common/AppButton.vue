<script setup lang="ts">
interface Props {
  /** 按钮类型: primary, success, warning, error, info */
  type?: 'primary' | 'success' | 'warning' | 'error' | 'info' | 'default'
  /** 按钮尺寸: mini, small, medium, large */
  size?: 'mini' | 'small' | 'medium' | 'large'
  /** 是否为朴素按钮 */
  plain?: boolean
  /** 是否禁用 */
  disabled?: boolean
  /** 是否加载中 */
  loading?: boolean
  /** 是否为块级元素 */
  block?: boolean
  /** 是否为圆角按钮 */
  round?: boolean
  /** 是否为圆形按钮 */
  circle?: boolean
  /** 图标名称（使用uView Plus图标名） */
  icon?: string
  /** 按钮表单类型 */
  formType?: 'submit' | 'reset'
}

const props = withDefaults(defineProps<Props>(), {
  type: 'default',
  size: 'medium',
  plain: false,
  disabled: false,
  loading: false,
  block: false,
  round: false,
  circle: false,
});

const emit = defineEmits<{
  (e: 'click', event: MouseEvent): void
}>();

function handleClick(event: MouseEvent) {
  if (props.disabled || props.loading)
    return;
  emit('click', event);
}
</script>

<template>
  <button
    class="app-button"
    :class="[
      `app-button--type-${type}`,
      `app-button--size-${size}`,
      {
        'app-button--plain': plain,
        'app-button--disabled': disabled,
        'app-button--loading': loading,
        'app-button--block': block,
        'app-button--round': round,
        'app-button--circle': circle,
      },
    ]"
    :disabled="disabled || loading"
    @click="handleClick"
  >
    <!-- 加载状态 -->
    <view v-if="loading" class="app-button__loading-icon">
      <AppIcon icon="spinner" spin />
    </view>

    <!-- 图标 -->
    <view v-if="icon && !loading" class="app-button__icon">
      <AppIcon :icon="icon" />
    </view>

    <!-- 按钮文本 -->
    <text
      v-if="$slots.default"
      class="app-button__text"
      :class="{ 'app-button__text--has-icon': icon || loading }"
    >
      <slot ></slot>
    </text>
  </button>
</template>

<style lang="scss" scoped>
  .app-button {
    position: relative;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    text-align: center;
    outline: none;
    transition: all 0.3s;
    user-select: none;
    touch-action: manipulation;
    appearance: none;
    -webkit-tap-highlight-color: transparent;
    font-family: inherit;

    // 基础样式
    border: 1px solid transparent;
    background-color: var(--bg-secondary, #f5f5f5);
    color: var(--text-primary, #333);
    border-radius: var(--radius-button, 8px);

    // 按钮尺寸
    &--size-mini {
      height: 28px;
      padding: 0 12px;
      font-size: 12px;
    }

    &--size-small {
      height: 32px;
      padding: 0 16px;
      font-size: 14px;
    }

    &--size-medium {
      height: 40px;
      padding: 0 24px;
      font-size: 16px;
    }

    &--size-large {
      height: 48px;
      padding: 0 32px;
      font-size: 18px;
    }

    // 按钮类型
    &--type-primary {
      background-color: var(--color-primary, #ff6b35);
      color: var(--text-inverse, #fff);

      &.app-button--plain {
        background-color: transparent;
        color: var(--color-primary, #ff6b35);
        border-color: var(--color-primary, #ff6b35);
      }
    }

    &--type-success {
      background-color: var(--color-success, #4caf50);
      color: var(--text-inverse, #fff);

      &.app-button--plain {
        background-color: transparent;
        color: var(--color-success, #4caf50);
        border-color: var(--color-success, #4caf50);
      }
    }

    &--type-warning {
      background-color: var(--color-warning, #ffc107);
      color: var(--text-inverse, #fff);

      &.app-button--plain {
        background-color: transparent;
        color: var(--color-warning, #ffc107);
        border-color: var(--color-warning, #ffc107);
      }
    }

    &--type-error {
      background-color: var(--color-error, #f44336);
      color: var(--text-inverse, #fff);

      &.app-button--plain {
        background-color: transparent;
        color: var(--color-error, #f44336);
        border-color: var(--color-error, #f44336);
      }
    }

    &--type-info {
      background-color: var(--color-info, #2196f3);
      color: var(--text-inverse, #fff);

      &.app-button--plain {
        background-color: transparent;
        color: var(--color-info, #2196f3);
        border-color: var(--color-info, #2196f3);
      }
    }

    &--type-default {
      background-color: var(--bg-secondary, #f5f5f5);
      color: var(--text-primary, #333);

      &.app-button--plain {
        background-color: transparent;
        border-color: var(--border-color, #e5e5e5);
      }
    }

    // 按钮状态
    &--disabled {
      opacity: 0.6;
      cursor: not-allowed;
      pointer-events: none;
    }

    &--loading {
      opacity: 0.8;
      pointer-events: none;
    }

    // 按钮形状
    &--block {
      display: flex;
      width: 100%;
    }

    &--round {
      border-radius: 999px;
    }

    &--circle {
      border-radius: 50%;
      padding: 0;
      width: 40px;
      height: 40px;

      &.app-button--size-mini {
        width: 28px;
        height: 28px;
      }

      &.app-button--size-small {
        width: 32px;
        height: 32px;
      }

      &.app-button--size-medium {
        width: 40px;
        height: 40px;
      }

      &.app-button--size-large {
        width: 48px;
        height: 48px;
      }
    }

    // 元素样式
    &__icon {
      margin-right: 4px;

      .app-button--circle & {
        margin-right: 0;
      }
    }

    &__loading-icon {
      margin-right: 4px;

      .app-button--circle & {
        margin-right: 0;
      }
    }

    &__text {
      &--has-icon {
        margin-left: 4px;

        .app-button--circle & {
          margin-left: 0;
        }
      }
    }

    // 点击动效
    &:active:not(.app-button--disabled) {
      opacity: 0.9;
      transform: scale(0.98);
    }
  }

  /* 暗黑模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-button {
      &--type-default {
        background-color: var(--bg-secondary, #1e1e1e);
        color: var(--text-primary, #f5f5f5);

        &.app-button--plain {
          border-color: var(--border-color, #333);
        }
      }
    }
  }

  /* 规范化：plain+primary 按钮点击态（橙色背景+白色字体） */
  .app-button--type-primary.app-button--plain:active:not(.app-button--disabled),
  .app-button--type-primary.app-button--plain.active:not(.app-button--disabled) {
    background-color: var(--color-primary, #FF6B35) !important;
    color: var(--text-inverse, #FFFFFF) !important;
    border-color: var(--color-primary, #FF6B35) !important;
  }
  .app-button--type-primary.app-button--plain:active .app-button__text,
  .app-button--type-primary.app-button--plain.active .app-button__text {
    color: var(--text-inverse, #FFFFFF) !important;
  }
</style>
