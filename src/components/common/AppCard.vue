<script setup lang="ts">
import { computed } from 'vue';

interface Props {
  /** 卡片标题 */
  title?: string
  /** 卡片副标题 */
  subtitle?: string
  /** 阴影样式：none, light, default, heavy */
  shadow?: 'none' | 'light' | 'default' | 'heavy'
  /** 圆角大小：none, sm, md, lg, xl */
  radius?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  /** 是否可悬浮高亮 */
  hoverable?: boolean
  /** 内容区域是否取消内边距 */
  noPadding?: boolean
  /** 自定义样式 */
  customStyle?: string | object
  /** 自定义背景色或渐变色 */
  background?: string
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  shadow: 'default',
  radius: 'md',
  hoverable: false,
  noPadding: false,
  background: '',
});

const emit = defineEmits<{
  /** 点击卡片事件 */
  (e: 'click', event: Event): void
}>();

// 计算最终样式，优先使用background prop
const finalStyle = computed(() => {
  // 合并自定义样式和背景色
  const styleObj: any = typeof props.customStyle === 'string' ? {} : props.customStyle || {};
  if (props.background) {
    styleObj.background = props.background;
  }
  return typeof props.customStyle === 'string'
    ? `${props.customStyle}${props.background ? `;background:${props.background}` : ''}`
    : styleObj;
});

// 处理卡片点击
function handleClick(event: Event) {
  emit('click', event);
}
</script>

<template>
  <view
    class="app-card"
    :class="[
      `app-card--shadow-${shadow}`,
      `app-card--radius-${radius}`,
      { 'app-card--hoverable': hoverable },
    ]"
    :style="finalStyle"
    @click="handleClick"
  >
    <!-- 卡片头部 -->
    <view v-if="$slots.header || title || subtitle" class="app-card__header">
      <slot name="header">
        <view v-if="title || subtitle" class="app-card__header-content">
          <text v-if="title" class="app-card__title">
            {{ title }}
          </text>
          <text v-if="subtitle" class="app-card__subtitle">
            {{ subtitle }}
          </text>
        </view>
      </slot>
      <slot name="header-extra" ></slot>
    </view>

    <!-- 卡片内容 -->
    <view class="app-card__content" :class="{ 'app-card__content--no-padding': noPadding }">
      <slot ></slot>
    </view>

    <!-- 卡片底部 -->
    <view v-if="$slots.footer" class="app-card__footer">
      <slot name="footer" ></slot>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-card {
    position: relative;
    box-sizing: border-box;
    background-color: var(--card-bg, #fff);
    /* margin-bottom 移除，由页面控制卡片间距 */
    overflow: hidden;
    transition: all 0.3s ease;

    /* 阴影样式 */
    &--shadow-none {
      box-shadow: none;
    }

    &--shadow-light {
      box-shadow: 0 1px 5px rgb(0 0 0 / 5%);
    }

    &--shadow-default {
      box-shadow: var(--shadow-card, 0 2px 8px rgb(0 0 0 / 10%));
    }

    &--shadow-heavy {
      box-shadow: var(--shadow-raised, 0 4px 16px rgb(0 0 0 / 12%));
    }

    /* 圆角样式 */
    &--radius-none {
      border-radius: 0;
    }

    &--radius-sm {
      border-radius: var(--radius-sm, 4px);
    }

    &--radius-md {
      border-radius: var(--radius-card, 12px);
    }

    &--radius-lg {
      border-radius: var(--radius-lg, 16px);
    }

    &--radius-xl {
      border-radius: var(--radius-xl, 20px);
    }

    /* 悬浮效果 */
    &--hoverable {
      cursor: pointer;

      &:hover,
      &:active {
        box-shadow: var(--shadow-raised, 0 4px 16px rgb(0 0 0 / 12%));
        transform: translateY(-2px);
      }
    }

    /* 卡片头部 */
    &__header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px; // 头部内边距缩小
      border-bottom: 1px solid var(--border-color, #e5e5e5);
    }

    &__header-content {
      display: flex;
      flex-direction: column;
    }

    &__title {
      font-size: var(--font-size-lg, 18px);
      font-weight: 600;
      color: var(--text-primary, #333);
      line-height: 1.4;
    }

    &__subtitle {
      font-size: var(--font-size-sm, 14px);
      color: var(--text-secondary, #666);
      margin-top: var(--spacing-xs, 4px);
      line-height: 1.4;
    }

    /* 卡片内容 */
    &__content {
      padding: 12px; // 内容区内边距缩小

      &--no-padding {
        padding: 0;
      }
    }

    /* 卡片底部 */
    &__footer {
      padding: 12px; // 底部内边距缩小
      border-top: 1px solid var(--border-color, #e5e5e5);
      display: flex;
      justify-content: flex-end;
      flex-wrap: wrap;
      gap: var(--spacing-sm, 8px);
    }
  }

  /* 暗黑模式适配 */
  @media (prefers-color-scheme: dark) {
    .app-card {
      &__header,
      &__footer {
        border-color: var(--border-color, #333);
      }
    }
  }
</style>
