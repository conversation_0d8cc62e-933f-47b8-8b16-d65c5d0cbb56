<script setup lang="ts">
import { computed } from 'vue';
import { getPlatformStyle } from '@/utils/platform';
import AppIcon from './AppIcon.vue';

// 定义Tab项目接口
interface TabItem {
  /** 显示标签文本 */
  label: string
  /** 图标名称 */
  icon: string
  /** 页面路径 */
  pagePath: string
}

// 定义Props
interface Props {
  /** 当前激活的选项卡索引 */
  activeIndex: number
  /** 选项卡列表 */
  tabs: TabItem[]
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  /** 切换选项卡 */
  (e: 'change', index: number): void
}>();

// 根据平台获取底部安全区域高度
const safeAreaBottom = computed(() => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  if (systemInfo.platform === 'ios') {
    return 'env(safe-area-inset-bottom)';
  }
  // #endif
  return '0';
});

// 处理选项卡点击
function handleTabClick(index: number) {
  if (index !== props.activeIndex) {
    emit('change', index);
  }
}

// 将常用图标名称映射到uView Plus图标名称
const iconMap: Record<string, string> = {
  'home': 'home',
  'list': 'order',
  'pie-chart': 'integral',
  'chart-pie': 'integral',
  'account': 'account',
  'user': 'account'
};

// 获取实际图标名称
function getIconName(iconName: string): string {
  return iconMap[iconName] || iconName;
}
</script>

<template>
  <view
    class="app-tab-bar"
    :style="{
      paddingBottom: safeAreaBottom,
    }"
  >
    <!-- 先渲染tab项 -->
    <view
      v-for="(tab, index) in tabs"
      :key="index"
      class="app-tab-bar__item"
      :class="{ 'app-tab-bar__item--active': index === activeIndex }"
      @click="handleTabClick(index)"
    >
      <AppIcon
        :icon="getIconName(tab.icon)"
        size="20"
        :color="index === activeIndex ? 'var(--color-primary)' : 'var(--text-tertiary)'"
      />
      <text class="app-tab-bar__label">{{ tab.label }}</text>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .app-tab-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
    display: flex;
    justify-content: space-around;
    align-items: center;
  height: var(--tab-bar-height, 50px);
  background-color: var(--bg-primary, #ffffff);
  box-shadow: 0 -1px 5px rgba(0, 0, 0, 0.05);
  z-index: 999;
  
    &__item {
    flex: 1;
      display: flex;
      flex-direction: column;
    align-items: center;
      justify-content: center;
    height: 100%;
    transition: all 0.2s;

      &--active {
      color: var(--color-primary, #FF6B35);
      }
    }

    &__label {
      font-size: 12px;
    margin-top: 2px;
    color: inherit;
      }
    }

/* iOS适配 */
  /* #ifdef APP-PLUS */
  .app-tab-bar {
  padding-bottom: env(safe-area-inset-bottom);
}
  /* #endif */
</style>
