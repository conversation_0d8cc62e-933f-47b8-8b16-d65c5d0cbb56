/**
 * 通用API响应体结构
 * (如果后端不遵循此结构，请删除或修改)
 */
export interface BaseResponse<T = any> {
  code: number // 业务状态码，例如 0 表示成功
  message: string // 提示信息
  data: T // 实际数据
}

/**
 * 用户信息接口
 */
export interface UserInfo {
  userId: string
  name?: string
  avatar?: string
  phone?: string
  email?: string
  // 其他可能的用户信息字段
}

/**
 * 登录接口凭证类型
 */
export interface LoginCredentials {
  phone: string
  smsCode: string
  // 可以添加其他登录方式所需的字段，如 password, wechatCode 等
}

/**
 * 登录接口成功响应的数据结构
 * 请确保这里的字段与后端实际返回以及 mocks/auth.mock.ts 中的 mockLoginSuccess 一致
 */
export interface LoginResponseData {
  userId: string
  token: string
  refreshToken: string
  expires: number // Token 过期时间戳 (毫秒)
  refreshExpires: number // Refresh Token 过期时间戳 (毫秒)
  needSetupSecurity?: boolean // 是否需要进行安全设置（如手势密码）
  userInfo?: UserInfo // 使用UserInfo类型
}

/**
 * 发送短信验证码的参数类型
 */
export interface SmsCodeParams {
  phone: string
  type: 'register' | 'login' | 'reset' | 'verify' // 验证码类型
}

/**
 * 发送短信验证码成功响应的数据结构
 * (如果后端不遵循此结构，请删除或修改)
 */
export interface SmsCodeResponseData {
  expireIn: number // 验证码有效时间（秒）
  requestId?: string // 请求ID，可选
}

/**
 * 刷新令牌请求参数
 */
export interface RefreshTokenParams {
  refreshToken: string
}

/**
 * 错误响应结构
 */
export interface ErrorResponse {
  code: number
  message: string
  data?: any
}

// 其他接口相关的类型定义可以继续添加在这里...
