// 图表通用类型定义

/**
 * 柱状图数据项
 */
export interface BarChartDataItem {
  value: number;
  itemStyle?: {
    color?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * 柱状图系列
 */
export interface BarChartSeries {
  name: string;
  data: BarChartDataItem[];
  [key: string]: any;
}

/**
 * 柱状图数据
 */
export interface BarChartData {
  xAxisData: string[];
  series: BarChartSeries[];
  [key: string]: any;
}

/**
 * 饼图数据项
 */
export interface PieChartDataItem {
  name: string;
  value: number;
  itemStyle?: {
    color?: string;
    [key: string]: any;
  };
  [key: string]: any;
}

/**
 * 饼图系列
 */
export interface PieChartSeries {
  name: string;
  data: PieChartDataItem[];
  [key: string]: any;
}

/**
 * 饼图数据
 */
export interface PieChartData {
  series: PieChartSeries[];
  radius?: string | string[];
  legendPosition?: 'top' | 'bottom' | 'left' | 'right';
  animation?: boolean;
  [key: string]: any;
}

/**
 * 图表类型
 */
export type ChartType = 'bar' | 'pie';

/**
 * 通用图表数据
 */
export type ChartData = BarChartData | PieChartData;

/**
 * 分类统计数据
 */
export interface CategoryPercentage {
  id: string;
  name: string;
  icon: string;
  color: string;
  amount: number;
  percentage: number;
  percentageText?: string;
} 