/**
 * 交易记录类型定义
 */

/**
 * 交易模式枚举（expense-支出，income-收入，transfer-转账）
 */
export enum ModeType {
  EXPENSE = 'expense',
  INCOME = 'income',
  TRANSFER = 'transfer'
}

/**
 * 交易状态枚举
 */
export enum TransactionStatus {
  PENDING = 'pending',   // 待处理
  COMPLETED = 'completed', // 已完成
  CANCELED = 'canceled',  // 已取消
  FAILED = 'failed'       // 失败
}

export interface Transaction {
  id: number | string // 后端返回可能是数字或字符串，前端统一处理
  type: 'income' | 'expense' | 'transfer' // 交易类型：收入、支出或转账
  amount: number // 交易金额
  date: string // 交易日期时间 (YYYY-MM-DD HH:MM:SS 或 ISO 8601 格式)
  category: string | number | TransactionCategory // 分类ID，只允许string|number，或者完整的分类对象
  account?: TransactionAccount // 关联的账户 (可选)
  notes?: string // 备注信息 (可选)
  tags?: string[] // 标签 (可选)
  location?: any // 地点 (可选)
  status?: TransactionStatus // 交易状态 (可选)
  // ... 可根据需要添加其他字段，如 recurring (是否周期性) 等
}

/**
 * 交易分类类型
 */
export interface TransactionCategory {
  id: number | string
  name: string // 分类名称，例如 "餐饮", "交通"
  icon: string // 分类图标名称 (例如 Font Awesome 图标名 "utensils", "bus")
  type: 'income' | 'expense' // 分类属于收入还是支出
  parentId?: number | string // 父分类ID (用于支持多级分类)
}

/**
 * 交易账户类型
 */
export interface TransactionAccount {
  id: number | string
  name: string // 账户名称，例如 "现金", "支付宝", "招商银行储蓄卡"
  type: string // 账户类型，例如 "cash", "online", "bank", "credit_card"
  balance?: number // 账户余额 (可选，可能由后端单独接口提供)
  icon?: string // 账户图标 (可选)
}

// --- API 相关类型 (从 api.ts 移过来或在这里统一定义/导出) ---

/**
 * 获取交易列表的查询参数
 */
export interface TransactionListParams {
  page?: number // 页码
  pageSize?: number // 每页数量
  startDate?: string // 开始日期 (YYYY-MM-DD)
  endDate?: string // 结束日期 (YYYY-MM-DD)
  type?: 'income' | 'expense' // 按类型筛选
  categoryId?: number | string // 按分类ID筛选
  accountId?: number | string // 按账户ID筛选
  keyword?: string // 按备注/标签等关键词搜索
  // ... 其他筛选/排序参数
}

/**
 * 获取交易列表的响应体
 */
export interface TransactionListResponse {
  items: Transaction[] // 当前页的交易列表
  pagination: {
    page: number // 当前页码
    pageSize: number // 每页数量
    total: number // 总记录数
    totalPages: number // 总页数
  }
}

/**
 * 创建交易记录的请求体 (Payload)
 * 通常不包含 id，可能包含关联 ID
 */
export type CreateTransactionPayload = Omit<Transaction, 'id' | 'category' | 'account'> & {
  categoryId?: number | string
  accountId?: number | string
}
/**
 * 更新交易记录的请求体 (Payload)
 * 通常是部分更新，所有字段可选
 */
export type UpdateTransactionPayload = Partial<CreateTransactionPayload>

