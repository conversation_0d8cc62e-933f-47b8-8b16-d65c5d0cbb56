/**
 * 聊天相关类型定义
 */

/**
 * 消息类型枚举
 */
export enum MessageType {
  TEXT = 'text',
  IMAGE = 'image',
  VOICE = 'voice',
  TRANSACTION = 'transaction',
  SYSTEM = 'system'
}

/**
 * 消息来源枚举
 */
export enum MessageSource {
  USER = 'user',
  AI = 'ai',
  SYSTEM = 'system'
}

/**
 * 消息状态枚举
 */
export enum MessageStatus {
  SENDING = 'sending',
  SENT = 'sent',
  FAILED = 'failed'
}

/**
 * 基础消息接口
 */
export interface BaseMessage {
  id: string;
  type: MessageType;
  source: MessageSource;
  createdAt: number;
  status?: MessageStatus;
}

/**
 * 文本消息
 */
export interface TextMessage extends BaseMessage {
  type: MessageType.TEXT;
  content: string;
}

/**
 * 图片消息
 */
export interface ImageMessage extends BaseMessage {
  type: MessageType.IMAGE;
  imageUrl: string;
  thumbnail?: string;
}

/**
 * 语音消息
 */
export interface VoiceMessage extends BaseMessage {
  type: MessageType.VOICE;
  audioUrl: string;
  duration: number; // 语音时长，单位秒
  transcript?: string; // 语音转文字内容，可选
}

/**
 * 交易消息特有数据
 */
export interface TransactionData {
  id: string;
  amount: number;
  categoryId: string;
  categoryName: string;
  categoryIcon: string;
  date: string;
  description: string;
}

/**
 * 交易消息
 */
export interface TransactionMessage extends BaseMessage {
  type: MessageType.TRANSACTION;
  transaction: TransactionData;
}

/**
 * 系统消息
 */
export interface SystemMessage extends BaseMessage {
  type: MessageType.SYSTEM;
  content: string;
}

/**
 * 消息联合类型
 */
export type Message = TextMessage | ImageMessage | VoiceMessage | TransactionMessage | SystemMessage;

/**
 * 发送消息的请求体
 */
export interface ChatMessage {
  content: string;
  type: MessageType;
  resourceUrl?: string; // 图片或语音URL
  metadata?: Record<string, any>; // 额外数据
}

/**
 * 发送消息响应体
 */
export interface ChatMessageResponse {
  code: number;
  message: string;
  data: Message;
}

/**
 * 聊天历史记录响应体
 */
export interface ChatHistoryResponse {
  code: number;
  message: string;
  data: {
    messages: Message[];
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
  };
}

/**
 * 语音识别响应体
 */
export interface VoiceRecognitionResponse {
  code: number;
  message: string;
  data: {
    transcript: string;
    confidence: number;
    possibleTransaction?: TransactionData;
  };
}

/**
 * 智能对话建议选项
 */
export interface ChatSuggestion {
  id: string;
  text: string;
  type: 'quick' | 'transaction' | 'analysis';
  icon?: string;
} 