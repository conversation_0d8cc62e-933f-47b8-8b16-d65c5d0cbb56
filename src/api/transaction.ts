// 假设类型已定义在 @/types/api 或类似位置
import type {
  CreateTransactionPayload,
  Transaction,
  TransactionListParams,
  TransactionListResponse,
  UpdateTransactionPayload,
  TransactionAccount,
} from '@/types/transaction';
import { request } from '@/utils/request';
// 引入分类API，用于类型
import transactionMock from './mocks/transaction.mock'; // 导入 Mock 文件

const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // 读取环境变量

/**
 * 获取交易记录列表
 * @param params - 查询参数 (分页, 筛选条件等)
 * @returns Promise<TransactionListResponse>
 */
export function getTransactions(params: TransactionListParams): Promise<TransactionListResponse> {
  // 检查是否使用Mock数据
  if (useMock) {
    // 使用Mock数据
    return transactionMock.getTransactionList(params);
  }
  
  // 使用真实API
  return request.get('/api/v1/transactions', { params });
}

/**
 * 创建新的交易记录
 * @param payload - 交易数据
 * @returns Promise<Transaction> 新创建的交易详情
 */
export function createTransaction(payload: CreateTransactionPayload): Promise<Transaction> {
  if (useMock) {
    return transactionMock.createTransaction(payload);
  }
  
  return request.post('/api/v1/transactions', payload);
}

/**
 * 获取单个交易记录详情
 * @param id - 交易ID
 * @returns Promise<Transaction>
 */
export function getTransactionById(id: string): Promise<Transaction> {
  if (useMock) {
    return transactionMock.getTransactionById(id);
  }
  
  return request.get(`/api/v1/transactions/${id}`);
}

/**
 * 更新交易记录
 * @param id - 交易ID
 * @param payload - 更新的数据
 * @returns Promise<Transaction> 更新后的交易详情
 */
export function updateTransaction(
  id: string,
  payload: UpdateTransactionPayload,
): Promise<Transaction> {
  if (useMock) {
    return transactionMock.updateTransaction(id, payload);
  }
  
  return request.put(`/api/v1/transactions/${id}`, payload);
}

/**
 * 删除交易记录
 * @param id - 交易ID
 * @returns Promise<boolean> 是否成功
 */
export function deleteTransaction(id: string): Promise<boolean> {
  if (useMock) {
    return transactionMock.deleteTransaction(id);
    }
  
  return request.delete(`/api/v1/transactions/${id}`).then(() => true);
}

/**
 * 新增：获取指定月份的交易记录
 * @param year 年份
 * @param month 月份 (1-12)
 * @returns Promise<Transaction[]>
 */
export function getMonthlyTransactions(year: number, month: number): Promise<Transaction[]> {
  if (useMock) {
    // 如果启用 Mock，返回 Mock 数据
    console.log(`[API Mock] getMonthlyTransactions called for ${year}-${month}`);
    // 确保 mock 函数存在再调用
    return transactionMock?.getMonthlyTransactions
      ? transactionMock.getMonthlyTransactions(year, month)
      : Promise.reject('Mock function getMonthlyTransactions not implemented');
  }
  // 否则，发起真实 API 请求 (此处为占位符)
  console.log(`[API Real] getMonthlyTransactions called for ${year}-${month}`);
  // 示例: return request.get(`/api/v1/transactions/monthly`, { params: { year, month } });
  // 暂时返回空数组作为真实 API 的占位符
  return Promise.resolve([]);
}

/**
 * 获取所有账户
 * @returns Promise<TransactionAccount[]> 账户列表
 */
export function getAccounts(): Promise<TransactionAccount[]> {
  if (useMock) {
    return transactionMock.getAllAccounts();
  }
  
  return request.get('/api/v1/accounts');
}

/**
 * 批量添加示例数据
 * @returns Promise<boolean> 是否成功
 */
export function addSampleTransactions(): Promise<boolean> {
  if (useMock) {
    return transactionMock.addSampleTransactions();
  }
  // 在真实环境中，这可能是调用一个专门的API端点
  return request.post('/api/v1/transactions/samples').then(() => true);
}

/**
 * 清空所有交易记录（仅用于开发/测试环境）
 * @returns Promise<boolean> 是否成功
 */
export function clearAllTransactions(): Promise<boolean> {
  if (useMock) {
    return transactionMock.clearAllTransactions();
  }
  // 在真实环境中，这可能不存在或受到严格限制
  return request.delete('/api/v1/transactions/all').then(() => true);
}

// 为了兼容性，我们保留一个重定向函数，调用category.ts中的函数
export { getAllCategories } from './category';
