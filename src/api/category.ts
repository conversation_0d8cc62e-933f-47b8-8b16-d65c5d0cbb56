import type { TransactionCategory } from '@/types/transaction';
import { request } from '@/utils/request';
import categoryMock from './mocks/category.mock'; // 导入 Mock 文件

/**
 * 获取所有分类
 * @returns Promise<TransactionCategory[]> 所有分类列表
 */
export function getAllCategories(): Promise<TransactionCategory[]> {
  if (import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    return categoryMock.getAllCategories();
  }
  return request.get('/api/v1/categories');
}

/**
 * 根据类型获取分类
 * @param type 分类类型: 'income' | 'expense'
 * @returns Promise<TransactionCategory[]> 指定类型的分类列表
 */
export function getCategoriesByType(type: 'income' | 'expense'): Promise<TransactionCategory[]> {
  if (import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    return categoryMock.getCategoriesByType(type);
  }
  return request.get(`/api/v1/categories/type/${type}`);
}

/**
 * 创建新分类
 * @param category 分类数据(不含id)
 * @returns Promise<TransactionCategory> 创建后的分类数据
 */
export function createCategory(
  category: Omit<TransactionCategory, 'id'>,
): Promise<TransactionCategory> {
  if (import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    return categoryMock.createCategory(category);
  }
  return request.post('/api/v1/categories', category);
}

/**
 * 更新分类
 * @param id 分类ID
 * @param updates 更新的分类数据
 * @returns Promise<TransactionCategory> 更新后的分类数据
 */
export function updateCategory(
  id: string | number,
  updates: Partial<Omit<TransactionCategory, 'id'>>,
): Promise<TransactionCategory> {
  if (import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    return categoryMock.updateCategory(id, updates);
  }
  return request.put(`/api/v1/categories/${id}`, updates);
}

/**
 * 删除分类
 * @param id 分类ID
 * @returns Promise<boolean> 删除结果
 */
export function deleteCategory(id: string | number): Promise<boolean> {
  if (import.meta.env.VITE_USE_MOCK_DATA === 'true') {
    return categoryMock.deleteCategory(id);
  }
  return request.delete(`/api/v1/categories/${id}`);
}
