// src/api/asset.ts
// Placeholder for real API calls. Implement actual API requests here later.
import type { Asset, AssetType } from '@/types/asset'; // Assuming types exist
// Assuming request util exists

// Placeholder implementation - replace with actual API calls
export async function getAssets(): Promise<Asset[]> {
  console.warn('[API STUB] getAssets called, returning empty array.');
  // Example: return request.get('/api/v1/assets');
  return Promise.resolve([]);
}

export async function getAssetTypes(): Promise<AssetType[]> {
  console.warn('[API STUB] getAssetTypes called, returning empty array.');
  // Example: return request.get('/api/v1/asset-types');
  return Promise.resolve([]);
}

export async function getAssetById(id: string): Promise<Asset | undefined> {
  console.warn(`[API STUB] getAssetById called for ${id}, returning undefined.`);
  // Example: return request.get(`/api/v1/assets/${id}`);
  return Promise.resolve(undefined);
}

export async function getBudgetData(): Promise<{
  total: number
  used: number
  remaining: number
}> {
  console.warn('[API STUB] getBudgetData called, returning zero budget.');
  // Example: return request.get('/api/v1/budget');
  return Promise.resolve({ total: 0, used: 0, remaining: 0 });
}

export async function createAsset(newAsset: Omit<Asset, 'id'>): Promise<Asset> {
  console.warn('[API STUB] createAsset called, returning dummy asset.', newAsset);
  // Example: return request.post('/api/v1/assets', newAsset);
  // Return a dummy object matching the Asset structure for now
  return Promise.resolve({
    ...newAsset,
    id: `real_asset_${Date.now()}`,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    balance: newAsset.balance || 0, // Ensure balance exists
  });
}

export async function updateAsset(id: string, payload: Partial<Asset>): Promise<Asset> {
  console.warn(
    `[API STUB] updateAsset called for ${id}, returning partial payload as Asset.`,
    payload,
  );
  // Example: return request.put(`/api/v1/assets/${id}`, payload);
  // Return a dummy object matching the Asset structure for now
  return Promise.resolve({
    id,
    name: 'Updated Asset', // Dummy name
    type: 'other', // Dummy type
    balance: 0, // Dummy balance
    ...payload,
    updatedAt: new Date().toISOString(),
  } as Asset);
}

export async function deleteAsset(id: string): Promise<void> {
  console.warn(`[API STUB] deleteAsset called for ${id}.`);
  // Example: return request.delete(`/api/v1/assets/${id}`);
  return Promise.resolve();
}
