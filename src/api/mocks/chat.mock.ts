/**
 * 聊天模块的模拟数据和接口
 */
import { v4 as uuidv4 } from 'uuid';
import { useCategoryStore } from '@/stores/category.store';
import { storage } from '@/utils/storage';

// 模拟延迟时间
const MOCK_DELAY = 800;

// 模拟网络延迟的工具函数
const mockDelay = (ms = MOCK_DELAY) => {
  return new Promise(resolve => setTimeout(resolve, ms));
};

// 使用统一的存储键，从storage工具导入
const CHAT_STORAGE_KEY = storage.CHAT_MESSAGES_KEY;
const CONFIRMED_TRANSACTIONS_KEY = storage.CONFIRMED_TRANSACTIONS_KEY;

// 用于智能分析用户输入的关键词
const EXPENSE_KEYWORDS = ['花了', '消费', '支出', '买了', '付款', '交了', '缴费', '充值', '花费', '支付'];
const INCOME_KEYWORDS = ['收入', '赚了', '工资', '报销', '收款', '收到', '奖金', '分红', '利息', '退款'];
const CATEGORY_KEYWORDS = {
  'cat-1': ['吃饭', '午餐', '晚餐', '外卖', '餐厅', '食物', '早餐'],
  'cat-2': ['购物', '淘宝', '京东', '买', '衣服', '鞋子', '包', '电器'],
  'cat-4': ['打车', '地铁', '公交', '高铁', '飞机', '火车', '出租车', '油费', '加油', '停车费'],
  'cat-6': ['水果', '苹果', '香蕉', '橙子', '樱桃', '水果店'],
  'cat-9': ['电影', '游戏', '唱歌', '娱乐', '门票', '演唱会'],
  'cat-18': ['医院', '药', '看病', '体检', '诊所', '医疗'],
  'cat-20': ['学费', '书', '培训', '课程', '辅导', '教育']
};

/**
 * 从本地存储加载消息
 */
const loadMessagesFromStorage = () => {
  try {
    const data = storage.getItem(CHAT_STORAGE_KEY);
    return data || [];
  } catch (error) {
    console.error('Failed to load chat messages from storage', error);
    return [];
  }
};

/**
 * 保存消息到本地存储
 */
const saveMessagesToStorage = (messages) => {
  try {
    storage.setItem(CHAT_STORAGE_KEY, messages);
  } catch (error) {
    console.error('Failed to save chat messages to storage', error);
  }
};

/**
 * 从消息中提取交易信息
 */
const extractTransactionInfo = (text: string): TransactionRecognized | null => {
  console.log('提取交易信息:', text);
  
  // 简单的模式匹配来识别交易
  // 收入模式: "收入 100元 工资"
  // 支出模式: "支出 50元 餐饮"
  // 带日期的模式: "今天支出 30元 交通" 或 "昨天收入 200元 奖金"
  
  // 获取分类Store以匹配分类ID
  const categoryStore = useCategoryStore();
  
  // 1. 尝试匹配简单模式
  const simplePattern = /(收入|支出)\s*(\d+(?:\.\d+)?)\s*元\s*(.+)/;
  const datePattern = /(今天|昨天|前天|\d{4}[\/\-]\d{1,2}[\/\-]\d{1,2})\s*(收入|支出)\s*(\d+(?:\.\d+)?)\s*元\s*(.+)/;
  
  let match;
  let transaction: TransactionRecognized | null = null;
  
  if ((match = datePattern.exec(text)) !== null) {
    // 带日期的模式
    const dateText = match[1];
    const type = match[2] === '收入' ? 'income' : 'expense';
    const amount = parseFloat(match[3]);
    const categoryText = match[4].trim();
    
    // 尝试查找匹配的分类
    let categoryId = '';
    if (type === 'income') {
      const category = categoryStore.incomeCategories.find(c => 
        c.name.includes(categoryText) || categoryText.includes(c.name)
      );
      categoryId = category?.id || '';
    } else {
      const category = categoryStore.expenseCategories.find(c => 
        c.name.includes(categoryText) || categoryText.includes(c.name)
      );
      categoryId = category?.id || '';
    }
    
    if (!categoryId) {
      console.log('未找到匹配的分类, 使用默认分类');
      categoryId = type === 'income' ? 'income-01' : 'expense-01';
    }
    
    // 处理日期
    let date = new Date();
    if (dateText === '昨天') {
      date.setDate(date.getDate() - 1);
    } else if (dateText === '前天') {
      date.setDate(date.getDate() - 2);
    } else if (dateText !== '今天') {
      // 尝试解析具体日期
      date = new Date(dateText);
    }
    
    // 创建交易对象
    transaction = {
      id: `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type,
      amount: type === 'income' ? amount : -amount,
      date,
      categoryId,
      categoryName: categoryText,
      description: `${dateText}${type === 'income' ? '收入' : '支出'} ${amount}元 ${categoryText}`
    };
  } else if ((match = simplePattern.exec(text)) !== null) {
    // 简单模式（无日期）
    const type = match[1] === '收入' ? 'income' : 'expense';
    const amount = parseFloat(match[2]);
    const categoryText = match[3].trim();
    
    // 尝试查找匹配的分类
    let categoryId = '';
    if (type === 'income') {
      const category = categoryStore.incomeCategories.find(c => 
        c.name.includes(categoryText) || categoryText.includes(c.name)
      );
      categoryId = category?.id || '';
    } else {
      const category = categoryStore.expenseCategories.find(c => 
        c.name.includes(categoryText) || categoryText.includes(c.name)
      );
      categoryId = category?.id || '';
    }
    
    if (!categoryId) {
      console.log('未找到匹配的分类, 使用默认分类');
      categoryId = type === 'income' ? 'income-01' : 'expense-01';
    }
    
    // 创建交易对象
    transaction = {
      id: `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
      type,
      amount: type === 'income' ? amount : -amount,
      date: new Date(),
      categoryId,
      categoryName: categoryText,
      description: `${type === 'income' ? '收入' : '支出'} ${amount}元 ${categoryText}`
    };
  }
  
  return transaction;
};

/**
 * 识别可能的交易信息
 * @param content 用户消息内容
 */
const recognizeTransaction = (content) => {
  console.log('尝试识别交易:', content);
  
  // 检查是否包含支出或收入关键词
  const isExpense = EXPENSE_KEYWORDS.some(keyword => content.includes(keyword));
  const isIncome = INCOME_KEYWORDS.some(keyword => content.includes(keyword));
  
  // 如果不是支出也不是收入，则无法识别
  if (!isExpense && !isIncome) {
    return null;
  }
  
  // 交易类型
  const type = isExpense ? 'expense' : 'income';
  
  // 尝试识别金额 - 增强识别能力
  // 支持更多金额模式：XX元、XX块、XX块钱、花了XX、XX.XX等
  const amountPatterns = [
    /(\d+)(\.?\d*)元/,                  // XX元、XX.XX元
    /(\d+)(\.?\d*)块钱?/,               // XX块、XX块钱
    /花了\s*(\d+)(\.?\d*)/,             // 花了XX、花了XX.XX
    /支出\s*(\d+)(\.?\d*)/,             // 支出XX
    /消费\s*(\d+)(\.?\d*)/,             // 消费XX
    /买了.{0,20}?(\d+)(\.?\d*)/,        // 买了...XX
    /(\d+)(\.?\d*)/                     // 纯数字（最后尝试，避免误匹配）
  ];
  
  let amount = 0;
  let amountMatch = null;
  
  // 依次尝试各种模式
  for (const pattern of amountPatterns) {
    amountMatch = content.match(pattern);
    if (amountMatch) {
      // 找到第一个非空的数字组
      for (let i = 1; i < amountMatch.length; i += 2) {
        if (amountMatch[i]) {
          amount = parseFloat(amountMatch[i] + (amountMatch[i+1] || ''));
          break;
        }
      }
      if (amount > 0) break; // 找到有效金额则停止
    }
  }
  
  if (amount <= 0) {
    console.log('识别到金额为0或负数，无法处理');
    return null;
  }
  
  // 确保金额符号正确
  if (isExpense) {
    amount = -Math.abs(amount);
  } else {
    amount = Math.abs(amount);
  }
  
  // 识别分类
  let categoryId = null;
  
  // 获取分类Store
  const categoryStore = useCategoryStore();
  
  // 根据关键词匹配分类
  for (const [category, keywords] of Object.entries(CATEGORY_KEYWORDS)) {
    for (const keyword of keywords) {
      if (content.includes(keyword)) {
        categoryId = category;
        break;
      }
    }
    if (categoryId) break;
  }
  
  // 如果没有匹配到具体分类，使用默认分类
  if (!categoryId) {
    const categories = categoryStore.getCategoriesByType(type);
    if (categories && categories.length > 0) {
      categoryId = categories[0].id;
    } else {
      // 兜底分类
      categoryId = type === 'expense' ? 'cat-1' : 'cat-income-1';
    }
  }
  
  // 尝试提取描述
  let description = content;
  
  // 创建交易对象
  return {
    id: `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`,
    type,
    amount,
    date: new Date(),
    categoryId,
    description
  };
};

/**
 * 发送消息（Mock实现）
 * @param params - 发送内容
 * @returns Promise<ApiResponse<SendMessageResponseData>>
 */
const sendMessage = async (params: SendMessageParams): Promise<ApiResponse<SendMessageResponseData>> => {
  const { content } = params;
  console.log('Mock: 发送消息', content);
  
  // 模拟网络延迟
  await mockDelay(800);
  
  // 尝试提取交易信息
  const transactionInfo = recognizeTransaction(content);
  
  if (transactionInfo) {
    console.log('识别到交易信息:', transactionInfo);
    
    // 为交易生成唯一ID
    const transactionWithId = {
      ...transactionInfo,
      id: `tx-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    };
    try {
      // 保存到本地存储
      const oldMessages = loadMessagesFromStorage();
      oldMessages.push({
        id: `msg-${Date.now()}`,
        sender: 'assistant',
        type: 'transaction',
        transaction: transactionWithId,
        content: `我已识别到一笔${transactionWithId.type === 'income' ? '收入' : '支出'}，金额为${Math.abs(transactionWithId.amount)}元。`,
        timestamp: Date.now()
      });
      saveMessagesToStorage(oldMessages);
    } catch (e) {
      console.error('保存mock消息到本地失败', e);
    }
    // 返回带有交易信息的响应
    return {
      code: 0,
      message: 'success',
      data: {
        id: `msg-${Date.now()}`,
        sender: 'assistant',
        type: 'transaction',
        transaction: transactionWithId,
        content: `我已识别到一笔${transactionWithId.type === 'income' ? '收入' : '支出'}，金额为${Math.abs(transactionWithId.amount)}元。`,
        timestamp: Date.now()
      }
    };
  }
  
  // 常规聊天回复
  const aiResponses = [
    '很高兴为您提供记账服务!',
    '我是您的AI记账助手账无忌，有什么我可以帮您的吗?',
    '您可以试着让我记录一笔收入或支出，比如"今天支出30元餐饮"。',
    '我可以帮您分析消费习惯，管理预算，请问有什么我可以帮您的?',
    '记账小技巧: 养成每天记账的好习惯，可以更好地掌控财务状况。',
    '您可以尝试语音记账，更加方便快捷哦!',
    '我可以帮您识别消费类型，比如"昨天支出58元水果"，我会自动归类。',
    '想了解您的消费统计和分析，可以查看"分析"页面哦。'
  ];
  
  // 随机选择一条回复
  const randomIndex = Math.floor(Math.random() * aiResponses.length);
  const aiReply = aiResponses[randomIndex];
  try {
    // 保存到本地存储
    const oldMessages = loadMessagesFromStorage();
    oldMessages.push({
      id: `msg-${Date.now()}`,
      sender: 'assistant',
      type: 'text',
      content: aiReply,
      timestamp: Date.now()
    });
    saveMessagesToStorage(oldMessages);
  } catch (e) {
    console.error('保存mock消息到本地失败', e);
  }
  return {
    code: 0,
    message: 'success',
    data: {
      id: `msg-${Date.now()}`,
      sender: 'assistant',
      type: 'text',
      content: aiReply,
      timestamp: Date.now()
    }
  };
};

/**
 * 语音识别接口
 * @param voiceFile 语音文件
 */
const recognizeVoice = async (voiceFile) => {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY * 2));
  
  // 随机模拟一个语音识别的结果
  const randomTexts = [
    '今天午餐花了38元',
    '买水果花了25块钱',
    '刚刚网购花了199元',
    '地铁充值花了50元',
    '收到工资3000元',
    '今天电影票花了60块',
    '早上买早餐花了15元'
  ];
  
  const transcript = randomTexts[Math.floor(Math.random() * randomTexts.length)];
  
  // 识别交易信息
  const possibleTransaction = recognizeTransaction(transcript);
  
  return {
    code: 0,
    message: 'success',
    data: {
      transcript,
      possibleTransaction
    }
  };
};

/**
 * 获取随机回复
 * @param userInput 用户输入
 */
const getRandomResponse = (userInput) => {
  const responses = [
    '我明白了，还有什么需要记录的吗？',
    '好的，我已经了解了，有其他记账需求吗？',
    '收到！我可以帮你记录各种支出和收入，需要我怎么协助你？',
    '明白了！如果有账单需要记录，可以直接告诉我金额和类别哦。',
    '好的！你可以试着对我说"今天午餐花了30元"，我会自动帮你记账。',
    '我不太理解这个请求。你可以告诉我你的支出或收入，我会帮你记录。',
    '我可以帮你记录账单，比如"今天买衣服花了300元"这样的信息。'
  ];
  
  // 如果用户询问功能，提供功能介绍
  if (userInput.includes('你能做什么') || userInput.includes('有什么功能') || userInput.includes('怎么用')) {
    return '我可以帮你记录日常支出和收入，只需告诉我"今天买了什么花了多少钱"，我就能自动识别并记账。此外，我还能分析你的消费习惯，提供预算建议。';
  }
  
  // 如果用户询问帮助，提供使用指南
  if (userInput.includes('帮助') || userInput.includes('不会用')) {
    return '使用很简单：1. 直接告诉我你的收支情况，如"今天午餐花了30元"；2. 确认我识别的信息无误后点击"确认"；3. 如需修改，可以点击"编辑"按钮调整详情。';
  }
  
  // 常见问题回复
  if (userInput.includes('今天') && userInput.includes('天气')) {
    return '抱歉，我是记账助手，无法查询天气信息。我可以帮你记录收支，如"今天买水果花了20元"。';
  }
  
  // 返回随机响应
  return responses[Math.floor(Math.random() * responses.length)];
};

/**
 * 获取聊天历史记录
 */
const getChatHistory = async (params) => {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY));
  
  const messages = loadMessagesFromStorage();
  
  return {
    code: 0,
    message: 'success',
    data: {
      messages,
      pagination: {
        total: messages.length,
        pageNum: params.pageNum || 1,
        pageSize: params.pageSize || 20,
        pages: Math.ceil(messages.length / (params.pageSize || 20))
      }
    }
  };
};

/**
 * 上传图片
 */
const uploadImage = async (imageFile) => {
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, MOCK_DELAY * 1.5));
  
  // 生成随机图片URL
  const imageUrl = `https://example.com/uploads/${uuidv4()}.jpg`;
  
  return {
    code: 0,
    message: 'success',
    imageUrl
  };
};

export default {
  sendMessage,
  recognizeVoice,
  getChatHistory,
  uploadImage
}; 