import type {
  LoginCredentials,
  LoginResponseData,
  SmsCodeResponseData,
  UserInfo,
} from '@/types/api'; // 导入类型定义
import config from '@/config';

// --- 模拟数据定义 ---

const mockLoginSuccessBase = {
  token: `mock_jwt_token_${Date.now()}`,
  refreshToken: `mock_refresh_token_${Date.now() + 1000}`,
  expires: Date.now() + 30 * 60 * 1000, // 模拟30分钟后过期
  refreshExpires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 模拟7天后过期
};

// 从本地存储读取已有用户数据，实现持久化
function getStoredUserData(phone: string): UserInfo | null {
  try {
    // 使用配置前缀拼接手机号作为key，读取存储的用户数据
    const key = `${config.storage.prefix}user_data_${phone}`;
    const storedData = uni.getStorageSync(key);
    if (storedData) {
      return JSON.parse(storedData);
    }
  } catch (e) {
    console.error('[MOCK] Error reading stored user data:', e);
  }
  return null;
}

// 保存用户数据到本地存储
function storeUserData(phone: string, userData: UserInfo): void {
  try {
    const key = `${config.storage.prefix}user_data_${phone}`;
    uni.setStorageSync(key, JSON.stringify(userData));
    console.log('[MOCK] User data stored for phone:', phone);
  } catch (e) {
    console.error('[MOCK] Error storing user data:', e);
  }
}

// --- 模拟函数实现 ---

/**
 * 模拟登录接口
 * @param credentials - 登录凭证 (phone, smsCode)
 * @returns Promise resolving with LoginResponseData or rejecting with error
 */
function mockLogin(credentials: LoginCredentials): Promise<LoginResponseData> {
  console.log('[MOCK] login called with:', credentials);

  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      // 精确匹配测试账号1: 老用户 *********** + 验证码1234
      if (credentials.phone === '***********' && credentials.smsCode === '1234') {
        // 先尝试从本地存储获取持久化的老用户数据
        let userInfo = getStoredUserData('***********');
        
        // 如果没有持久化数据，则使用默认值
        if (!userInfo) {
          userInfo = {
            userId: 'mock_u_old_user',
          name: '老用户张三',
          avatar: '/static/logo/2LOGO.png', // 老用户有头像
          phone: '***********',
        };
          // 存储用户数据，便于下次使用
          storeUserData('***********', userInfo);
        }

        // 检查是否设置了手势密码
        const hasSetGesture = !!uni.getStorageSync(`${config.storage.prefix}${config.storage.keys.GESTURE_PASSWORD}_${userInfo.userId}`);

        const response: LoginResponseData = {
          userId: userInfo.userId,
          token: mockLoginSuccessBase.token,
          refreshToken: mockLoginSuccessBase.refreshToken,
          expires: mockLoginSuccessBase.expires,
          refreshExpires: mockLoginSuccessBase.refreshExpires,
          needSetupSecurity: !hasSetGesture, // 根据是否设置了手势密码决定
          userInfo,
        };

        console.log('[MOCK] login success (老用户):', response);
        resolve(response);
      }
      // 精确匹配测试账号2: 新用户 *********** + 验证码1234
      else if (credentials.phone === '***********' && credentials.smsCode === '1234') {
        // 先尝试从本地存储获取持久化的新用户数据
        let userInfo = getStoredUserData('***********');
        
        // 如果没有持久化数据，则使用默认值（新用户）
        if (!userInfo) {
          userInfo = {
            userId: 'mock_u_new_user',
          name: '新用户小明',
          avatar: '', // 新用户没有头像
          phone: '***********',
        };
          // 存储用户数据，便于下次使用
          storeUserData('***********', userInfo);
        }

        // 检查是否设置了手势密码（新用户应该没有）
        const hasSetGesture = !!uni.getStorageSync(`${config.storage.prefix}${config.storage.keys.GESTURE_PASSWORD}_${userInfo.userId}`);

        const response: LoginResponseData = {
          userId: userInfo.userId,
          token: mockLoginSuccessBase.token,
          refreshToken: mockLoginSuccessBase.refreshToken,
          expires: mockLoginSuccessBase.expires,
          refreshExpires: mockLoginSuccessBase.refreshExpires,
          needSetupSecurity: !hasSetGesture, // 新用户通常需要设置手势密码
          userInfo,
        };

        console.log('[MOCK] login success (新用户):', response);
        resolve(response);
      }
      else {
        // 如果是测试账号但验证码错误
        if (credentials.phone === '***********' || credentials.phone === '***********') {
          console.log('[MOCK] login failed: 测试账号验证码错误');
          reject({ code: 1002, message: '验证码错误 (测试账号请使用1234)' });
        }
        else {
          // 非测试账号或其他情况
          console.log('[MOCK] login failed: 非测试账号或凭证错误');
          reject({ code: 1002, message: '验证码错误或用户不存在' });
        }
      }
    }, 800); // 模拟网络延迟，让用户体验更真实
  });
}

/**
 * 模拟发送短信验证码接口
 * @param params - 参数 (phone, type)
 * @returns Promise resolving with SmsCodeResponseData
 */
function mockSendSmsCode(params: { phone: string, type: string }): Promise<SmsCodeResponseData> {
  console.log('[MOCK] sendSmsCode called with:', params);

  return new Promise((resolve, reject) => {
    // 模拟网络延迟
    setTimeout(() => {
      // 验证手机号格式
      if (!/^1[3-9]\d{9}$/.test(params.phone)) {
        console.log('[MOCK] sendSmsCode failed: 手机号格式错误');
        reject({ code: 1001, message: '手机号格式错误' });
        return;
      }

      // 是否为测试账号
      const isTestAccount = params.phone === '***********' || params.phone === '***********';

      // 根据不同类型可以有不同的验证逻辑
      switch (params.type) {
        case 'login':
          console.log(`[MOCK] 发送登录验证码${isTestAccount ? '(测试账号固定验证码1234)' : ''}`);
          break;
        case 'register':
          console.log(`[MOCK] 发送注册验证码${isTestAccount ? '(测试账号固定验证码1234)' : ''}`);
          break;
        case 'reset':
          console.log(
            `[MOCK] 发送重置密码验证码${isTestAccount ? '(测试账号固定验证码1234)' : ''}`,
          );
          break;
        default:
          console.log(`[MOCK] 发送未知类型验证码: ${params.type}`);
      }

      // 返回成功响应
      resolve({
        expireIn: 300, // 验证码有效期5分钟
        requestId: `mock_sms_${Date.now()}`,
      });
    }, 600); // 模拟网络延迟
  });
}

/**
 * 模拟刷新令牌
 * @param refreshToken - 刷新令牌
 * @returns Promise resolving with new token data
 */
function mockRefreshToken(refreshToken: string) {
  console.log('[MOCK] refreshToken called with:', refreshToken);

  // 生成新的token数据
  const newTokenData = {
    token: `mock_jwt_token_refresh_${Date.now()}`,
    refreshToken: `mock_refresh_token_refresh_${Date.now()}`,
    expires: Date.now() + 30 * 60 * 1000, // 新token 30分钟后过期
    refreshExpires: Date.now() + 7 * 24 * 60 * 60 * 1000, // 新refreshToken 7天后过期
  };

  return Promise.resolve(newTokenData);
}

/**
 * 模拟退出登录
 * @param token - 令牌
 * @returns Promise resolving with void
 */
function mockLogout(token: string) {
  console.log('[MOCK] logout called with token:', token);
  return Promise.resolve();
}

// 导出所有模拟函数
export const authMock = {
  login: mockLogin,
  sendSmsCode: mockSendSmsCode,
  refreshToken: mockRefreshToken,
  logout: mockLogout,
};
