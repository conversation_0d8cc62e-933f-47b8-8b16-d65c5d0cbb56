/**
 * 交易API模拟数据
 */
import type {
  CreateTransactionPayload,
  Transaction,
  TransactionAccount,
  TransactionCategory,
  TransactionListParams,
  TransactionListResponse,
  UpdateTransactionPayload,
} from '@/types/transaction';
import { categories } from './category.mock'; // 仍然使用分类数据
import config from '@/config'; // 导入配置
import { storage } from '@/utils/storage'; // 导入统一存储工具

// 模拟账户
const accounts = [
  { id: '1', name: '现金' },
  { id: '2', name: '银行卡-招商' },
  { id: '3', name: '支付宝' },
  { id: '4', name: '微信' },
  { id: '5', name: '银行卡-建设' },
];

// 使用统一的存储键，从storage工具导入
const STORAGE_KEY = storage.TRANSACTIONS_KEY;

/**
 * 从本地存储获取交易数据（多端兼容）
 * @returns Transaction[] 交易数据数组
 */
function getStoredTransactions(): Transaction[] {
  try {
    // 使用统一的storage工具获取交易数据，而不是直接使用uni.getStorageSync
    const transactions = storage.getItem<Transaction[]>(storage.TRANSACTIONS_KEY, []);
    return transactions || [];
  } catch (error) {
    console.error('[MOCK] Error retrieving transactions from storage:', error);
    return [];
  }
}

/**
 * 将交易数据保存到本地存储（多端兼容）
 * @param transactions 要保存的交易数据数组
 */
function saveTransactionsToStorage(transactions: Transaction[]): void {
  try {
    // 使用统一的storage工具保存交易数据，而不是直接使用uni.setStorageSync
    storage.setItem(storage.TRANSACTIONS_KEY, transactions);
  } catch (error) {
    console.error('[MOCK] Error saving transactions to storage:', error);
  }
}

/**
 * 根据分类ID获取完整的分类对象
 * @param categoryId 分类ID或分类对象
 * @param type 交易类型，用于在找不到时提供默认值
 * @returns TransactionCategory 完整的分类对象
 */
function getCategoryObject(
  categoryId: string | number | TransactionCategory | undefined, 
  type: 'income' | 'expense' = 'expense',
): TransactionCategory {
  // 如果已经是对象，直接返回
  if (typeof categoryId === 'object' && categoryId !== null) {
    return categoryId;
  }
  
  // 尝试从分类列表中查找
  const category = categories.find(c => c.id === categoryId);
  
  // 如果找到，返回该分类
  if (category) {
    return category;
  }
  
  // 否则返回对应类型的第一个分类
  return categories.find(c => c.type === type) || categories[0];
}

/**
 * 根据账户ID获取完整的账户对象
 * @param accountId 账户ID或账户对象
 * @returns TransactionAccount 完整的账户对象
 */
function getAccountObject(accountId: string | number | TransactionAccount | undefined): TransactionAccount | undefined {
  // 如果是undefined，返回undefined
  if (accountId === undefined) {
    return undefined;
  }
  
  // 如果已经是对象，直接返回
  if (typeof accountId === 'object' && accountId !== null) {
    return accountId;
  }
  
  // 尝试从账户列表中查找
  return accounts.find(a => a.id === accountId);
}

/**
 * 清空所有交易数据
 * @returns boolean 操作是否成功
 */
function clearAllTransactions(): Promise<boolean> {
  console.log('[MOCK] Clearing all transactions');
  try {
    // 使用统一的storage工具操作，设置为空数组而不是直接操作localStorage
    storage.setItem(storage.TRANSACTIONS_KEY, []);
    return Promise.resolve(true);
  } catch (error) {
    console.error('[MOCK] Error clearing transactions:', error);
    return Promise.resolve(false);
  }
}

// 模拟获取交易列表API
function getTransactionList(params: TransactionListParams): Promise<TransactionListResponse> {
  console.log('[MOCK] getTransactionList called with params:', params);

  // 从localStorage获取所有交易
  let transactions = getStoredTransactions();

  // 根据查询参数进行过滤
  if (params.startDate || params.endDate) {
    if (params.startDate) {
      const startDate = new Date(params.startDate);
      transactions = transactions.filter(t => new Date(t.date) >= startDate);
    }
    
    if (params.endDate) {
      const endDate = new Date(params.endDate);
      endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间
      transactions = transactions.filter(t => new Date(t.date) <= endDate);
    }
  }

  // 应用类别筛选
  if (params.categoryId) {
    transactions = transactions.filter(t => {
      if (typeof t.category === 'object' && t.category !== null) {
        return t.category.id === params.categoryId;
      }
      return t.category === params.categoryId;
    });
  }

  // 应用类型筛选
  if (params.type) {
    transactions = transactions.filter(t => t.type === params.type);
  }

  // 应用账户筛选
  if (params.accountId) {
    transactions = transactions.filter(t => {
      if (typeof t.account === 'object' && t.account !== null) {
        return t.account.id === params.accountId;
      }
      return t.account === params.accountId;
    });
  }

  // 应用金额范围筛选
  if (params.minAmount !== undefined) {
    transactions = transactions.filter(t => Math.abs(t.amount) >= params.minAmount!);
  }
  if (params.maxAmount !== undefined) {
    transactions = transactions.filter(t => Math.abs(t.amount) <= params.maxAmount!);
  }

  // 应用关键词搜索
  if (params.keyword) {
    const keyword = params.keyword.toLowerCase();
    transactions = transactions.filter(t => 
      (t.notes && t.notes.toLowerCase().includes(keyword)) ||
      (typeof t.category === 'object' && t.category.name.toLowerCase().includes(keyword)) ||
      (typeof t.account === 'object' && t.account.name.toLowerCase().includes(keyword)),
    );
  }

  // 排序：默认按日期降序（最新的在前）
  transactions = transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());

  // 分页处理
  const { page = 1, pageSize = 20 } = params;
  const total = transactions.length;
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const items = transactions.slice(startIndex, endIndex);

  return Promise.resolve({
    items,
    pagination: {
      page,
      pageSize,
      total,
      totalPages: Math.ceil(total / pageSize),
    },
  });
}

// 获取特定月份的所有交易
function getMonthlyTransactions(yearOrParams: number | { year: number, month: number, categoryId?: string }, monthParam?: number): Promise<Transaction[]> {
  // 支持对象参数和分离参数两种调用方式
  let year: number;
  let month: number;
  let categoryId: string | undefined;

  if (typeof yearOrParams === 'object') {
    // 对象参数方式
    year = yearOrParams.year;
    month = yearOrParams.month;
    categoryId = yearOrParams.categoryId;
    console.log(
      `[MOCK] getMonthlyTransactions for ${year}-${month}${categoryId ? `, category: ${categoryId}` : ''}`,
    );
  } else {
    // 分离参数方式
    year = yearOrParams;
    month = monthParam!;
    console.log(`[MOCK] getMonthlyTransactions for ${year}-${month}`);
  }

  // 从localStorage获取所有交易
  let transactions = getStoredTransactions();

  // 过滤指定年月的交易
  transactions = transactions.filter(t => {
    const date = new Date(t.date);
    return date.getFullYear() === year && date.getMonth() + 1 === month;
  });

  // 如果有类别筛选，进一步过滤
  if (categoryId) {
    transactions = transactions.filter(t => {
      if (typeof t.category === 'object' && t.category !== null) {
        return t.category.id === categoryId;
      }
      return t.category === categoryId;
    });
  }

  // 排序：按日期降序
  return Promise.resolve(
    transactions.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()),
  );
}

/**
 * 根据ID获取交易详情
 * @param id 交易ID
 * @returns Promise<Transaction> 交易详情
 */
function getTransactionById(id: string): Promise<Transaction> {
  console.log('[MOCK] getTransactionById called for id:', id);

  // 从localStorage获取所有交易
  const transactions = getStoredTransactions();
  
  // 查找特定交易
  const transaction = transactions.find(t => t.id === id);
  
  // 如果找到，返回它
  if (transaction) {
    return Promise.resolve(transaction);
  }
  
  // 如果未找到，模拟一个基本的交易对象
  // 尝试从URL查询参数中提取信息
  let amount = -44; // 默认金额
  let type = 'expense'; // 默认类型
  
  try {
    // 使用uni-app提供的方法获取查询参数，而不是使用window.location
    // 获取当前页面路由信息
    const pages = getCurrentPages();
    const currentPage = pages[pages.length - 1];
    const query = currentPage?.$page?.options || {};
    
    // 如果URL中有amount参数，优先使用它
    if (query.amount) {
      amount = parseFloat(query.amount);
      // 如果URL中指定了income模式，则设为收入类型
      if (query.mode === 'income') {
        type = 'income';
      } else {
        type = 'expense';
        // 确保支出金额为负数
        amount = -Math.abs(amount);
      }
    }
  } catch (error) {
    console.error('[MOCK] Error parsing query parameters:', error);
  }

  console.log(`[MOCK] Creating placeholder transaction with amount: ${amount}, type: ${type}`);

  // 选择合适的分类
  const categoryObj = getCategoryObject(undefined, type);

  // 解析日期，默认今天
  const now = new Date();
  const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')}`;

  return Promise.resolve({
    id,
    date: dateStr,
    category: categoryObj,
    amount,
    type,
    notes: `Transaction ${id}`,
  });
}

// 模拟创建交易API
function createTransaction(payload: CreateTransactionPayload): Promise<Transaction> {
  console.log('[MOCK] createTransaction called with payload:', payload);

  // 获取现有交易
  const transactions = getStoredTransactions();
  
  // 准备分类对象 (确保获取完整的分类对象而非仅ID)
  const categoryObj = getCategoryObject(
    payload.category || payload.categoryId, 
    payload.type,
  );
  
  // 准备账户对象
  const accountObj = getAccountObject(payload.account || payload.accountId);

  // 生成一个新的交易记录
  let dateStr = payload.date;
  if (typeof dateStr === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
    // 如果只有日期，补全为当天当前时间
    const now = new Date();
    const [y, m, d] = dateStr.split('-');
    dateStr = `${y}-${m}-${d}T${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}:${String(now.getSeconds()).padStart(2, '0')}`;
  } else if (!dateStr) {
    // 如果没有传date，使用当前时间
    dateStr = new Date().toISOString().slice(0,19);
  }
  const newTransaction: Transaction = {
    id: `tx_${Date.now()}${Math.floor(Math.random() * 1000)}`,
    date: dateStr,
    category: categoryObj, // 存储完整的分类对象
    amount: payload.amount,
    type: payload.type,
    notes: payload.notes || '',
  };
  
  // 如果有账户信息，添加到交易中
  if (accountObj) {
    newTransaction.account = accountObj;
  }

  // 添加到交易列表并保存
  transactions.push(newTransaction);
  saveTransactionsToStorage(transactions);

  return Promise.resolve(newTransaction);
}

// 模拟更新交易API
function updateTransaction(id: string, payload: UpdateTransactionPayload): Promise<Transaction> {
  console.log('[MOCK] updateTransaction called for id:', id, 'with payload:', payload);

  // 获取现有交易
  const transactions = getStoredTransactions();
  
  // 查找要更新的交易索引
  const index = transactions.findIndex(t => t.id === id);
  
  if (index === -1) {
    return Promise.reject(new Error(`Transaction with id ${id} not found`));
  }
  
  // 当前交易
  const currentTransaction = transactions[index];

  // 准备更新后的交易
  const updatedTransaction: Transaction = {
    ...currentTransaction,
  };
  
  // 更新各个字段
  if (payload.date) {
    updatedTransaction.date = payload.date;
  }
  
  if (payload.categoryId || payload.category) {
    updatedTransaction.category = getCategoryObject(
      payload.category || payload.categoryId, 
      updatedTransaction.type,
    );
  }
  
  if (payload.amount !== undefined) {
    updatedTransaction.amount = payload.amount;
  }
  
  if (payload.type) {
    updatedTransaction.type = payload.type;
    // 如果类型变了但分类没变，需要检查分类类型是否匹配新的交易类型
    if (typeof updatedTransaction.category === 'object' && 
        updatedTransaction.category.type !== payload.type) {
      // 类型不匹配，获取新类型的默认分类
      updatedTransaction.category = getCategoryObject(undefined, payload.type);
    }
  }
  
  if (payload.notes !== undefined) {
    updatedTransaction.notes = payload.notes;
  }
  
  if (payload.accountId || payload.account) {
    updatedTransaction.account = getAccountObject(payload.account || payload.accountId);
  }
  
  // 更新列表并保存
  transactions[index] = updatedTransaction;
  saveTransactionsToStorage(transactions);

  return Promise.resolve(updatedTransaction);
}

// 模拟删除交易API
function deleteTransaction(id: string): Promise<boolean> {
  console.log('[MOCK] deleteTransaction called for id:', id);

  // 获取现有交易
  const transactions = getStoredTransactions();
  
  // 过滤掉要删除的交易
  const filteredTransactions = transactions.filter(t => t.id !== id);
  
  // 检查是否成功删除
  const success = filteredTransactions.length < transactions.length;
  
  // 保存更新后的交易列表
  if (success) {
    saveTransactionsToStorage(filteredTransactions);
  }

  return Promise.resolve(success);
}

// 模拟获取所有账户
function getAllAccounts(): Promise<TransactionAccount[]> {
  return Promise.resolve(accounts);
}

// 修改：移除自动添加样例数据功能，改为简单返回false
function addSampleTransactions(): Promise<boolean> {
  try {
    // 获取当前交易列表
    const currentTransactions = getStoredTransactions();
    
    // 如果已经有数据，不再添加样例
    if (currentTransactions.length > 0) {
      return Promise.resolve(false);
    }
    
    // 修改：不再自动添加样例数据，交由用户手动添加
    console.log('[MOCK] addSampleTransactions: 不再自动添加样例数据，请手动添加交易记录');
    
    // 始终返回false，表示不自动添加样例
    return Promise.resolve(false);
  } catch (error) {
    console.error('[MOCK] Error in addSampleTransactions:', error);
    return Promise.resolve(false);
  }
}

// 模块导出所有API，确保名称与真实API保持一致
export default {
  getTransactionList,
  getMonthlyTransactions,
  getTransactionById,
  createTransaction,
  updateTransaction,
  deleteTransaction,
  getAllAccounts,
  addSampleTransactions,
  clearAllTransactions,
};
