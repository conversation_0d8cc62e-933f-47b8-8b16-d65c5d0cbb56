/**
 * 分类API模拟数据
 */
import type { TransactionCategory } from '@/types/transaction';
import config from '@/config'; // 导入配置
import { storage } from '@/utils/storage'; // 导入统一存储工具

// 使用统一的存储键，从storage工具导入
const CATEGORIES_STORAGE_KEY = storage.CATEGORIES_KEY;

// 默认分类数据
const defaultCategories: TransactionCategory[] = [
  // 支出分类
  { id: 'cat-1', name: '餐饮', type: 'expense', icon: 'utensils', bgColor: '#FF6B35' },
  { id: 'cat-2', name: '购物', type: 'expense', icon: 'cart-shopping', bgColor: '#4CAF50' },
  { id: 'cat-3', name: '日用', type: 'expense', icon: 'basket-shopping', bgColor: '#2196F3' },
  { id: 'cat-4', name: '交通', type: 'expense', icon: 'car', bgColor: '#9C27B0' },
  { id: 'cat-5', name: '蔬菜', type: 'expense', icon: 'carrot', bgColor: '#8BC34A' },
  { id: 'cat-6', name: '水果', type: 'expense', icon: 'apple-alt', bgColor: '#FF9800' },
  { id: 'cat-7', name: '零食', type: 'expense', icon: 'cookie-bite', bgColor: '#FF5722' },
  { id: 'cat-8', name: '运动', type: 'expense', icon: 'dumbbell', bgColor: '#03A9F4' },
  { id: 'cat-9', name: '娱乐', type: 'expense', icon: 'gamepad', bgColor: '#E91E63' },
  { id: 'cat-10', name: '通讯', type: 'expense', icon: 'phone', bgColor: '#3F51B5' },
  { id: 'cat-11', name: '服饰', type: 'expense', icon: 'shirt', bgColor: '#9C27B0' },
  { id: 'cat-12', name: '美容', type: 'expense', icon: 'spa', bgColor: '#F06292' },
  { id: 'cat-13', name: '住房', type: 'expense', icon: 'house', bgColor: '#795548' },
  { id: 'cat-14', name: '孩子', type: 'expense', icon: 'child', bgColor: '#9575CD' },
  { id: 'cat-15', name: '社交', type: 'expense', icon: 'user-group', bgColor: '#4CAF50' },
  { id: 'cat-16', name: '旅行', type: 'expense', icon: 'plane', bgColor: '#009688' },
  { id: 'cat-17', name: '烟酒', type: 'expense', icon: 'wine-glass', bgColor: '#8D6E63' },
  { id: 'cat-18', name: '医疗', type: 'expense', icon: 'suitcase-medical', bgColor: '#F44336' },
  { id: 'cat-19', name: '书籍', type: 'expense', icon: 'book', bgColor: '#CDDC39' },
  { id: 'cat-20', name: '学习', type: 'expense', icon: 'graduation-cap', bgColor: '#9E9E9E' },
  { id: 'cat-21', name: '宠物', type: 'expense', icon: 'paw', bgColor: '#FFCDD2' },
  { id: 'cat-22', name: '礼物', type: 'expense', icon: 'gift', bgColor: '#D32F2F' },
  { id: 'cat-23', name: '维修', type: 'expense', icon: 'screwdriver-wrench', bgColor: '#607D8B' },
  { id: 'cat-24', name: '其他', type: 'expense', icon: 'circle-question', bgColor: '#757575' },

  // 收入分类
  { id: 'cat-101', name: '工资', type: 'income', icon: 'wallet', bgColor: '#FFC107' },
  { id: 'cat-102', name: '兼职', type: 'income', icon: 'briefcase', bgColor: '#00BCD4' },
  { id: 'cat-103', name: '理财', type: 'income', icon: 'chart-line', bgColor: '#4CAF50' },
  { id: 'cat-104', name: '礼金', type: 'income', icon: 'gift', bgColor: '#F44336' },
  { id: 'cat-105', name: '其他', type: 'income', icon: 'circle-question', bgColor: '#9E9E9E' },
];

/**
 * 从存储中获取分类数据
 * @returns TransactionCategory[] 分类数据数组
 */
function getStoredCategories(): TransactionCategory[] {
  try {
    const storedCategories = storage.getItem<TransactionCategory[]>(CATEGORIES_STORAGE_KEY);
    if (storedCategories && storedCategories.length > 0) {
      console.log('[MOCK] 从存储读取分类数据成功');
      return storedCategories;
    }
  } catch (error) {
    console.error('[MOCK] 读取分类数据出错:', error);
  }
  // 如果没有存储数据或发生错误，返回默认分类
  return [...defaultCategories];
}

/**
 * 保存分类数据到存储
 * @param categories 分类数据数组
 */
function saveCategoriesToStorage(categories: TransactionCategory[]): void {
  try {
    storage.setItem(CATEGORIES_STORAGE_KEY, categories);
    console.log('[MOCK] 保存分类数据到存储成功');
  } catch (error) {
    console.error('[MOCK] 保存分类数据出错:', error);
  }
}

// 导出分类供其他mock模块使用
export const categories = getStoredCategories();

/**
 * 获取所有分类
 * @returns Promise<TransactionCategory[]>
 */
function getAllCategories(): Promise<TransactionCategory[]> {
  console.log('[MOCK] getAllCategories called');
  const currentCategories = getStoredCategories();
  return Promise.resolve([...currentCategories]);
}

/**
 * 根据类型获取分类
 * @param type 分类类型: 'income' 或 'expense'
 * @returns Promise<TransactionCategory[]>
 */
function getCategoriesByType(type: 'income' | 'expense'): Promise<TransactionCategory[]> {
  console.log(`[MOCK] getCategoriesByType called with type: ${type}`);
  const currentCategories = getStoredCategories();
  return Promise.resolve(currentCategories.filter(category => category.type === type));
}

/**
 * 创建新分类
 * @param category 分类数据
 * @returns Promise<TransactionCategory>
 */
function createCategory(category: Omit<TransactionCategory, 'id'>): Promise<TransactionCategory> {
  console.log('[MOCK] createCategory called with:', category);
  const currentCategories = getStoredCategories();

  // 生成新ID
  const newId = `custom-${Date.now().toString().slice(-6)}`;

  // 创建新分类
  const newCategory: TransactionCategory = {
    ...category,
    id: newId,
  };

  // 添加到分类列表并保存
  currentCategories.push(newCategory);
  saveCategoriesToStorage(currentCategories);

  return Promise.resolve(newCategory);
}

/**
 * 更新分类
 * @param id 分类ID
 * @param updates 更新数据
 * @returns Promise<TransactionCategory>
 */
function updateCategory(id: string | number, updates: Partial<Omit<TransactionCategory, 'id'>>): Promise<TransactionCategory> {
  console.log(`[MOCK] updateCategory called for id: ${id} with:`, updates);
  const currentCategories = getStoredCategories();

  // 查找分类
  const categoryIndex = currentCategories.findIndex(c => c.id === id);

  if (categoryIndex === -1) {
    return Promise.reject(new Error(`Category with id ${id} not found`));
  }

  // 更新分类
  const updatedCategory: TransactionCategory = {
    ...currentCategories[categoryIndex],
    ...updates,
  };

  // 保存更新
  currentCategories[categoryIndex] = updatedCategory;
  saveCategoriesToStorage(currentCategories);

  return Promise.resolve(updatedCategory);
}

/**
 * 删除分类
 * @param id 分类ID
 * @returns Promise<boolean>
 */
function deleteCategory(id: string | number): Promise<boolean> {
  console.log(`[MOCK] deleteCategory called for id: ${id}`);
  const currentCategories = getStoredCategories();

  // 查找分类
  const initialLength = currentCategories.length;
  const categoryIndex = currentCategories.findIndex(c => c.id === id);

  if (categoryIndex === -1) {
    return Promise.reject(new Error(`Category with id ${id} not found`));
  }

  // 删除分类
  currentCategories.splice(categoryIndex, 1);
  saveCategoriesToStorage(currentCategories);

  return Promise.resolve(currentCategories.length < initialLength);
}

// 导出API对象
export default {
  getAllCategories,
  getCategoriesByType,
  createCategory,
  updateCategory,
  deleteCategory,
};
