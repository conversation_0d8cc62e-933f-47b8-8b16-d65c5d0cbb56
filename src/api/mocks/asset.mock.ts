// src/api/mocks/asset.mock.ts
import type { Asset, AssetType } from '@/types/asset'; // Assuming types are defined in @/types/asset
import config from '@/config'; // 导入配置
import { storage } from '@/utils/storage'; // 导入统一存储工具

// 使用统一的存储键，从storage工具导入
const ASSET_STORAGE_KEY = storage.ASSETS_KEY;
const ASSET_TYPES_STORAGE_KEY = `${ASSET_STORAGE_KEY}_types`;
const BUDGET_STORAGE_KEY = storage.BUDGET_KEY;

// 默认资产数据
const defaultAssets: Asset[] = [
  {
    id: '1',
    name: '现金',
    type: 'cash',
    icon: 'money-bill',
    balance: 5000,
    color: '#FF6B35',
    isDefault: true,
  },
  {
    id: '2',
    name: '银行卡',
    type: 'bank',
    icon: 'credit-card',
    balance: 12000,
    color: '#3A7CFF',
  },
  {
    id: '3',
    name: '信用卡',
    type: 'credit',
    icon: 'credit-card',
    balance: -3500, // 负数表示负债
    color: '#F44336',
  },
  {
    id: '4',
    name: '支付宝',
    type: 'alipay',
    icon: 'alipay',
    balance: 2500,
    color: '#1677FF',
  },
  {
    id: '5',
    name: '微信',
    type: 'wechat',
    icon: 'weixin',
    balance: 3500,
    color: '#07C160',
  },
];

// 默认资产类型
const defaultAssetTypes: AssetType[] = [
  { id: 'cash', name: '现金', icon: 'money-bill' },
  { id: 'bank', name: '银行卡', icon: 'credit-card' },
  { id: 'credit', name: '信用卡', icon: 'credit-card' },
  { id: 'alipay', name: '支付宝', icon: 'alipay' },
  { id: 'wechat', name: '微信', icon: 'weixin' },
  { id: 'other', name: '其他', icon: 'wallet' },
];

// 默认预算数据
const defaultBudgetData = {
  total: 5000, // 总预算
  used: 3520, // 已使用
  remaining: 1480, // 剩余
};

/**
 * 从存储中获取资产数据
 */
function getStoredAssets(): Asset[] {
  try {
    const storedAssets = storage.getItem<Asset[]>(ASSET_STORAGE_KEY);
    if (storedAssets && storedAssets.length > 0) {
      console.log('[MOCK] 从存储中读取资产数据成功');
      return storedAssets;
    }
  } catch (error) {
    console.error('[MOCK] 读取资产数据出错:', error);
  }
  // 返回默认数据
  return [...defaultAssets];
}

/**
 * 保存资产数据到存储
 */
function saveAssetsToStorage(assets: Asset[]): void {
  try {
    storage.setItem(ASSET_STORAGE_KEY, assets);
    console.log('[MOCK] 保存资产数据到存储成功');
  } catch (error) {
    console.error('[MOCK] 保存资产数据出错:', error);
  }
}

/**
 * 从存储中获取资产类型
 */
function getStoredAssetTypes(): AssetType[] {
  try {
    const storedTypes = storage.getItem<AssetType[]>(ASSET_TYPES_STORAGE_KEY);
    if (storedTypes && storedTypes.length > 0) {
      console.log('[MOCK] 从存储中读取资产类型成功');
      return storedTypes;
    }
  } catch (error) {
    console.error('[MOCK] 读取资产类型出错:', error);
  }
  // 返回默认数据
  return [...defaultAssetTypes];
}

/**
 * 保存资产类型到存储
 */
function saveAssetTypesToStorage(types: AssetType[]): void {
  try {
    storage.setItem(ASSET_TYPES_STORAGE_KEY, types);
    console.log('[MOCK] 保存资产类型到存储成功');
  } catch (error) {
    console.error('[MOCK] 保存资产类型出错:', error);
  }
}

/**
 * 从存储中获取预算数据
 */
function getStoredBudgetData(): { total: number, used: number, remaining: number } {
  try {
    const storedBudget = storage.getItem(BUDGET_STORAGE_KEY);
    if (storedBudget) {
      console.log('[MOCK] 从存储中读取预算数据成功');
      return storedBudget;
    }
  } catch (error) {
    console.error('[MOCK] 读取预算数据出错:', error);
  }
  // 返回默认数据
  return { ...defaultBudgetData };
}

/**
 * 保存预算数据到存储
 */
function saveBudgetToStorage(budget: { total: number, used: number, remaining: number }): void {
  try {
    storage.setItem(BUDGET_STORAGE_KEY, budget);
    console.log('[MOCK] 保存预算数据到存储成功');
  } catch (error) {
    console.error('[MOCK] 保存预算数据出错:', error);
  }
}

// Mock functions corresponding to the API calls in asset.store.ts
export default {
  getAssets: async (): Promise<Asset[]> => {
    console.log('[MOCK] getAssets called');
    const assets = getStoredAssets();
    return Promise.resolve(assets);
  },

  getAssetTypes: async (): Promise<AssetType[]> => {
    console.log('[MOCK] getAssetTypes called');
    const types = getStoredAssetTypes();
    return Promise.resolve(types);
  },

  getAssetById: async (id: string): Promise<Asset | undefined> => {
    console.log(`[MOCK] getAssetById called with id: ${id}`);
    const assets = getStoredAssets();
    const asset = assets.find(a => a.id === id);
    return Promise.resolve(asset);
  },

  getBudgetData: async (): Promise<{ total: number, used: number, remaining: number }> => {
    console.log('[MOCK] getBudgetData called');
    const budget = getStoredBudgetData();
    return Promise.resolve(budget);
  },

  createAsset: async (newAsset: Omit<Asset, 'id'>): Promise<Asset> => {
    console.log('[MOCK] createAsset called with:', newAsset);
    const assets = getStoredAssets();
    
    const createdAsset: Asset = {
      ...newAsset,
      id: `mock_asset_${Date.now()}`,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    
    assets.push(createdAsset);
    saveAssetsToStorage(assets);
    
    return Promise.resolve(createdAsset);
  },

  updateAsset: async (id: string, payload: Partial<Asset>): Promise<Asset> => {
    console.log(`[MOCK] updateAsset called for id: ${id} with payload:`, payload);
    const assets = getStoredAssets();
    const index = assets.findIndex(a => a.id === id);
    
    if (index !== -1) {
      assets[index] = { 
        ...assets[index], 
        ...payload, 
        updatedAt: new Date().toISOString() 
      };
      
      saveAssetsToStorage(assets);
      return Promise.resolve(assets[index]);
    }
    else {
      return Promise.reject(new Error('Mock Asset not found'));
    }
  },

  removeAsset: async (id: string): Promise<void> => {
    console.log(`[MOCK] removeAsset called for id: ${id}`);
    const assets = getStoredAssets();
    const index = assets.findIndex(a => a.id === id);
    
    if (index !== -1) {
      assets.splice(index, 1);
      saveAssetsToStorage(assets);
      return Promise.resolve();
    }
    else {
      return Promise.reject(new Error('Mock Asset not found'));
    }
  },
  
  // 更新预算数据
  updateBudgetData: async (budgetData: { total: number, used: number, remaining: number }): Promise<{ total: number, used: number, remaining: number }> => {
    console.log('[MOCK] updateBudgetData called with:', budgetData);
    saveBudgetToStorage(budgetData);
    return Promise.resolve(budgetData);
  },
};
