/**
 * 聊天模块API
 */
import { request } from '@/utils/request';
import type { ChatMessage, ChatMessageResponse, ChatHistoryResponse, VoiceRecognitionResponse } from '@/types/chat';
import chatMock from './mocks/chat.mock';

const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true';

/**
 * 发送消息
 * @param message 消息内容
 */
export async function sendMessage(message: { content: string }) {
  if (useMock) {
    return chatMock.sendMessage(message);
  }
  return request.post('/api/v1/chat/messages', message);
}

/**
 * 获取聊天历史记录
 * @param params 查询参数
 */
export async function getChatHistory(params: {
  pageSize?: number;
  pageNum?: number;
  startTime?: number;
  endTime?: number;
}) {
  if (useMock) {
    return chatMock.getChatHistory(params);
  }
  return request.get('/api/v1/chat/history', { params });
}

/**
 * 上传语音进行识别
 * @param voiceFile 语音文件
 */
export async function recognizeVoice(voiceFile: File) {
  if (useMock) {
    return chatMock.recognizeVoice(voiceFile);
  }
  
  const formData = new FormData();
  formData.append('voiceFile', voiceFile);
  
  return request.post('/api/v1/chat/voice/recognize', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
}

/**
 * 上传图片
 * @param imageFile 图片文件
 */
export async function uploadImage(imageFile: File) {
  if (useMock) {
    return chatMock.uploadImage(imageFile);
  }
  
  const formData = new FormData();
  formData.append('imageFile', imageFile);
  
  return request.post('/api/v1/chat/images/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  });
} 