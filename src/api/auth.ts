import type {
  BaseResponse,
  LoginCredentials,
  LoginResponseData,
  SmsCodeParams,
  SmsCodeResponseData,
} from '@/types/api'; // 导入类型
import config from '@/config'; // 导入配置文件
import { request } from '@/utils/request';
import { authMock } from './mocks/auth.mock'; // 导入 Mock 文件，使用命名导入

// 读取环境变量决定是否启用 Mock 数据
const useMock = config.env.useMockData; // 从配置中心读取

/**
 * 登录接口
 * @param credentials - 登录凭证
 * @returns Promise<LoginResponseData> - 登录响应数据
 */
export function login(credentials: LoginCredentials): Promise<LoginResponseData> {
  console.log('[API] login called with:', credentials, 'useMock:', useMock);

  if (useMock) {
    // 如果启用 Mock，调用 Mock 函数
    console.log('[API] Using mock data for login');
    return authMock.login(credentials);
  }

  // 否则，发起真实 API 请求
  console.log('[API] Using real API for login');
  return request
    .post<BaseResponse<LoginResponseData>>('/api/v1/auth/login', credentials)
    .then((response) => {
      // 假设接口返回的是 BaseResponse，需要取出 data 字段
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return Promise.reject(new Error(response.message || '登录失败'));
    });
}

/**
 * 注册接口
 * @param userInfo - 用户注册信息
 * @returns Promise<BaseResponse<null>> - 基础响应
 */
export function register(userInfo: Record<string, any>): Promise<BaseResponse<null>> {
  console.log('[API] register called with:', userInfo, 'useMock:', useMock);

  if (useMock) {
    // 如果启用了 Mock 数据
    console.log('[API] Using mock data for register');
    return Promise.resolve({ code: 0, message: '注册成功 (Mock)', data: null });
  }

  // 真实请求
  console.log('[API] Using real API for register');
  return request.post<BaseResponse<null>>('/api/v1/auth/register', userInfo);
}

/**
 * 发送短信验证码
 * @param phone - A手机号
 * @param type - 验证码类型
 * @returns Promise<SmsCodeResponseData> - 验证码响应数据
 */
export function sendSmsCode(phone: string, type: 'register' | 'login' | 'reset'): Promise<SmsCodeResponseData> {
  const params: SmsCodeParams = { phone, type };
  console.log('[API] sendSmsCode called with:', params, 'useMock:', useMock);

  if (useMock) {
    console.log('[API] Using mock data for sendSmsCode');
    return authMock.sendSmsCode(params);
  }

  // 否则，发起真实 API 请求
  console.log('[API] Using real API for sendSmsCode');
  return request
    .post<BaseResponse<SmsCodeResponseData>>('/api/v1/sms/send', params)
    .then((response) => {
      // 假设接口返回的是 BaseResponse，需要取出 data 字段
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return Promise.reject(new Error(response.message || '发送验证码失败'));
    });
}

/**
 * 退出登录接口
 */
export function logout(): Promise<void> {
  const userStore = uni.getStorageSync(`${config.storage.prefix}user`); // 从存储中获取用户信息
  const token = userStore?.token || '';

  console.log('[API] logout called, useMock:', useMock);

  if (useMock) {
    // 如果启用了 Mock，可以调用模拟登出，它主要打印日志
    console.log('[API] Using mock data for logout');
    return authMock.logout(token);
  }

  // 否则，发起真实 API 请求
  console.log('[API] Using real API for logout');
  return request.post<BaseResponse<null>>('/api/v1/auth/logout').then((response) => {
    if (response.code === 0) {
      return;
    }
    return Promise.reject(new Error(response.message || '退出登录失败'));
  });
}

/**
 * 刷新令牌
 * @param refreshToken - 刷新令牌
 * @returns Promise<LoginResponseData> - 新的令牌信息
 */
export function refreshToken(refreshToken: string): Promise<Partial<LoginResponseData>> {
  console.log('[API] refreshToken called, useMock:', useMock);

  if (useMock) {
    console.log('[API] Using mock data for refreshToken');
    return authMock.refreshToken(refreshToken);
  }

  console.log('[API] Using real API for refreshToken');
  return request
    .post<BaseResponse<Partial<LoginResponseData>>>('/api/v1/auth/refresh', { refreshToken })
    .then((response) => {
      if (response.code === 0 && response.data) {
        return response.data;
      }
      return Promise.reject(new Error(response.message || '刷新令牌失败'));
    });
}
