import { createI18n } from 'vue-i18n';

// 默认语言
const defaultLocale = 'zh-CN';

// 导入语言文件 (稍后创建)
// import en from './en.json';
// import zhCN from './zh-CN.json';

// 如果现在没有语言文件，可以先用空对象
const messages = {
  // en: en,
  // 'zh-CN': zhCN,
  'en': {
    hello: 'Hello'
  },
  'zh-CN': {
    hello: '你好'
  }
};

export function setupI18n() {
  const i18n = createI18n({
    legacy: false, // 使用 Composition API 模式
    locale: defaultLocale,
    fallbackLocale: 'en', // 如果当前语言缺少翻译，则回退到英语
    messages,
    // 如果想在template中直接使用 $t, $d, $n，可以设置为true
    // globalInjection: true,
    // silentTranslationWarn: true, // 关闭翻译警告
    // missingWarn: false, // 关闭key不存在的警告
    // fallbackWarn: false, // 关闭回退警告
  });
  return i18n;
}

// 如果需要在其他地方获取 i18n 实例或 t 函数
// let i18nInstance: ReturnType<typeof createI18n> | null = null;

// export function getI18nInstance() {
//   if (!i18nInstance) {
//     i18nInstance = setupI18n();
//   }
//   return i18nInstance;
// }

// export const t = (key: string, ...args: any[]) => {
//   const instance = getI18nInstance().global;
//   return instance.t(key, ...args);
// }; 