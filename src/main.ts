// 导入 Pinia 实例
import pinia from '@/stores';

import { createSSRApp } from 'vue';
import * as Pinia from 'pinia';

// 导入 uView Plus
import uviewPlus from 'uview-plus'; 
// 导入uView Plus的图标组件
import UIcon from 'uview-plus/components/u-icon/u-icon.vue';
// 导入字体预加载工具
import { preloadFonts } from '@/utils/font-preloader';
// 导入图标配置
import { uViewPlusIconConfig } from '@/config/icon.config';

import App from './App.vue';
import { setupI18n } from './locales/i18n';

import AppIcon from './components/common/AppIcon.vue'; // 导入AppIcon组件

// 导入uCharts注册函数
import { registerUCharts, initUChartsConfig } from '@/utils/chart-register';

// 初始化uCharts配置
initUChartsConfig();

// 初始化pinia
const store = Pinia.createPinia();

// 创建应用
export function createApp() {
  const app = createSSRApp(App);
  
  // 挂载pinia
  app.use(store);
  
  // 挂载uview-plus
  app.use(uviewPlus);
  
  // 设置uView Plus全局配置 - 遵循官方推荐的配置方式
  uni.$u.setConfig({
    // 修改默认单位为rpx
    config: {
      unit: 'rpx'
    },
    // 修改主题色
    color: {
      primary: '#FF6B35'
    },
    // 自定义props
    props: {
      // 设置图标默认大小
      icon: {
        size: 28
      }
    }
  });
  
  // 全局注册 AppIcon 组件
  app.component('AppIcon', AppIcon);
  
  // 全局注册 UIcon 组件
  app.component('UIcon', UIcon);
  
  // 注册uCharts组件
  registerUCharts(app);
  
  // 在H5环境下确保图标字体加载
  // #ifdef H5
  // 使用DOM渲染完成后的事件确保字体加载
  setTimeout(() => {
    // 预加载字体资源
    preloadFonts();
    console.log('[main.ts] 图标字体预加载完成');
  }, 0);
  // #endif

  return {
    app,
    Pinia,
  };
}