<!-- 测试自动化检查流程：ESLint/Stylelint/Prettier -->
<template>
  <view >
    <text >测试Lint自动修复</text>
    <text>  这里有多余空格</text>
  </view>
</template>

<script setup lang="ts">
const msg = 'hello world';   // 故意多空格和缺少分号
const arr = [1,2,3];
</script>

<style lang="scss" scoped>
.test-lint {
  color: var(--text-primary, #333);
  font-size: var(--font-size-md, 16px);
  background: var(--bg-primary, #fff);
}
</style> 