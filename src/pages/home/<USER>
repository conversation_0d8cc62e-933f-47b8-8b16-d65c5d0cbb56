<script setup lang="ts">
import type { Transaction } from '@/types/transaction';
import AppButton from '@/components/common/AppButton.vue';
import AppCard from '@/components/common/AppCard.vue';
import AppDatePicker from '@/components/common/AppDatePicker.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppNavBar from '@/components/common/AppNavBar.vue';
import AppTabBar from '@/components/common/AppTabBar.vue';
import AppSwipeAction from '@/components/common/AppSwipeAction.vue';
import AppSwipeActionButton from '@/components/common/AppSwipeActionButton.vue';
import { useAssetStore } from '@/stores/asset.store';
import { useCategoryStore } from '@/stores/category.store';
import { useTransactionStore } from '@/stores/transaction.store';
import { useUserStore } from '@/stores/user.store';
import { getPlatformStyle } from '@/utils/platform';
import { computed, onMounted, reactive, ref, watch } from 'vue';
import { useRouter } from 'vue-router';

const userStore = useUserStore();
const assetStore = useAssetStore();
const transactionStore = useTransactionStore();
const categoryStore = useCategoryStore();
const router = useRouter();
const navHeight = getPlatformStyle('nav-height') || '60px';

const currentDate = reactive({
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
});

const showWheelDatePicker = ref(false);

const showCalendar = ref(false);
const calendarDate = reactive({
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1,
});
const selectedCalendarDay = ref<number | null>(null);
const activeTabIndex = ref(0);
const tabBarConfig = [
  { label: '首页', icon: 'home', pagePath: '/pages/home/<USER>' },
  { label: '账单', icon: 'list', pagePath: '/pages/transaction/list' },
  { label: '统计', icon: 'pie-chart', pagePath: '/pages/analysis/index' },
  { label: '我的', icon: 'account', pagePath: '/pages/profile/index' },
];

const daysInMonth = computed(() => new Date(calendarDate.year, calendarDate.month, 0).getDate());

function hasRecordOnDay(day: number): boolean {
  const dateStr = `${calendarDate.year}-${String(calendarDate.month).padStart(2, '0')}-${String(day).padStart(2, '0')}`;
  return transactionStore.monthlyTransactions.some(tx => tx.date.startsWith(dateStr));
}

const selectedDayTransactions = computed(() => {
  if (!selectedCalendarDay.value)
    return [];
  const dateStr = `${calendarDate.year}-${String(calendarDate.month).padStart(2, '0')}-${String(selectedCalendarDay.value).padStart(2, '0')}`;
  return transactionStore.monthlyTransactions.filter(tx => tx.date.startsWith(dateStr));
});

function formatAmount(amount: number, type: 'income' | 'expense'): string {
  const absAmount = Math.abs(amount);
  const sign = type === 'income' ? '+' : '-';
  return `${sign} ${absAmount.toFixed(2)}`;
}

const isLoading = ref(true); // 添加加载状态

onMounted(async () => {
  isLoading.value = true;
  try {
    // 确保先加载分类数据
    await categoryStore.fetchCategories?.();
    // 验证分类数据是否已加载
    if (!categoryStore.categories || categoryStore.categories.length === 0) {
      console.warn('分类数据未正确加载，将重新尝试');
      await categoryStore.resetToDefaultCategories?.();
    }
    
    // 确保先从本地存储加载所有交易数据
    transactionStore.loadFromStorage();
    console.log('首页：已从本地存储加载交易数据');
    
    // 再加载交易数据
    await transactionStore.fetchMonthlyTransactions({
      year: currentDate.year,
      month: currentDate.month,
    });
    // 资产功能未开放，暂不加载资产数据
    // await assetStore.fetchAssetsData();
  }
  catch (error) {
    console.error('加载首页数据失败:', error);
  }
  finally {
    isLoading.value = false;
  }
});

watch(
  calendarDate,
  async (newDate) => {
    await loadCalendarData(newDate.year, newDate.month);
  },
  { deep: true },
);

watch(
  currentDate,
  async (newDate) => {
    try {
      await transactionStore.fetchMonthlyTransactions({
        year: newDate.year,
        month: newDate.month,
      });
    }
    catch (error) {
      console.error(`Failed to load data for ${newDate.year}-${newDate.month}:`, error);
    }
  },
  { deep: true },
);

async function loadInitialData() {
  try {
    await transactionStore.fetchMonthlyTransactions({
      year: currentDate.year,
      month: currentDate.month,
    });
    await assetStore.loadInitialData();
  }
  catch (error) {
    console.error('Failed to load initial data:', error);
  }
}

function handleMonthSelectorClick() {
  showWheelDatePicker.value = true;
}

function handleWheelDateConfirm(dateStr: string) {
  // 只取年月
  const [year, month] = dateStr.split('-');
  currentDate.year = Number(year);
  currentDate.month = Number(month);
  showWheelDatePicker.value = false;
}

function handleWheelDateCancel() {
  showWheelDatePicker.value = false;
}

async function loadCalendarData(year: number, month: number) {
  try {
    await transactionStore.fetchMonthlyTransactions({ year, month });
    selectedCalendarDay.value = null;
  }
  catch (error) {
    console.error('Failed to load calendar transactions:', error);
  }
}

async function handleCalendarClick() {
  calendarDate.year = currentDate.year;
  calendarDate.month = currentDate.month;
  await loadCalendarData(calendarDate.year, calendarDate.month);
  showCalendar.value = true;
}

function closeCalendar() {
  showCalendar.value = false;
  selectedCalendarDay.value = null;
}

function selectCalendarDay(day: number) {
  selectedCalendarDay.value = day;
}

function handleFeatureClick(feature: string) {
  if (feature === 'book') {
    uni.showToast({ title: '此功能仅对会员开放', icon: 'none' });
  }
}

function handleTabChange(index: number) {
  if (index !== activeTabIndex.value) {
    const targetPath = tabBarConfig[index]?.pagePath;
    if (targetPath && targetPath !== '/pages/home/<USER>') {
      uni.switchTab({
        url: targetPath,
        fail: (err) => {
          console.error(`switchTab failed for path ${targetPath}:`, JSON.stringify(err));
        },
      });
    }
  }
}

function handleFabClick() {
  uni.navigateTo({ url: '/pages/chat/index' });
}

function formatTransactionDate(dateStr: string): string {
  if (!dateStr)
    return '';
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const txDate = new Date(dateStr);
  txDate.setHours(0, 0, 0, 0);
  if (txDate.getTime() === today.getTime()) {
    return '今天';
  }
  else if (txDate.getTime() === yesterday.getTime()) {
    return '昨天';
  }
  else {
    return `${txDate.getMonth() + 1}月${txDate.getDate()}日`;
  }
}

function navigateToTransactionList() {
  router.push('/transaction/list');
}

function handleTransactionClick(transaction: Transaction) {
  router.push(`/transaction/detail?id=${transaction.id}`);
}

function handleShortcutClick(type: string) {
  if (type === 'expense' || type === 'income') {
    uni.navigateTo({ url: `/pages/transaction/record?type=${type}` });
  }
  else if (type === 'voice') {
    uni.navigateTo({ url: '/pages/chat/index' });
  }
  else if (type === 'scan') {
    uni.showToast({ title: '扫票功能待开发', icon: 'none' });
  }
}

function navigateToRecord() {
  uni.navigateTo({ url: '/pages/transaction/record' });
}

function formatNumber(num: number | undefined | null) {
  return num != null ? num.toFixed(2) : '0.00';
}

// 从categoryStore获取分类，处理找不到的情况
function getCategoryFromStore(category: any) {
  // 图标名称映射：将Font Awesome风格图标名映射为uView Plus图标名
  const iconMapping: Record<string, string> = {
    'utensils': 'shop',
    'basket-shopping': 'bag',
    'cart-shopping': 'shopping-cart',
    'cookie-bite': 'tags',
    'dumbbell': 'tags',
    'gamepad': 'play-circle',
    'shirt': 'tags',
    'spa': 'woman',
    'child': 'man',
    'suitcase-medical': 'heart',
    'briefcase-medical': 'heart'
  };

  // 如果category为空，返回默认值
  if (!category) {
    return {
      id: 'unknown',
      name: '未分类',
      icon: 'question', // uView Plus general question mark
      iconContainerBgColor: 'var(--bg-secondary, #f5f5f5)',
      color: 'var(--text-primary, #333333)', // Default icon color for neutral background
      type: 'expense',
    };
  }
  
  // 1. 如果category已经是对象，直接使用
  if (typeof category === 'object') {
    const fallbackCat = categoryStore.getCategoryById(category.id);
    
    // 处理图标名称映射
    let iconName = category.icon || fallbackCat?.icon || 'question';
    // 如果是Font Awesome风格的图标名，映射为uView Plus图标名
    if (iconMapping[iconName]) {
      iconName = iconMapping[iconName];
      console.log(`图标映射：将${category.icon}转换为${iconName}`);
    }
    
    return {
      id: category.id || 'unknown',
      name: category.name || '未分类',
      icon: iconName,
      iconContainerBgColor: category.iconContainerBgColor || fallbackCat?.iconContainerBgColor || 'var(--bg-secondary, #f5f5f5)',
      color: category.color || fallbackCat?.color || 'var(--text-primary, #333333)',
      type: category.type || 'expense',
    };
  }
  
  // 2. 如果category是ID，尝试从store获取
  const cat = categoryStore.getCategoryById(category);
  if (cat) {
    // 处理图标名称映射
    let iconName = cat.icon;
    // 如果是Font Awesome风格的图标名，映射为uView Plus图标名
    if (iconMapping[iconName]) {
      iconName = iconMapping[iconName];
      console.log(`图标映射：将${cat.icon}转换为${iconName}`);
    }
    
    return {
      id: cat.id,
      name: cat.name,
      icon: iconName,
      iconContainerBgColor: cat.iconContainerBgColor,
      color: cat.color, // Use color from store
      type: cat.type,
    };
  }
  
  // 3. 找不到对应分类时的默认值
  return {
    id: 'unknown',
    name: '未分类',
    icon: 'question', // uView Plus general question mark
    iconContainerBgColor: 'var(--bg-secondary, #f5f5f5)',
    color: 'var(--text-primary, #333333)', // Default icon color for neutral background
    type: 'expense',
  };
}

// 添加处理编辑和删除交易记录的方法
function handleEditTransaction(transaction: Transaction) {
  // 跳转到记账页面并传递编辑参数
  const txId = transaction.id;
  const amount = Math.abs(transaction.amount);
  // 优化：参数统一为type
  uni.navigateTo({
    url: `/pages/transaction/record?id=${txId}&amount=${amount}&type=${transaction.type}`,
    fail: (error) => {
      console.error('Navigate to record page failed:', error);
      uni.showToast({ title: '页面跳转失败', icon: 'none' });
    },
  });
}

async function handleDeleteTransaction(transaction: Transaction) {
  // 确认是否删除
  uni.showModal({
    title: '删除确认',
    content: '确定要删除该笔交易记录吗？',
    confirmColor: 'var(--color-error, #F44336)',
    success: async (res) => {
      if (res.confirm) {
        try {
          const txId = transaction.id;
          if (!txId) {
            uni.showToast({ title: '交易ID无效', icon: 'none' });
            return;
          }
          
          // 调用Store删除交易
          await transactionStore.removeTransaction(txId.toString());
          uni.showToast({ title: '删除成功', icon: 'success' });
        } catch (error) {
          console.error('Failed to delete transaction:', error);
          uni.showToast({ title: '删除失败', icon: 'none' });
        }
      }
    },
  });
}
</script>

<template>
  <view class="home-page">
    <!-- 1. 使用 AppNavBar 组件替代自定义导航栏 -->
    <AppNavBar title="账无忌" theme="default" :show-back="false">
      <template #left>
        <view>
          <view class="month-selector" @click="handleMonthSelectorClick">
            {{ currentDate.year }}年{{ String(currentDate.month).padStart(2, '0') }}月
            <AppIcon icon="arrow-down" size="sm" color="var(--text-inverse, #FFFFFF)" />
          </view>
        </view>
      </template>
      <template #right>
        <view class="nav-right">
          <AppIcon icon="bell" color="var(--text-inverse, #FFFFFF)" class="nav-icon" />
          <AppIcon
            icon="calendar"
            color="var(--text-inverse, #FFFFFF)" 
            class="nav-icon"
            @click="handleCalendarClick"
          />
        </view>
      </template>
    </AppNavBar>

    <!-- 内容区域 -->
    <view class="home-page__content-area">
      <!-- 2. 功能模块 (移除背景) -->
      <view class="feature-modules">
        <!-- TODO: 账本切换、预算、资产、财务顾问、日历 功能和跳转 -->
        <view class="feature-item locked" @click="handleFeatureClick('book')">
          <view class="feature-icon-wrapper bg-book">
            <AppIcon icon="file-text" color="var(--color-primary, #FF6B35)" class="feature-icon" />
            <view class="lock-badge">
              <AppIcon icon="lock-fill" size="sm" color="var(--color-primary, #FF6B35)" />
            </view>
          </view>
          <text class="feature-label">
            账本
          </text>
        </view>
        <view class="feature-item" @click="handleFeatureClick('budget')">
          <view class="feature-icon-wrapper bg-budget">
            <AppIcon icon="integral" color="#3A7CFF" class="feature-icon" />
          </view>
          <text class="feature-label">
            预算
          </text>
        </view>
        <view class="feature-item" @click="handleFeatureClick('asset')">
          <view class="feature-icon-wrapper bg-asset">
            <AppIcon icon="rmb-circle" color="#FFA53D" class="feature-icon" />
          </view>
          <text class="feature-label">
            资产
          </text>
        </view>
        <view class="feature-item" @click="handleFeatureClick('advisor')">
          <view class="feature-icon-wrapper bg-advisor">
            <AppIcon icon="tags" color="#9A6AFF" class="feature-icon" />
          </view>
          <text class="feature-label">
            顾问
          </text>
        </view>
        <view class="feature-item" @click="handleFeatureClick('calendar')">
          <view class="feature-icon-wrapper bg-calendar">
            <AppIcon
              icon="calendar"
              color="var(--color-primary, #FF6B35)"
              class="feature-icon"
            />
          </view>
          <text class="feature-label">
            日历
          </text>
        </view>
      </view>

      <!-- 3. 使用 AppCard 包装本月预算摘要 -->
      <AppCard custom-class="budget-card" no-padding>
        <view class="budget-card__content">
          <view class="budget-card__header">
            <view>
              <view class="budget-card__label">
                本月预算
              </view>
              <view class="budget-card__total">
                ￥{{ assetStore.budget.total.toLocaleString('zh-CN') }}
              </view>
            </view>
            <view class="text-align-right">
              <view class="budget-card__label">
                已使用
              </view>
              <view class="budget-card__used">
                ￥{{ assetStore.budget.used.toLocaleString('zh-CN') }}
              </view>
            </view>
          </view>
          <view class="budget-card__progress-bar">
            <view
              class="budget-card__progress-inner"
              :style="{ width: `${assetStore.budgetPercentage}%` }"
            />
          </view>
          <view class="budget-card__footer">
            <view>{{ assetStore.budgetPercentage }}%</view>
            <view>剩余: ￥{{ assetStore.budget.remaining.toLocaleString('zh-CN') }}</view>
          </view>
        </view>
      </AppCard>

      <!-- 4. 使用 AppCard 包装快捷记账 -->
      <AppCard custom-class="shortcuts-card">
        <template #header>
          <view class="home-page__section-header">
            <text class="section-title">
              快捷记账
            </text>
            <text class="view-all">
              全部
            </text>
            <!-- TODO: 跳转 -->
          </view>
        </template>

        <view class="shortcuts__grid">
          <!-- TODO: 跳转到对应功能 -->
          <view class="shortcut-item" @click="handleShortcutClick('voice')">
            <view class="shortcut-icon-wrapper bg-voice">
              <AppIcon icon="mic" color="var(--color-info, #2878FF)" class="shortcut-icon" />
            </view>
            <text class="shortcut-label">
              语音
            </text>
          </view>
          <view class="shortcut-item" @click="handleShortcutClick('scan')">
            <view class="shortcut-icon-wrapper bg-scan">
              <AppIcon icon="scan" color="var(--color-success, #10B981)" class="shortcut-icon" />
            </view>
            <text class="shortcut-label">
              扫票
            </text>
          </view>
          <view class="shortcut-item" @click="handleShortcutClick('expense')">
            <view class="shortcut-icon-wrapper bg-expense">
              <AppIcon icon="minus-circle" color="var(--color-primary, #FF6B35)" class="shortcut-icon" />
            </view>
            <text class="shortcut-label">
              支出
            </text>
          </view>
          <view class="shortcut-item" @click="handleShortcutClick('income')">
            <view class="shortcut-icon-wrapper bg-income">
              <AppIcon
                icon="plus-circle-fill"
                color="var(--color-success, #10B981)"
                class="shortcut-icon"
              />
            </view>
            <text class="shortcut-label">
              收入
            </text>
          </view>
        </view>
      </AppCard>

      <!-- 5. 使用 AppCard 包装待办提醒 -->
      <AppCard background="linear-gradient(135deg, #FFF5F2, #FFEDE8)">
        <template #header>
          <view class="home-page__section-header">
            <view class="reminder-card__title">
              <AppIcon
                icon="bell"
                color="var(--color-primary, #FF6B35)"
                class="reminder-icon"
              />
              待办提醒
            </view>
            <text class="view-all">
              查看全部
            </text>
            <!-- TODO: 跳转 -->
          </view>
        </template>
        <view class="reminder-items-container">
          <!-- TODO: 动态列表 -->
          <view class="reminder-item">
            <view>
              <view class="reminder-item__title">
                信用卡还款提醒
              </view>
              <view class="reminder-item__due">
                距离到期还有2天
              </view>
            </view>
            <AppButton size="mini" type="primary" round class="repay-button u-button-compact">
              立即还款
            </AppButton>
            <!-- TODO: 跳转 -->
          </view>
          <view class="reminder-item">
            <view>
              <view class="reminder-item__title">
                房租分期付款
              </view>
              <view class="reminder-item__date">
                11月15日到期
              </view>
            </view>
            <text class="reminder-item__amount">
              ￥1,200
            </text>
          </view>
        </view>
      </AppCard>

      <!-- 6. 近期账单 (使用增强的 AppCard 组件) -->
      <AppCard custom-class="transaction-card">
        <template #header>
          <view class="home-page__section-header">
            <text class="section-title">
              近期账单
            </text>
            <text class="view-all" @click="navigateToTransactionList">
              查看更多
            </text>
          </view>
        </template>

        <view class="transaction-list-wrapper">
          <!-- 添加加载状态 -->
          <view v-if="isLoading" class="loading-state">
            <text>数据加载中...</text>
          </view>
          <!-- 使用Store中的交易数据 -->
          <view v-else-if="transactionStore.recentTransactions.length > 0">
            <view
              v-for="tx in transactionStore.getRecentTransactions(3)"
              :key="tx.id"
              class="transaction-list-item-wrapper"
            >
              <AppSwipeAction :buttons-width="160">
                <template #default>
                  <view class="transaction-item" @click="handleTransactionClick(tx)">
                    <view class="item-left">
                      <!-- 直接从store获取分类信息 -->
                      <view
                        class="item-icon-wrapper"
                        :style="{ backgroundColor: getCategoryFromStore(tx.category).iconContainerBgColor }"
                      >
                        <AppIcon
                          :icon="getCategoryFromStore(tx.category).icon"
                          color="white"
                          class="transaction-icon"
                        />
                      </view>
                      <view class="item-details">
                        <view class="item-title">
                          {{ getCategoryFromStore(tx.category).name }}
                        </view>
                        <view class="item-subtitle">
                          {{ formatTransactionDate(tx.date) }}
                        </view>
                      </view>
                    </view>
                    <view class="item-amount" :class="tx.type">
                      {{
                        formatAmount(tx.amount, tx.type as 'income' | 'expense')
                      }}
                    </view>
                  </view>
                </template>
                
                <template #actions>
                  <AppSwipeActionButton
                    type="primary"
                    text="编辑"
                    icon="edit-pen"
                    :width="80"
                    @click="handleEditTransaction(tx)"
                  />
                  <AppSwipeActionButton
                    type="danger"
                    text="删除"
                    icon="trash"
                    :width="80"
                    @click="handleDeleteTransaction(tx)"
                  />
                </template>
              </AppSwipeAction>
            </view>
          </view>
          <view v-else class="no-records">
            暂无交易记录
          </view>
        </view>
      </AppCard>
    </view>

    <!-- 7. 浮动按钮 -->
    <!-- TODO: 跳转到语音记账 -->
    <!-- 悬浮按钮 -->
    <view class="home-page__fab" @click="handleFabClick">
      <!-- 提示文本 -->
      <view class="fab-tooltip">
        点击我聊天记账
      </view>
      <!-- 按钮内部容器 -->
      <view class="fab-inner">
        <!-- Logo图片 -->
        <image src="/static/logo/2LOGO.png" class="fab-logo" mode="aspectFit" />
      </view>
    </view>

    <!-- 8. 底部导航栏 -->
    <AppTabBar :active-index="activeTabIndex" :tabs="tabBarConfig" @change="handleTabChange" />

    <!-- 日历视图弹窗 -->
    <view v-if="showCalendar" class="modal-overlay" @click="closeCalendar">
      <view class="modal-content calendar-view" @click.stop>
        <view class="modal-header">
          <text>{{ calendarDate.year }}年{{ String(calendarDate.month).padStart(2, '0') }}月</text>
          <AppIcon
            icon="close"
            color="var(--text-primary, #333333)"
            class="close-icon"
            @click="closeCalendar"
          />
        </view>
        <view class="calendar-grid">
          <view class="calendar-weekdays">
            <text
              v-for="(day, index) in ['日', '一', '二', '三', '四', '五', '六']"
              :key="index"
              class="weekday"
            >
              {{ day }}
            </text>
          </view>
          <view class="calendar-days">
            <view
              v-for="day in daysInMonth"
              :key="day"
              class="calendar-day"
              :class="{
                'has-record': hasRecordOnDay(day),
                'active': day === selectedCalendarDay,
              }"
              @click="selectCalendarDay(day)"
            >
              {{ day }}
              <view v-if="hasRecordOnDay(day)" class="record-dot" />
            </view>
          </view>
        </view>
        <!-- 显示选中日期的账单记录 -->
        <view v-if="selectedCalendarDay" class="calendar-records">
          <view class="home-page__section-header">
            <text class="section-title">
              {{ calendarDate.year }}年{{ String(calendarDate.month).padStart(2, '0') }}月{{
                String(selectedCalendarDay).padStart(2, '0')
              }}日 账单
            </text>
          </view>
          <view v-if="selectedDayTransactions.length > 0">
            <view
              v-for="tx in selectedDayTransactions"
              :key="tx.id"
              class="transaction-item-placeholder"
            >
              <view class="item-left">
                <view
                  class="item-icon-wrapper"
                  :style="{ backgroundColor: getCategoryFromStore(tx.category).iconContainerBgColor }"
                >
                                                <AppIcon
                  :icon="getCategoryFromStore(tx.category).icon"
                  color="white"
                />
                </view>
                <view>
                  <view class="item-title">
                    {{ getCategoryFromStore(tx.category).name }}
                  </view>
                  <view class="item-subtitle">
                    {{ tx.notes || '无备注' }}
                  </view>
                </view>
              </view>
              <view class="item-amount" :class="tx.type">
                {{
                  formatAmount(tx.amount, tx.type as 'income' | 'expense')
                }}
              </view>
            </view>
          </view>
          <view v-else class="no-records">
            当日无账单记录
          </view>
        </view>
      </view>
    </view>

    <!-- 恢复：全局弹窗方式集成AppDatePicker，仅弹窗不展示按钮 -->
    <AppDatePicker
      v-model:visible="showWheelDatePicker"
      class="u-hide-date-picker-display"
      :model-value="`${currentDate.year}-${String(currentDate.month).padStart(2, '0')}`"
      type="month"
      title="选择月份"
      min-date="2000-01"
      max-date="2100-12"
      @confirm="handleWheelDateConfirm"
      @cancel="handleWheelDateCancel"
    >
      <template #trigger ></template>
    </AppDatePicker>
  </view>
</template>

<style lang="scss" scoped>
  .home-page {
    min-height: 100vh;
    background-color: var(--bg-primary, #fff);
  }

  // 保证内容区域顶部有合适间距
  .home-page__content-area {
    padding: 0 16px 20px;
    margin-top: 5px; // 减少内容区与导航栏的间距，避免顶部空白过大
  }

  // 修改后的导航部分样式 - 为AppNavBar组件添加自定义样式
  :deep(.app-nav-bar) {
    border-bottom: none;
  }

  // 月份选择器样式调整，确保不换行并符合原型设计
  .month-selector {
    display: flex;
    align-items: center;
    color: var(--text-inverse, #fff);
    font-size: 14px;
    font-weight: 500;
    background-color: var(--color-primary, #ff6b35);
    padding: 5px 10px;
    border-radius: 16px;
    min-width: 100px; // 增加最小宽度
    white-space: nowrap; // 防止文本换行

    .app-icon {
      margin-left: 3px; // 为下拉图标添加左边距
      font-size: 12px; // 调整图标大小
    }
  }

  .nav-right {
    display: flex;
    align-items: center;
  }

  .nav-icon {
    font-size: 20px;
    margin-left: 16px;
  }

  /* 功能模块，继续优化间距 */
  .feature-modules {
    display: flex;
    justify-content: space-between;
    padding: 6px 0; // 进一步减少上下内边距
    background-color: transparent;
    border-radius: 0;
    box-shadow: none;
    margin: 0 5px 10px; // 进一步减少底部外边距
  }

  .feature-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 60px;
    position: relative;

    &.locked .lock-badge {
      display: flex;
    }
  }

  .feature-icon-wrapper {
    width: 42px;
    height: 42px;
    border-radius: 12px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 8px;
    position: relative;

    // 使用CSS变量替代硬编码颜色
    &.bg-book {
      background-color: var(--bg-feature-book, #fff5f2);
    }

    &.bg-budget {
      background-color: var(--bg-feature-budget, #f0f7ff);
    }

    &.bg-asset {
      background-color: var(--bg-feature-asset, #fff6e9);
    }

    &.bg-advisor {
      background-color: var(--bg-feature-advisor, #f5f1ff);
    }

    &.bg-calendar {
      background-color: var(--bg-feature-calendar, #f5f6f8);
    }
  }

  .feature-icon {
    font-size: 20px;
  }

  .lock-badge {
    position: absolute;
    top: -8px;
    right: -8px;
    width: 16px;
    height: 16px;
    background-color: var(--bg-badge, #ffebeb);
    border-radius: 50%;
    display: none;
    justify-content: center;
    align-items: center;
  }

  .feature-label {
    font-size: var(--font-size-xs, 12px);
    color: var(--text-secondary, #666);
    margin-top: 4px;
  }

  /* 预算卡片样式 */
  .budget-card {
    overflow: visible;
    margin-bottom: 15px;

    :deep(.app-card__content) {
      padding: 0;
    }
  }

  .budget-card__content {
    background: linear-gradient(
      135deg,
      var(--color-primary, #ff6b35),
      var(--color-primary-light, #ff8c66)
    );
    color: var(--text-inverse, #fff);
    border-radius: var(--radius-lg, 18px);
    padding: 20px;
    position: relative;
    overflow: hidden;

    /* 添加装饰圆点 */
    &::before {
      content: '';
      position: absolute;
      bottom: -20%;
      left: -10%;
      width: 80px;
      height: 80px;
      background: rgb(255 255 255 / 8%);
      border-radius: 50%;
      z-index: 1;
    }

    &::after {
      content: '';
      position: absolute;
      top: -10%;
      right: -10%;
      width: 120px;
      height: 120px;
      background: rgb(255 255 255 / 10%);
      border-radius: 50%;
      z-index: 1;
    }
  }

  .budget-card__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    position: relative;
    z-index: 2;
  }

  .budget-card__label {
    font-size: 15px;
    margin-bottom: 5px;
    opacity: 0.9;
  }

  .budget-card__total {
    font-size: 30px;
    font-weight: 700;
    position: relative;
    z-index: 2;
  }

  .budget-card__used {
    font-size: 22px;
    font-weight: 600;
  }

  .budget-card__progress-bar {
    height: 10px;
    background-color: rgb(255 255 255 / 20%);
    border-radius: 5px;
    margin-bottom: 12px;
    overflow: hidden;
    position: relative;
    z-index: 2;
  }

  .budget-card__progress-inner {
    height: 100%;
    background-color: rgb(255 255 255 / 90%);
    border-radius: 5px;
    transition: width 0.5s ease;
  }

  .budget-card__footer {
    display: flex;
    justify-content: space-between;
    font-size: 14px;
    position: relative;
    z-index: 2;
  }

  /* 快捷记账卡片 */
  .shortcuts-card {
    margin-bottom: 15px;

    :deep(.app-card__header) {
      border-bottom: none;
      padding-bottom: 8px;
    }
  }

  .shortcuts__grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-md, 16px);
    padding: 0 var(--spacing-sm, 8px) var(--spacing-md, 16px);
  }

  .shortcut-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .shortcut-icon-wrapper {
    width: 54px;
    height: 54px;
    border-radius: var(--radius-lg, 16px);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: var(--spacing-xs, 8px);

    // 使用CSS变量替代硬编码颜色
    &.bg-voice {
      background-color: var(--bg-voice, #f0f9ff);
    }

    &.bg-scan {
      background-color: var(--bg-scan, #ecfdf5);
    }

    &.bg-expense {
      background-color: var(--bg-expense, #fff5f2);
    }

    &.bg-income {
      background-color: var(--bg-income, #ecfdf5);
    }
  }

  .shortcut-icon {
    font-size: 22px;
  }

  .shortcut-label {
    font-size: var(--font-size-xs, 12px);
    color: var(--text-secondary, #666);
  }

  /* 待办提醒卡片 */
  .reminder-card {
    margin-bottom: var(--spacing-md, 16px);
    overflow: visible;

    :deep(.app-card__content) {
      background: linear-gradient(
        135deg,
        var(--bg-reminder-start, #fff5f2),
        var(--bg-reminder-end, #ffede8)
      );
      border-radius: var(--radius-card, 12px);
      padding: var(--spacing-md, 16px);
    }

    :deep(.app-card__header) {
      padding: var(--spacing-sm, 12px) var(--spacing-md, 16px);
      border-bottom: none;
    }
  }

  .reminder-card__title {
    display: flex;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    color: var(--text-primary, #333);
  }

  .reminder-icon {
    font-size: 16px;
    margin-right: var(--spacing-sm, 8px);
  }

  .reminder-items-container {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm, 10px);
  }

  .reminder-item {
    background-color: var(--bg-primary, #fff);
    border-radius: 12px;
    padding: var(--spacing-sm, 12px);
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 5px rgb(0 0 0 / 3%);

    &__title {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
      color: var(--text-primary, #333);
    }

    &__due {
      color: var(--color-error, #f44336);
      font-size: 12px;
    }

    &__date {
      color: var(--text-secondary, #666);
      font-size: 12px;
    }

    &__amount {
      color: var(--text-secondary, #666);
      font-size: 13px;
      font-weight: 500;
    }
  }

  // 调整还款按钮样式
  .repay-button {
    padding: 6px 12px;
    font-size: 12px;
    height: auto;
    line-height: normal;
    border-radius: 20px;
  }

  /* 近期账单卡片 */
  .transaction-card {
    margin-bottom: 20px;

    :deep(.app-card__content) {
      padding: 0;
    }

    :deep(.app-card__header) {
      padding: 14px 20px;
      border-bottom: none;
      margin-bottom: 0;
    }
  }

  // 添加滑动列表项的容器样式
  .transaction-list-item-wrapper {
    margin-bottom: 1px; // 为列表项添加间距
    width: 100%;
  }

  // 重用的标题样式
  .home-page__section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-xs, 8px);
    width: 100%;
  }

  .section-title {
    font-size: 17px;
    font-weight: 600;
    color: var(--text-primary, #333);
    position: relative;
    display: inline-block;
  }

  .section-title::after {
    content: '';
    display: block;
    width: 20px;
    height: 3px;
    background: var(--color-primary, #ff6b35);
    position: absolute;
    bottom: -5px;
    left: 0;
    border-radius: 2px;
  }

  .view-all {
    color: var(--color-primary, #ff6b35);
    font-size: 14px;
    text-decoration: none;
  }

  // 剩下的样式保持不变，但确保使用CSS变量替代硬编码颜色
  .transaction-list-wrapper {
    padding: 0;
  }

  .transaction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 20px; // 增加左右内边距
    background-color: var(--bg-primary, #fff);
    width: 100%;
    box-sizing: border-box;

    &:active {
      background-color: var(--bg-secondary, #f5f5f5);
    }
  }

  .item-left {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
  }

  .item-icon-wrapper {
    width: 40px;
    height: 40px;
    border-radius: var(--radius-circle, 50%);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-right: 12px;
    flex-shrink: 0;
  }

  .transaction-icon {
    font-size: 18px;
  }

  .item-details {
    display: flex;
    flex-direction: column;
    justify-content: center;
  }

  .item-title {
    font-size: var(--font-size-normal, 15px);
    color: var(--text-primary, #333);
    font-weight: 500;
    margin-bottom: 2px;
  }

  .item-subtitle {
    font-size: var(--font-size-small, 13px);
    color: var(--text-hint, #999);
  }

  .item-amount {
    font-size: var(--font-size-normal, 15px);
    font-weight: 600;

    &.income {
      color: var(--color-success, #4caf50);
    }

    &.expense {
      color: var(--color-error, #f44336);
    }
  }

  /* 无记录提示样式 */
  .no-records {
    padding: 24px 0;
    text-align: center;
    color: var(--text-hint, #999);
  }

  /* 浮动按钮 */
  @keyframes floating {
    0% {
      transform: translateY(0);
    }

    50% {
      transform: translateY(-8px);
    }

    100% {
      transform: translateY(0);
    }
  }

  .home-page__fab {
    position: fixed;
    bottom: calc(var(--tab-bar-height, 60px) + var(--space-lg, 24px));
    right: var(--space-lg, 24px);
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: var(--z-index-sticky, 100);
    cursor: pointer;
  }

  .fab-inner {
    width: 60px;
    height: 60px;
    border-radius: var(--radius-circle, 50%);
    background-color: var(--bg-primary, #fff);
    box-shadow: var(--shadow-raised, 0 4px 16px rgb(0 0 0 / 12%));
    display: flex;
    justify-content: center;
    align-items: center;
    animation: floating 3s ease-in-out infinite;
    z-index: var(--z-index-overlay, 1000);
  }

  .fab-logo {
    display: block;
    width: 150%;
    height: 150%;
    object-fit: contain;
  }

  .fab-tooltip {
    position: absolute;
    top: -45px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--tooltip-bg, #333);
    color: var(--text-inverse, #fff);
    font-size: 14px;
    padding: 6px 12px;
    border-radius: 20px;
    white-space: nowrap;
    box-shadow: 0 2px 8px rgb(0 0 0 / 20%);

    &::after {
      content: '';
      position: absolute;
      bottom: -6px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid var(--tooltip-bg, #333);
    }
  }

  // 保留其他必要样式，但确保使用CSS变量替代硬编码颜色
  .text-align-right {
    text-align: right;
  }

  // 移除可能干扰图标颜色的样式
  :deep(.app-icon) {
    color: inherit; // 使用继承的颜色，而不是强制覆盖
  }

  .loading-state {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px 0;
    color: var(--text-hint, #999);
  }
</style>
