<script setup lang="ts">
import AppTabBar from '@/components/common/AppTabBar.vue';
import { ref } from 'vue';

const activeTabIndex = ref(3); // 当前页面是"我的"页，索引为3

// 底部标签栏配置
const tabBarConfig = [
  {
    label: '首页',
    icon: 'house',
    pagePath: '/pages/home/<USER>',
  },
  {
    label: '账单',
    icon: 'list',
    pagePath: '/pages/transaction/list',
  },
  {
    label: '统计',
    icon: 'chart-pie',
    pagePath: '/pages/analysis/index',
  },
  {
    label: '我的',
    icon: 'user',
    pagePath: '/pages/profile/index',
  },
];

// 处理TabBar切换事件
function handleTabChange(index: number) {
  if (index !== activeTabIndex.value) {
    switch (index) {
      case 0:
        uni.switchTab({ url: '/pages/home/<USER>' }); // 首页
        break;
      case 1:
        uni.switchTab({ url: '/pages/transaction/list' }); // 账单页
        break;
      case 2:
        uni.switchTab({ url: '/pages/analysis/index' }); // 统计页
        break;
      case 3:
        // 当前页面，不需要跳转
        break;
    }
  }
}
</script>

<template>
  <view class="profile-page">
    <!-- 页面内容 -->
    <view class="content">
      <view class="nav-bar" data-custom="true">
        <view class="nav-title">
          我的
        </view>
      </view>
      <view class="page-content">
        <text class="placeholder-text">
          个人页面开发中
        </text>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <AppTabBar :active-index="activeTabIndex" :tabs="tabBarConfig" @change="handleTabChange" />
  </view>
</template>

<style lang="scss" scoped>
  .profile-page {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
    background-color: var(--bg-primary, #fff);
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding-bottom: calc(
      var(--tab-bar-height, 60px) + env(safe-area-inset-bottom, 0px)
    ); /* 为底部TabBar预留空间 */
    // H5端底部导航栏高度

    /* #ifdef H5 */
    padding-bottom: 50px;

    /* #endif */
    // 小程序底部导航栏高度

    /* #ifdef MP-WEIXIN */
    padding-bottom: 56px;

    /* #endif */
    // APP端根据平台动态调整

    /* #ifdef APP-PLUS */

    /* #ifdef APP-PLUS-IOS */
    padding-bottom: calc(60px + env(safe-area-inset-bottom, 0px));

    /* #endif */

    /* #ifdef APP-PLUS-ANDROID */
    padding-bottom: 56px;

    /* #endif */

    /* #endif */
  }

  .nav-bar {
    height: var(--nav-height, 60px);
    padding-top: var(--status-bar-height, 25px);
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: var(--bg-primary, #fff);
    position: relative;
    box-shadow: 0 2px 4px rgb(0 0 0 / 5%);
  }

  .nav-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary, #333);
  }

  .page-content {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
  }

  .placeholder-text {
    font-size: 16px;
    color: var(--text-hint, #999);
  }
</style>
