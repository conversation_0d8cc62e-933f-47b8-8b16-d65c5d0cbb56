<script setup lang="ts">
import AppButton from '@/components/common/AppButton.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import { useUserStore } from '@/stores/user.store';
import { isFromPages, navigateHome, safeNavigateBack } from '@/utils/navigate';
import { getPlatformStyle } from '@/utils/platform';
import { ref } from 'vue';
import AppNavBar from '@/components/common/AppNavBar.vue';

const navHeight = getPlatformStyle('nav-height') || '60px';
const userStore = useUserStore();

// 返回按钮处理函数
function goBack() {
  // 判断是否从手势密码页面跳转而来
  if (isFromPages(['/pages/auth/gesture'])) {
    // 如果是从手势密码页面来，则跳转至首页
    safeNavigateBack({
      title: '提示',
      content: '您确定要取消设置个人资料吗？',
      confirmText: '确定',
      cancelText: '继续设置',
      success: (res) => {
        if (res.confirm) {
          // 如果用户确认返回，则跳转至首页
          navigateHome();
        }
      },
    });
  }
  else {
    // 否则正常返回上一页
    safeNavigateBack();
  }
}

// 模拟数据和状态
const avatarUrl = ref(
  'https://img.zcool.cn/community/01f09e5930bc44a8012193a314d745.png@1280w_1l_2o_100sh.png',
);
const nickname = ref('');
const genderOptions = ['男', '女', '保密'];
const gender = ref('');
const birthdate = ref('');
const location = ref(''); // 地区状态
const interests = ref<string[]>([]); // 兴趣爱好状态，使用数组

function skipSetup() {
  console.log('[DEBUG] 跳过基础资料设置，直接前往AI个性化设置页面');
  uni.redirectTo({
    url: '/pages/ai-personality/index',
    success: () => console.log('[DEBUG] 跳转到 ai-personality 成功'),
    fail: err => console.error('[DEBUG] 跳转到 ai-personality 失败:', err),
  });
}

function changeAvatar() {
  console.log('[DEBUG] 更换头像');
  uni.chooseImage({
    count: 1,
    success: (res) => {
      if (res.tempFilePaths && res.tempFilePaths.length > 0) {
        avatarUrl.value = res.tempFilePaths[0];
      }
    },
  });
}

function onGenderChange(e: any) {
  gender.value = genderOptions[e.detail.value];
}

function onBirthdateChange(e: any) {
  birthdate.value = e.detail.value;
}

// 模拟地区选择
function selectLocation() {
  console.log('[DEBUG] 选择地区');
  // 实际应用中会弹出地区选择器
  location.value = '广东省 深圳市'; // 模拟选择
}

// 模拟兴趣选择
function selectInterests() {
  console.log('[DEBUG] 选择兴趣爱好');
  // 实际应用中会弹出兴趣选择器
  interests.value = ['阅读', '旅行']; // 模拟选择
}

function saveProfile() {
  // 补充校验（手机号、邮箱格式等），这里省略
  if (!nickname.value) {
    uni.showToast({ title: '请输入昵称', icon: 'none' });
    return;
  }
  if (!gender.value) {
    uni.showToast({ title: '请选择性别', icon: 'none' });
    return;
  }
  if (!birthdate.value) {
    uni.showToast({ title: '请选择出生日期', icon: 'none' });
    return;
  }

  console.log('[DEBUG] 保存个人资料:', {
    avatar: avatarUrl.value,
    nickname: nickname.value,
    gender: gender.value,
    birthdate: birthdate.value,
    location: location.value,
    interests: interests.value,
  });

  // 更新用户状态
  userStore.setUserInfo({
    ...userStore.userInfo,
    name: nickname.value,
    avatar: avatarUrl.value,
    // 其他属性
    isProfileCompleted: true, // 标记基础资料已完成
  });

  // 保存成功，跳转到AI个性化设置页
  uni.showToast({ title: '保存成功', icon: 'success' });
  setTimeout(() => {
    console.log('[DEBUG] 资料保存成功，跳转到AI个性化设置页面');
    // 使用redirectTo替换当前页面，避免用户返回到个人资料设置页
    uni.redirectTo({
      url: '/pages/ai-personality/index',
      success: () => console.log('[DEBUG] 跳转到 ai-personality 成功'),
      fail: err => console.error('[DEBUG] 跳转到 ai-personality 失败:', err),
    });
  }, 1000);
}
</script>

<template>
  <view class="basic-setup-page">
    <!-- 顶部通用导航栏，右侧用slot放跳过按钮 -->
    <AppNavBar title="完善个人资料" theme="primary" :showBack="true">
      <template #right>
        <text class="skip-button" @click="skipSetup">跳过</text>
      </template>
    </AppNavBar>
    <!-- 内容区域 -->
    <view class="content">
      <text class="page-prompt">
        补充信息，让 AI 更好地为你服务 ✨
      </text>

      <view class="card">
        <!-- 头像 -->
        <view class="setting-item avatar-setting">
          <view class="setting-icon-wrapper icon-bg-avatar">
            <AppIcon icon="user" color="var(--text-inverse, #FFFFFF)" />
          </view>
          <text class="setting-label">
            头像
          </text>
          <view class="setting-action">
            <image :src="avatarUrl" class="avatar-preview" @click="changeAvatar" />
            <AppIcon icon="chevron-right" color="var(--text-hint, #999999)" />
          </view>
        </view>

        <!-- 昵称 -->
        <view class="setting-item">
          <view class="setting-icon-wrapper icon-bg-nickname">
            <AppIcon icon="pencil-alt" color="var(--text-inverse, #FFFFFF)" />
          </view>
          <text class="setting-label">
            昵称
          </text>
          <view class="setting-action">
            <input v-model="nickname" type="text" class="setting-value" placeholder="请输入昵称"/>
          </view>
        </view>

        <!-- 性别 -->
        <picker mode="selector" :range="genderOptions" @change="onGenderChange">
          <view class="setting-item">
            <view class="setting-icon-wrapper icon-bg-gender">
              <AppIcon icon="venus-mars" color="var(--text-inverse, #FFFFFF)" />
            </view>
            <text class="setting-label">
              性别
            </text>
            <view class="setting-action">
              <text class="setting-value" :class="{ placeholder: !gender }">
                {{
                  gender || '请选择'
                }}
              </text>
              <AppIcon icon="chevron-right" color="var(--text-hint, #999999)" />
            </view>
          </view>
        </picker>

        <!-- 出生日期 -->
        <picker mode="date" :value="birthdate" @change="onBirthdateChange">
          <view class="setting-item">
            <view class="setting-icon-wrapper icon-bg-birthday">
              <AppIcon icon="birthday-cake" color="var(--text-inverse, #FFFFFF)" />
            </view>
            <text class="setting-label">
              出生日期
            </text>
            <view class="setting-action">
              <text class="setting-value" :class="{ placeholder: !birthdate }">
                {{
                  birthdate || '请选择'
                }}
              </text>
              <AppIcon icon="chevron-right" color="var(--text-hint, #999999)" />
            </view>
          </view>
        </picker>
      </view>

      <!-- 第三个卡片：其他信息 -->
      <view class="card">
        <!-- 所在地区 -->
        <view class="setting-item" @click="selectLocation">
          <view class="setting-icon-wrapper icon-bg-location">
            <AppIcon icon="map-marker-alt" color="var(--text-inverse, #FFFFFF)" />
          </view>
          <text class="setting-label">
            所在地区
          </text>
          <view class="setting-action">
            <text class="setting-value" :class="{ placeholder: !location }">
              {{
                location || '请选择'
              }}
            </text>
            <AppIcon icon="chevron-right" color="var(--text-hint, #999999)" />
          </view>
        </view>

        <!-- 兴趣爱好 -->
        <view class="setting-item" @click="selectInterests">
          <view class="setting-icon-wrapper icon-bg-interests">
            <AppIcon icon="heart" color="var(--text-inverse, #FFFFFF)" />
          </view>
          <text class="setting-label">
            兴趣爱好
          </text>
          <view class="setting-action">
            <text
              class="setting-value interest-preview"
              :class="{ placeholder: interests.length === 0 }"
            >
              {{ interests.length > 0 ? interests.join(' ') : '请选择 (可选)' }}
            </text>
            <AppIcon icon="chevron-right" color="var(--text-hint, #999999)" />
          </view>
        </view>
      </view>
    </view>

    <!-- 保存按钮 -->
    <view class="save-button-container">
      <AppButton type="primary" size="large" @click="saveProfile">
        <view class="save-button-content">
          <AppIcon icon="check" class="save-button-icon" />
          <text>完成</text>
        </view>
      </AppButton>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .basic-setup-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-secondary, #f8f9fa); // 使用变量
  }

  .content {
    flex: 1;
    padding: var(--space-md, 16px);
    overflow-y: auto;

    .page-prompt {
      display: block; // 独占一行
      text-align: center;
      color: var(--text-secondary, #666);
      font-size: 14px;
      margin-bottom: var(--space-lg, 24px);
    }
  }

  .card {
    background-color: var(--color-bg-primary, #fff);
    border-radius: var(--radius-card, 18px); // 使用变量
    margin-bottom: var(--space-lg, 24px); // 增大卡片间距
    box-shadow: 0 4px 12px var(--color-shadow, rgb(0 0 0 / 6%)); // 使用变量
    overflow: hidden;

    &:last-of-type {
      margin-bottom: 0; // 最后一个卡片下方无外边距
    }

    .setting-item:last-child {
      border-bottom: none; // 卡片内最后一个设置项无下边框
    }
  }

  .setting-item {
    display: flex;
    align-items: center;
    padding: var(--space-sm, 8px) var(--space-md, 12px);
    border-bottom: 1px solid var(--color-border, rgb(0 0 0 / 8%));
    min-height: 24px;
    background-color: var(--color-bg-primary, #fff);
    transition: background-color 0.1s ease;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f0f0f0;
    }
  }

  .setting-icon-wrapper {
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px; // 调整圆角
    margin-right: var(--space-md, 16px);
    flex-shrink: 0;

    &.icon-bg-avatar {
      background-color: #4caf50;
    }

    &.icon-bg-nickname {
      background-color: #2196f3;
    }

    &.icon-bg-gender {
      background-color: #e91e63;
    }

    &.icon-bg-birthday {
      background-color: #ff9800;
    }

    &.icon-bg-phone {
      background-color: #673ab7;
    } // Deep Purple (来自原型)
    &.icon-bg-email {
      background-color: #00bcd4;
    } // Cyan (来自原型)
    &.icon-bg-location {
      background-color: #795548;
    } // Brown (来自原型)
    &.icon-bg-interests {
      background-color: #ff5722;
    } // Deep Orange (来自原型)
  }

  .setting-label {
    flex-shrink: 0;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary, #333);
    margin-right: auto; // 将标签推到左边
  }

  .setting-action {
    display: flex;
    align-items: center;
    gap: var(--space-sm, 8px);
    margin-left: var(--space-md, 16px); // 与标签的间距
  }

  .avatar-preview {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-avatar, 50%); // 使用变量
    object-fit: cover;
    border: 1px solid var(--color-border, rgb(0 0 0 / 8%));
  }

  .setting-value {
    font-size: 16px;
    color: var(--text-secondary, #666);
    text-align: right;
    border: none;
    background: transparent;
    outline: none;
    width: 100%; // 确保输入框能接收点击

    &.placeholder {
      color: var(--text-hint, #999);
    }
  }

  .picker {
    width: 100%;
  }

  .picker > .setting-item {
    padding: 0;
    border-bottom: none;
    background: transparent;
    min-height: unset;

    &:active {
      background-color: transparent;
    }
  }

  .setting-chevron {
    font-size: 14px;
    color: var(--text-hint, #999);
  }

  .save-button-container {
    padding: var(--space-md, 16px) var(--space-md, 16px)
      calc(var(--space-md, 16px) + env(safe-area-inset-bottom)); // 安全区域

    background: var(--color-bg-primary, #fff);
    border-top: 1px solid var(--color-border, rgb(0 0 0 / 8%));
    position: sticky; // 固定在底部
    bottom: 0;
    left: 0;
    right: 0;

    // 使用高特异性选择器处理按钮样式
    &:deep(.app-button) {
      margin: 0;
      width: 100%; // 添加宽度 100%
    }
  }

  .save-button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm, 8px);
  }

  .save-button-icon {
    font-size: 16px;
    color: var(--text-inverse, #fff);
  }

  .interest-preview {
    color: var(--text-secondary, #666);
    text-align: right;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    max-width: 180px; // 限制最大宽度

    &.placeholder {
      color: var(--text-hint, #999);
    }
  }
</style>
