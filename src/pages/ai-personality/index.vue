<script setup lang="ts">
import AppButton from '@/components/common/AppButton.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppModal from '@/components/common/AppModal.vue';
import AppNavBar from '@/components/common/AppNavBar.vue';
import { useUserStore } from '@/stores/user.store';
import { isFromPages, safeNavigateBack } from '@/utils/navigate';
import { getPlatformStyle } from '@/utils/platform';
import { onLoad } from '@dcloudio/uni-app';
import { ref } from 'vue';

const userStore = useUserStore();
const navHeight = ref('56px'); // Default nav height

onLoad(() => {
  navHeight.value = getPlatformStyle('nav-height') || '56px';
});

// --- State References ---
const trustLevel = ref(35); // Example trust level
const activeTab = ref('relationship');
const aiNickname = ref('');
const selectedRelationship = ref('理财助手');
const selectedTone = ref('亲切友好');
const useEmojis = ref(true);
const selectedConfirmMethod = ref('仅复杂时确认');
const notifyBudget = ref(true);
const notifySummary = ref(false);
const selectedAnalysisFocus = ref('消费模式分析');

// --- Modal State ---
const modalRef = ref<InstanceType<typeof AppModal> | null>(null);
const modalTitle = ref('');
const currentModalOptions = ref<string[]>([]);
const currentModalType = ref<'relationship' | 'tone' | 'confirm' | 'analysis' | null>(null);

// --- Options Data ---
const relationshipOptions = [
  '财务顾问',
  '理财教练',
  '理财助手',
  '记账伙伴',
  '金融导师',
  '预算管家',
  '财富管理师',
  '省钱小能手',
  '投资顾问',
  '财务规划师',
];
const toneOptions = ['亲切友好', '专业正式', '简洁明了', '幽默风趣', '耐心细致', '高冷效率'];
const confirmOptions = ['总是确认', '仅复杂时确认', '尽量直接记录'];
const analysisOptions = [
  '消费模式分析',
  '省钱建议',
  '预算执行情况',
  '收入来源分析',
  '投资回报分析',
];

// --- Methods ---
function goBack() {
  // 判断是否从个人资料设置页面跳转而来
  if (isFromPages(['/pages/profile/basic-setup'])) {
    // 如果用户已经走到了AI个性化这一步，但点击返回，通常是完成流程中的返回
    // 提示用户
    safeNavigateBack({
      title: '提示',
      content: '您的设置尚未完成，确定要返回吗？',
      confirmText: '确定返回',
      cancelText: '继续设置',
    });
  }
  else {
    // 正常返回
    safeNavigateBack();
  }
}

function switchTab(tabId: 'relationship' | 'communication' | 'interaction') {
  activeTab.value = tabId;
}

function openModal(type: 'relationship' | 'tone' | 'confirm' | 'analysis') {
  currentModalType.value = type;
  switch (type) {
    case 'relationship':
      modalTitle.value = '选择与 AI 的关系';
      currentModalOptions.value = relationshipOptions;
      break;
    case 'tone':
      modalTitle.value = '选择对话语气';
      currentModalOptions.value = toneOptions;
      break;
    case 'confirm':
      modalTitle.value = '选择记账确认方式';
      currentModalOptions.value = confirmOptions;
      break;
    case 'analysis':
      modalTitle.value = '选择分析侧重点';
      currentModalOptions.value = analysisOptions;
      break;
  }
  modalRef.value?.open();
}

function isSelected(option: string): boolean {
  switch (currentModalType.value) {
    case 'relationship':
      return option === selectedRelationship.value;
    case 'tone':
      return option === selectedTone.value;
    case 'confirm':
      return option === selectedConfirmMethod.value;
    case 'analysis':
      return option === selectedAnalysisFocus.value;
    default:
      return false;
  }
}

function selectOption(option: string) {
  // This function is primarily for visual feedback within the modal.
  // The actual update happens in handleModalConfirm.
  // We need to trigger a re-render or update a temporary selection state if immediate feedback is needed.
  console.log(`Option tentatively selected: ${option}`);
  // To provide immediate visual feedback:
  switch (currentModalType.value) {
    case 'relationship':
      selectedRelationship.value = option;
      break; // Temporarily update for visual feedback
    case 'tone':
      selectedTone.value = option;
      break;
    case 'confirm':
      selectedConfirmMethod.value = option;
      break;
    case 'analysis':
      selectedAnalysisFocus.value = option;
      break;
  }
  // Close the modal after selection
  // Delay closing slightly to see the selection feedback
  setTimeout(() => {
    modalRef.value?.close();
    // Actual confirmation logic is not here, it's handled by AppModal's confirm event if needed
    // Or we can directly update the final state here since we don't have a confirm button
    handleModalConfirm(); // Call confirm logic directly since there's no confirm button
  }, 100);
}

function handleModalConfirm() {
  // This would be triggered by AppModal's confirm event if it had one,
  // or called directly after selection as done in selectOption.
  console.log(`Settings confirmed for type: ${currentModalType.value}`);
  // Final state update based on the selection made in selectOption
  // (already done tentatively in selectOption for visual feedback)
}

function saveSettings() {
  // 准备要保存的数据
  const settingsToSave = {
    aiNickname: aiNickname.value,
    relationship: selectedRelationship.value,
    tone: selectedTone.value,
    useEmojis: useEmojis.value,
    confirmMethod: selectedConfirmMethod.value,
    notifyBudget: notifyBudget.value,
    notifySummary: notifySummary.value,
    analysisFocus: selectedAnalysisFocus.value,
    isAIPersonalized: true, // 标记已完成个性化
  };

  console.log('[DEBUG] Saving AI Personality Settings:', settingsToSave);

  // 更新 Pinia Store (假设有相应 action 或直接修改 state)
  // Example: userStore.updateAiSettings(settingsToSave);
  // Or update individual states if they map to userInfo
  userStore.setUserInfo({
    ...userStore.userInfo,
    // Update relevant fields from settingsToSave if they map to userInfo
    isAIPersonalized: true, // Mark as personalized
  });

  // 调用 API 保存到后端 (如果需要)
  // await apiSaveAiSettings(settingsToSave);

  uni.showToast({ title: '设置已应用', icon: 'success' });

  // 跳转到首页 (根据流程图)
  setTimeout(() => {
    // 使用 switchTab 确保跳转到 TabBar 首页
    uni.switchTab({ 
      url: '/pages/home/<USER>',
      success: () => console.log('[DEBUG] 完成AI设置，跳转到首页成功'),
      fail: err => console.error('[DEBUG] 跳转到首页失败:', err),
    });
  }, 1000);
}
</script>

<template>
  <view class="ai-personality-page">
    <!-- 顶部通用导航栏，完全替换自定义导航栏 -->
    <AppNavBar title="定制你的助手" theme="primary" :showBack="true" />
    <!-- 内容区域 -->
    <view class="content-area">
      <!-- AI Profile Card - 添加内联 margin-top -->
      <view class="card ai-profile" :style="{ marginTop: navHeight }">
        <view id="relationDisplay" class="relation-badge">
          {{ selectedRelationship }}
        </view>
        <view class="ai-avatar" :style="{ backgroundImage: 'url(/static/2LOGO.png)' }" />
        <text class="ai-name">
          账无忌
        </text>
        <view class="ai-status">
          <AppIcon icon="circle" size="xs" color="#4CAF50" /> 在线・持续进化中
        </view>
        <view class="trust-meter">
          <view class="trust-bar">
            <view class="trust-level" :style="{ width: `${trustLevel}%` }" />
          </view>
          <view class="trust-label">
            <text>初识</text>
            <text>熟悉</text>
            <text>默契</text>
          </view>
        </view>
      </view>

      <!-- Settings Tabs -->
      <view class="settings-tabs">
        <view
          class="tab-item"
          :class="{ active: activeTab === 'relationship' }"
          @click="switchTab('relationship')"
        >
          称呼与关系
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'communication' }"
          @click="switchTab('communication')"
        >
          沟通风格
        </view>
        <view
          class="tab-item"
          :class="{ active: activeTab === 'interaction' }"
          @click="switchTab('interaction')"
        >
          交互偏好
        </view>
      </view>

      <!-- Settings Sections -->
      <view v-show="activeTab === 'relationship'" class="settings-section">
        <view class="card">
          <view class="setting-item">
            <view class="setting-icon icon-nickname">
              <AppIcon icon="pencil-alt" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                AI 如何称呼您？
              </text>
              <input
                v-model="aiNickname"
                type="text"
                class="setting-input"
                placeholder="输入昵称，如'主人'"
                placeholder-class="input-placeholder"
              />
            </view>
          </view>
          <view class="setting-item" @click="openModal('relationship')">
            <view class="setting-icon icon-relationship">
              <AppIcon icon="users" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                与 AI 的关系
              </text>
              <text class="setting-desc">
                定义你们的互动模式
              </text>
            </view>
            <view class="setting-action">
              <text class="setting-value">
                {{ selectedRelationship }}
              </text>
              <AppIcon icon="chevron-right" color="var(--color-text-hint)" />
            </view>
          </view>
        </view>
      </view>

      <view v-show="activeTab === 'communication'" class="settings-section">
        <view class="card">
          <view class="setting-item" @click="openModal('tone')">
            <view class="setting-icon icon-tone">
              <AppIcon icon="comment-dots" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                对话语气
              </text>
              <text class="setting-desc">
                影响 AI 回答的风格
              </text>
            </view>
            <view class="setting-action">
              <text class="setting-value">
                {{ selectedTone }}
              </text>
              <AppIcon icon="chevron-right" color="var(--color-text-hint)" />
            </view>
          </view>
          <view class="setting-item">
            <view class="setting-icon icon-emoji">
              <AppIcon icon="smile-beam" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                使用 Emoji 表情
              </text>
              <text class="setting-desc">
                让对话更生动
              </text>
            </view>
            <view class="setting-action">
              <switch
                :checked="useEmojis"
                color="var(--color-primary)"
                @change="useEmojis = $event.detail.value"
              />
            </view>
          </view>
        </view>
      </view>

      <view v-show="activeTab === 'interaction'" class="settings-section">
        <view class="card">
          <view class="setting-item" @click="openModal('confirm')">
            <view class="setting-icon icon-confirm">
              <AppIcon icon="check-double" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                记账确认方式
              </text>
              <text class="setting-desc">
                平衡效率与准确性
              </text>
            </view>
            <view class="setting-action">
              <text class="setting-value">
                {{ selectedConfirmMethod }}
              </text>
              <AppIcon icon="chevron-right" color="var(--color-text-hint)" />
            </view>
          </view>
          <text class="setting-group-title">
            主动提醒设置
          </text>
          <view class="setting-item">
            <view
              class="setting-icon icon-reminders"
              style="background-color: var(--color-bg-icon-yellow)"
            >
              <AppIcon icon="exclamation-triangle" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                预算超支/临近提醒
              </text>
              <text class="setting-desc">
                及时了解预算状况
              </text>
            </view>
            <view class="setting-action">
              <switch
                :checked="notifyBudget"
                color="var(--color-primary)"
                @change="notifyBudget = $event.detail.value"
              />
            </view>
          </view>
          <view class="setting-item">
            <view class="setting-icon icon-reminders">
              <AppIcon icon="calendar-alt" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                每日/每周收支小结
              </text>
              <text class="setting-desc">
                定期回顾财务状况
              </text>
            </view>
            <view class="setting-action">
              <switch
                :checked="notifySummary"
                color="var(--color-primary)"
                @change="notifySummary = $event.detail.value"
              />
            </view>
          </view>
          <view class="setting-item" @click="openModal('analysis')">
            <view class="setting-icon icon-analysis">
              <AppIcon icon="chart-pie" color="#fff" />
            </view>
            <view class="setting-content">
              <text class="setting-label">
                分析侧重点
              </text>
              <text class="setting-desc">
                AI 分析时关注的方面
              </text>
            </view>
            <view class="setting-action">
              <text class="setting-value">
                {{ selectedAnalysisFocus }}
              </text>
              <AppIcon icon="chevron-right" color="var(--color-text-hint)" />
            </view>
          </view>
        </view>
      </view>
    </view>
    <!-- End content-area -->

    <!-- Save Button Area -->
    <view class="save-button-container">
      <AppButton type="primary" size="large" @click="saveSettings">
        <AppIcon icon="check-circle" custom-class="btn-icon" />
        应用设置
      </AppButton>
    </view>

    <!-- Modal Component -->
    <AppModal ref="modalRef" :title="modalTitle" @confirm="handleModalConfirm">
      <view class="tag-container">
        <view
          v-for="option in currentModalOptions"
          :key="option"
          class="tag"
          :class="{ selected: isSelected(option) }"
          @click="selectOption(option)"
        >
          {{ option }}
        </view>
      </view>
    </AppModal>
  </view>
</template>

<style lang="scss" scoped>
  .ai-personality-page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-primary, #fff);
  }

  /* --- Content Area --- */
  .content-area {
    flex: 1; /* Fill available space */
    overflow-y: auto; /* Scroll only if needed */
    padding-bottom: 90px; /* Space for fixed footer, adjust as needed */
    background-color: var(--color-bg-secondary);
    overscroll-behavior-y: contain; /* Prevent bounce/overscroll effect */
  }

  /* --- Card Base Style --- */
  .card {
    background-color: var(--color-bg-primary);
    border-radius: var(--radius-card);
    margin: 0 var(--spacing-md, 16px) var(--spacing-lg, 20px);
    box-shadow: 0 6px 16px var(--color-shadow);
    overflow: hidden;
  }

  /* --- AI Profile Card --- */
  .ai-profile {
    position: relative;
    padding: var(--spacing-lg, 25px) 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    background: linear-gradient(135deg, var(--color-primary-light), var(--color-primary-lighter));
    text-align: center;
    margin-left: 0;
    margin-right: 0;
    border-radius: 0 0 var(--radius-card) var(--radius-card);
  }

  .ai-avatar {
    width: 90px;
    height: 90px;
    border-radius: 30%;
    box-shadow: 0 8px 16px var(--color-shadow-orange);
    border: 3px solid var(--color-bg-primary);
    margin-bottom: var(--spacing-md, 15px);
    background-color: var(--color-bg-secondary);
    background-size: 120%;
    background-position: center;
    background-repeat: no-repeat;
  }

  .ai-name {
    font-size: 22px;
    font-weight: 700;
    color: var(--color-primary);
    margin: 0;
    text-shadow: 0 1px 0 var(--color-bg-primary);
  }

  .ai-status {
    margin-top: var(--spacing-sm, 8px);
    display: inline-flex;
    align-items: center;
    background-color: rgb(255 255 255 / 70%);
    padding: var(--spacing-xs, 4px) var(--spacing-md, 12px);
    border-radius: var(--radius-tag);
    font-size: 12px;
    color: var(--color-text-secondary);

    .ai-icon {
      color: #4caf50; /* Green dot */
      margin-right: 5px;
      font-size: 8px;
    }
  }

  .relation-badge {
    position: absolute;
    top: var(--spacing-lg, 20px);
    right: var(--spacing-lg, 20px);
    background-color: var(--color-primary);
    color: white;
    padding: 6px 12px;
    border-radius: var(--radius-tag);
    font-size: 12px;
    font-weight: 500;
    box-shadow: 0 2px 8px var(--color-shadow-orange);
  }

  .trust-meter {
    width: 85%;
    margin: var(--spacing-md, 15px) auto 0;
  }

  .trust-bar {
    height: 6px;
    background-color: rgb(255 107 53 / 20%);
    border-radius: 3px;
    overflow: hidden;
    margin-bottom: 5px;
  }

  .trust-level {
    height: 100%;
    background-color: var(--color-primary);
    border-radius: 3px;
    transition: width var(--transition-speed) ease;
  }

  .trust-label {
    display: flex;
    justify-content: space-between;
    font-size: 11px;
    color: var(--color-text-hint);

    text {
      flex: 1;
      text-align: center;

      &:first-child {
        text-align: left;
      }

      &:last-child {
        text-align: right;
      }
    }
  }

  /* --- Settings Tabs --- */
  .settings-tabs {
    display: flex;
    align-items: center;
    margin: var(--spacing-lg, 20px) var(--spacing-md, 16px);
    padding: var(--spacing-sm, 6px);
    background-color: var(--color-bg-primary);
    border-radius: 15px;
    box-shadow: 0 2px 8px var(--color-shadow);
  }

  .tab-item {
    flex: 1;
    padding: 8px 0;
    text-align: center;
    font-size: 14px;
    color: var(--color-text-secondary);
    border-radius: 10px;
    transition: all var(--transition-speed) ease;
    cursor: pointer;
    font-weight: 500;

    &.active {
      color: var(--color-primary);
      background-color: var(--color-primary-light);
      font-weight: 600;
    }
  }

  /* --- Settings Sections --- */
  .settings-section {
    /* No display none needed with v-show */
  }

  /* Setting Group Title */
  .setting-group-title {
    font-size: 14px;
    color: var(--color-text-hint);
    margin-bottom: var(--spacing-sm, 8px);
    padding-left: var(--spacing-lg, 30px); /* Align with setting content */
    margin-top: var(--spacing-md, 15px);
    font-weight: 500;
  }

  /* Settings Item */
  .setting-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md, 16px) var(--spacing-md, 16px);
    border-bottom: 1px solid var(--color-border);
    position: relative;
    background-color: var(--color-bg-primary);
    transition: background-color 0.2s;

    &:last-child {
      border-bottom: none;
    }

    &:active {
      background-color: #f9f9f9;
    }
  }

  .setting-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: var(--radius-icon);
    margin-right: var(--spacing-md, 15px);
    font-size: 16px;
    color: white;
    flex-shrink: 0;
  }

  .icon-nickname {
    background-color: var(--color-bg-icon-yellow);
  }

  .icon-relationship {
    background-color: var(--color-bg-icon-blue);
  }

  .icon-tone {
    background-color: var(--color-bg-icon-purple);
  }

  .icon-emoji {
    background-color: var(--color-bg-icon-green);
  }

  .icon-confirm {
    background-color: var(--color-bg-icon-blue);
  }

  .icon-reminders {
    background-color: var(--color-bg-icon-green);
  }

  .icon-analysis {
    background-color: var(--color-bg-icon-purple);
  }

  .setting-content {
    flex: 1;
    display: flex; /* Use flex for better alignment */
    flex-direction: column; /* Stack label and desc vertically */
  }

  .setting-label {
    font-size: 16px;
    font-weight: 500;

    // margin-bottom: 2px; /* Adjust spacing if desc exists */
    color: var(--color-text-primary);
  }

  .setting-desc {
    font-size: 13px;
    color: var(--color-text-secondary);
    line-height: 1.4;
    margin-top: 4px;
  }

  .setting-action {
    margin-left: var(--spacing-md, 15px);
    color: var(--color-text-hint);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm, 8px);
    flex-shrink: 0;
  }

  .setting-value {
    font-size: 14px;
    color: var(--color-text-secondary);
    font-weight: 500;
    max-width: 150px; /* Limit width */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    text-align: right;
  }

  /* Input Field inside Item */
  .setting-input {
    width: 100%;
    border: none;
    background: transparent;
    font-size: 16px;
    color: var(--color-text-primary);
    padding: 0; /* Remove padding */
    height: auto; /* Let it take natural height */
    line-height: 1.5; /* Ensure proper line height */
    margin-top: 4px; /* Spacing from label */
  }

  .input-placeholder {
    color: var(--color-text-hint);
  }

  /* Tag Container for selection in Modal */
  .tag-container {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-md, 10px);
    padding: var(--spacing-md, 15px);
  }

  .tag {
    padding: 8px 16px;
    border-radius: var(--radius-tag);
    background-color: var(--color-bg-secondary);
    color: var(--color-text-secondary);
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
    user-select: none;
    border: 1px solid transparent;

    &.selected {
      background-color: var(--color-primary-light);
      color: var(--color-primary);
      border: 1px solid var(--color-primary-border);
      font-weight: 600;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  /* --- Save Button --- */
  .save-button-container {
    position: fixed; /* Keep save button fixed */
    bottom: 0;
    left: 0;
    right: 0;
    padding: var(--spacing-md, 15px) var(--spacing-lg, 25px)
      calc(var(--spacing-lg, 25px) + env(safe-area-inset-bottom)); /* Adjust padding for safe area */

    background: linear-gradient(
      to top,
      var(--color-bg-primary) 80%,
      rgb(255 255 255 / 0%)
    ); /* Gradient background */

    z-index: 50;

    // Ensure it stays within the bounds in web view
    max-width: 414px;
    margin: 0 auto;

    // 使用高特异性选择器
    :deep(.app-button) {
      width: 100%; /* 使用高特异性选择器替代 !important */
      margin: 0;
      box-shadow: 0 6px 16px var(--color-shadow-orange);
    }
  }

  /* --- AppModal Styling --- */

  /* Ensure AppModal internal elements can be styled if needed */

  /* Example: Style the modal body's tag container */
  :deep(.app-modal__body .tag-container) {
    padding: var(--spacing-md, 15px);
  }

  :deep(.app-modal__body .tag) {
    /* Styles for tags inside the modal */
    padding: 8px 16px;

    /* ... other tag styles ... */
  }

  .ai-icon {
    margin-right: 12px;
    font-size: 20px; /* 确保图标大小适当 */
  }
</style>
