<script setup lang="ts">
// 1. Vue核心库
import { ref, computed, onMounted, onUnmounted, onActivated, getCurrentInstance, watch, nextTick } from 'vue';

// 2. Pinia状态
import { useCategoryStore } from '@/stores/category.store';
import { useTransactionStore } from '@/stores/transaction.store';
import { useUserStore } from '@/stores/user.store';

// 3. 组件导入
import AppTabBar from '@/components/common/AppTabBar.vue';
import AppDatePicker from '@/components/common/AppDatePicker.vue';
import AppSegmentedControl from '@/components/common/AppSegmentedControl.vue';
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppButton from '@/components/common/AppButton.vue';
import AppMonthNavigator from '@/components/common/AppMonthNavigator.vue';
import QiunDataCharts from '@/components/u-charts/qiun-data-charts.vue';

// 导入拆分的组件
import AnalysisCoreIndicators from './components/AnalysisCoreIndicators.vue';
import AnalysisTrendChart from './components/AnalysisTrendChart.vue';
import AnalysisExpenseComposition from './components/AnalysisExpenseComposition.vue';
import AnalysisBudgetExecution from './components/AnalysisBudgetExecution.vue';
import AnalysisFinancialInsights from './components/AnalysisFinancialInsights.vue';

// 4. Hooks
// (无hooks导入)

// 5. API和工具
import { formatAmount } from '@/utils/formatters';
import { getCssVariableValue, getChartColors } from '@/utils/colors';
import { getMonthStartAndEndDates } from '@/utils/date';
import { getCssVarValue } from '@/utils/theme';

// 类型导入
import type { CategoryPercentage } from '@/types/chart';
import type { Transaction, TransactionCategory } from '@/types/transaction';

// 当前页面是"分析"页，索引为2
const activeTabIndex = ref(2); 

// 图表渲染相关
const chartKey = ref(0); // 用于强制重新渲染图表
const forceRerenderChart = () => {
  chartKey.value += 1;
};

// 图表渲染相关
const trendChartKey = ref(0); // 用于强制重新渲染趋势图表
const pieChartKey = ref(0); // 用于强制重新渲染饼图
const forceRerenderTrendChart = () => {
  trendChartKey.value += 1;
};
const forceRerenderPieChart = () => {
  pieChartKey.value += 1;
};

// 当前年月
const currentDate = ref(new Date());
const currentYear = computed(() => currentDate.value.getFullYear());
const currentMonth = computed(() => currentDate.value.getMonth() + 1);
const currentSelectedYearMonth = computed(() => `${currentYear.value}-${String(currentMonth.value).padStart(2, '0')}`);

// 日期选择器控制
const showDatePicker = ref(false);

// 加载状态
const isLoading = ref(true);
const chartErrors = ref<ChartError[]>([]);
const chartWidth = ref(0);

// 底部标签栏配置
const tabBarConfig = [
  {
    label: '首页',
    icon: 'house',
    pagePath: '/pages/home/<USER>',
  },
  {
    label: '账单',
    icon: 'list',
    pagePath: '/pages/transaction/list',
  },
  {
    label: '分析',
    icon: 'chart-pie',
    pagePath: '/pages/analysis/index',
  },
  {
    label: '我的',
    icon: 'user',
    pagePath: '/pages/profile/index',
  },
];

// 引入Store
const categoryStore = useCategoryStore();
const transactionStore = useTransactionStore();
const userStore = useUserStore();

// 趋势时间段选项
const trendPeriodOptions = [
  { value: '7days', label: '近7天' },
  { value: '30days', label: '近30天' }, // 实际是当前日期往前推30天
  { value: '90days', label: '近3月' },  // 修改为更直观的表述
  { value: '12months', label: '近12月' },
];
const currentTrendPeriod = ref('7days'); // 修改默认值为 '7days'

// 添加财务洞察和视图类型定义
interface Insight {
  id: string;
  type: 'info' | 'warning' | 'success' | 'danger';
  title: string;
  description: string;
  icon: string;
}

// 支出分类视图配置
const categoryViewOptions = [
  { value: 'top5', label: 'Top 5' },
  { value: 'all', label: '全部' }
];
const currentCategoryView = ref<'top5' | 'all'>('top5');

// 添加模拟数据标记常量
const USE_MOCK_DATA = import.meta.env.VITE_USE_MOCK_DATA === 'true';

// 核心指标数据
const coreIndicatorsData = computed(() => {
  // 记录当前年月，确保依赖被正确收集
  const year = currentYear.value;
  const month = currentMonth.value;
  console.log(`计算 ${year}年${month}月 的核心指标`);
  
  // 获取总支出和总收入（从store获取）
  const expense = Math.abs(transactionStore.getTotalsByType?.expense || 0);
  const income = Math.abs(transactionStore.getTotalsByType?.income || 0);
  const balance = income - expense;
  
  // 计算日均消费 - 根据当月天数计算
  const daysInMonth = new Date(year, month, 0).getDate();
  const dailyExpense = daysInMonth > 0 ? (expense / daysInMonth) : 0;
  
  // 环比数据
  const expenseRatio = 12.5;
  const incomeRatio = 8.3;
  const balanceRatio = -2.1;
  const dailyExpenseRatio = 5.2;
  
  // 返回结构化的指标数据
  return [
    {
      title: '总支出',
      icon: 'money-bill-wave',
      iconColor: 'var(--color-primary)',
      value: formatAmount(expense),
      trend: expenseRatio,
      trendText: `↑ ${expenseRatio.toFixed(1)}% (vs 上期)`,
      trendStyle: 'negative',
    },
    {
      title: '总收入',
      icon: 'wallet',
      iconColor: 'var(--color-success)',
      value: formatAmount(income),
      trend: incomeRatio,
      trendText: `↑ ${incomeRatio.toFixed(1)}% (vs 上期)`,
      trendStyle: 'positive',
    },
    {
      title: '净收入',
      icon: 'piggy-bank',
      iconColor: 'var(--color-success)',
      value: formatAmount(balance),
      trend: balanceRatio,
      trendText: `↓ ${Math.abs(balanceRatio).toFixed(1)}% (vs 上期)`,
      trendStyle: 'positive',
    },
    {
      title: '平均日消费',
      icon: 'calendar-check',
      iconColor: 'var(--color-primary)',
      value: formatAmount(dailyExpense),
      trend: dailyExpenseRatio,
      trendText: `↑ ${dailyExpenseRatio.toFixed(1)}% (vs 上期)`,
      trendStyle: 'negative',
    },
  ];
});

// 支出分类占比数据
const categoryPercentages = computed(() => {
  // 从transactionStore获取的分类占比数据
  const data = transactionStore.getCategoryPercentages || [];
  
  // 根据当前视图模式过滤
  const filteredData = currentCategoryView.value === 'top5' 
    ? data.slice(0, 5) 
    : data;
  
  // 添加百分比文本属性
  return filteredData.map(item => ({
    ...item,
    // 添加格式化的百分比文本
    percentageText: `${item.percentage.toFixed(1)}%`
  }));
});

// 根据当前视图模式过滤后的分类数据
const filteredCategoryPercentages = computed(() => {
  return currentCategoryView.value === 'top5'
    ? categoryPercentages.value.slice(0, 5)
    : categoryPercentages.value;
});

// 饼图颜色数组 - 参考原型中的精确颜色 (仅用于初始化，会在onMounted时被替换)
const DEFAULT_PIE_COLORS = [
  '#FF6B35', // 主题橙色 - 用于第一个分类
  '#FFB74D', // 橙色400
  '#FFCC80', // 橙色300
  '#FFE0B2', // 橙色200
  '#BDBDBD'  // 灰色 - 用于"其他"
];

// 图表颜色变量 (ref)
const incomeColor = ref('#4CAF50');   // 收入颜色 (默认绿色)
const expenseColor = ref('#FF6B35');  // 支出颜色 (默认橙色)
const pieColors = ref([...DEFAULT_PIE_COLORS]);

// 获取分类颜色函数
function getCategoryColor(category: any, index: number): string {
  // 优先使用分类自身的颜色
  if (category && category.color) {
    return category.color;
  }
  // 如果分类有bgColor属性（类别对象格式）
  if (category && category.bgColor) {
    return category.bgColor;
  }
  // 返回预定义颜色数组中的对应颜色
  return pieColors.value[index % pieColors.value.length];
}

// =============== 简化的静态图表数据（柱状图） ===============
// 使用computed确保响应式更新
const staticBarChartData = computed(() => {
  return {
    categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
  series: [
      { 
        name: '收入', 
        data: [1200, 1500, 1800, 2100, 2500, 2800, 3000, 2700, 2400, 2200, 2000, 2300], 
        color: incomeColor.value // 使用响应式的颜色值
      },
      { 
        name: '支出', 
        data: [800, 1200, 1400, 1600, 1900, 2100, 2400, 2200, 1800, 1700, 1500, 1900], 
        color: expenseColor.value // 使用响应式的颜色值
      }
    ]
  };
});

// 静态柱状图配置优化
const staticBarChartOpts = computed(() => {
  return {
    type: 'column',
    padding: [15, 10, 15, 10], // 减少左右内边距，增加图表显示空间
    background: '#FFFFFF', // 使用实际颜色而非CSS变量
    enableScroll: false,
    fontSize: 12,
    fontColor: '#666666', // 使用实际颜色
    legend: {
      show: false, // 隐藏内置图例，使用自定义图例
    },
    xAxis: {
      disableGrid: true,
      fontColor: '#999999', // 使用实际颜色
      fontSize: 12,
      // 设置动态的标签显示数量 - 增加12个月模式下的自适应逻辑
      itemCount: currentTrendPeriod.value === '7days' ? 7 : // 7天显示全部
                 currentTrendPeriod.value === '30days' ? 5 : // 30天显示5个点
                 currentTrendPeriod.value === '90days' ? 3 : // 90天显示3个点
                 currentTrendPeriod.value === '12months' ? 
                  // 12个月模式下根据屏幕宽度自适应
                  (uni.getSystemInfoSync().windowWidth < 375 ? 4 : 
                   uni.getSystemInfoSync().windowWidth < 500 ? 6 : 12) :
                 6, // 默认值
      boundaryGap: true, // 在首尾增加留白
      // 标签旋转逻辑增强，对12个月模式增加屏幕宽度判断
      rotateLabel: currentTrendPeriod.value === '7days' ? true : 
                   currentTrendPeriod.value === '12months' ? 
                    (uni.getSystemInfoSync().windowWidth < 500) : false,
    },
    yAxis: {
      data: [{
        min: 0,
        fontColor: '#999999', // 使用实际颜色
        fontSize: 12,
        format: (val: number) => val.toFixed(0) // 格式化Y轴数值为整数
      }],
      showTitle: false,
      gridType: 'dash',
      dashLength: 4,
      gridColor: '#EFEFEF' // 使用实际颜色
    },
    extra: {
      column: {
        type: 'group', // 使用分组柱状图，并排显示
        width: 18, // 增加柱子宽度以更好填充空间
        barBorderRadius: [3, 3, 0, 0], // 柱子顶部圆角
        barBorderCircle: false,
        linearType: 'none', // 禁用渐变色
        seriesGap: 5 // 系列间隔
      }
    },
    width: '100%', // 使用100%宽度填充容器
    height: 240 // 明确指定高度
  };
});

// =============== 简化的静态图表数据（环形图） ===============
const staticPieChartData = computed(() => {
  return {
  series: [{
    data: [
        { name: '餐饮美食', value: 1500, color: pieColors.value[0] },
        { name: '交通出行', value: 800, color: pieColors.value[1] },
        { name: '购物消费', value: 600, color: pieColors.value[2] },
        { name: '休闲娱乐', value: 400, color: pieColors.value[3] },
        { name: '其他', value: 200, color: pieColors.value[4] }
    ]
  }]
};
});

// 饼图数据配置
const staticPieChartOpts = ref({
  type: 'ring',
  padding: [0, 0, 0, 0],
  background: '#FFFFFF',
  enableScroll: false,
  fontSize: 12,
  title: {
    name: '3,500',
    fontSize: 16, // 保持较小的字体大小
    color: '#333333',
    offsetY: -5  // 微调位置
  },
  subtitle: {
    name: '总支出',
    fontSize: 11, // 进一步减小字体
    color: '#666666',
    offsetY: 12  // 微调位置
  },
  legend: {
    show: false
  },
  dataLabel: false,
  color: pieColors.value,
  width: 130, // 从150px减小到130px
  height: 130, // 从150px减小到130px
  extra: {
    ring: {
      ringWidth: 16, // 使环形更细一些
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 0,
      border: false,
      borderWidth: 3, 
      borderColor: '#FFFFFF',
      centerColor: '#FFFFFF'
    },
    tooltip: {
      showBox: false
    },
    pie: {
      activeOpacity: 0.5,
      activeRadius: 10,
      offsetAngle: 0,
      labelWidth: 0,
      border: false,
      borderWidth: 3,
      borderColor: '#FFFFFF'
    }
  }
});

/**
 * 获取分类占比百分比
 * @param amount 金额
 * @returns 百分比值(0-100)
 */
function getCategoryPercent(amount: number): number {
  const total = Math.abs(transactionStore.getTotalsByType?.expense || 0);
  if (!total) return 0;
  return Math.round((amount / total) * 100);
}

// 跳转到预算管理页面
function navigateToBudgetSetup() {
  // TODO: 后续添加路由跳转逻辑
  // 目前无跳转，仅保留空函数，点击反馈交给CSS :active 伪类
}

// 处理月份导航
function navigateMonth(direction: 'prev' | 'next'): void {
  // 增加参数验证
  if (!direction || (direction !== 'prev' && direction !== 'next')) {
    console.error('导航月份参数无效:', direction);
    return;
  }
  
  try {
    // 设置加载状态，提供视觉反馈
    isLoading.value = true;
    
    // 记录当前月份供日志使用
    const oldYear = currentYear.value;
    const oldMonth = currentMonth.value;
    
    const newDate = new Date(currentDate.value);
    
    if (direction === 'prev') {
      newDate.setMonth(newDate.getMonth() - 1);
    } else {
      newDate.setMonth(newDate.getMonth() + 1);
    }
    
    // 更新当前日期（这会触发computed以及watch）
    currentDate.value = newDate;
    
    console.log(`手动导航月份: ${oldYear}年${oldMonth}月 -> ${currentYear.value}年${currentMonth.value}月`);
    
    // 清除可能存在的错误
    clearChartErrors();
    
    // 月份变化会通过watch监听器自动触发loadMonthlyData
    // 无需额外调用，避免重复操作
  } catch (error) {
    console.error('月份导航失败:', error);
    addChartError('data', '月份切换失败，请重试');
  }
}

// 处理日期选择
function handleDateSelected(date: Date | string): void {
  try {
    // 确保日期有效
    if (!date) {
      console.error('选择的日期无效');
      return;
    }
    
    // 记录当前月份供日志使用
    const oldYear = currentYear.value;
    const oldMonth = currentMonth.value;
    
    // 修复类型兼容性问题
    currentDate.value = date instanceof Date ? date : new Date(date);
    showDatePicker.value = false;
    
    console.log(`日期选择器选择月份: ${oldYear}年${oldMonth}月 -> ${currentYear.value}年${currentMonth.value}月`);
    
    // 注：不需要在这里调用loadMonthlyData，因为watch(currentSelectedYearMonth)会处理
  } catch (error) {
    console.error('处理选中日期失败:', error);
    addChartError('data', '设置日期失败，请重试');
  }
}

// 切换日期选择器显示状态
function toggleDatePicker() {
  showDatePicker.value = !showDatePicker.value;
}

// 设置时间段
function setTrendPeriod(period: string): void {
  if (!period) {
    console.error('时间段参数无效');
    return;
  }
  
  if (period === currentTrendPeriod.value) return; // 如果选择相同周期，不重新加载
  
  console.log('切换趋势时间段:', period);
  currentTrendPeriod.value = period;
  isLoading.value = true; // 设置加载状态
  chartErrors.value = chartErrors.value.filter(error => error.type !== 'data'); // 清除数据相关错误
  
  // 立即加载图表数据，不使用nextTick延迟
  loadTrendData(period);
  // 强制重新渲染图表
  forceRerenderTrendChart();
}

/**
 * 向图表错误列表添加新错误
 * @param type 错误类型
 * @param message 错误消息
 */
function addChartError(type: ChartError['type'], message: string): void {
  // 防止重复添加相同类型和消息的错误
  if (!chartErrors.value.some(err => err.type === type && err.message === message)) {
    chartErrors.value.push({ type, message });
  }
}

/**
 * 清除特定类型的图表错误
 * @param type 错误类型，如不指定则清除所有错误
 */
function clearChartErrors(type?: ChartError['type']): void {
  if (type) {
    chartErrors.value = chartErrors.value.filter(error => error.type !== type);
  } else {
    chartErrors.value = [];
  }
}

/**
 * 加载趋势图表数据
 * 根据选择的时间段从本地存储加载交易数据，并进行时间筛选和数据聚合
 * @param periodType 时间段类型：'7days'|'30days'|'90days'|'12months'
 */
function loadTrendData(periodType: string = '30days') {
  try {
    console.log('开始加载趋势图表数据，时间段:', periodType);
    isLoading.value = true;
    clearChartErrors('data');
    
    // 获取当前日期作为结束日期
    const endDate = new Date();
    endDate.setHours(23, 59, 59, 999); // 设置为当天结束时间
    
    let startDate = new Date();
    startDate.setHours(0, 0, 0, 0); // 设置为当天开始时间
    
    let categories: string[] = []; 
    
    // 根据时间段设置开始日期和X轴标签
    switch(periodType) {
      case '7days':
        startDate.setDate(endDate.getDate() - 6); // 过去7天
        // 使用最近7天的日期作为标签，采用"MM/DD"格式（月/日）
        categories = [];
        for (let i = 0; i < 7; i++) {
          const date = new Date(startDate);
          date.setDate(date.getDate() + i);
          const month = date.getMonth() + 1;
          const day = date.getDate();
          // 统一使用 M/D 格式，不带前导零，更符合中文用户习惯
          categories.push(`${month}/${day}`);
        }
        break;
      case '30days':
        startDate.setDate(endDate.getDate() - 29); // 过去30天
        
        // 计算30天期间的总天数
        const totalDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
        // 每段天数 - 固定为6段，平均分配天数
        const segmentCount = 6; // 固定为6段
        const daysPerSegment = Math.ceil(totalDays / segmentCount);
        
        console.log(`近30天总天数: ${totalDays}, 分为${segmentCount}段, 每段${daysPerSegment}天`);
        
        // 生成日期标签 - 使用与数据聚合相同的逻辑计算时间段
        categories = [];
        for (let i = 0; i < segmentCount; i++) {
          // 计算段的开始日期
          const segStart = new Date(startDate);
          segStart.setDate(startDate.getDate() + (i * daysPerSegment));
          
          // 计算段的结束日期
          const segEnd = new Date(segStart);
          segEnd.setDate(segStart.getDate() + daysPerSegment - 1);
          if (segEnd > endDate) segEnd.setTime(endDate.getTime());
          
          const startMonth = segStart.getMonth() + 1;
          const startDay = segStart.getDate();
          const endMonth = segEnd.getMonth() + 1;
          const endDay = segEnd.getDate();
          
          // 格式化日期标签，确保更紧凑直观
          if (startMonth === endMonth) {
            // 同月份，简化显示：M/D-D
            categories.push(`${startMonth}/${startDay}-${endDay}`);
          } else {
            // 跨月份，完整显示：M/D-M/D
            categories.push(`${startMonth}/${startDay}-${endMonth}/${endDay}`);
          }
        }
        break;
      case '90days':
        // 修改为按完整月份计算，而非固定天数
        // 获取当前月的第一天
        const currentMonth = new Date(endDate);
        currentMonth.setDate(1);
        currentMonth.setHours(0, 0, 0, 0);
        
        // 设置开始日期为最近3个月的第一天（包括当前月）
        startDate = new Date(currentMonth);
        startDate.setMonth(currentMonth.getMonth() - 2);
        
        console.log(`近3月模式 - 当前月份: ${currentMonth.getMonth() + 1}月, 开始月份: ${startDate.getMonth() + 1}月`);
        
        // 使用连续3个月作为标签
        const threeMonths: string[] = [];
        for (let i = 0; i < 3; i++) {
          const monthDate = new Date(startDate);
          monthDate.setMonth(startDate.getMonth() + i);
          threeMonths.push(`${monthDate.getMonth() + 1}月`);
        }
        categories = threeMonths;
        break;
      case '12months':
        startDate.setMonth(endDate.getMonth() - 11); // 过去12个月
        startDate.setDate(1); // 设置为月初
        // 使用月份为单位的标签
        const allMonths: string[] = [];
        for (let i = 0; i < 12; i++) {
          const monthDate = new Date(startDate);
          monthDate.setMonth(startDate.getMonth() + i);
          allMonths.push(`${monthDate.getMonth() + 1}月`);
        }
        categories = allMonths;
        break;
    }
    
    // 记录时间范围，方便调试
    console.log(`时间范围: ${startDate.toISOString()} 至 ${endDate.toISOString()}`);
    
    // 确保从本地存储加载交易数据
    if (transactionStore.transactions.length === 0) {
      transactionStore.loadFromStorage();
    }
    
    // 从transactionStore获取交易数据
    const allTransactions = transactionStore.transactions;
    
    if (!allTransactions || allTransactions.length === 0) {
      console.log('本地存储中无交易数据，显示默认图表');
      addChartError('data', '暂无交易数据');
      isLoading.value = false;
      return;
    }
    
    console.log(`从本地存储加载了 ${allTransactions.length} 条交易记录`);
    
    // 过滤时间段内的交易（改进日期比较逻辑）
    const filteredTransactions = allTransactions.filter(tx => {
      try {
        // 尝试多种方式解析日期
        let txDate: Date;
        
        if (typeof tx.date === 'string') {
          // 处理 YYYY-MM-DD 格式
          if (/^\d{4}-\d{2}-\d{2}$/.test(tx.date)) {
            const [year, month, day] = tx.date.split('-').map(Number);
            txDate = new Date(year, month - 1, day); // 月份需要减1
          } 
          // 处理 MM/DD/YYYY 格式
          else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(tx.date)) {
            const [month, day, year] = tx.date.split('/').map(Number);
            txDate = new Date(year, month - 1, day);
          }
          // 处理其他格式
          else {
            txDate = new Date(tx.date);
          }
        } else if (tx.date instanceof Date) {
          txDate = tx.date;
        } else {
          console.warn(`无法识别的交易日期格式: ${tx.date}`);
          return false; // 跳过无效日期
        }
        
        if (isNaN(txDate.getTime())) {
          console.warn(`无效的日期值: ${tx.date}`);
          return false;
        }
        
        // 规范化为00:00:00时间，只比较日期部分
        const txYear = txDate.getFullYear();
        const txMonth = txDate.getMonth();
        const txDay = txDate.getDate();
        
        const normalizedTxDate = new Date(txYear, txMonth, txDay, 0, 0, 0, 0);
        
        // 输出所有匹配的日期和部分边界日期，帮助调试
        const isInRange = normalizedTxDate >= startDate && normalizedTxDate <= endDate;
        
        // 为"近3月"选项增加额外日志
        if (periodType === '90days') {
          if (isInRange) {
            console.log(`近3月-包含交易: ${tx.date}(${tx.amount}), 类型:${tx.type}, 规范化后: ${normalizedTxDate.toISOString()}`);
          } else if (
            // 如果是4月份的数据，特别记录
            normalizedTxDate.getMonth() === 3 && // 4月
            normalizedTxDate.getFullYear() === currentDate.getFullYear()
          ) {
            console.log(`近3月-注意: 排除了4月交易: ${tx.date}(${tx.amount}), 规范化后: ${normalizedTxDate.toISOString()}`);
          }
        } else if (isInRange) {
          console.log(`包含交易: ${tx.date}(${tx.amount}), 类型:${tx.type}, 分类:${tx.category}`);
        } else if (
          // 检查边界附近的日期
          Math.abs(normalizedTxDate.getTime() - startDate.getTime()) < 86400000 || 
          Math.abs(normalizedTxDate.getTime() - endDate.getTime()) < 86400000
        ) {
          console.log(`排除边界交易: ${tx.date}, 规范化后: ${normalizedTxDate.toISOString()}`);
        }
        
        return isInRange;
      } catch (e) {
        console.error(`处理交易日期错误: ${tx.date}`, e);
        return false;
      }
    });
    
    console.log(`筛选出 ${filteredTransactions.length} 条时间段内的交易记录`);
    
    // 准备图表数据
    let incomeData: number[] = [];
    let expenseData: number[] = [];
    
    // 根据不同的时间段聚合数据
    if (periodType === '7days') {
      // 按日统计
      aggregateDataByDay(filteredTransactions, startDate, 7, incomeData, expenseData);
    } else if (periodType === '30days') {
      // 使用统一的参数进行数据聚合
      aggregateByDateRanges(filteredTransactions, startDate, endDate, 6, incomeData, expenseData);
    } else if (periodType === '90days' || periodType === '12months') {
      // 按月统计（3个月或12个月）
      const monthCount = periodType === '90days' ? 3 : 12;
      
      // 特殊处理3个月的情况，使用优化的逻辑
      if (periodType === '90days') {
        console.log('使用优化的3个月聚合逻辑');
        aggregateLastThreeMonths(filteredTransactions, startDate, endDate, incomeData, expenseData);
      } else {
        aggregateDataByMonth(filteredTransactions, startDate, endDate, monthCount, incomeData, expenseData);
      }
    }
    
    // 创建新的数据对象而不是修改原对象，确保触发响应式更新
    const updatedChartData = {
      categories: [...categories],
      series: [
        { 
          name: '收入', 
          data: [...incomeData], 
          color: incomeColor.value // 使用响应式颜色值
        },
        { 
          name: '支出', 
          data: [...expenseData], 
          color: expenseColor.value // 使用响应式颜色值
        }
      ]
    };
    
    // 更新图表数据 - 替换整个对象以确保响应式触发
    Object.assign(staticBarChartData.value, updatedChartData);
    
    // 对于12个月的数据，根据屏幕宽度更新图表配置
    if (periodType === '12months') {
      // 调用updateChartOptions以根据屏幕宽度优化显示
      updateChartOptions();
    }
    
    console.log('趋势图表数据已更新:', JSON.stringify(staticBarChartData.value));
  } catch (error) {
    console.error('加载趋势图表数据失败:', error);
    addChartError('data', '加载趋势数据失败');
  } finally {
    isLoading.value = false;
  }
}

/**
 * 按照日期范围聚合数据，适用于近30天的场景
 * @param transactions 交易数据列表
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param segmentCount 分段数量
 * @param incomeData 收入数据数组（会被修改）
 * @param expenseData 支出数据数组（会被修改）
 */
function aggregateByDateRanges(
  transactions: Transaction[],
  startDate: Date,
  endDate: Date,
  segmentCount: number,
  incomeData: number[],
  expenseData: number[]
) {
  // 清空输入数组
  incomeData.length = 0;
  expenseData.length = 0;

  console.log(`按日期范围聚合: 从 ${startDate.toISOString().split('T')[0]} 到 ${endDate.toISOString().split('T')[0]}，分 ${segmentCount} 段`);
  console.log(`交易记录总条数: ${transactions.length}`);
  
  // 计算日期范围总天数
  const totalDays = Math.round((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;
  // 每段天数
  const daysPerSegment = Math.ceil(totalDays / segmentCount);
  
  console.log(`总天数: ${totalDays}, 每段天数: ${daysPerSegment}`);
  
  // 创建一个日期映射，用于快速查找交易
  const dateTransactionMap = new Map<string, Transaction[]>();
  
  // 预处理交易按日期分组
  transactions.forEach(tx => {
    try {
      // 尝试多种方式解析日期字符串
      let txDate: Date;
      
      if (typeof tx.date === 'string') {
        // 处理 YYYY-MM-DD 格式
        if (/^\d{4}-\d{2}-\d{2}$/.test(tx.date)) {
          const [year, month, day] = tx.date.split('-').map(Number);
          txDate = new Date(year, month - 1, day); // 月份需要减1
        } 
        // 处理 MM/DD/YYYY 格式
        else if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(tx.date)) {
          const [month, day, year] = tx.date.split('/').map(Number);
          txDate = new Date(year, month - 1, day);
        }
        // 处理其他格式
        else {
          txDate = new Date(tx.date);
        }
      } else if (tx.date instanceof Date) {
        txDate = tx.date;
      } else {
        console.warn(`无法识别的交易日期格式: ${tx.date}`);
        return; // 跳过无效日期
      }
      
      if (isNaN(txDate.getTime())) {
        console.warn(`无效的日期值: ${tx.date}`);
        return;
      }
      
      // 规范化为YYYY-MM-DD格式的日期键
      const year = txDate.getFullYear();
      const month = String(txDate.getMonth() + 1).padStart(2, '0');
      const day = String(txDate.getDate()).padStart(2, '0');
      const dateKey = `${year}-${month}-${day}`;
      
      if (!dateTransactionMap.has(dateKey)) {
        dateTransactionMap.set(dateKey, []);
      }
      
      dateTransactionMap.get(dateKey)?.push(tx);
      
    } catch (e) {
      console.error(`预处理交易日期错误: ${tx.date}`, e);
    }
  });
  
  for (let i = 0; i < segmentCount; i++) {
    // 创建当前时间段的开始日期
    const segmentStart = new Date(startDate);
    segmentStart.setDate(startDate.getDate() + (i * daysPerSegment));
    segmentStart.setHours(0, 0, 0, 0);
    
    // 创建当前时间段的结束日期
    const segmentEnd = new Date(segmentStart);
    segmentEnd.setDate(segmentStart.getDate() + daysPerSegment - 1);
    segmentEnd.setHours(23, 59, 59, 999);
    
    // 确保不超过总结束日期
    if (segmentEnd > endDate) {
      segmentEnd.setTime(endDate.getTime());
    }
    
    const segmentStartFormatted = segmentStart.toISOString().split('T')[0];
    const segmentEndFormatted = segmentEnd.toISOString().split('T')[0];
    
    console.log(`第${i+1}段范围: ${segmentStartFormatted} 至 ${segmentEndFormatted}`);
    
    // 收集时间段内的所有交易
    let segmentTransactions: Transaction[] = [];
    
    // 遍历当前段的每一天
    const currentDate = new Date(segmentStart);
    while (currentDate <= segmentEnd) {
      // 创建YYYY-MM-DD格式的日期键，与上面预处理时使用相同的格式化方式
      const year = currentDate.getFullYear();
      const month = String(currentDate.getMonth() + 1).padStart(2, '0');
      const day = String(currentDate.getDate()).padStart(2, '0');
      const dateKey = `${year}-${month}-${day}`;
      
      const dayTransactions = dateTransactionMap.get(dateKey) || [];
      
      if (dayTransactions.length > 0) {
        console.log(`  找到日期 ${dateKey} 的交易: ${dayTransactions.length}条`);
        // 使用展开运算符合并数组，避免多次调用concat提高性能
        segmentTransactions = [...segmentTransactions, ...dayTransactions];
      }
      
      // 移动到下一天
      currentDate.setDate(currentDate.getDate() + 1);
    }
    
    // 计算该时间段内的收入和支出
    const segmentIncome = segmentTransactions
      .filter(tx => tx.type === 'income')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    const segmentExpense = segmentTransactions
      .filter(tx => tx.type === 'expense')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    console.log(`第${i+1}段 找到 ${segmentTransactions.length} 条交易，收入: ${segmentIncome}，支出: ${segmentExpense}`);
      
    incomeData.push(segmentIncome);
    expenseData.push(segmentExpense);
  }
}

/**
 * 按日聚合交易数据
 * @param transactions 交易数据列表
 * @param startDate 开始日期
 * @param dayCount 天数
 * @param incomeData 收入数据数组（会被修改）
 * @param expenseData 支出数据数组（会被修改）
 */
function aggregateDataByDay(
  transactions: Transaction[], 
  startDate: Date, 
  dayCount: number, 
  incomeData: number[], 
  expenseData: number[]
): void {
  // 清空输入数组
  incomeData.length = 0;
  expenseData.length = 0;
  
  console.log(`按日聚合: 从 ${startDate.toISOString().split('T')[0]} 开始，共 ${dayCount} 天`);
  console.log(`交易记录总条数: ${transactions.length}`);
  
  for (let i = 0; i < dayCount; i++) {
    const dayStart = new Date(startDate);
    dayStart.setDate(startDate.getDate() + i);
    dayStart.setHours(0, 0, 0, 0);
    
    const dayEnd = new Date(dayStart);
    dayEnd.setHours(23, 59, 59, 999);
    
    // 将日期转换为YYYY-MM-DD格式，确保与交易记录中的日期格式一致
    const year = dayStart.getFullYear();
    const month = String(dayStart.getMonth() + 1).padStart(2, '0');
    const day = String(dayStart.getDate()).padStart(2, '0');
    const dayStr = `${year}-${month}-${day}`;
    
    // 匹配精确的日期字符串，或者转换为相同格式后比较
    const dayTransactions = transactions.filter(tx => {
      // 首先检查直接字符串匹配
      if (tx.date === dayStr) {
        return true;
      }
      
      // 尝试将交易日期标准化为YYYY-MM-DD格式后再比较
      try {
        const txDate = new Date(tx.date);
        if (isNaN(txDate.getTime())) {
          // 无效日期，尝试解析其他格式
          return false;
        }
        
        const txYear = txDate.getFullYear();
        const txMonth = txDate.getMonth() + 1;
        const txDay = txDate.getDate();
        
        // 日期相等条件：年月日完全匹配
        const isMatch = 
          txYear === year && 
          txMonth === (dayStart.getMonth() + 1) && 
          txDay === dayStart.getDate();
          
        return isMatch;
      } catch (e) {
        console.error(`处理交易日期错误: ${tx.date}`, e);
        return false;
      }
    });
    
    const dayIncome = dayTransactions
      .filter(tx => tx.type === 'income')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    const dayExpense = dayTransactions
      .filter(tx => tx.type === 'expense')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    if (dayTransactions.length > 0) {
      console.log(`${dayStr} 找到 ${dayTransactions.length} 条交易，收入: ${dayIncome}，支出: ${dayExpense}`);
    }
      
    incomeData.push(dayIncome);
    expenseData.push(dayExpense);
  }
}

/**
 * 按周聚合交易数据
 * @param transactions 交易数据列表
 * @param startDate 开始日期
 * @param weekCount 周数
 * @param incomeData 收入数据数组（会被修改）
 * @param expenseData 支出数据数组（会被修改）
 */
function aggregateDataByWeek(
  transactions: Transaction[], 
  startDate: Date, 
  weekCount: number, 
  incomeData: number[], 
  expenseData: number[]
) {
  // 清空输入数组
  incomeData.length = 0;
  expenseData.length = 0;

  console.log(`按周聚合: 从 ${startDate.toISOString().split('T')[0]} 开始，共 ${weekCount} 周`);
  console.log(`交易记录总条数: ${transactions.length}`);
  
  for (let i = 0; i < weekCount; i++) {
    const weekStart = new Date(startDate);
    weekStart.setDate(startDate.getDate() + (i * 7));
    weekStart.setHours(0, 0, 0, 0);
    
    const weekEnd = new Date(weekStart);
    weekEnd.setDate(weekStart.getDate() + 6);
    weekEnd.setHours(23, 59, 59, 999);
    
    const weekStartFormatted = weekStart.toISOString().split('T')[0];
    const weekEndFormatted = weekEnd.toISOString().split('T')[0];
    
    console.log(`第${i+1}周范围: ${weekStartFormatted} 至 ${weekEndFormatted}`);
    
    const weekTransactions = transactions.filter(tx => {
      try {
        // 先尝试直接格式比较，转换为YYYY-MM-DD格式
        const txDate = new Date(tx.date);
        if (isNaN(txDate.getTime())) {
          console.warn(`无效的交易日期格式: ${tx.date}`);
          return false;
        }
        
        // 规范化交易日期为相同格式的日期对象
        const txYear = txDate.getFullYear();
        const txMonth = txDate.getMonth();
        const txDay = txDate.getDate();
        
        // 创建一个新的日期对象，只包含年月日
        const normalizedTxDate = new Date(txYear, txMonth, txDay, 0, 0, 0, 0);
        
        // 比较日期对象，确保它在周的范围内
        return normalizedTxDate >= weekStart && normalizedTxDate <= weekEnd;
      } catch (e) {
        console.error(`处理交易日期错误: ${tx.date}`, e);
        return false;
      }
    });
    
    const weekIncome = weekTransactions
      .filter(tx => tx.type === 'income')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    const weekExpense = weekTransactions
      .filter(tx => tx.type === 'expense')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    console.log(`第${i+1}周 找到 ${weekTransactions.length} 条交易，收入: ${weekIncome}，支出: ${weekExpense}`);
      
    incomeData.push(weekIncome);
    expenseData.push(weekExpense);
  }
}

/**
 * 按月聚合交易数据
 * @param transactions 交易数据列表
 * @param startDate 开始日期
 * @param endDate 结束日期
 * @param monthCount 月份数
 * @param incomeData 收入数据数组（会被修改）
 * @param expenseData 支出数据数组（会被修改）
 */
function aggregateDataByMonth(
  transactions: Transaction[], 
  startDate: Date, 
  endDate: Date,
  monthCount: number,
  incomeData: number[], 
  expenseData: number[]
) {
  // 清空输入数组
  incomeData.length = 0;
  expenseData.length = 0;

  console.log(`按月聚合: 从 ${startDate.toISOString().split('T')[0]} 开始，共 ${monthCount} 个月`);
  console.log(`交易记录总条数: ${transactions.length}`);
  
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  
  for (let i = 0; i < monthCount; i++) {
    const monthStart = new Date(startDate);
    monthStart.setMonth(startDate.getMonth() + i);
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);
    
    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthStart.getMonth() + 1);
    monthEnd.setDate(0);
    monthEnd.setHours(23, 59, 59, 999);
    
    const monthStartFormatted = monthStart.toISOString().split('T')[0];
    const monthEndFormatted = monthEnd.toISOString().split('T')[0];
    const monthName = months[monthStart.getMonth()];
    
    console.log(`${monthName} 范围: ${monthStartFormatted} 至 ${monthEndFormatted}`);
    
    const monthTransactions = transactions.filter(tx => {
      try {
        const txDate = new Date(tx.date);
        if (isNaN(txDate.getTime())) {
          console.warn(`无效的交易日期格式: ${tx.date}`);
          return false;
        }
        
        // 规范化交易日期为相同格式的日期对象（只包含年月日）
        const txYear = txDate.getFullYear();
        const txMonth = txDate.getMonth();
        const txDay = txDate.getDate();
        
        const normalizedTxDate = new Date(txYear, txMonth, txDay, 0, 0, 0, 0);
        
        // 比较月份，确保它在当月范围内
        return normalizedTxDate >= monthStart && normalizedTxDate <= monthEnd;
      } catch (e) {
        console.error(`处理交易日期错误: ${tx.date}`, e);
        return false;
      }
    });
    
    const monthIncome = monthTransactions
      .filter(tx => tx.type === 'income')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    const monthExpense = monthTransactions
      .filter(tx => tx.type === 'expense')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    console.log(`${monthName} 找到 ${monthTransactions.length} 条交易，收入: ${monthIncome}，支出: ${monthExpense}`);
    
    incomeData.push(monthIncome);
    expenseData.push(monthExpense);
  }
}

// =============== 计算支出总额，用于环形图中心显示 ===============
const totalExpense = computed(() => {
  const expense = Math.abs(transactionStore.getTotalsByType?.expense || 0);
  return formatAmount(expense);
});

// 创建动态饼图配置，显示实际总支出金额
const dynamicPieChartOpts = computed(() => {
  // 获取总支出金额
  const totalExpense = Math.abs(transactionStore.getTotalsByType?.expense || 0);
  
  // 复制静态配置并更新标题
  return {
    ...staticPieChartOpts.value,
    title: {
      ...staticPieChartOpts.value.title,
      name: formatAmount(totalExpense, false), // 不显示货币符号，只显示数字
    }
  };
});

// 处理图表加载完成
function onChartComplete(data: any) {
  isLoading.value = false;
  console.log('图表加载完成:', data);
}

// 处理图表加载错误
function onChartError(error: any) {
  console.error('图表加载错误:', error);
  addChartError('load', error.message || '图表加载失败');
}

// 页面数据加载函数
// 初始化图表颜色，加载分类和交易数据
async function loadData(forceRefresh = false) {
  console.log('Analysis page: loadData triggered', { forceRefresh, year: currentYear.value, month: currentMonth.value });
  isLoading.value = true;
  chartErrors.value = []; // 重置错误记录

  try {
    // 并行加载数据
    await Promise.all([
      transactionStore.fetchMonthlyTransactions({ 
        year: currentYear.value, 
        month: currentMonth.value, 
        forceRefresh 
      }),
      categoryStore.fetchCategories(), // 确保分类已加载
      // userStore.fetchUserSettings(), // 如果需要用户设置相关的预算等
    ]);

    // 数据加载完成后，再强制更新图表
    await nextTick(); // 等待DOM更新
    loadTrendData(currentTrendPeriod.value); // 新增：确保趋势图数据在初次加载时被填充
    forceRerenderTrendChart();
    forceRerenderPieChart();
    
    console.log('Analysis page: Data loaded successfully.');
  } catch (error) {
    console.error('Analysis page: Error loading data:', error);
    // 根据错误类型，可以设置更具体的错误提示
    // chartErrors.value.push({ id: 'dataLoad', message: '数据加载失败，请稍后重试' });
  } finally {
    isLoading.value = false;
  }
}

// 日期选择器确认事件
async function handleDateChange(selectedDate: Date) {
  console.log('Analysis page: Date changed to:', selectedDate);
  currentDate.value = selectedDate;
  showDatePicker.value = false;
  await loadData(true); // 日期变化，强制刷新数据
  // 日期变化后，数据会重新加载，图表会在loadData内部通过nextTick重新渲染
}

// 支出分类视图切换
async function handleCategoryViewChange(view: 'top5' | 'all') {
  console.log('Analysis page: Category view changed to:', view);
  if (currentCategoryView.value !== view) {
    currentCategoryView.value = view;
    // 视图切换，可能不需要完全重新加载数据，但需要重新渲染饼图
    await nextTick(); // 等待DOM更新 (虽然这里可能不是DOM直接变化，但保持一致性)
    forceRerenderPieChart(); 
  }
}

// 监听 filteredCategoryPercentages 变化，更新饼图数据
watch(filteredCategoryPercentages, (newValue) => {
  if (!newValue || newValue.length === 0) return;
  
  try {
    // 更新饼图数据
    staticPieChartData.value.series[0].data = newValue.map((item, index) => ({
      name: item.name,
      value: item.amount,
      // 优先使用分类自身颜色，如果没有才使用预设颜色
      color: getCategoryColor(item, index)
    }));
    
    // 只更新饼图颜色配置，不影响趋势图
    updatePieChartColors();
  } catch (error) {
    console.error('更新饼图数据失败:', error);
  }
});

// 监听 categoryPercentages 变化，动态更新饼图数据
watch(categoryPercentages, (newValue) => {
  if (!newValue || newValue.length === 0) return;
  
  try {
    // 由于我们现在有filteredCategoryPercentages计算属性，
    // 这里不需要重复过滤逻辑，它会由上面的监听器处理
    console.log('分类占比数据已更新，总数：', newValue.length);
  } catch (error) {
    console.error('更新饼图数据失败:', error);
  }
});

// 添加页面激活标志，用于追踪页面是否从其他页面返回
const isActivated = ref(false);
const isFirstLoad = ref(true);

// 添加组件状态标记，用于避免在组件卸载后继续执行异步操作
const isComponentMounted = ref(true);

// 添加onActivated钩子，处理从其他页面返回的情况
onActivated(() => {
  console.log('统计分析页面被激活');
  isActivated.value = true;
  
  // 如果不是首次加载，则需要重新初始化图表
  if (!isFirstLoad.value) {
    refreshData();
  }
});

// 添加onUnmounted钩子，清理资源
onUnmounted(() => {
  console.log('统计分析页面被卸载');
  isComponentMounted.value = false;
  // 清理资源，避免内存泄漏
  chartErrors.value = [];
});

// 添加页面刷新数据的函数
async function refreshData() {
  try {
    console.log('刷新统计分析页面数据');
    isLoading.value = true;
    clearChartErrors();
    
    // 确保从本地存储加载最新的交易数据
    transactionStore.loadFromStorage();
    
    // 重新加载当月交易数据
    await loadData();
    
    console.log('统计分析页面数据刷新完成');
  } catch (error) {
    console.error('刷新页面数据失败:', error);
    addChartError('data', '数据刷新失败，请下拉刷新重试');
  } finally {
    isLoading.value = false;
  }
}

// 页面加载完成
onMounted(async () => {
  try {
    console.log('分析页面挂载完成');
    isLoading.value = true;
    clearChartErrors();
    
    // 初始化颜色
    initChartColors();
    
    // 加载数据
    await loadData();
    
    console.log('分析页面初始化完成');
  } catch (error) {
    console.error('初始化数据加载失败:', error);
    addChartError('init', '初始化数据失败，请刷新页面');
  } finally {
    isLoading.value = false;
  }
});

// 获取月度数据，根据选中的年月
// 在月份变化时调用此函数
async function loadMonthlyData() {
  isLoading.value = true;
  clearChartErrors();
  
  try {
    // 获取选定月份的年月信息
    const year = currentYear.value;
    const month = currentMonth.value;
    
    console.log(`开始加载 ${year}年${month}月 的月度数据`);
    
    // 确保分类数据已加载
    if (categoryStore.expenseCategories.length === 0) {
      await categoryStore.fetchCategories();
    }
    
    // 先确保交易数据从本地存储加载（不会清空数据）
    if (transactionStore.transactions.length === 0) {
      console.log('交易数据为空，从本地存储加载');
      transactionStore.loadFromStorage();
    }
    
    // 注意：这里移除了forceRefresh: true参数，避免不必要的强制刷新
    // 而是通过月份条件直接过滤获取数据
    await transactionStore.fetchMonthlyTransactions({
      year,
      month
    });
    
    // 输出日志
    console.log(`成功加载 ${year}年${month}月 的月度数据`);
    console.log(`月度交易数量: ${transactionStore.monthlyTransactions.length}`);
    
    // 更新图表数据
    // 由于核心指标是计算属性，会自动更新
    // 需要手动更新图表数据和配置
    setCategoryView(currentCategoryView.value as 'top5' | 'all', true);
    loadTrendData(currentTrendPeriod.value);
    
    // 使用延迟确保DOM完全更新后再重绘图表
    setTimeout(() => {
      // 强制图表重绘
      forceRerenderChart();
      forceRerenderTrendChart();
      forceRerenderPieChart();
      console.log('月度数据图表重绘完成');
    }, 100);
  } catch (error) {
    console.error(`加载 ${currentYear.value}年${currentMonth.value}月 数据失败:`, error);
    addChartError('data', '数据加载失败，请重试');
  } finally {
    isLoading.value = false;
  }
}

// 更新全局月份变化监听器 - 移除强制刷新
watch(currentSelectedYearMonth, async (newYearMonth, oldYearMonth) => {
  if (newYearMonth !== oldYearMonth) {
    console.log(`全局月份变化: ${oldYearMonth} -> ${newYearMonth}`);
    
    try {
      isLoading.value = true;
      clearChartErrors();
      
      // 注意：这里不再调用transactionStore.fetchTransactions
      // 而是直接使用月份筛选已有数据
      
      // 1. 加载月度数据 - 已优化不会强制刷新
      await loadMonthlyData();
      
      // 2. 更新图表和指标
      setCategoryView(currentCategoryView.value as 'top5' | 'all', true);
      loadTrendData(currentTrendPeriod.value);
      
      // 3. 强制重绘所有图表
      setTimeout(() => {
        forceRerenderChart();
        forceRerenderTrendChart();
        forceRerenderPieChart();
      }, 100);
      
    } catch (error) {
      console.error(`月份变化处理失败:`, error);
      addChartError('data', '切换月份失败，请重试');
    } finally {
      isLoading.value = false;
    }
  }
});

// 添加处理底部标签栏切换事件的函数
function handleTabChange(index: number) {
  try {
    const targetPath = tabBarConfig[index]?.pagePath;
    if (targetPath) {
      uni.switchTab({ url: targetPath });
    } else {
      console.error('切换标签栏失败：目标路径不存在');
    }
  } catch (error) {
    console.error('切换标签栏时发生错误:', error);
  }
}

/**
 * 设置分类视图类型
 * 
 * @param view 视图类型: 'top5' 显示前5个分类 | 'all' 显示所有分类
 * @param forceRefresh 强制刷新，即使视图类型没有变化
 */
function setCategoryView(view: 'top5' | 'all', forceRefresh: boolean = false) {
  try {
    // 如果视图类型相同且不强制刷新，则不进行操作
    if (view === currentCategoryView.value && !forceRefresh) return;
    
    console.log('切换分类视图类型:', view, forceRefresh ? '(强制刷新)' : '');
    currentCategoryView.value = view;
    
    // 清除可能存在的错误信息
    chartErrors.value = chartErrors.value.filter(e => e.type !== 'data');
    
    // 确保数据已从本地存储加载
    if (transactionStore.getCategoryPercentages.length === 0 && transactionStore.transactions.length > 0) {
      console.log('从交易记录重新计算分类占比数据');
      // 确保交易数据已经从本地存储加载，重新触发计算属性
      transactionStore.loadFromStorage();
    }
    
    // 强制重新渲染饼图
    forceRerenderPieChart();
    
    // 如果没有数据，显示空状态
    if (filteredCategoryPercentages.value.length === 0) {
      console.log('没有找到分类占比数据，显示空状态');
    } else {
      console.log(`找到 ${filteredCategoryPercentages.value.length} 条分类占比数据`);
    }
  } catch (error) {
    console.error('设置分类视图失败:', error);
  }
}

/**
 * 更新图表的颜色配置
 * 应该在onMounted阶段或颜色值发生变化时调用
 */
function updateChartColors() {
  try {
    console.log('更新图表颜色配置');
    
    // 更新柱状图系列颜色
    if (staticBarChartData.value.series && staticBarChartData.value.series.length >= 2) {
      staticBarChartData.value.series[0].color = incomeColor.value; // 收入
      staticBarChartData.value.series[1].color = expenseColor.value; // 支出
    }
    
    // 更新饼图颜色配置
    if (staticPieChartOpts.value) {
      staticPieChartOpts.value.color = [...pieColors.value];
    }
    
    // 更新饼图数据颜色
    if (staticPieChartData.value.series && staticPieChartData.value.series[0].data) {
      staticPieChartData.value.series[0].data.forEach((item, index) => {
        item.color = getCategoryColor(item, index);
      });
    }
    
    // 分别更新趋势图和饼图，不要相互影响
    forceRerenderTrendChart();
    forceRerenderPieChart();
  } catch (error) {
    console.error('更新图表颜色配置失败:', error);
  }
}

/**
 * 更新趋势图表的颜色配置
 */
function updateTrendChartColors() {
  try {
    console.log('更新趋势图表颜色配置');
    
    // 更新柱状图系列颜色
    if (staticBarChartData.value.series && staticBarChartData.value.series.length >= 2) {
      staticBarChartData.value.series[0].color = incomeColor.value; // 收入
      staticBarChartData.value.series[1].color = expenseColor.value; // 支出
    }
    
    // 只更新趋势图
    forceRerenderTrendChart();
  } catch (error) {
    console.error('更新趋势图表颜色配置失败:', error);
  }
}

/**
 * 更新饼图的颜色配置
 */
function updatePieChartColors() {
  try {
    console.log('更新饼图颜色配置');
    
    // 更新饼图颜色配置
    if (staticPieChartOpts.value) {
      staticPieChartOpts.value.color = [...pieColors.value];
    }
    
    // 更新饼图数据颜色
    if (staticPieChartData.value.series && staticPieChartData.value.series[0].data) {
      staticPieChartData.value.series[0].data.forEach((item, index) => {
        item.color = getCategoryColor(item, index);
      });
    }
    
    // 只更新饼图
    forceRerenderPieChart();
  } catch (error) {
    console.error('更新饼图颜色配置失败:', error);
  }
}

// 监听颜色变量变化，分别更新趋势图和饼图
watch([incomeColor, expenseColor], () => {
  // 只更新趋势图
  updateTrendChartColors();
});

watch(pieColors, () => {
  // 只更新饼图
  updatePieChartColors();
});

// 类型定义改进
interface ChartError {
  type: 'data' | 'load' | 'rendering';
  message: string;
}

/**
 * 检查图表是否存在指定类型的错误
 * @param errorType 错误类型，如'data'表示数据错误
 * @returns 是否存在该类型的错误
 */
function hasChartError(errorType: ChartError['type']): boolean {
  return chartErrors.value.some(error => error.type === errorType);
}

// 读取全局主题色和字体，供图表组件使用
const chartColors = ref<string[]>([]);
const chartFontFamily = ref('');

/**
 * 初始化图表颜色
 * 从CSS变量中获取实际颜色值，并设置到图表配置中
 */
function initChartColors() {
  try {
    // 从CSS变量获取对应的实际颜色值
    // 使用getCssVariableValue确保能获取到真实的颜色值而非CSS变量名
    incomeColor.value = getCssVariableValue('--color-success', '#4CAF50');
    expenseColor.value = getCssVariableValue('--color-primary', '#FF6B35');
    
    // 获取预设的图表颜色
    chartColors.value = getChartColors('bar');
    chartFontFamily.value = getCssVariableValue('--font-family-base', 'PingFang SC, Arial, sans-serif');
    
    // 获取预设的饼图颜色
    const pieColorPalette = getChartColors('pie') || [
      getCssVariableValue('--color-primary', '#FF6B35'),
      getCssVariableValue('--color-primary-light', '#FFB74D'),
      getCssVariableValue('--color-primary-lighter', '#FFCC80'),
      getCssVariableValue('--color-primary-lightest', '#FFE0B2'),
      getCssVariableValue('--color-disabled', '#BDBDBD')
    ];
    
    // 更新饼图颜色数组
    pieColors.value = pieColorPalette;
    
    // 分别更新趋势图和饼图颜色
    updateTrendChartColors();
    updatePieChartColors();
  } catch (error) {
    console.error('初始化图表颜色失败:', error);
    // 设置安全的默认颜色
    chartColors.value = ['#4CAF50', '#FF6B35'];
    pieColors.value = ['#FF6B35', '#FFB74D', '#FFCC80', '#FFE0B2', '#BDBDBD'];
  }
}

// 模拟数据集中管理
const mockDataConfig = {
  // 模拟预算数据
  budgets: [
    {
      id: 'budget-1',
      name: '餐饮',
      icon: 'utensils',
      amount: 2000,
      used: 1500,
      usedPercentage: 75
    },
    {
      id: 'budget-2',
      name: '交通',
      icon: 'car',
      amount: 1000,
      used: 1200,
      usedPercentage: 120
    },
    {
      id: 'budget-3',
      name: '购物',
      icon: 'shopping-bag',
      amount: 1500,
      used: 800,
      usedPercentage: 53
    }
  ],
  
  // 模拟财务洞察数据
  financialInsights: [
    {
      id: 'insight-1',
      type: 'success' as 'success',
      title: '餐饮支出下降',
      icon: 'check-circle',
      description: '您的餐饮支出较上期有所下降，继续保持！'
    },
    {
      id: 'insight-2',
      type: 'warning' as 'warning',
      title: '购物预算超支',
      icon: 'exclamation-triangle',
      description: '购物预算已超支，请关注下期该项支出'
    },
    {
      id: 'insight-3',
      type: 'info' as 'info',
      title: '储蓄目标达成',
      icon: 'thumbs-up',
      description: '本月储蓄率良好，已达到您设定目标的95%'
    }
  ]
};

// 从模拟数据配置中获取预算数据
const mockBudgets = computed(() => {
  // 如果有真实API数据，可以在这里替换为真实数据
  return mockDataConfig.budgets;
});

// 从模拟数据配置中获取财务洞察数据
const financialInsights = computed(() => {
  // 如果有真实API数据，可以在这里替换为真实数据
  return mockDataConfig.financialInsights;
});

// 添加新的方法用于根据屏幕宽度计算应显示的标签数量和配置
const updateChartOptions = () => {
  // 获取当前屏幕宽度
  const screenWidth = uni.getSystemInfoSync().windowWidth;
  
  // 根据屏幕宽度调整图表配置
  let labelCount = 12; // 默认显示所有标签
  let rotateLabel = false;
  
  if (currentTrendPeriod.value === '12months') {
    // 响应式调整标签显示策略
    if (screenWidth < 375) {
      // 小屏幕：只显示4个标签并旋转
      labelCount = 4;
      rotateLabel = true;
    } else if (screenWidth >= 375 && screenWidth < 500) {
      // 中等屏幕：显示6个标签，轻微旋转
      labelCount = 6;
      rotateLabel = true;
    } else {
      // 大屏幕：显示所有标签，但使用紧凑布局
      labelCount = 12;
      rotateLabel = false;
    }
  }
  
  // 更新图表配置对象
  staticBarChartOpts.value = {
    ...staticBarChartOpts.value,
    xAxis: {
      ...staticBarChartOpts.value.xAxis,
      itemCount: labelCount,
      rotateLabel: rotateLabel
    }
  };
  
  console.log(`屏幕宽度 ${screenWidth}px，调整近12月图表显示：标签数量=${labelCount}，旋转标签=${rotateLabel}`);
};

// 监听屏幕旋转或窗口大小变化
onMounted(() => {
  // 注册窗口大小变化事件（H5环境）
  // #ifdef H5
  window.addEventListener('resize', updateChartOptions);
  // #endif
  
  // 监听设备方向变化（App环境）
  // #ifdef APP-PLUS
  plus.orientation.addEventListener('change', updateChartOptions);
  // #endif
});

onUnmounted(() => {
  // 清理事件监听器，避免内存泄漏
  // #ifdef H5
  window.removeEventListener('resize', updateChartOptions);
  // #endif
  
  // #ifdef APP-PLUS
  plus.orientation.removeEventListener('change', updateChartOptions);
  // #endif
});

/**
 * 专门优化的近3个月数据聚合函数
 * @param transactions 交易数据列表
 * @param startDate 开始日期（第一个月的1号）
 * @param endDate 结束日期（当前日期）
 * @param incomeData 收入数据数组（会被修改）
 * @param expenseData 支出数据数组（会被修改）
 */
function aggregateLastThreeMonths(
  transactions: Transaction[],
  startDate: Date,
  endDate: Date,
  incomeData: number[],
  expenseData: number[]
) {
  // 清空输入数组
  incomeData.length = 0;
  expenseData.length = 0;

  console.log(`近3月聚合: 从 ${startDate.toISOString().split('T')[0]} 到 ${endDate.toISOString().split('T')[0]}`);
  console.log(`交易记录总条数: ${transactions.length}`);
  
  // 记录月份名称，用于日志输出
  const months = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'];
  
  // 处理连续的3个月
  for (let i = 0; i < 3; i++) {
    // 创建当前处理月份的起始日期
    const monthStart = new Date(startDate);
    monthStart.setMonth(startDate.getMonth() + i);
    monthStart.setDate(1);
    monthStart.setHours(0, 0, 0, 0);
    
    // 创建当前处理月份的结束日期
    const monthEnd = new Date(monthStart);
    monthEnd.setMonth(monthStart.getMonth() + 1);
    monthEnd.setDate(0); // 设置为当月最后一天
    monthEnd.setHours(23, 59, 59, 999);
    
    // 确保不超过今天
    if (monthEnd > endDate) {
      monthEnd.setTime(endDate.getTime());
    }
    
    const monthName = months[monthStart.getMonth()];
    const monthStartFormatted = monthStart.toISOString().split('T')[0];
    const monthEndFormatted = monthEnd.toISOString().split('T')[0];
    
    console.log(`处理 ${monthName} 范围: ${monthStartFormatted} 至 ${monthEndFormatted}`);
    
    // 过滤当月交易记录
    const monthTransactions = transactions.filter(tx => {
      try {
        // 解析交易日期
        const txDate = new Date(tx.date);
        if (isNaN(txDate.getTime())) {
          return false;
        }
        
        // 将交易日期规范化为日期对象（去除时分秒）
        const normalizedTxDate = new Date(
          txDate.getFullYear(),
          txDate.getMonth(),
          txDate.getDate(),
          0, 0, 0, 0
        );
        
        // 是否在当前月份范围内
        const isInRange = normalizedTxDate >= monthStart && normalizedTxDate <= monthEnd;
        
        // 如果在范围内，输出详细日志
        if (isInRange) {
          console.log(`${monthName} 包含交易: ${tx.date}, 金额: ${tx.amount}, 类型: ${tx.type}`);
        }
        
        return isInRange;
      } catch (e) {
        console.error(`处理交易日期错误: ${tx.date}`, e);
        return false;
      }
    });
    
    // 计算当月收入和支出总额
    const monthIncome = monthTransactions
      .filter(tx => tx.type === 'income')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
      
    const monthExpense = monthTransactions
      .filter(tx => tx.type === 'expense')
      .reduce((sum, tx) => sum + Math.abs(tx.amount), 0);
    
    console.log(`${monthName} 找到 ${monthTransactions.length} 条交易，收入: ${monthIncome}，支出: ${monthExpense}`);
    
    // 添加到数据数组
    incomeData.push(monthIncome);
    expenseData.push(monthExpense);
  }
  
  // 输出最终的数据数组
  console.log('近3月数据聚合完成，收入数据:', incomeData, '支出数据:', expenseData);
}

</script>

<template>
  <div class="analysis-page">
    <div class="analysis-page__content">
      <!-- 月份选择器包装器 -->
      <div class="analysis-page__date-selector">
        <AppMonthNavigator
          :year="currentYear"
          :month="currentMonth"
          @prev="navigateMonth('prev')"
          @next="navigateMonth('next')"
          @month-click="toggleDatePicker"
        />
      </div>
    
      <!-- 核心指标卡片 -->
      <AnalysisCoreIndicators 
        :indicators="coreIndicatorsData"
        class="analysis-page__card"
      />
    
      <!-- 收支趋势图表 -->
      <AnalysisTrendChart
        :chart-data="staticBarChartData"
        :chart-opts="staticBarChartOpts"
        :chart-key="trendChartKey"
        :is-loading="isLoading"
        :has-data-error="hasChartError('data')"
        :current-period="currentTrendPeriod"
        :period-options="trendPeriodOptions"
        :chart-colors="chartColors"
        :chart-font-family="chartFontFamily"
        class="analysis-page__card"
        @period-change="setTrendPeriod"
        @chart-complete="onChartComplete"
        @chart-error="onChartError"
      />
    
      <!-- 支出构成图表 -->
      <AnalysisExpenseComposition
        :categories="filteredCategoryPercentages"
        :chart-data="staticPieChartData"
        :chart-opts="dynamicPieChartOpts"
        :chart-key="pieChartKey"
        :is-loading="isLoading"
        :current-view="currentCategoryView"
        :chart-colors="chartColors"
        :chart-font-family="chartFontFamily"
        class="analysis-page__card"
        @view-change="handleCategoryViewChange"
        @chart-complete="onChartComplete"
        @chart-error="onChartError"
      />
    
      <!-- 预算执行卡片 -->
      <AnalysisBudgetExecution
        :budgets="mockBudgets"
        class="analysis-page__card"
        @manage-budget="navigateToBudgetSetup"
      />
    
      <!-- 财务洞察卡片 -->
      <AnalysisFinancialInsights
        :insights="financialInsights"
        :is-loading="false"
        class="analysis-page__card analysis-page__card--last"
      />
    </div>
    
    <!-- 日期选择器弹窗 -->
    <AppDatePicker
      v-model="currentDate"
      :visible="showDatePicker"
      type="month"
      :hide-trigger="true"
      @confirm="handleDateChange"
      @cancel="showDatePicker = false"
      @update:visible="showDatePicker = $event"
    />
    
    <!-- 底部导航栏 -->
    <AppTabBar 
      :active-index="activeTabIndex" 
      :tabs="tabBarConfig" 
      @change="handleTabChange"
    />
  </div>
</template>

<style lang="scss" scoped>
.analysis-page {
  box-sizing: border-box;
  padding: 0;
  background-color: var(--bg-secondary, #F8F9FA);
  min-height: 100vh;
  width: 100%;
  position: relative;

  &__content {
    width: 100%;
    max-width: 750px; 
    margin: 0 auto;
    padding: 0 var(--spacing-md, 16px);
    padding-top: var(--spacing-md, 16px);
    padding-bottom: var(--spacing-xs, 4px);
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    gap: 0;
  }

  &__date-selector {
    width: 100%;
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-xs, 6px);
    padding: var(--spacing-xs, 8px) 0;
    position: relative;
    z-index: 2;
    
    :deep(.app-month-navigator) {
      min-height: 48px;
      padding: var(--spacing-sm, 8px) var(--spacing-md, 16px);
      border-radius: 24px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
  }

  &__card {
    margin-bottom: var(--spacing-xs, 8px);
    width: 100%;
    overflow: hidden;
    border-radius: var(--radius-card, 12px);
    
    :deep(.app-card__content) {
      padding: var(--spacing-md, 16px);
    }
    
    &--last {
      margin-bottom: 0;
    }
  }
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>









