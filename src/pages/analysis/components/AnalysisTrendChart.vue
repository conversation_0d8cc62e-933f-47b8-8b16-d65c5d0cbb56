<script setup lang="ts">
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppSectionHeader from '@/components/common/AppSectionHeader.vue';
import QiunDataCharts from '@/components/u-charts/qiun-data-charts.vue';

// 定义组件属性
interface PeriodOption {
  value: string;
  label: string;
}

interface ChartData {
  categories: string[];
  series: {
    name: string;
    data: number[];
    color?: string;
  }[];
}

interface ChartOpts {
  type: string;
  padding: number[];
  background: string;
  enableScroll: boolean;
  fontSize: number;
  fontColor: string;
  legend: {
    show: boolean;
  };
  xAxis: {
    disableGrid: boolean;
    fontColor: string;
    fontSize: number;
    itemCount: number;
    boundaryGap: boolean;
    rotateLabel: boolean;
  };
  yAxis: {
    data: {
      min: number;
      fontColor: string;
      fontSize: number;
      format: (val: number) => string;
    }[];
    showTitle: boolean;
    gridType: string;
    dashLength: number;
    gridColor: string;
  };
  extra: {
    column: {
      type: string;
      width: number;
      barBorderRadius: number[];
      barBorderCircle: boolean;
      linearType: string;
      seriesGap: number;
    };
  };
  width: string;
  height: number;
}

interface Props {
  chartData: ChartData;
  chartOpts: ChartOpts;
  chartKey: number;
  isLoading: boolean;
  hasDataError: boolean;
  currentPeriod: string;
  periodOptions: PeriodOption[];
  chartColors: string[];
  chartFontFamily: string;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  (e: 'periodChange', period: string): void;
  (e: 'chartComplete', res: any): void;
  (e: 'chartError', err: any): void;
}>();

// 处理时间段变更
function handlePeriodChange(period: string): void {
  if (!period) {
    console.warn('时间段参数无效');
    return;
  }
  
  if (period !== props.currentPeriod) {
    emit('periodChange', period);
  }
}

// 处理图表完成事件
function onChartComplete(res: any): void {
  try {
    emit('chartComplete', res);
  } catch (error) {
    console.error('处理图表完成事件错误:', error);
  }
}

// 处理图表错误事件
function onChartError(err: any): void {
  try {
    console.error('图表渲染错误:', err);
    emit('chartError', err);
  } catch (error) {
    console.error('处理图表错误事件失败:', error);
  }
}

// 监听图表数据变化，确保组件能响应数据更新
watch(
  () => [props.chartData, props.chartKey],
  () => {
    console.log('趋势图表数据或key变化，图表将重新渲染');
  }
);

// 组件挂载完成
onMounted(() => {
  console.log('趋势图表组件挂载完成');
});
</script>

<template>
  <AppCard class="trend-chart-card">
    <div class="trend-chart-card__header">
      <AppSectionHeader 
        title="收支趋势" 
        icon="chart-line" 
        class="trend-chart-card__section-header"
      />
      
      <div class="period-selector">
        <button 
          v-for="period in periodOptions" 
          :key="period.value"
          class="period-btn"
          :class="{'period-btn--active': currentPeriod === period.value}" 
          @click="handlePeriodChange(period.value)"
        >
          {{ period.label }}
        </button>
      </div>
    </div>
    
    <div class="trend-chart-card__content">
      <!-- 图表区域 -->
      <div class="chart-container">
        <QiunDataCharts
          canvas-id="trend-bar-chart"
          type="column"
          :chartData="chartData"
          :opts="{ 
            ...chartOpts, 
            color: chartColors, 
            fontFamily: chartFontFamily,
            legend: { show: false } // 禁用内置图例，使用自定义图例
          }"
          width="100%"
          height="240px"
          @complete="onChartComplete"
          @error="onChartError"
          :key="`trend-chart-${currentPeriod}-${chartKey}`"
          v-show="!isLoading && !hasDataError"
        />
        
        <!-- 错误显示 -->
        <div v-if="hasDataError" class="chart-empty-state">
          <AppIcon icon="chart-bar" size="48px" color="var(--color-disabled)" />
          <p class="chart-empty-state__title">暂无交易数据</p>
          <p class="chart-empty-state__description">添加收支记录后即可查看趋势分析</p>
        </div>
        
        <!-- 加载指示器 -->
        <div v-if="isLoading" class="chart-loading">
          <AppIcon icon="spinner" class="rotating-icon" />
          加载中...
        </div>
      </div>

      <!-- 图例移动到这里 -->
      <div class="trend-chart-card__legend">
        <div class="trend-chart-card__legend-item">
          <div class="trend-chart-card__legend-dot" style="background-color: var(--color-success);"></div>
          <span>收入</span>
        </div>
        <div class="trend-chart-card__legend-item">
          <div class="trend-chart-card__legend-dot" style="background-color: var(--color-primary);"></div>
          <span>支出</span>
        </div>
      </div>
    </div>
  </AppCard>
</template>

<style lang="scss" scoped>
/* 收支趋势卡片 */
.trend-chart-card {
  width: 100%;
  border-radius: var(--radius-card, 12px);
  overflow: hidden;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md, 16px);
    flex-wrap: wrap; /* 允许在窄屏幕上换行 */

    /* 当空间不足时，确保标题能显示完整 */
    @media (max-width: 400px) {
      :deep(.section-header) {
        margin-bottom: 8px;
        width: 100%;
      }
      
      .period-selector {
        width: 100%;
        margin-top: 8px;
        justify-content: flex-start;
      }
    }
  }
  
  &__section-header {
    flex-shrink: 0; /* 防止标题被压缩 */
    
    :deep(.section-header__title) {
      white-space: nowrap; /* 防止标题文字换行 */
      font-size: var(--font-size-md, 16px); /* 确保标题字体大小合适 */
      margin-right: var(--spacing-sm, 8px); /* 与右侧按钮保持距离 */
    }
  }
  
  &__content {
    position: relative;
  }
  
  &__legend {
    display: flex;
    justify-content: center;
    gap: var(--spacing-md, 16px);
    margin-top: var(--spacing-sm, 8px);
    
    &-item {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs, 4px);
      font-size: var(--font-size-sm, 14px);
      color: var(--text-secondary, #666666);
    }
    
    &-dot {
      width: 10px;
      height: 10px;
      border-radius: 50%;
    }
  }
}

/* 时间段选择器 */
.period-selector {
  display: flex;
  gap: var(--spacing-xs, 4px);
  align-items: center;
  flex-wrap: wrap; /* 允许在极窄屏幕上换行 */
  
  .period-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    background: none;
    border: 1px solid var(--color-border, #E0E0E0);
    color: var(--text-secondary, #666666);
    font-size: var(--font-size-xs, 12px); /* 减小字体大小 */
    height: 28px; /* 调整高度，符合原型 */
    line-height: 1;
    padding: 0 8px; /* 减小水平内边距 */
    border-radius: var(--radius-sm, 4px);
    cursor: pointer;
    transition: all 0.2s ease;
    white-space: nowrap; /* 防止文本换行 */
    overflow: hidden; /* 如果文本过长，隐藏溢出部分 */
    text-overflow: ellipsis; /* 显示省略号 */
    min-width: 40px; /* 设置最小宽度 */
    max-width: 55px; /* 设置最大宽度，防止过宽 */
    
    /* 针对"近12月"按钮特殊处理，确保可以完全显示 */
    &:last-child {
      max-width: 60px; /* 稍微加宽最后一个按钮 */
    }
    
    &--active {
      background-color: var(--color-primary, #FF6B35);
      color: white;
      border-color: var(--color-primary, #FF6B35);
    }
    
    &:hover:not(&--active) {
      background-color: var(--bg-tertiary, #F2F2F2);
    }
  }
  
  /* 针对不同屏幕尺寸优化筛选按钮 */
  @media (max-width: 360px) {
    /* 在特别窄的屏幕上进一步缩小按钮 */
    .period-btn {
      min-width: 32px;
      padding: 0 5px;
      font-size: 11px; /* 进一步缩小字体 */
      
      /* 对近12月按钮做特殊处理 */
      &:last-child {
        min-width: 35px; /* 确保有足够空间显示文本 */
      }
    }
    
    /* 更紧凑的排列 */
    gap: 2px;
  }
  
  @media (min-width: 375px) and (max-width: 499px) {
    /* 中等尺寸屏幕适配 */
    .period-btn {
      font-size: 12px;
      padding: 0 6px;
      min-width: 36px;
      
      &:last-child {
        min-width: 42px;
      }
    }
  }
  
  @media (min-width: 500px) {
    /* 在较宽的屏幕上允许按钮显示更多内容 */
    .period-btn {
      min-width: 45px;
      max-width: 65px;
      font-size: var(--font-size-sm, 13px);
      padding: 0 10px;
      
      &:last-child {
        min-width: 50px;
        max-width: 70px;
      }
    }
  }
}

/* 图表容器 */
.chart-container {
  position: relative;
  min-height: 240px;
  
  /* 图表容器响应式调整 */
  @media (max-width: 375px) {
    /* 在小屏幕上，增加图表高度，以便旋转的标签有更多空间 */
    min-height: 250px;
  }
  
  @media (min-width: 500px) {
    /* 在大屏幕上，增加一些空间展示更多数据 */
    min-height: 260px;
  }
}

/* 图表加载状态 */
.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm, 14px);
  color: var(--text-secondary, #666666);
  background-color: rgba(255, 255, 255, 0.7);
  
  .rotating-icon {
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-xs, 4px);
    font-size: var(--font-size-xl, 20px);
  }
}

/* 图表空状态 */
.chart-empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: var(--bg-primary, #FFFFFF);
  
  &__title {
    margin-top: var(--spacing-sm, 8px);
    font-size: var(--font-size-md, 16px);
    color: var(--text-secondary, #666666);
    font-weight: 500;
  }
  
  &__description {
    margin-top: var(--spacing-xs, 4px);
    font-size: var(--font-size-sm, 14px);
    color: var(--text-hint, #999999);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 