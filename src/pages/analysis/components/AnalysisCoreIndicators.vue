<script setup lang="ts">
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppSectionHeader from '@/components/common/AppSectionHeader.vue';

// 定义组件属性
interface Indicator {
  title: string;
  icon: string;
  iconColor: string;
  value: string;
  trend: number;
  trendText: string;
  trendStyle: string;
}

interface Props {
  indicators: Indicator[];
}

const props = defineProps<Props>();
</script>

<template>
  <AppCard class="core-indicators-card">
    <AppSectionHeader 
      title="核心指标" 
      icon="bullseye" 
      withSpacing 
    />
    
    <div class="core-indicators__grid">
      <div 
        v-for="(indicator, index) in indicators" 
        :key="index"
        class="core-indicators__item"
      >
        <div class="core-indicators__item-title">
          <AppIcon :icon="indicator.icon" :color="indicator.iconColor" />
          {{ indicator.title }}
        </div>
        <div class="core-indicators__item-value">{{ indicator.value }}</div>
        <div 
          class="core-indicators__item-trend"
          :class="`core-indicators__item-trend--${indicator.trendStyle}`"
        >
          {{ indicator.trendText }}
        </div>
      </div>
    </div>
  </AppCard>
</template>

<style lang="scss" scoped>
/* 核心指标卡片 */
.core-indicators-card {
  width: 100%;
  border-radius: var(--radius-card, 12px);
  overflow: hidden;
}

/* 核心指标组件 */
.core-indicators__grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-sm, 8px);
  margin-top: var(--spacing-sm, 8px);
}

.core-indicators__item {
  background-color: var(--color-background-light, #F8F8F8);
  padding: var(--spacing-sm, 8px);
  border-radius: var(--radius-button, 8px);
  
  &-title {
    font-size: var(--font-size-sm, 14px);
    color: var(--color-text-secondary, #666666);
    display: flex;
    align-items: center;
    gap: var(--spacing-xs, 4px);
    
    /* 增强图标样式 */
    :deep(.app-icon) {
      font-size: var(--font-size-md, 16px);
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--color-background-tertiary, #f0f0f0);
      border-radius: 50%;
      padding: 4px;
    }
  }
  
  &-value {
    font-size: var(--font-size-xl, 20px);
    font-weight: bold;
    margin-top: var(--spacing-xs, 4px);
  }
  
  &-trend {
    font-size: var(--font-size-xs, 12px);
    color: var(--color-text-hint, #999999);
    margin-top: var(--spacing-xs, 4px);
    
    &--positive {
      color: var(--color-success, #4CAF50);
    }
    
    &--negative {
      color: var(--color-primary, #FF6B35);
    }
  }
}
</style> 