<script setup lang="ts">
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppSectionHeader from '@/components/common/AppSectionHeader.vue';

// 定义组件属性
interface Insight {
  id: string;
  type: 'info' | 'warning' | 'success' | 'danger';
  title: string;
  description: string;
  icon: string;
}

interface Props {
  insights: Insight[];
  isLoading: boolean;
}

const props = defineProps<Props>();

// 获取图标颜色
const getIconColor = (type: string) => {
  switch (type) {
    case 'info':
      return 'var(--color-info, #2196F3)';
    case 'warning':
      return 'var(--color-warning, #FFC107)';
    case 'success':
      return 'var(--color-success, #4CAF50)';
    case 'danger':
      return 'var(--color-error, #F44336)';
    default:
      return 'var(--color-primary, #FF6B35)';
  }
};
</script>

<template>
  <AppCard class="financial-insights-card">
    <AppSectionHeader 
      title="财务洞察" 
      icon="lightbulb" 
      iconColor="var(--color-primary)" 
      withSpacing 
    />
    
    <div class="financial-insights-card__content">
      <!-- 加载状态 -->
      <div v-if="isLoading" class="insights-loading">
        <AppIcon icon="spinner" class="rotating-icon" />
        <p>AI正在分析您的财务数据...</p>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else-if="insights.length === 0" class="insights-empty">
        <AppIcon icon="robot" size="lg" />
        <p class="insights-empty__text">暂无AI财务洞察</p>
        <p class="insights-empty__desc">随着您记录更多收支数据，AI将为您提供个性化的财务建议</p>
      </div>
      
      <!-- 有洞察内容时显示 -->
      <div v-else class="insights-list">
        <div 
          v-for="insight in insights" 
          :key="insight.id"
          class="insight-item"
          :class="`insight-item--${insight.type}`"
        >
          <div class="insight-item__icon-wrapper">
            <AppIcon :icon="insight.icon" :color="getIconColor(insight.type)" />
          </div>
          
          <div class="insight-item__content">
            <h3 class="insight-item__title">{{ insight.title }}</h3>
            <p class="insight-item__description">{{ insight.description }}</p>
          </div>
        </div>
      </div>
    </div>
  </AppCard>
</template>

<style lang="scss" scoped>
/* 财务洞察卡片 */
.financial-insights-card {
  width: 100%;
  border-radius: var(--radius-card, 12px);
  overflow: hidden;
}

/* 加载状态 */
.insights-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg, 24px);
  
  .rotating-icon {
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-sm, 8px);
    font-size: var(--font-size-xl, 20px);
    color: var(--color-primary, #FF6B35);
  }
  
  p {
    color: var(--text-secondary, #666666);
    font-size: var(--font-size-md, 16px);
  }
}

/* 空状态 */
.insights-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg, 24px);
  text-align: center;
  
  :deep(.app-icon) {
    color: var(--color-disabled, #CCCCCC);
    font-size: 40px;
    margin-bottom: var(--spacing-sm, 8px);
  }
  
  &__text {
    color: var(--text-secondary, #666666);
    font-size: var(--font-size-md, 16px);
    margin-bottom: var(--spacing-xs, 4px);
  }
  
  &__desc {
    color: var(--text-hint, #999999);
    font-size: var(--font-size-sm, 14px);
    line-height: 1.5;
  }
}

/* 洞察列表 */
.insights-list {
  padding: 0;
}

/* 洞察项目 */
.insight-item {
  display: flex;
  padding: var(--spacing-sm, 8px);
  margin-bottom: var(--spacing-sm, 8px);
  background-color: var(--color-background-light, #F8F8F8);
  border-radius: var(--radius-sm, 4px);
  
  &:last-child {
    margin-bottom: 0;
  }
  
  &__icon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--color-background-tertiary, #F0F0F0);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-sm, 8px);
    flex-shrink: 0;
  }
  
  &__content {
    flex: 1;
  }
  
  &__title {
    font-size: var(--font-size-md, 16px);
    font-weight: 500;
    color: var(--text-primary, #333333);
    margin-bottom: var(--spacing-xs, 4px);
  }
  
  &__description {
    font-size: var(--font-size-sm, 14px);
    color: var(--text-secondary, #666666);
    line-height: 1.5;
  }
  
  /* 不同类型的洞察样式变化 */
  &--info {
    background-color: rgba(33, 150, 243, 0.05);
    
    .insight-item__icon-wrapper {
      background-color: rgba(33, 150, 243, 0.1);
    }
  }
  
  &--warning {
    background-color: rgba(255, 193, 7, 0.05);
    
    .insight-item__icon-wrapper {
      background-color: rgba(255, 193, 7, 0.1);
    }
  }
  
  &--success {
    background-color: rgba(76, 175, 80, 0.05);
    
    .insight-item__icon-wrapper {
      background-color: rgba(76, 175, 80, 0.1);
    }
  }
  
  &--danger {
    background-color: rgba(244, 67, 54, 0.05);
    
    .insight-item__icon-wrapper {
      background-color: rgba(244, 67, 54, 0.1);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 