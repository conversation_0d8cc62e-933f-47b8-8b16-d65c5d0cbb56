<script setup lang="ts">
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppButton from '@/components/common/AppButton.vue';
import AppSectionHeader from '@/components/common/AppSectionHeader.vue';
import { formatAmount } from '@/utils/formatters';

// 定义组件属性
interface BudgetItem {
  id: string;
  name: string;
  icon: string;
  amount: number;
  used: number;
  usedPercentage: number;
}

interface Props {
  budgets: BudgetItem[];
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  (e: 'manageBudget'): void;
}>();

// 管理预算按钮点击
function handleManageBudget() {
  emit('manageBudget');
}
</script>

<template>
  <AppCard class="budget-execution-card">
    <div class="budget-execution-card__header">
      <AppSectionHeader 
        title="预算执行" 
        icon="money-bill-wave" 
        iconColor="var(--color-primary)" 
      />
      
      <AppButton 
        size="small"
        type="primary"
        plain
        @click="handleManageBudget"
        class="budget-execution-card__manage-btn"
      >
        管理预算
      </AppButton>
    </div>
    
    <div class="budget-execution-card__content">
      <div 
        v-for="budget in budgets" 
        :key="budget.id"
        class="budget-item"
      >
        <div class="budget-item__header">
          <div class="budget-item__icon-wrapper">
            <AppIcon :icon="budget.icon || 'question-circle'" />
          </div>
          
          <div class="budget-item__details">
            <div class="budget-item__name">{{ budget.name }}</div>
            
            <!-- 预算进度条 -->
            <div class="budget-item__progress-bar-container">
              <div 
                class="budget-item__progress-bar"
                :style="{ width: `${Math.min(budget.usedPercentage, 100)}%` }"
                :class="{ 'budget-item__progress-bar--exceeded': budget.usedPercentage > 100 }"
              ></div>
            </div>
          </div>
        </div>
        
        <div class="budget-item__summary">
          <span>已用 {{ formatAmount(budget.used) }} / {{ formatAmount(budget.amount) }}</span>
          
          <span 
            :class="budget.used <= budget.amount ? 'budget-item__summary-remaining' : 'budget-item__summary-exceeded'"
          >
            {{ budget.used <= budget.amount 
              ? `剩余 ${formatAmount(budget.amount - budget.used)}` 
              : `超支 ${formatAmount(budget.used - budget.amount)}` 
            }}
          </span>
        </div>
      </div>
      
      <!-- 无预算提示，当预算数据为空时显示 -->
      <div v-if="budgets.length === 0" class="budget-execution-card__empty">
        <AppIcon icon="info-circle" size="lg" class="budget-execution-card__empty-icon" />
        <p class="budget-execution-card__empty-text">您还没有设置预算，点击"管理预算"开始规划您的支出</p>
      </div>
    </div>
  </AppCard>
</template>

<style lang="scss" scoped>
/* 预算执行卡片 */
.budget-execution-card {
  width: 100%;
  border-radius: var(--radius-card, 12px);
  overflow: hidden;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md, 16px);
  }
  
  &__empty {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-md, 16px);
    text-align: center;
    
    &-icon {
      color: var(--color-hint, #C0C0C0);
      margin-bottom: var(--spacing-sm, 8px);
      font-size: 36px;
    }
    
    &-text {
      color: var(--text-hint, #999999);
      font-size: var(--font-size-sm, 14px);
      line-height: 1.5;
    }
  }
}

/* 预算项目 */
.budget-item {
  margin-bottom: var(--spacing-sm, 8px);
  padding-bottom: var(--spacing-sm, 8px);
  border-bottom: 1px solid var(--color-border, #EFEFEF);
  
  &:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
  }
  
  &__header {
    display: flex;
    align-items: center;
    margin-bottom: var(--spacing-xs, 4px);
  }
  
  &__icon-wrapper {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: var(--color-background-light, #F5F5F5);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: var(--spacing-sm, 8px);
    
    :deep(.app-icon) {
      color: var(--color-primary, #FF6B35);
      font-size: var(--font-size-md, 16px);
    }
  }
  
  &__details {
    flex: 1;
  }
  
  &__name {
    font-size: var(--font-size-md, 16px);
    color: var(--text-primary, #333333);
    font-weight: 500;
    margin-bottom: var(--spacing-xs, 4px);
  }
  
  &__progress-bar-container {
    width: 100%;
    height: 6px;
    background-color: var(--color-background-light, #F5F5F5);
    border-radius: 3px;
    overflow: hidden;
  }
  
  &__progress-bar {
    height: 100%;
    background-color: var(--color-success, #4CAF50);
    border-radius: 3px;
    transition: width 0.3s ease;
    
    &--exceeded {
      background-color: var(--color-error, #F44336);
    }
  }
  
  &__summary {
    display: flex;
    justify-content: space-between;
    font-size: var(--font-size-xs, 12px);
    color: var(--text-hint, #999999);
    margin-left: 40px; /* 与图标对齐 */
    
    &-remaining {
      color: var(--color-success, #4CAF50);
    }
    
    &-exceeded {
      color: var(--color-error, #F44336);
    }
  }
}
</style> 