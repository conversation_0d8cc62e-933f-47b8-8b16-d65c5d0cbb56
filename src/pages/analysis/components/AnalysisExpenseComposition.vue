<script setup lang="ts">
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppSectionHeader from '@/components/common/AppSectionHeader.vue';
import QiunDataCharts from '@/components/u-charts/qiun-data-charts.vue';
import { formatAmount } from '@/utils/formatters';

// 定义组件属性
interface CategoryItem {
  id?: string;
  name: string;
  amount: number;
  percentage?: number;
  percentageText?: string;
  color?: string;
  bgColor?: string;
}

interface ChartData {
  series: {
    data: {
      name: string;
      value: number;
      color?: string;
    }[];
  }[];
}

interface ChartOpts {
  type: string;
  padding: number[];
  background: string;
  enableScroll: boolean;
  fontSize: number;
  title?: {
    name: string;
    fontSize: number;
    color: string;
    offsetY: number;
  };
  subtitle?: {
    name: string;
    fontSize: number;
    color: string;
    offsetY: number;
  };
  legend: {
    show: boolean;
  };
  dataLabel: boolean;
  color: string[];
  width: number | string;
  height: number | string;
  extra: {
    ring?: {
      ringWidth: number;
      activeOpacity: number;
      activeRadius: number;
      offsetAngle: number;
      labelWidth: number;
      border: boolean;
      borderWidth: number;
      borderColor: string;
      centerColor: string;
    };
    tooltip?: {
      showBox: boolean;
    };
    pie?: {
      activeOpacity: number;
      activeRadius: number;
      offsetAngle: number;
      labelWidth: number;
      border: boolean;
      borderWidth: number;
      borderColor: string;
    };
  };
}

interface Props {
  categories: CategoryItem[];
  chartData: ChartData;
  chartOpts: ChartOpts;
  chartKey: number;
  isLoading: boolean;
  currentView: 'top5' | 'all';
  chartColors: string[];
  chartFontFamily: string;
}

const props = defineProps<Props>();

// 定义事件
const emit = defineEmits<{
  (e: 'viewChange', view: 'top5' | 'all'): void;
  (e: 'chartComplete', res: any): void;
  (e: 'chartError', err: any): void;
}>();

// 处理视图变更
function handleViewChange(view: 'top5' | 'all'): void {
  // 确保视图类型有效
  if (view !== 'top5' && view !== 'all') {
    console.warn(`无效的视图类型: ${view}`);
    return;
  }
  
  if (view !== props.currentView) {
    emit('viewChange', view);
  }
}

// 处理图表完成事件
function onChartComplete(res: any): void {
  try {
    emit('chartComplete', res);
  } catch (error) {
    console.error('处理图表完成事件错误:', error);
  }
}

// 处理图表错误事件
function onChartError(err: any): void {
  try {
    console.error('图表渲染错误:', err);
    emit('chartError', err);
  } catch (error) {
    console.error('处理图表错误事件失败:', error);
  }
}

/**
 * 获取分类占比百分比
 * @param amount 金额
 * @returns 百分比值(0-100)
 */
function getCategoryPercent(amount: number): number {
  if (typeof amount !== 'number' || isNaN(amount)) {
    console.warn(`无效的金额值: ${amount}`);
    return 0;
  }
  
  // 计算总金额时防止数组为空
  const total = props.categories?.reduce((sum, cat) => sum + (typeof cat.amount === 'number' ? cat.amount : 0), 0) || 0;
  
  if (!total) return 0;
  return Math.round((amount / total) * 100);
}

// 监听分类数据变化
watch(
  () => props.categories,
  (newCategories) => {
    if (!newCategories) {
      console.warn('分类数据为空');
      return;
    }
    console.log('支出分类数据更新，数量:', newCategories.length);
  }
);

// 监听图表数据和配置变化
watch(
  () => [props.chartData, props.chartOpts, props.chartKey],
  () => {
    console.log('支出占比图表数据或配置变化，图表将重新渲染');
  }
);

// 组件挂载完成
onMounted(() => {
  console.log('支出占比组件挂载完成');
});
</script>

<template>
  <AppCard class="expense-composition-card">
    <div class="expense-composition-card__header">
      <AppSectionHeader 
        title="支出构成" 
        icon="chart-pie" 
      />
      
      <div class="expense-composition-card__view-selector">
        <button 
          class="period-btn"
          :class="{'period-btn--active': currentView === 'top5'}" 
          @click="handleViewChange('top5')"
        >
          Top 5
        </button>
        <button 
          class="period-btn"
          :class="{'period-btn--active': currentView === 'all'}" 
          @click="handleViewChange('all')"
        >
          全部
        </button>
      </div>
    </div>
    
    <div class="expense-composition-card__content">
      <!-- 左侧环形图 -->
      <div class="expense-composition-card__chart-area">
        <div class="pie-chart-container">
          <QiunDataCharts
            type="ring"
            :opts="props.chartOpts"
            :chartData="props.chartData"
            :canvasId="`expense-pie-chart-${props.chartKey}`"
            :canvas2d="false"
            :key="props.chartKey"
            :animation="true"
            width="130px"
            height="130px"
            @complete="onChartComplete"
            @error="onChartError"
            v-if="!isLoading && categories.length > 0"
          />
            
          <!-- 空状态显示 -->
          <div v-if="categories.length === 0 && !isLoading" class="pie-empty-state">
            <div class="chart-empty-state__content">
              <AppIcon icon="chart-pie" size="32px" color="var(--color-disabled)" />
              <p class="chart-empty-state__title">暂无支出数据</p>
            </div>
          </div>
          
          <!-- 加载指示器 -->
          <div v-if="isLoading" class="chart-loading">
            <AppIcon icon="spinner" class="rotating-icon" />
            加载中...
          </div>
        </div>
      </div>
      
      <!-- 右侧类别图例 -->
      <div class="category-legend">
        <template v-if="categories.length > 0">
          <div 
            v-for="(category, index) in categories" 
            :key="category.id || index"
            class="legend-item"
          >
            <div class="legend-item__color-wrapper">
              <div 
                class="legend-item__color-dot"
                :style="{ backgroundColor: category.color || category.bgColor }"
              ></div>
            </div>
            <div class="legend-item__label">{{ category.name }}</div>
            <div class="legend-item__value">{{ formatAmount(category.amount) }}</div>
            <div class="legend-item__percent">{{ getCategoryPercent(category.amount) }}%</div>
          </div>
        </template>
        
        <!-- 如果没有数据，显示空状态图例提示信息 -->
        <div v-if="categories.length === 0 && !isLoading" class="legend-empty">
          <p class="legend-empty__tips">添加支出记录后即可查看分类占比</p>
        </div>
      </div>
    </div>
  </AppCard>
</template>

<style lang="scss" scoped>
/* 支出构成卡片 */
.expense-composition-card {
  width: 100%;
  border-radius: var(--radius-card, 12px);
  overflow: hidden;
  
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md, 16px);
  }
  
  &__content {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
  }
  
  &__chart-area {
    width: 130px;
    height: 130px;
    flex: 0 0 auto;
  }
  
  &__view-selector {
    display: flex;
    gap: var(--spacing-xs, 4px);
  }
}

/* 图表容器 */
.pie-chart-container {
  position: relative;
  width: 130px;
  height: 130px;
}

/* 类别图例 */
.category-legend {
  flex: 1;
  margin-left: var(--spacing-md, 16px);
  overflow-y: auto;
  max-height: 200px;
}

.legend-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs, 4px) 0;
  font-size: var(--font-size-sm, 14px);
  color: var(--text-secondary, #666666);
  
  &__color-wrapper {
    margin-right: var(--spacing-xs, 4px);
    width: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  
  &__color-dot {
    width: 10px;
    height: 10px;
    border-radius: 50%;
  }
  
  &__label {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: var(--spacing-xs, 4px);
  }
  
  &__value {
    margin-right: var(--spacing-sm, 8px);
    font-weight: 500;
    color: var(--text-primary, #333333);
  }
  
  &__percent {
    width: 40px;
    text-align: right;
  }
}

.legend-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100px;
  
  &__tips {
    font-size: var(--font-size-sm, 14px);
    color: var(--text-hint, #999999);
    text-align: center;
  }
}

/* 按钮样式 */
.period-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: 1px solid var(--color-border, #E0E0E0);
  color: var(--text-secondary, #666666);
  font-size: var(--font-size-sm, 14px);
  height: 24px;
  line-height: 24px;
  padding: 0 10px; /* 水平内边距，去除垂直padding */
  border-radius: var(--radius-sm, 4px);
  cursor: pointer;
  transition: all 0.2s ease;
  
  &--active {
    background-color: var(--color-primary, #FF6B35);
    color: white;
    border-color: var(--color-primary, #FF6B35);
  }
  
  &:hover:not(&--active) {
    background-color: var(--bg-tertiary, #F2F2F2);
  }
}

/* 图表空状态 */
.pie-empty-state {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  
  .chart-empty-state__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  .chart-empty-state__title {
    margin-top: var(--spacing-xs, 4px);
    font-size: var(--font-size-xs, 12px);
    color: var(--text-hint, #999999);
  }
}

/* 图表加载状态 */
.chart-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm, 14px);
  color: var(--text-secondary, #666666);
  background-color: rgba(255, 255, 255, 0.7);
  
  .rotating-icon {
    animation: spin 1s linear infinite;
    margin-bottom: var(--spacing-xs, 4px);
    font-size: var(--font-size-xl, 20px);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 