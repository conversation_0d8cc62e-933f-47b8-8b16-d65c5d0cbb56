<script setup lang="ts">
import AppTabBar from '@/components/common/AppTabBar.vue';
import { ref } from 'vue';

const title = ref('示例页面');
const activeTabIndex = ref(0); // 默认选中首页(索引为0)

// 底部标签栏配置
const tabBarConfig = [
  {
    label: '首页',
    icon: 'house',
    pagePath: '/pages/home/<USER>',
  },
  {
    label: '账单',
    icon: 'list-ul',
    pagePath: '/pages/transaction/list',
  },
  {
    label: '统计',
    icon: 'chart-pie',
    pagePath: '/pages/analysis/index',
  },
  {
    label: '我的',
    icon: 'user',
    pagePath: '/pages/profile/index',
  },
];

// 处理TabBar切换事件
function handleTabChange(index: number) {
  if (index !== activeTabIndex.value) {
    switch (index) {
      case 0:
        uni.switchTab({ url: '/pages/home/<USER>' }); // 首页
        break;
      case 1:
        uni.switchTab({ url: '/pages/transaction/list' }); // 账单页
        break;
      case 2:
        uni.switchTab({ url: '/pages/analysis/index' }); // 统计页
        break;
      case 3:
        uni.switchTab({ url: '/pages/profile/index' }); // 我的页
        break;
    }
  }
}
</script>

<template>
  <view class="index-page">
    <view class="content">
      <image class="logo" src="/static/logo.png" />
      <view class="text-area">
        <text class="title">
          {{ title }}
        </text>
      </view>
    </view>

    <!-- 底部导航栏 -->
    <AppTabBar :active-index="activeTabIndex" :tabs="tabBarConfig" @change="handleTabChange" />
  </view>
</template>

<style lang="scss" scoped>
  .index-page {
    width: 100%;
    height: 100vh;
    display: flex;
    flex-direction: column;
    position: relative;
  }

  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center; /* 为底部TabBar预留空间 */
    background-color: var(--bg-primary, #fff);

    // H5端底部导航栏高度

    /* #ifdef H5 */

    /* #endif */
    // 小程序底部导航栏高度

    /* #ifdef MP-WEIXIN */

    /* #endif */
    // APP端根据平台动态调整

    /* #ifdef APP-PLUS */

    /* #ifdef APP-PLUS-IOS */

    /* #endif */

    /* #ifdef APP-PLUS-ANDROID */
    padding-bottom: 56px;

    /* #endif */

    /* #endif */
  }

  .logo {
    height: 200px;
    width: 200px;
    margin: 0 auto 50px;
  }

  .text-area {
    display: flex;
    justify-content: center;
  }

  .title {
    font-size: 36px;
    color: var(--text-secondary, #8f8f94);
    font-weight: 500;
  }
</style>
