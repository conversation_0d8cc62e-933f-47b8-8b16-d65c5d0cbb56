<script setup lang="ts">
import type { Transaction, TransactionCategory } from '@/types/transaction'; // 确保引入 TransactionCategory
import CategorySelector from '@/components/business/CategorySelector.vue';
import TransactionItem from '@/components/business/TransactionItem.vue';
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppTabBar from '@/components/common/AppTabBar.vue'; // 引入 AppTabBar
import { useCategoryStore } from '@/stores/category.store';
import { useTransactionStore } from '@/stores/transaction.store'; // 引入Store
import { getCategoryStyle, getUniqueColors } from '@/utils/category-helper'; // 添加 getUniqueColors
import { formatAmount, formatMonthYear } from '@/utils/formatters';
import { computed, onMounted, reactive, ref } from 'vue';
import CategoryProgressBar from '@/components/business/CategoryProgressBar.vue';
import AppSegmentedControl from '@/components/common/AppSegmentedControl.vue';
import AppDatePicker from '@/components/common/AppDatePicker.vue';
import AppMonthNavigator from '@/components/common/AppMonthNavigator.vue';

// --- Store Instance ---
const transactionStore = useTransactionStore();
const categoryStore = useCategoryStore();

// --- State ---
const currentDate = reactive({
  year: new Date().getFullYear(),
  month: new Date().getMonth() + 1, // 1-12
});

// 添加返回上一页功能
function goBack() {
  // 🔑 优化：判断页面栈深度，只有一页时直接跳首页
  const pages = getCurrentPages();
  if (pages.length > 1) {
    uni.navigateBack({
      fail: () => {
        uni.switchTab({ url: '/pages/home/<USER>' });
      },
    });
  } else {
    uni.switchTab({ url: '/pages/home/<USER>' });
  }
}

const viewOptions = [
  { label: '支出', value: 'expense' },
  { label: '收入', value: 'income' },
  { label: '结余', value: 'balance' },
];
const currentView = ref<'expense' | 'income' | 'balance'>('expense');

const showDatePicker = ref(false);
const isRefresherTriggered = ref(false);
// isLoading 和 isLoadingMore 可以从 store 获取
const hasMoreData = ref(true); // 添加此行，定义hasMoreData变量
const isLoadingMore = computed(() => transactionStore.isLoading); // 添加isLoadingMore计算属性

// 底部标签栏配置
const activeTabIndex = ref(1); // 当前页面是"账单"页，索引为1
const tabBarConfig = [
  {
    label: '首页',
    icon: 'house',
    pagePath: '/pages/home/<USER>',
  },
  {
    label: '账单',
    icon: 'list',
    pagePath: '/pages/transaction/list',
  },
  {
    label: '统计',
    icon: 'chart-pie',
    pagePath: '/pages/analysis/index',
  },
  {
    label: '我的',
    icon: 'user',
    pagePath: '/pages/profile/index',
  },
];

// --- Mock Data (移除) ---
// const mockTransactions: Transaction[] = [...];
// const summaryData = reactive({...});

// --- Computed ---

const displayMonthYear = computed(() => formatMonthYear(currentDate.year, currentDate.month));

const currentMonth = computed(() => currentDate.month);

const viewLabel = computed(() => viewOptions.find(v => v.value === currentView.value)?.label || '');

// 从 Store 获取月度汇总数据
const summaryData = computed(() => transactionStore.getTotalsByType);

// 从 Store 获取并按日期分组交易记录
const groupedTransactions = computed(() => {
  // 1. Filter based on currentView
  const filtered = transactionStore.monthlyTransactions.filter((tx) => {
    if (currentView.value === 'balance')
      return true; // 结余显示所有
    
    // 确保对象的类型和当前视图匹配
    // 注意：tx.type 应该是 'income' 或 'expense'
    return tx.type === currentView.value;
  });

  console.log(`当前视图: ${currentView.value}, 筛选后交易数量: ${filtered.length}`);

  // 2. Group by date
  const groups: Record<string, Transaction[]> = {};
  filtered.forEach((tx) => {
    const date = tx.date.split('T')[0]; // 确保只取日期部分 YYYY-MM-DD
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(tx);
  });

  // 3. Format for display
  return Object.entries(groups)
    .map(([date, transactions]) => ({
      date,
      displayDate: getDisplayDateLabel(date),
      transactions,
    }))
    .sort((a, b) => b.date.localeCompare(a.date));
});

// 计算支出分类占比
const expenseCategoriesPercentage = computed(() => {
  try {
    // 防御性检查：确保summaryData.value存在且有expense属性
    if (!summaryData.value || typeof summaryData.value.expense !== 'number') {
      console.warn('summaryData.value.expense不是有效数字', summaryData.value);
      return [];
    }
    
    const expenseTotal = Math.abs(summaryData.value.expense);
    if (expenseTotal === 0 || isNaN(expenseTotal)) {
      return [];
    }

    const categoryTotals: Record<
      string,
      { name: string, amount: number, icon: string, id: string }
    > = {};

    // 防御性检查：确保monthlyTransactions是数组
    if (!Array.isArray(transactionStore.monthlyTransactions)) {
      console.warn('monthlyTransactions不是有效数组');
      return [];
    }

    transactionStore.monthlyTransactions
      .filter(tx => tx && tx.type === 'expense' && tx.category)
      .forEach((tx) => {
        try {
          const cat = tx.category as TransactionCategory; // 强制类型转换，因为前面过滤了
          const catId = typeof cat === 'object' ? cat.id : String(cat);
          
          if (!catId) {
            console.warn('交易记录的分类ID无效:', tx);
            return;
          }
          
          if (!categoryTotals[catId]) {
            categoryTotals[catId] = {
              id: String(catId),
              name: typeof cat === 'object' ? cat.name : '未知分类',
              amount: 0,
              icon: typeof cat === 'object' ? cat.icon : 'question-circle',
            };
          }
          
          // 防御性检查：确保tx.amount是有效数字
          if (typeof tx.amount === 'number' && !isNaN(tx.amount)) {
            categoryTotals[catId].amount += Math.abs(tx.amount);
          }
        } catch (error) {
          console.error('处理交易分类数据出错:', error, tx);
        }
      });

    // 获取分类数据并计算百分比
    const categoriesWithPercentage = Object.values(categoryTotals)
      .map((cat) => {
        try {
          const style = getCategoryStyle(cat.name); // 获取样式
          // 确保百分比计算正确（防御性编程）
          const percentage = expenseTotal > 0 ? cat.amount / expenseTotal : 0;
          return {
            ...cat,
            percentage, // 存储为0-1之间的小数，方便CategoryProgressBar使用
            color: style.color, // 从 style 对象获取 color
          };
        } catch (error) {
          console.error('计算分类百分比出错:', error, cat);
          return {
            ...cat,
            percentage: 0,
            color: 'var(--text-hint, #999)'
          };
        }
      })
      .filter(cat => cat.percentage > 0) // 过滤掉百分比为0的分类
      .sort((a, b) => b.percentage - a.percentage) // 按百分比排序
      .slice(0, 5); // 最多显示前5项

    // 使用getUniqueColors确保颜色不重复
    const result = getUniqueColors(categoriesWithPercentage);

    // 调试输出
    console.log('支出分类占比:', result);
    return result;
  } catch (error) {
    console.error('计算支出分类占比时出错:', error);
    return [];
  }
});

// 计算收入分类占比
const incomeCategoriesPercentage = computed(() => {
  try {
    // 防御性检查：确保summaryData.value存在且有income属性
    if (!summaryData.value || typeof summaryData.value.income !== 'number') {
      console.warn('summaryData.value.income不是有效数字', summaryData.value);
      return [];
    }
    
    const incomeTotal = Math.abs(summaryData.value.income);
    if (incomeTotal === 0 || isNaN(incomeTotal)) {
      return [];
    }

    const categoryTotals: Record<
      string,
      { name: string, amount: number, icon: string, id: string }
    > = {};

    // 防御性检查：确保monthlyTransactions是数组
    if (!Array.isArray(transactionStore.monthlyTransactions)) {
      console.warn('monthlyTransactions不是有效数组');
      return [];
    }

    transactionStore.monthlyTransactions
      .filter(tx => tx && tx.type === 'income' && tx.category)
      .forEach((tx) => {
        try {
          const cat = tx.category as TransactionCategory;
          const catId = typeof cat === 'object' ? cat.id : String(cat);
          
          if (!catId) {
            console.warn('交易记录的分类ID无效:', tx);
            return;
          }
          
          if (!categoryTotals[catId]) {
            categoryTotals[catId] = {
              id: String(catId),
              name: typeof cat === 'object' ? cat.name : '未知收入',
              amount: 0,
              icon: typeof cat === 'object' ? cat.icon : 'wallet',
            };
          }
          
          // 防御性检查：确保tx.amount是有效数字
          if (typeof tx.amount === 'number' && !isNaN(tx.amount)) {
            categoryTotals[catId].amount += Math.abs(tx.amount);
          }
        } catch (error) {
          console.error('处理交易分类数据出错:', error, tx);
        }
      });

    // 获取分类数据并计算百分比
    const categoriesWithPercentage = Object.values(categoryTotals)
      .map((cat) => {
        try {
          const style = getCategoryStyle(cat.name);
          // 确保百分比计算正确（防御性编程）
          const percentage = incomeTotal > 0 ? cat.amount / incomeTotal : 0;
          return {
            ...cat,
            percentage, // 存储为0-1之间的小数，方便CategoryProgressBar使用
            color: style.color,
          };
        } catch (error) {
          console.error('计算分类百分比出错:', error, cat);
          return {
            ...cat,
            percentage: 0,
            color: 'var(--text-hint, #999)'
          };
        }
      })
      .filter(cat => cat.percentage > 0) // 过滤掉百分比为0的分类
      .sort((a, b) => b.percentage - a.percentage)
      .slice(0, 5); // 最多显示前5项

    // 使用getUniqueColors确保颜色不重复
    const result = getUniqueColors(categoriesWithPercentage);

    console.log('收入分类占比:', result);
    return result;
  } catch (error) {
    console.error('计算收入分类占比时出错:', error);
    return [];
  }
});

// --- Methods ---

// 切换月份 - 增强防御性编程
function changeMonth(delta: number) {
  try {
    // 防御性检查：确保delta是有效数值
    if (typeof delta !== 'number' || isNaN(delta)) {
      console.warn('无效的月份变化值:', delta);
      return;
    }
    
    let newMonth = currentDate.month + delta;
    let newYear = currentDate.year;
    
    if (newMonth > 12) {
      newMonth = 1;
      newYear++;
    }
    else if (newMonth < 1) {
      newMonth = 12;
      newYear--;
    }
    
    // 防御性检查：确保生成的年月有效
    if (newYear < 1970 || newYear > 2100) {
      console.warn('年份超出有效范围:', newYear);
      return;
    }
    
    currentDate.month = newMonth;
    currentDate.year = newYear;
    loadData(); // 切换月份后重新加载数据
  } catch (error) {
    console.error('月份切换出错:', error);
  }
}

// 切换视图
function changeView(view: 'expense' | 'income' | 'balance') {
  currentView.value = view;
}

// 打开日期选择器
function openMonthPicker() {
  showDatePicker.value = true;
}

// 确认日期选择
function handleDatePickerConfirm(result: { year: number, month: number } | string) {
  // 处理不同格式的参数
  if (typeof result === 'string') {
    // 如果是字符串格式，例如 "2025-04"
    const parts = result.split('-');
    if (parts.length >= 2) {
      currentDate.year = parseInt(parts[0], 10);
      currentDate.month = parseInt(parts[1], 10);
    }
  } else {
    // 如果是对象格式，例如 { year: 2025, month: 4 }
    currentDate.year = result.year;
    currentDate.month = result.month;
  }
  
  // 重新加载数据并关闭选择器
  loadData();
  showDatePicker.value = false;
}

// 获取日期显示标签
function getDisplayDateLabel(dateStr: string): string {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  const yesterday = new Date(today);
  yesterday.setDate(yesterday.getDate() - 1);
  const txDate = new Date(dateStr);
  txDate.setHours(0, 0, 0, 0);

  if (txDate.getTime() === today.getTime()) {
    return `今天 (${today.getMonth() + 1}月${today.getDate()}日)`;
  }
  else if (txDate.getTime() === yesterday.getTime()) {
    return `昨天 (${yesterday.getMonth() + 1}月${yesterday.getDate()}日)`;
  }
  else {
    return new Intl.DateTimeFormat('zh-CN', { month: 'long', day: 'numeric' }).format(txDate);
  }
}

// 跳转到报表页面
function goToReport() {
  uni.navigateTo({ url: '/pages/analysis/report' }); // 假设报表页路径
}

// 点击交易项
function handleTransactionClick(transaction: Transaction) {
  if (!transaction?.id) {
    console.error('Invalid transaction or missing ID:', transaction);
    uni.showToast({
      title: '无效的交易记录',
      icon: 'none',
    });
    return;
  }

  // 确保id是字符串
  const id = transaction.id.toString();
  uni.navigateTo({
    url: `/pages/transaction/detail?id=${id}`,
    fail: (err) => {
      console.error('Navigation failed:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none',
      });
    },
  });
}

// 标签切换处理
function handleTabChange(index: number) {
  if (index !== activeTabIndex.value) {
    switch (index) {
      case 0:
        uni.switchTab({ url: '/pages/home/<USER>' });
        break;
      case 1:
        // 当前页面，不需要跳转
        break;
      case 2:
        uni.switchTab({ url: '/pages/analysis/index' });
        break;
      case 3:
        uni.switchTab({ url: '/pages/profile/index' });
        break;
    }
  }
}

// --- Data Loading ---

async function loadData(isRefresh = false) {
  if (transactionStore.isLoading && !isRefresh)
    return; // 防止重复加载

  console.log(
    `Loading data for ${currentDate.year}-${currentDate.month}, view: ${currentView.value}`,
  );

  try {
    await transactionStore.fetchMonthlyTransactions({
      year: currentDate.year,
      month: currentDate.month,
      categoryId: selectedCategoryId.value || undefined,
    });
    console.log('Data loaded successfully from store.');
    // 月度数据一次加载，可能不需要分页
    // hasMoreData.value = transactionStore.pagination.page < transactionStore.pagination.totalPages;
  }
  catch (error) {
    console.error('Failed to load transaction data:', error);
    // 可以显示错误提示
    uni.showToast({ title: '加载数据失败', icon: 'none' });
  }
  finally {
    if (isRefresh) {
      isRefresherTriggered.value = false; // 停止下拉刷新动画
    }
  }
}

// 下拉刷新处理
function handleRefresherRefresh() {
  console.log('Pull down refresh triggered');
  isRefresherTriggered.value = true;
  loadData(true); // 强制刷新当前月份数据
}

// 上拉加载更多处理 - 月度数据一次性加载
function handleScrollToLower() {
  console.log('Scroll to lower - currently loads all monthly data at once.');
  // 如果未来需要分页加载，可以在此处实现
}

// 监听视图切换，如果需要根据视图重新请求数据(如果API支持按类型筛选月度数据)
// watch(currentView, () => loadData());

// 监听日期变化 - 已在 changeMonth 和 confirmDateSelection 中处理
// watch(currentDate, () => loadData());

onMounted(async () => {
  await categoryStore.fetchCategories(); // 先加载分类
  
  // 确保先从本地存储加载所有交易数据
  transactionStore.loadFromStorage();
  console.log('账单页：已从本地存储加载交易数据');
  
  await transactionStore.fetchMonthlyTransactions({
    year: currentDate.year,
    month: currentDate.month,
    categoryId: selectedCategoryId.value || undefined,
  });
  // 在 onMounted 中设置初始 Tab 索引
  activeTabIndex.value = 1;
  console.log('Setting active tab index to 1 for transaction list page on mount');
});

// 新增：导航到手动记账
function goToAddTransaction() {
  uni.navigateTo({ url: '/pages/transaction/record' }); // 修正路径
  console.log('Navigate to Transaction Record');
}

// 新增：导航到语音记账
function goToVoiceInput() {
  uni.navigateTo({ url: '/pages/chat/index' });
  console.log('Navigate to AI Chat');
}

// 新增：打开账单提醒设置
function openReminderSettings() {
  uni.navigateTo({ url: '/pages/transaction/reminder-settings' }); // 假设账单提醒设置页面路径
  console.log('打开账单提醒设置');
}

// 新增：类别选择器相关逻辑
const showCategorySelector = ref(false);
const categoryOptions = computed(() => {
  // 从categoryStore获取分类数据，根据当前视图类型选择收入或支出分类
  return currentView.value === 'income'
    ? categoryStore.incomeCategories
    : currentView.value === 'expense'
      ? categoryStore.expenseCategories
      : [...categoryStore.incomeCategories, ...categoryStore.expenseCategories];
});
const selectedCategoryId = ref('');

function handleCategorySelect(id: string) {
  try {
    // 防御性检查：确保传入的id有效
    if (typeof id !== 'string') {
      console.warn('无效的分类ID:', id);
      return;
    }
    
    // 验证id是否存在于当前分类列表中
    const categoryExists = categoryOptions.value.some(cat => cat.id === id);
    if (id && !categoryExists) {
      console.warn('选择的分类ID不存在于当前分类列表中:', id);
      // 仍然允许继续，因为可能是"全部"选项或其他特殊值
    }
    
    selectedCategoryId.value = id;
    showCategorySelector.value = false;
    loadData();
  } catch (error) {
    console.error('处理分类选择时出错:', error);
  }
}
</script>

<template>
  <view class="transaction-list-page">
    <!-- 页面内容滚动区域 -->
    <scroll-view
      scroll-y
      class="page-content"
      :refresher-enabled="true"
      :refresher-triggered="isRefresherTriggered"
      @scrolltolower="handleScrollToLower"
      @refresherrefresh="handleRefresherRefresh"
    >
      <!-- 顶部月份和筛选区域(移到scroll-view内) -->
      <view class="filter-section">
        <!-- 替换为AppMonthNavigator组件 -->
        <AppMonthNavigator
          :year="currentDate.year"
          :month="currentDate.month"
          @prev="changeMonth(-1)"
          @next="changeMonth(1)"
          @month-click="openMonthPicker"
        />
        <!-- 视图切换按钮：优化样式 -->
        <AppSegmentedControl
          v-model="currentView"
          :options="viewOptions"
          class="segmented-control-optimized"
        />
      </view>
      
      <!-- 月度汇总卡片 -->
      <AppCard padding="large" class="summary-card">
        <template #header>
          <view class="summary-header">
            <view class="summary-title">
              {{ currentMonth }}月汇总
            </view>
            <view class="report-link" @click="goToReport">
              查看报告 <AppIcon icon="chevron-right" size="xs" />
            </view>
          </view>
        </template>

        <view class="summary-stats">
          <view class="stat-item">
            <view class="stat-label">
              <AppIcon icon="arrow-down" size="xs" />
              <span>收入</span>
            </view>
            <view class="stat-value income">
              {{ formatAmount(summaryData.income) }}
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-label">
              <AppIcon icon="arrow-up" size="xs" />
              <span>支出</span>
            </view>
            <view class="stat-value expense">
              {{ formatAmount(summaryData.expense) }}
            </view>
          </view>
          <view class="stat-item">
            <view class="stat-label">
              <AppIcon icon="equals" size="xs" />
              <span>结余</span>
            </view>
            <view class="stat-value balance">
              {{ formatAmount(summaryData.balance) }}
            </view>
          </view>
        </view>

        <!-- 图表区域 - 支出视图 -->
        <view
          v-if="currentView === 'expense' && expenseCategoriesPercentage.length > 0"
          class="chart-section"
        >
          <view class="chart-title">
            <span>主要支出分类</span>
          </view>
          <!-- 修复：不需要再除以100，直接传入原始percentage值 -->
          <CategoryProgressBar :categories="expenseCategoriesPercentage" />
        </view>
        <view
          v-else-if="currentView === 'expense' && expenseCategoriesPercentage.length === 0"
          class="chart-section"
        >
          <view class="chart-title">
            主要支出分类
          </view>
          <view class="no-chart-data">
            暂无支出数据
          </view>
        </view>

        <!-- 图表区域 - 收入视图 -->
        <view
          v-else-if="currentView === 'income' && incomeCategoriesPercentage.length > 0"
          class="chart-section"
        >
          <view class="chart-title">
            主要收入来源
          </view>
          <!-- 修复：不需要再除以100，直接传入原始percentage值 -->
          <CategoryProgressBar :categories="incomeCategoriesPercentage" />
        </view>
        <view
          v-else-if="currentView === 'income' && incomeCategoriesPercentage.length === 0"
          class="chart-section"
        >
          <view class="chart-title">
            主要收入来源
          </view>
          <view class="no-chart-data">
            暂无收入数据
          </view>
        </view>

        <!-- 结余视图不显示图表 -->
        <view
          v-else-if="currentView === 'balance'"
          class="chart-section"
        >
          <view class="chart-title">
            月度收支对比
          </view>
          <view class="no-chart-data">
            （暂无收支对比图表）
          </view>
        </view>
      </AppCard>

      <!-- 交易列表 -->
      <view v-if="groupedTransactions.length > 0">
        <template v-for="group in groupedTransactions" :key="group.date">
          <view class="date-heading">
            {{ group.displayDate }}
          </view>
          <view class="custom-transaction-card">
            <TransactionItem
              v-for="tx in group.transactions"
              :key="tx.id"
              :transaction="tx"
              @click="handleTransactionClick"
              class="custom-transaction-item"
            />
          </view>
        </template>
      </view>
      <view v-else class="no-transactions">
        <AppIcon icon="folder-open" size="lg" />
        <text>本月暂无{{ viewLabel }}记录</text>
      </view>

      <!-- 加载更多状态 - 结构占位符 -->
      <view v-if="isLoadingMore" class="loading-more">
        加载中...
      </view>
      <view v-if="!hasMoreData && groupedTransactions.length > 0" class="no-more-data">
        没有更多记录了
      </view>
    </scroll-view>

    <!-- 日期选择器弹窗(保持在scroll-view外) -->
    <AppDatePicker
      v-model:visible="showDatePicker"
      type="month"
      :modelValue="`${currentDate.year}-${String(currentDate.month).padStart(2, '0')}`"
      :hide-trigger="true"
      @confirm="handleDatePickerConfirm"
      @cancel="showDatePicker = false"
    />

    <!-- 新增：手动记账 FAB -->
    <view class="fab manual-fab" @click="goToAddTransaction">
      <AppIcon icon="plus" />
    </view>
    <!-- 新增：AI记账 FAB -->
    <view class="fab ai-fab" @click="goToVoiceInput">
      <AppIcon icon="microphone-alt" />
      <view class="ai-fab-tooltip">
        语音记账
      </view>
    </view>

    <!-- 添加底部导航栏 -->
    <AppTabBar :active-index="activeTabIndex" :tabs="tabBarConfig" @change="handleTabChange" />

    <!-- 类别选择器弹窗 -->
    <view v-if="showCategorySelector" class="category-selector-modal">
      <view class="category-selector-backdrop" @click="showCategorySelector = false" />
      <view class="category-selector-container">
        <view class="category-selector-header">
          <view class="category-selector-title">
            选择类别
          </view>
          <view class="category-selector-close" @click="showCategorySelector = false">
            <AppIcon icon="xmark" />
          </view>
        </view>
        <CategorySelector
          :categories="categoryOptions"
          :model-value="selectedCategoryId"
          title="支出类别"
          @update:model-value="handleCategorySelect"
        />
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.transaction-list {
  min-height: 100vh;
  padding-bottom: 100px; // 为底部导航留出空间
  background-color: var(--bg-tertiary, #f7f7f7);

  &__header {
    padding: var(--spacing-md, 16px);
    background-color: var(--bg-primary, #ffffff);
    border-bottom: 1px solid var(--border-color-light, rgba(0, 0, 0, 0.05));
  }

  &__filters {
    display: flex;
    justify-content: space-between;
    margin-top: var(--spacing-xs, 8px);
    padding: 0 var(--spacing-sm, 12px);
    
    .filter-btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-xs, 8px) var(--spacing-sm, 12px);
      border-radius: var(--radius-sm, 4px);
      font-size: var(--font-size-sm, 14px);
      color: var(--text-secondary, #666);
      background-color: var(--bg-secondary, #f5f5f5);
      
      .app-icon {
        margin-right: 4px;
      }
    }
  }

  &__content {
    position: relative;
  }

  &__empty {
    padding: var(--spacing-xl, 32px);
    text-align: center;
  }

  &__fab {
    position: fixed;
    right: var(--spacing-xl, 32px);
    bottom: calc(var(--spacing-xl, 32px) + var(--tab-bar-height, 60px));
    z-index: 99;
  }

  &__date-group {
    margin-bottom: var(--spacing-md, 16px);

    &-header {
      padding: var(--spacing-sm, 12px) var(--spacing-md, 16px);
      font-size: var(--font-size-sm, 14px);
      font-weight: 500;
      color: var(--text-secondary, #666);
      background-color: var(--bg-tertiary, #f7f7f7);
      border-bottom: 1px solid var(--border-color-light, rgba(0, 0, 0, 0.05));
      position: sticky; // 使日期标题固定在顶部
      top: 0;
      z-index: 2;
    }

    &-transactions {
      background-color: var(--bg-primary, #ffffff);
      border-radius: var(--radius-md, 8px);
      margin: 0 var(--spacing-sm, 12px);
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
      overflow: hidden; // 确保内部元素不突出边界
    }
  }

  // 日期分组内的交易项样式
  .transaction-item {
    position: relative;
    
    // 最后一个交易项不需要下边线
    &:last-child::after {
      display: none;
    }
  }
}

// 骨架屏加载样式
.skeleton-loading {
  padding: var(--spacing-md, 16px);
  background-color: var(--bg-primary, #ffffff);
  border-radius: var(--radius-md, 8px);
  margin: var(--spacing-sm, 12px);
  animation: pulse 1.5s infinite;
  
  &__row {
    height: 24px;
    background-color: var(--bg-tertiary, #f7f7f7);
    border-radius: var(--radius-sm, 4px);
    margin-bottom: var(--spacing-sm, 12px);
    
    &:last-child {
      margin-bottom: 0;
      width: 70%;
    }
  }
}

@keyframes pulse {
  0% { opacity: 0.6; }
  50% { opacity: 1; }
  100% { opacity: 0.6; }
}

/* 使用高特异性选择器，避免使用 !important */
:deep(uni-page-body) {
  // 保持页面主体背景为白色
  background-color: var(--bg-primary, #fff);
  height: 100%; // 确保 page-body 占满高度
  display: flex; // 确保 page-body 内部元素可以 flex
  flex-direction: column; // 确保 page-body 内部元素垂直排列
}

/* 添加更多选择器以提高特异性 */
:deep(.uni-page-head) + uni-page-body,
:deep(uni-app) uni-page-body,
.transaction-list-page + :deep(uni-page-body) {
  background-color: var(--bg-primary, #fff);
}

.transaction-list-page {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 var(--page-padding-x, 15px);
  box-sizing: border-box;
  margin-top: 0;
  padding-top: 0;
  /* 优化底部间距，适配多平台安全区域 */
  padding-bottom: calc(
    var(--tab-bar-height, 60px) + 
    env(safe-area-inset-bottom, 0px) + 
    var(--content-bottom-padding, 10px)
  );
  background-color: var(--bg-secondary, #f8f9fa);
  -webkit-overflow-scrolling: touch;
  position: relative;
  
  /* 多端适配 - 特定平台底部间距调整 */
  /* #ifdef MP-WEIXIN */
  padding-bottom: calc(var(--tab-bar-height, 60px) + 15px);
  /* #endif */
  
  /* #ifdef APP-PLUS */
  padding-bottom: calc(var(--tab-bar-height, 60px) + env(safe-area-inset-bottom, 0px) + 5px);
  /* #endif */
}

.filter-section {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
  margin-top: 15px;
  padding: 0;
  background: transparent;
  position: static;
  justify-content: space-between;
  z-index: 2;
  width: 100%;
}

/* 修改AppMonthNavigator在当前页面的特定样式 */
:deep(.app-month-navigator) {
  flex-shrink: 0;
  height: 40px;
  border-radius: 20px;
  border: 1.5px solid var(--border-color, #ececec);
  box-shadow: 0 2px 8px rgb(0 0 0 / 7%);
  background: var(--bg-primary, #fff);
  padding: 0 6px;
  width: auto;
  max-width: 130px;
}

.segmented-control-optimized {
  flex-shrink: 0;
  min-width: 0;
  width: auto;
  max-width: 220px;
  height: 40px;
  display: flex;
  align-items: center;
  border-radius: var(--radius-pill, 20px);
  background: var(--bg-primary, #fff);
  border: 1.5px solid var(--border-color, #ececec);
  box-shadow: var(--shadow-sm, 0 2px 8px rgb(0 0 0 / 7%));
  overflow: hidden;
  margin-left: auto;
  padding: 0;
  transition: box-shadow 0.2s;
  position: relative;
}

:deep(.app-segmented-control) {
  width: 100%;
  height: 40px;
  border-radius: 20px;
  background: transparent;
  box-shadow: none;
  display: flex;
  align-items: center;
  gap: 0;
  overflow: hidden;
  position: relative;
}

:deep(.segmented-btn) {
  flex: 1 1 0;
  height: 40px;
  min-width: 70px;
  padding: 0 13px;
  font-size: 14px;
  border: none;
  border-radius: 0;
  background: transparent;
  color: var(--text-secondary, #666);
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  transition: background 0.2s, color 0.2s;
  box-shadow: none;
  z-index: 1;
  cursor: pointer;
  overflow: hidden;
  white-space: nowrap;
}

:deep(.segmented-btn.active) {
  background: linear-gradient(90deg, var(--color-primary, #FF6B35) 80%, var(--color-primary-light, #ffbfa3) 100%);
  color: var(--text-inverse, #fff);
  z-index: 2;
  box-shadow: 0 1px 4px rgb(255 107 53 / 10%);
  border-radius: var(--radius-button, 8px);
}

/* 分隔线 */
:deep(.segmented-btn:not(:last-child))::after {
  content: '';
  position: absolute;
  right: 0;
  top: 25%;
  width: 1px;
  height: 50%;
  background: var(--border-color, #ececec);
  z-index: 1;
}

/* 优化点击反馈：主色淡色圆角块，贴合按钮形状 */
:deep(.segmented-btn:active):not(.active) {
  background: linear-gradient(90deg, #ffe3d3 80%, #fff6f0 100%);
  color: var(--color-primary, #FF6B35);
  border-radius: 16px;
  box-shadow: 0 1px 4px rgb(255 107 53 / 6%);
}

/* 汇总卡片样式 */
.summary-card {
  margin-bottom: 15px;
  margin-left: 0;
  margin-right: 0;
  width: 100%;
  overflow: hidden;
  background-color: var(--bg-primary, #fff);
  border: none;
  border-radius: var(--radius-card, 12px);
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.05);
  
  &:deep(.uni-card__content) {
    padding: 0;
    border-radius: var(--radius-card, 12px);
    overflow: hidden;
  }
}

.summary-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  width: 100%;
}

.summary-title {
  font-size: 16px;
  color: var(--text-primary, #555);
  font-weight: 600;
  flex-shrink: 0;
}

.report-link {
  font-size: 14px;
  color: var(--color-primary, #ff6b35);
  text-decoration: none;
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 2px;
  margin-left: auto;
  flex-shrink: 0;
}

.summary-stats {
  display: flex;
  margin-bottom: 16px;
  gap: 10px;
}

.stat-item {
  flex: 1;
  text-align: center;
  padding: 0 3px;
  position: relative;
  min-width: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
  height: 46px;

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    right: -5px;
    top: 15%;
    height: 70%;
    width: 1px;
    background-color: var(--border-color, #f0f0f0);
  }
}

.stat-label {
  font-size: 13px;
  color: var(--text-secondary, #888);
  margin-bottom: 8px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  gap: 4px;
  white-space: nowrap;

  span {
    line-height: 1.2;
  }
}

.stat-value {
  font-size: 16px;
  font-weight: 700;
  line-height: 1.4;
  width: 100%;
  text-align: center;
  padding: 0 4px;
  box-sizing: border-box;
  overflow: visible;
  white-space: nowrap;
  max-width: 110px;
  margin: 0 auto;
  display: block;

  &.income {
    color: var(--color-success, #10b981);
  }

  &.expense {
    color: var(--color-error, #ff6b35);
  }

  &.balance {
    color: var(--text-primary, #333);
  }
}

.chart-section {
  margin-top: 8px;
  padding-top: 10px;
  border-top: 1px solid var(--border-color, #f0f0f0);
}

.chart-title {
  font-size: 15px;
  font-weight: 600;
  margin-bottom: 12px;
  color: var(--text-secondary, #555);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.no-chart-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 60px;
  color: var(--text-hint, #999);
  background-color: var(--bg-secondary, #f9f9f9);
  border-radius: var(--radius-sm, 6px);
  font-size: 14px;
}

.date-heading {
  margin: 20px 0 10px 5px;
  font-size: 14px;
  font-weight: 600;
  color: var(--text-secondary, #666);
}

/* 更高级的自定义交易卡片样式，完全避免边框渲染问题 */
.custom-transaction-card {
  overflow: hidden;
  border-radius: var(--radius-card, 12px);
  background-color: var(--bg-primary, #fff);
  margin-bottom: 15px;
  position: relative;
  z-index: 1;
  border: none;
  /* 启用硬件加速、解决亚像素渲染问题 */
  transform: translateZ(0);
  /* 使用内部阴影代替边框，避免边框渲染问题 */
  box-shadow: 
    inset 0 0 0 1px rgba(0, 0, 0, 0.01),
    0 1px 4px rgba(0, 0, 0, 0.05); 
  will-change: transform;
  /* 只在这里控制左右padding */
  padding-left: var(--spacing-md, 16px);
  padding-right: var(--spacing-md, 16px);
}

/* 覆盖TransactionItem子组件样式，解决边框渲染问题 */
.custom-transaction-item {
  /* 特殊处理确保每个项目正常渲染，不受父级或子级影响 */
  position: relative; 
  z-index: 1;
  /* 移除左右padding，避免重复 */
  margin: 0;
  /* 不再设置padding-left/right */
  /* 为了解决边框渲染问题，我们在列表项之间使用特殊分隔线 */
  &:not(:last-child)::after {
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 0.5px;
    background-color: var(--border-color-light, rgba(0, 0, 0, 0.05));
    transform: scaleY(0.5);
    transform-origin: bottom;
  }
}

.no-transactions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 0;
  color: var(--text-hint, #999);
  font-size: 14px;
  text-align: center;
  gap: 10px;
}

.loading-more,
.no-more-data {
  text-align: center;
  color: var(--text-hint, #999);
  padding: 15px 0;
  font-size: 13px;
}

/* FAB 按钮样式 - 优化使用CSS变量 */
@keyframes float {
  0% {
    transform: translateY(0);
    box-shadow: var(--shadow-fab, 0 6px 16px rgb(0 0 0 / 15%));
  }

  50% {
    transform: translateY(-5px);
    box-shadow: var(--shadow-fab-hover, 0 10px 20px rgb(0 0 0 / 20%));
  }

  100% {
    transform: translateY(0);
    box-shadow: var(--shadow-fab, 0 6px 16px rgb(0 0 0 / 15%));
  }
}

.fab {
  position: fixed;
  width: var(--fab-size, 52px);
  height: var(--fab-size, 52px);
  border-radius: var(--radius-circle, 50%);
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: var(--font-size-icon-md, 22px);
  color: var(--text-inverse, white);
  cursor: pointer;
  z-index: var(--z-index-fab, 100);
  animation: float 3s ease-in-out infinite;
  transition:
    transform 0.1s ease-out,
    background-color 0.2s;

  &:active {
    transform: scale(0.95);
    animation-play-state: paused;
  }
}

.manual-fab {
  left: var(--space-md, 16px);
  bottom: calc(
    var(--tab-bar-height, 60px) + var(--space-xl, 25px) + env(safe-area-inset-bottom, 0px)
  );
  background: linear-gradient(135deg, var(--color-primary, #ff6b35), var(--color-primary-light, #ff8c61));
  box-shadow: var(--shadow-primary, 0 6px 16px rgb(255 107 53 / 35%));
}

.ai-fab {
  right: var(--space-md, 16px);
  bottom: calc(
    var(--tab-bar-height, 60px) + var(--space-xl, 25px) + env(safe-area-inset-bottom, 0px)
  );
  background-color: var(--bg-primary, white);
  color: var(--color-primary, #ff6b35);
  box-shadow: var(--shadow-raised, 0 6px 16px rgb(0 0 0 / 15%));
  padding: 0;
  overflow: visible;

  .ai-fab-tooltip {
    position: absolute;
    top: -40px;
    left: 50%;
    transform: translateX(-50%);
    background-color: var(--bg-tooltip, #4a4a4a);
    color: var(--text-inverse, white);
    font-size: var(--font-size-sm, 12px);
    padding: 6px 12px;
    border-radius: var(--radius-pill, 16px);
    white-space: nowrap;
    box-shadow: var(--shadow-sm, 0 2px 8px rgb(0 0 0 / 15%));
    opacity: 1;
    transition: opacity 0.3s ease;
    pointer-events: none;

    &::after {
      content: '';
      position: absolute;
      bottom: -5px;
      left: 50%;
      transform: translateX(-50%);
      width: 0;
      height: 0;
      border-left: 6px solid transparent;
      border-right: 6px solid transparent;
      border-top: 6px solid var(--bg-tooltip, #4a4a4a);
    }
  }
}

.category-selector-modal {
  position: fixed;
  inset: 0;
  background-color: rgb(0 0 0 / 50%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;

  .category-selector-backdrop {
    position: absolute;
    inset: 0;
  }

  .category-selector-container {
    background-color: white;
    padding: 20px;
    border-radius: 10px;
    width: 80%;
    max-width: 400px;

    .category-selector-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;

      .category-selector-title {
        font-size: 18px;
        font-weight: 600;
      }

      .category-selector-close {
        cursor: pointer;
      }
    }
  }
}
</style>
