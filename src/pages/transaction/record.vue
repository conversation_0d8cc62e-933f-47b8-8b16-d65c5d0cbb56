<script setup lang="ts">
import type { CategoryWithResolvedIcon } from '@/stores/category.store';
import type { CreateTransactionPayload, Transaction, UpdateTransactionPayload } from '@/types/transaction';
import AmountInput from '@/components/business/AmountInput.vue';
import CategorySelector from '@/components/business/CategorySelector.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppButton from '@/components/common/AppButton.vue';
import { useCategoryStore } from '@/stores/category.store';
import { useTransactionStore } from '@/stores/transaction.store';
import { getPlatformStyle } from '@/utils/platform'; // 平台适配工具
import { onLoad } from '@dcloudio/uni-app';
import { computed, ref, watchEffect } from 'vue';
import dayjs from 'dayjs';
import { ModeType, TransactionStatus } from '@/types/transaction';

// 将 modes 定义移到 setup 函数外部，作为模块级常量
const modes = [
  { label: '支出', value: ModeType.EXPENSE },
  { label: '收入', value: ModeType.INCOME },
  { label: '转账', value: ModeType.TRANSFER },
];

// 获取 store 实例
const transactionStore = useTransactionStore();
const categoryStore = useCategoryStore();

// 页面状态
const currentMode = ref<ModeType>(ModeType.EXPENSE);
const keyboardVisible = ref(false);
const amountValue = ref(0); // 金额输入值，应始终为 number
const actualKeyboardHeight = ref(0); // To potentially adjust scroll padding
const selectedDate = ref(new Date()); // 存储选中的日期
const isEditMode = ref(false); // 是否是编辑模式
const editingTransactionId = ref<string>(''); // 编辑中的交易ID
const amountInputKey = ref(0); // 用于强制刷新AmountInput组件

// 新增全局变量保存options
const pageOptions = ref<any>({});

// 监听当前模式变化，同步更新 categoryStore 的当前类型
watchEffect(() => {
  if (currentMode.value === ModeType.EXPENSE || currentMode.value === ModeType.INCOME) {
    categoryStore.setCurrentType(currentMode.value);
  }
});

// 计算属性：根据当前选中的分类 ID 获取完整的分类对象
// selectedCategory 现在直接从 store 中获取，不需要本地 ref 或 computed 包装器来直接修改
// const selectedCategory = computed(() => categoryStore.selectedCategory); // 可以移除，直接使用 categoryStore.selectedCategory

// 计算属性：获取带有解析后图标的分类列表
const categoriesWithResolvedIcons = computed(() => categoryStore.currentCategoriesWithResolvedIcons);

// 新增：保证v-model为字符串，并始终格式化为两位小数
const amountValueStr = computed({
  get: () => amountValue.value.toFixed(2), // 始终显示两位小数
  set: (val: string) => {
    amountValue.value = parseFloat(val) || 0;
  },
});

// 辅助函数：将小写type参数标准化为ModeType
function normalizeModeType(type: string): ModeType {
  if (!type) return ModeType.EXPENSE;
  if (type.toLowerCase() === 'income') return ModeType.INCOME;
  if (type.toLowerCase() === 'expense') return ModeType.EXPENSE;
  if (type.toLowerCase() === 'transfer') return ModeType.TRANSFER;
  return ModeType.EXPENSE;
}

// 生命周期 - 页面加载
onLoad((options?: Record<string, string | undefined>) => {
  pageOptions.value = options || {};
  const rawType = options?.type || options?.mode;
  const typeParam = normalizeModeType(rawType as string);
  if (options?.id) {
    isEditMode.value = true;
    editingTransactionId.value = options.id;
    if (options.amount) {
      amountValue.value = parseFloat(options.amount);
    }
    if (typeParam) {
      currentMode.value = typeParam;
    }
    fetchTransactionDetails(options.id, { ...(options || {}), type: typeParam });
  } else {
    isEditMode.value = false;
    initDefaultValues({ ...(options || {}), type: typeParam });
  }
});

// 获取交易详情
const fetchTransactionDetails = async (id: string, params?: any) => {
  try {
    uni.showLoading({ title: '加载中...' });
    const transaction = await transactionStore.fetchTransactionById(id);
    if (transaction) {
      // 只在参数未传递时才用store数据补全
      if (!params?.type) {
        currentMode.value = transaction.type as ModeType;
      }
      if (!params?.amount) {
        amountValue.value = Math.abs(transaction.amount || 0);
        amountInputKey.value++;
      }
      if (transaction.date) {
        selectedDate.value = new Date(transaction.date);
      }
      if (transaction.category && typeof transaction.category === 'object' && 'id' in transaction.category) {
        categoryStore.selectCategory(transaction.category.id as string | null);
      } else if (typeof transaction.category === 'string') {
        categoryStore.selectCategory(transaction.category as string | null);
      }
      setTimeout(() => {
        keyboardVisible.value = true;
      }, 300);
    }
  } catch (error) {
    console.error('获取交易详情失败:', error);
    uni.showToast({
      title: '获取交易详情失败',
      icon: 'none',
    });
  } finally {
    uni.hideLoading();
  }
};

// 修改initDefaultValues，接收options参数
const initDefaultValues = (options?: any) => {
  // 🔑 优化：根据跳转参数自动切换tab，优先type参数
  const typeParam = normalizeModeType(options?.type);
  if (typeParam === ModeType.INCOME || typeParam === ModeType.EXPENSE) {
    currentMode.value = typeParam;
    categoryStore.setCurrentType(typeParam);
  } else {
    // 默认设置为支出模式
    currentMode.value = ModeType.EXPENSE;
    categoryStore.setCurrentType(ModeType.EXPENSE);
  }

  // 如果URL参数中有amount，则使用该值
  if (!amountValue.value) {
    amountValue.value = 0;
  }

  // 延迟显示键盘，确保界面渲染完成
  setTimeout(() => {
    keyboardVisible.value = true;
  }, 300);
};

// 切换记账模式（支出/收入/转账）
function switchMode(mode: ModeType) {
  currentMode.value = mode;

  // 重置分类选择状态
  categoryStore.selectCategory(null);

  // 如果是支出或收入模式，更新 categoryStore 的当前类型
  if (mode === ModeType.EXPENSE || mode === ModeType.INCOME) {
    categoryStore.setCurrentType(mode);
  }

  keyboardVisible.value = false; // 切换模式时隐藏键盘
}

// 处理分类选择
function handleCategorySelect(category: CategoryWithResolvedIcon) {
  // 设置选中的分类ID到store
  categoryStore.selectCategory(category.id);
  // 显示键盘
  keyboardVisible.value = true;
}

// 确认交易记录
async function handleConfirmTransaction(amount: number) {
  // 使用 categoryStore.selectedCategory 替代本地的 selectedCategory 计算属性
  if (!categoryStore.selectedCategory) {
    uni.showToast({ title: '请先选择分类', icon: 'none' });
    return;
  }

  // 检查金额是否有效
  if (isNaN(amount) || amount <= 0) {
    uni.showToast({ title: '请输入有效金额', icon: 'none' });
    return;
  }

  // 检查当前模式是否为转账，处理相应逻辑
  if (currentMode.value === ModeType.TRANSFER) {
    uni.showToast({ title: '转账功能暂未实现', icon: 'none' });
    return; // 暂时阻止转账类型的提交
  }

  try {
    // 修复：使用本地时区格式化日期，包含具体时间，避免时区问题
    const formattedDate = dayjs(selectedDate.value || new Date()).format('YYYY-MM-DDTHH:mm:ss');

    // 根据是否是编辑模式，执行不同的操作
    if (isEditMode.value && editingTransactionId.value) {
      // 构造更新数据
      const updateData: UpdateTransactionPayload = {
        type: currentMode.value,
        amount, // 使用计算好的金额值
        date: formattedDate, // 使用本地格式化的日期，避免时区问题
        categoryId: categoryStore.selectedCategory?.id || '', // <--- 修改
        notes: '' // 增加防御性编程，提供默认值
      };
      
      // 更新交易
      await transactionStore.updateTransaction(editingTransactionId.value, updateData);
      // 🔑 交易更新后，重新拉取最新数据并同步金额
      const updatedTx = await transactionStore.fetchTransactionById(editingTransactionId.value);
      // 修改：确保赋给 amountValue 的是 number
      amountValue.value = parseFloat(Math.abs(updatedTx?.amount || 0).toFixed(2)); 
      amountInputKey.value++; // 强制刷新AmountInput组件
      uni.showToast({ title: '更新成功', icon: 'success' });
    } else {
      // 构造新交易数据
      const transactionData: CreateTransactionPayload = {
        type: currentMode.value,
        amount, // 使用计算好的金额值
        date: formattedDate, // 使用本地格式化的日期，避免时区问题
        categoryId: categoryStore.selectedCategory?.id || '', // <--- 修改
        notes: '', // 暂时不支持备注
      };
      
      // 添加交易记录
      await transactionStore.addTransaction(transactionData);
      uni.showToast({ title: '记账成功', icon: 'success' });
    }
    
    // 重置状态并关闭键盘
    keyboardVisible.value = false;
    amountValue.value = 0; // <--- 修改：重置为 number 0
    categoryStore.selectCategory(null);
    
    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 500);
  }
  catch (error) {
    console.error('Failed to process transaction:', error);
    uni.showToast({ title: isEditMode.value ? '更新失败' : '记账失败', icon: 'none' });
  }
}

// 关闭当前页面
function closePage() {
  uni.navigateBack();
}

// 处理聊天记录功能
function handleChatRecord() {
  // 实现聊天记录的逻辑
}

// 处理日期选择改变
function handleDateChange(date: Date) {
  // 创建一个新的日期对象，明确设置年月日
  const year = date.getFullYear();
  const month = date.getMonth();
  const day = date.getDate();
  const newDate = new Date(year, month, day, 0, 0, 0);
  
  selectedDate.value = newDate;
}

// 适配平台导航栏高度
const navBarHeight = computed(() => getPlatformStyle('nav-height') || '56px');
const navBarPaddingTop = getPlatformStyle('status-bar-height') || '0px';
const pageContainerStyle = computed(() => ({
  paddingTop: navBarHeight.value,
}));

// 页面标题
const pageTitle = computed(() => isEditMode.value ? '编辑记账' : '记一笔');
</script>

<template>
  <view class="transaction-record-container">
    <!-- 导航栏 -->
    <view class="nav-bar">
      <!-- 左侧返回按钮 -->
      <view class="nav-bar__left">
        <view class="nav-bar__back-btn" @click="closePage">
          <AppIcon icon="xmark" size="md" color="var(--text-primary, #333333)" />
        </view>
      </view>
      
      <!-- 中部模式切换 -->
      <view class="nav-bar__mode-tabs">
        <view
          v-for="(item, index) in modes"
          :key="index"
          class="nav-bar__mode-item"
          :class="{ 'nav-bar__mode-item--active': currentMode === item.value }"
          @click="switchMode(item.value)"
        >
          {{ item.label }}
        </view>
      </view>
      
      <!-- 右侧占位 -->
      <view class="nav-bar__right"/>
    </view>

    <!-- 内容区域 -->
    <view class="content">
      <!-- 选择分类 -->
      <CategorySelector 
        v-if="currentMode !== ModeType.TRANSFER"
        :categories="categoriesWithResolvedIcons"
        :type="currentMode" 
        :useStore="true"
        @select="handleCategorySelect"
      />
      <view v-else class="transfer-placeholder">
        <text>转账功能即将上线，敬请期待</text>
      </view>
      
      <!-- 金额输入 -->
      <AmountInput 
        :key="amountInputKey" 
        v-model="amountValueStr"
        :visible="keyboardVisible"
        :showDatePicker="true"
        :date="selectedDate"
        :notes="''"
        :isValid="true"
        @confirm="handleConfirmTransaction"
        @date-change="handleDateChange"
      />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.transaction-record-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: var(--bg-primary, #FFFFFF);
}

.nav-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 60px;
  padding: 0 16px;
  border-bottom: 1px solid var(--border-color, #E5E5E5);
  
  &__left, &__right {
    width: 40px;
    display: flex;
    align-items: center;
  }
  
  &__back-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background-color: var(--bg-secondary, #F5F5F5);
  }
  
  &__mode-tabs {
    display: flex;
    align-items: center;
    gap: 24px;
  }
  
  &__mode-item {
    position: relative;
    font-size: 16px;
    color: var(--text-secondary, #666666);
    padding: 8px 0;
    transition: color 0.3s ease;
    
    &--active {
      color: var(--color-primary, #FF6B35);
      font-weight: 500;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: var(--color-primary, #FF6B35);
        border-radius: 1px;
      }
    }
  }
}

.content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.transfer-placeholder {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
  font-size: 16px;
  color: var(--text-secondary, #666666);
}
</style>
