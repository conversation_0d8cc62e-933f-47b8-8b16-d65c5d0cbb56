<script setup lang="ts">
import { ref, onMounted } from 'vue';

// 导入组件 - 使用相对路径而非别名导入
import WelcomeSwiper from './components/WelcomeSwiper.vue';
import WelcomeSlideContent from './components/WelcomeSlideContent.vue';
import WelcomeCover from './components/WelcomeCover.vue';
import WelcomeFeature from './components/WelcomeFeature.vue';

// 内部状态
const currentIndex = ref(0);

// 处理滑动切换事件
function handleSlideChange(index: number) {
  console.log('幻灯片切换至:', index);
}

// 跳转到首页或主界面
function startExperience() {
  // 欢迎页的"立即体验"应该跳转到登录/注册流程
  console.log('欢迎页：跳转到登录页');
  // 使用redirectTo而不是navigateTo，避免用户返回到欢迎页
  uni.redirectTo({
    url: '/pages/auth/login', // 跳转到登录页
  });
}

// 跳转到登录页
function goToLogin() {
  console.log('欢迎页：跳转到登录页');
  // 使用redirectTo而不是navigateTo，避免用户返回到欢迎页
  uni.redirectTo({
    url: '/pages/auth/login',
  });
}

// 确保页面加载后正确显示
onMounted(() => {
  console.log('欢迎页已加载');
  // 强制重新渲染以确保正确显示
  setTimeout(() => {
    currentIndex.value = 0;
  }, 100);
});
</script>

<template>
  <view class="welcome-page">
    <!-- 使用优化后的WelcomeSwiper组件，整合了滑动和分页逻辑 -->
    <WelcomeSwiper
      v-model="currentIndex"
      :total-slides="4"
      @slide-change="handleSlideChange"
    >
      <!-- 封面幻灯片 -->
      <WelcomeSlideContent isCover>
        <WelcomeCover />
      </WelcomeSlideContent>

      <!-- 第二张幻灯片：语音识别 -->
      <WelcomeSlideContent>
        <WelcomeFeature
          title="智能语音记账"
          icon="volume"
          iconColor="var(--color-primary, #FF6B35)"
          badgeIcon="info-circle"
          badgeText="AI识别"
        >
          通过语音录入，智能识别账单分类与金额，让记账更轻松快捷。只需说出"买菜花了20元"，AI就能自动帮你完成记账。
        </WelcomeFeature>
      </WelcomeSlideContent>

      <!-- 第三张幻灯片：扫描票据 -->
      <WelcomeSlideContent>
        <WelcomeFeature
          title="智能票据识别"
          icon="file-text"
          iconColor="#FFBA08"
          badgeIcon="camera"
          badgeText="自动识别"
        >
          拍摄票据即可自动识别金额、日期和类别，省去手动输入的烦恼，记账更快捷准确。
        </WelcomeFeature>
      </WelcomeSlideContent>

      <!-- 第四张幻灯片：数据分析 -->
      <WelcomeSlideContent>
        <WelcomeFeature
          title="全方位数据分析"
          icon="integral"
          iconColor="#7B61FF"
          badgeIcon="arrow-upward"
          badgeText="智能分析"
          :showActions="true"
          @startExperience="startExperience"
          @login="goToLogin"
        >
          智能分析您的收支情况，提供直观的图表与报告，让您轻松了解自己的财务状况，理财更明智。
        </WelcomeFeature>
      </WelcomeSlideContent>
    </WelcomeSwiper>
  </view>
</template>

<style lang="scss" scoped>
.welcome-page {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background-color: var(--bg-primary, #fff);
  
  /* 多端适配 */
  /* #ifdef H5 */
  height: calc(100vh - var(--window-top));
  /* #endif */
  
  /* #ifdef MP */
  height: 100vh;
  /* #endif */
  
  /* #ifdef APP-PLUS */
  height: calc(100vh - var(--status-bar-height));
  /* #endif */
}
</style>