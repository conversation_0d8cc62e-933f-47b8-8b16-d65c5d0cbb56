<template>
  <view class="cover-container">
    <image src="/static/logo/LOGO.png" class="logo-image animate-fade" mode="aspectFit" />
    <text class="cover-title-1 animate-fade delay-200">
      智能
    </text>
    <text class="cover-title-2 animate-fade delay-400">
      记账
    </text>
    <text class="cover-desc animate-fade delay-600">
      账无忌，让财务管理轻松自在
    </text>
    <text class="cover-highlight animate-fade delay-600">
      AI驱动 安全可靠
    </text>
  </view>
</template>

<script setup lang="ts">
// 无需额外逻辑
</script>

<style lang="scss" scoped>
.cover-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  max-height: 70%;
  margin: auto 0;
}

.logo-image {
  width: 160px;
  height: 160px;
  margin-bottom: var(--spacing-lg, 24px);
  object-fit: contain;
  
  /* #ifdef H5 */
  width: 180px;
  height: 180px;
  /* #endif */
  
  /* #ifdef MP */
  width: 200rpx;
  height: 200rpx;
  /* #endif */
}

.cover-title-1,
.cover-title-2 {
  font-size: var(--font-size-xxl, 40px);
  font-weight: bold;
  color: var(--text-primary, #333);
  margin-bottom: var(--spacing-md, 16px);
  position: relative;
  display: inline-block;
  text-align: center;
}

.cover-title-1::after {
  content: '';
  display: block;
  width: 6px;
  height: 40px;
  background: var(--color-primary, #ff6b35);
  position: absolute;
  left: -15px;
  top: 5px;
}

.cover-title-2 {
  color: var(--color-primary, #ff6b35);
  margin-bottom: var(--spacing-lg, 24px);
}

.cover-desc {
  font-size: var(--font-size-md, 18px);
  color: var(--text-secondary, #666);
  margin-bottom: var(--spacing-xl, 32px);
  line-height: 1.6;
  width: 90%;
  margin-left: auto;
  margin-right: auto;
  text-align: center;
}

.cover-highlight {
  font-size: var(--font-size-lg, 24px);
  color: var(--text-primary, #333);
  margin-top: var(--spacing-md, 16px);
  margin-bottom: var(--spacing-lg, 24px);
  position: relative;
  display: inline-block;
  text-align: center;
}

.cover-highlight::before,
.cover-highlight::after {
  content: '';
  display: block;
  width: 10px;
  height: 10px;
  border-radius: var(--radius-circle, 50%);
  background: var(--color-primary, #ff6b35);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.cover-highlight::before {
  left: -20px;
}

.cover-highlight::after {
  right: -20px;
}

@media screen and (height <= 700px) {
  .logo-image {
    width: 150px;
    height: 150px;
    margin-bottom: var(--spacing-md, 16px);
  }
  
  .cover-title-1,
  .cover-title-2 {
    font-size: var(--font-size-xl, 36px);
    margin-bottom: var(--spacing-sm, 8px);
  }
  
  .cover-title-1::after {
    height: 36px;
    width: 5px;
  }
  
  .cover-highlight {
    font-size: var(--font-size-md, 20px);
  }
}
</style>