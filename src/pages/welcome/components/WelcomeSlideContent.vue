<template>
  <swiper-item class="welcome-slide">
    <view class="welcome-slide__container" :class="{ 'welcome-slide__container--feature': !isCover }">
      <slot></slot>
    </view>
  </swiper-item>
</template>

<script setup lang="ts">
interface Props {
  /**
   * 是否为封面幻灯片
   */
  isCover?: boolean;
}

// 默认值
const props = withDefaults(defineProps<Props>(), {
  isCover: false,
});
</script>

<style lang="scss" scoped>
.welcome-slide {
  width: 100%;
  height: 100%;
  position: relative;
  
  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
    padding: 0 var(--spacing-lg, 32px);
    box-sizing: border-box;
    position: relative;
    
    // 特性页容器
    &--feature {
      /* 确保特性页面的内容区域有一致的高度 */
      /* 移除最小高度限制，确保一致性 */
    }
  }
}
</style> 