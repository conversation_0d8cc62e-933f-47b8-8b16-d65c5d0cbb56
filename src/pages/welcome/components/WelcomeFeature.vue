<template>
  <view class="feature-container">
    <view class="feature-content">
      <view class="feature-icon-container animate-fade">
        <AppIcon 
          :icon="getIconName(icon)"
          size="60"
          :color="iconColor" 
        />
      </view>
      <text class="feature-title animate-fade delay-200">
        {{ title }}
      </text>
      <view class="feature-badge animate-fade delay-400">
        <AppIcon :icon="getIconName(badgeIcon)" size="16" class="badge-icon" />
        {{ badgeText }}
      </view>
      <text class="feature-desc animate-fade delay-600">
        <slot></slot>
      </text>
    </view>
    
    <!-- 底部按钮区域，仅在 showActions 为 true 时显示 -->
    <view v-if="showActions" class="feature-actions">
      <button
        class="start-button animate-fade delay-600"
        @click="handleStartExperience"
      >
        立即开始体验
      </button>
      <text class="login-text animate-fade delay-600">
        已有账号？<text class="login-link" @click="handleLogin">立即登录</text>
      </text>
    </view>
  </view>
</template>

<script setup lang="ts">
// 修改导入方式 - 测试是否为路径问题
// import AppButton from '@/components/common/AppButton.vue';
// AppButton 应已全局注册，无需导入

// 定义Props
interface Props {
  /**
   * 特性标题
   */
  title: string;
  
  /**
   * 图标名称 (uView Plus图标名)
   */
  icon: string;
  
  /**
   * 图标颜色
   */
  iconColor?: string;
  
  /**
   * 徽章图标 (uView Plus图标名)
   */
  badgeIcon?: string;
  
  /**
   * 徽章文本
   */
  badgeText?: string;
  
  /**
   * 是否显示按钮操作区
   */
  showActions?: boolean;
}

// 默认值
const props = withDefaults(defineProps<Props>(), {
  iconColor: 'var(--color-primary, #FF6B35)',
  badgeIcon: 'star',
  badgeText: '智能记账',
  showActions: false,
});

// 定义事件
const emit = defineEmits(['startExperience', 'login']);

// 处理"立即开始体验"点击事件
function handleStartExperience() {
  emit('startExperience');
}

// 处理"立即登录"点击事件
function handleLogin() {
  emit('login');
}

// 图标名称映射函数 - 将原始图标名映射到 uView Plus 兼容的图标名
function getIconName(iconName: string): string {
  // 图标名称映射表 - 更新为 uView Plus 的准确图标名
  const iconMap: Record<string, string> = {
    // 常用图标映射 - 使用 uView Plus 确认支持的图标名
    'mic': 'mic',
    'volume': 'mic',
    'file-text': 'file-text', 
    'pie-chart': 'integral', // 更适合数据分析的图标
    'preset': 'integral',    // 更改为积分(integral)图标，类似饼图效果
    'line': 'arrow-up',
    'arrow-upward': 'arrow-up',
    'info': 'info-circle',
    'camera': 'camera',
    'star': 'star',
  };
  
  // 对未知图标返回一个有效的默认图标
  return iconMap[iconName] || 'info-circle';
}
</script>

<style lang="scss" scoped>
.feature-container {
  width: 100%;
  height: 100%; /* 确保容器占满整个高度 */
  position: relative;
  display: flex;
  flex-direction: column;
}

.feature-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-lg, 24px);
  text-align: center;
  max-height: 70%; /* 限制内容区域最大高度，保持与其他页面一致 */
  margin: auto 0; /* 帮助垂直居中 */
}

// 图标容器
.feature-icon-container {
  width: 120px;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: var(--spacing-lg, 24px);
  
  /* #ifdef MP */
  width: 200rpx; /* 调整小程序下尺寸，更合适一些 */
  height: 200rpx;
  /* #endif */
}

.feature-title {
  font-size: var(--font-size-xl, 24px);
  font-weight: 600;
  color: var(--text-primary, #333);
  margin-bottom: var(--spacing-md, 16px);
}

.feature-badge {
  display: inline-flex;
  align-items: center;
  background-color: var(--color-primary-light, #FFE0D6);
  color: var(--color-primary, #FF6B35);
  padding: var(--spacing-xs, 8px) var(--spacing-md, 16px);
  border-radius: var(--radius-pill, 30px);
  margin-bottom: var(--spacing-md, 16px);
  font-size: var(--font-size-sm, 14px);
}

.badge-icon {
  margin-right: var(--spacing-xs, 8px);
}

.feature-desc {
  font-size: var(--font-size-md, 16px);
  color: var(--text-secondary, #666);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl, 40px);
  padding-bottom: var(--spacing-md, 16px);
}

// 按钮区域
.feature-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: var(--spacing-xl, 40px);
  position: absolute; /* 使用绝对定位确保按钮位置不变 */
  bottom: var(--spacing-xxl, 60px); /* 从底部固定位置 */
  left: 0;
  right: 0;
  width: 100%;
}

.start-button {
  min-width: var(--button-min-width, 200px);
  /* 调整垂直内边距，降低按钮高度 */
  padding: var(--spacing-sm, 12px) var(--spacing-lg, 24px);
  border-radius: var(--radius-pill, 30px);
  box-shadow: 0 4px 10px rgba(255, 107, 53, 0.3);
  font-weight: bold;
  font-size: var(--font-size-md, 16px);
  margin-bottom: 15px;
  background-color: var(--color-primary, #FF6B35);
  color: var(--text-inverse, #fff);
  border: none;
  text-align: center;
  display: flex;
  justify-content: center;
  align-items: center;
  /* 增加过渡效果，提升交互体验 */
  transition: all 0.3s ease;
}

/* 添加按钮按下效果 */
.start-button:active {
  transform: translateY(2px);
  box-shadow: 0 2px 5px rgba(255, 107, 53, 0.3);
}

.login-text {
  margin-top: var(--spacing-md, 16px);
  font-size: var(--font-size-sm, 14px);
  color: var(--text-secondary, #666);
}

.login-link {
  color: var(--color-primary, #FF6B35);
}
</style>