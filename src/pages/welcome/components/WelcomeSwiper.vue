<template>
  <view class="welcome-swiper">
    <!-- 使用uni-app原生swiper组件，确保多端兼容 -->
    <swiper
      class="welcome-swiper__container"
      :indicator-dots="false"
      :autoplay="false"
      :circular="false"
      :vertical="false"
      :current="currentIndex"
      :duration="300"
      @change="handleSwiperChange"
    >
      <slot></slot>
    </swiper>

    <!-- 分页指示器 -->
    <view class="welcome-swiper__pagination">
      <view
        v-for="index in totalSlides"
        :key="index - 1"
        class="welcome-swiper__bullet"
        :class="{ 'welcome-swiper__bullet--active': currentIndex === index - 1 }"
        @click="handleBulletClick(index - 1)"
      />
    </view>

    <!-- 动画类和样式应用在这个最外层容器上，以便子组件继承 -->
    <view class="welcome-swiper__animations">
      <slot name="animations"></slot>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue';

interface Props {
  /** 总幻灯片数量 */
  totalSlides: number;
  /** 当前活动的幻灯片索引 */
  modelValue?: number;
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: 0,
});

const emit = defineEmits(['update:modelValue', 'slideChange']);

// 内部状态跟踪当前幻灯片索引
const currentIndex = ref(props.modelValue);

// 监听外部传入的modelValue变化
watch(() => props.modelValue, (newValue) => {
  currentIndex.value = newValue;
});

// 处理swiper组件change事件
function handleSwiperChange(e: any) {
  const index = e.detail.current;
  currentIndex.value = index;
  emit('update:modelValue', index);
  emit('slideChange', index);
}

// 处理分页指示器点击事件
function handleBulletClick(index: number) {
  currentIndex.value = index;
  emit('update:modelValue', index);
  emit('slideChange', index);
}
</script>

<style lang="scss" scoped>
.welcome-swiper {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  background-color: var(--bg-primary, #fff);

  &__container {
    width: 100%;
    height: 100%;
    position: relative;
  }

  &__pagination {
    position: absolute;
    bottom: var(--spacing-xl, 40px); /* 使用CSS变量替代硬编码值 */
    left: 0;
    right: 0;
    display: flex;
    justify-content: center;
    gap: var(--spacing-xs, 8px);
    z-index: 5;
    
    /* 适配小屏幕设备 */
    @media screen and (height <= 700px) {
      bottom: var(--spacing-lg, 24px); /* 减小小屏幕设备上的底部间距 */
    }
    
    /* 多端适配 */
    /* #ifdef MP */
    bottom: calc(var(--spacing-xl, 40px) + var(--mp-safe-area-bottom, 0px)); /* 小程序适配，考虑安全区域 */
    /* #endif */
    
    /* #ifdef APP-PLUS */
    bottom: calc(var(--spacing-xl, 40px) + var(--safe-area-bottom, 0px)); /* 考虑安全区域，使用CSS变量 */
    /* #endif */
  }

  &__bullet {
    width: var(--bullet-size, 8px); /* 使用CSS变量替代硬编码值 */
    height: var(--bullet-size, 8px); /* 使用CSS变量替代硬编码值 */
    border-radius: var(--radius-circle, 50%);
    background-color: var(--bg-secondary, #ddd);
    opacity: 0.6;
    transition: all 0.3s ease;
    
    /* 增大点击区域 */
    position: relative;
    
    &::before {
      content: '';
      position: absolute;
      top: var(--spacing-neg-sm, -10px);
      bottom: var(--spacing-neg-sm, -10px);
      left: var(--spacing-neg-sm, -10px);
      right: var(--spacing-neg-sm, -10px);
    }

    &--active {
      width: var(--bullet-active-width, 20px); /* 使用CSS变量替代硬编码值 */
      border-radius: var(--radius-sm, 4px);
      background-color: var(--color-primary, #ff6b35);
      opacity: 1;
    }
    
    /* 多端适配 */
    /* #ifdef MP */
    width: var(--mp-bullet-size, 16rpx); /* 使用CSS变量替代硬编码值 */
    height: var(--mp-bullet-size, 16rpx); /* 使用CSS变量替代硬编码值 */
    
    &--active {
      width: var(--mp-bullet-active-width, 40rpx); /* 使用CSS变量替代硬编码值 */
    }
    /* #endif */
  }

  /* 动画效果 */
  &__animations {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    pointer-events: none;
  }
}

/* 动画效果 */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

:deep(.animate-fade) {
  animation: fade-in 0.8s ease forwards;
  opacity: 0; /* 确保初始状态是不可见的 */
  
  /* #ifdef MP-WEIXIN */
  /* 小程序降级处理 - 静态显示 */
  opacity: 1;
  transform: translateY(0);
  /* #endif */
}

:deep(.delay-200) {
  animation-delay: 0.2s;
  
  /* #ifdef MP-WEIXIN */
  /* 小程序降级处理 - 延迟模拟 */
  animation-delay: 0.1s;
  /* #endif */
}

:deep(.delay-400) {
  animation-delay: 0.4s;
  
  /* #ifdef MP-WEIXIN */
  /* 小程序降级处理 - 延迟模拟 */
  animation-delay: 0.2s;
  /* #endif */
}

:deep(.delay-600) {
  animation-delay: 0.6s;
  
  /* #ifdef MP-WEIXIN */
  /* 小程序降级处理 - 延迟模拟 */
  animation-delay: 0.3s;
  /* #endif */
}

/* 确保动画在APP中正常显示 */
/* #ifdef APP-PLUS */
:deep(.animate-fade) {
  transform: translate3d(0, 20px, 0);
}

:deep(.animate-fade.delay-200),
:deep(.animate-fade.delay-400),
:deep(.animate-fade.delay-600) {
  opacity: 0;
}
/* #endif */
</style> 