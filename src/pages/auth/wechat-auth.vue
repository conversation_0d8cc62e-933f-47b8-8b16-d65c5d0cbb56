<script setup lang="ts">
function mockAuthSuccess(isNewUser: boolean) {
  console.log('模拟微信授权成功');
  // 实际应用中，这里会获取 code 或用户信息，然后调用后端接口完成登录
  // 登录成功后，根据是否新用户进行跳转
  if (isNewUser) {
    // 新用户应该先设置手势密码，然后再设置个人资料
    uni.redirectTo({ url: '/pages/auth/gesture?mode=set' });
  }
  else {
    uni.redirectTo({ url: '/pages/guide/index' });
  }
}

function mockAuthFail() {
  console.log('模拟微信授权失败');
  uni.showToast({
    title: '微信授权失败',
    icon: 'none',
  });
  setTimeout(() => {
    uni.navigateBack(); // 返回登录页
  }, 1500);
}
</script>

<template>
  <view class="placeholder-page">
    <text>模拟微信授权...</text>
    <button @click="mockAuthSuccess(true)">
      模拟授权成功 (新用户)
    </button>
    <button @click="mockAuthSuccess(false)">
      模拟授权成功 (老用户)
    </button>
    <button @click="mockAuthFail">
      模拟授权失败
    </button>
  </view>
</template>

<style scoped>
  .placeholder-page {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    height: 100vh;
    font-size: 20px;
    color: #999;
    gap: 15px;
  }
</style>
