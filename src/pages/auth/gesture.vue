<script setup lang="ts">
// 1. Vue核心库
import { computed, onMounted, ref } from 'vue';

// 2. Pinia状态
import { useUserStore } from '@/stores/user.store';

// 3. 组件导入
import AppButton from '@/components/common/AppButton.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppNavBar from '@/components/common/AppNavBar.vue';
import GesturePassword from '@/components/business/GesturePassword.vue';

// 4. 配置和工具
import config from '@/config';
import { navigateHome, navigateToLogin, safeNavigateBack } from '@/utils/navigate';
import { getPlatformStyle } from '@/utils/platform';

// 状态初始化
const userStore = useUserStore();
// 通过组件类型引用组件实例，而非使用any
const gesturePasswordRef = ref<InstanceType<typeof GesturePassword> | null>(null);

// 导航条高度（从平台适配函数获取）
const navHeight = computed(() => getPlatformStyle('nav-height'));

// 状态栏高度
const statusBarHeight = computed(() => getPlatformStyle('status-bar-height'));

// 计算手势图案尺寸，根据屏幕宽度自适应，最大不超过320px
const patternSize = computed(() => {
  const screenWidth = uni.getSystemInfoSync().screenWidth;
  return Math.min(screenWidth * 0.8, 320); // 屏幕宽度的80%，但不超过320px
});

// 判断是设置模式还是验证模式
const mode = ref<'set' | 'verify'>('set');

// 读取用户信息
const userName = computed(() => userStore.userName || '未登录用户');
const userAvatar = computed(() => userStore.avatar || '');

// 导航栏标题
const navTitle = computed(() => (mode.value === 'set' ? '设置手势密码' : '手势密码验证'));

// 移除重复的消息状态变量
const storedPassword = ref<number[]>([]);
const tempPassword = ref<number[]>([]);
const verifyStep = ref(false);

onMounted(() => {
  // 根据路由参数判断模式
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  // @ts-ignore - 小程序API
  const options = currentPage.options || {};

  // 设置导航条标题
  uni.setNavigationBarTitle({
    title: navTitle.value,
  });

  // 判断是验证模式还是设置模式
  if (options.mode === 'verify') {
    mode.value = 'verify';
    // 组件内已有初始消息设置，不需要重复设置

    // 获取存储的手势密码
    try {
      const storedGesture = uni.getStorageSync(
        config.storage.prefix + config.storage.keys.GESTURE_PASSWORD,
      );
      if (storedGesture) {
        storedPassword.value = JSON.parse(storedGesture);
      }
      else {
        // 如果没有存储的密码，但却是验证模式，说明状态不一致
        console.warn('验证模式下未找到存储的手势密码，将切换到设置模式');
        mode.value = 'set';
      }
    }
    catch (e) {
      console.error('读取手势密码失败:', e);
      // 降级处理：切换到设置模式
      mode.value = 'set';
    }
  }
});

/**
 * 处理返回按钮点击
 */
function goBack() {
  // 如果是设置模式，弹出提示确认是否放弃设置
  if (mode.value === 'set') {
    safeNavigateBack({
      title: '确认放弃',
      content: '您确定要放弃设置手势密码吗？',
      showCancel: true,
      confirmText: '放弃设置',
      cancelText: '继续设置',
      success: (res) => {
        if (res.confirm) {
          // 用户确认放弃设置
          uni.navigateBack();
        }
      },
    });
  }
  else {
    // 验证模式下，显示需要验证手势密码的提示
    uni.showModal({
      title: '需要验证',
      content: '您需要验证手势密码才能返回',
      showCancel: true,
      confirmText: '重新登录',
      cancelText: '继续验证',
      success: (res) => {
        if (res.confirm) {
          // 用户选择重新登录
          navigateToLogin();
        }
      },
    });
  }
}

// 手势密码设置事件
function handleSet(password: number[]) {
  if (mode.value !== 'set')
    return;

  if (!verifyStep.value) {
    // 第一次设置
    if (password.length >= 4) {
      tempPassword.value = [...password];
      // 直接使用组件的消息显示功能
      if (gesturePasswordRef.value) {
        gesturePasswordRef.value.setMessage('请再次绘制手势密码确认');
      }
      verifyStep.value = true;

      // 必须重置组件，确保可以继续进行第二次绘制
      setTimeout(() => {
        if (gesturePasswordRef.value) {
          gesturePasswordRef.value.reset();
        }
      }, 500);
    }
    else {
      // 让组件自己处理错误消息，无需在页面中重复
      if (gesturePasswordRef.value) {
        gesturePasswordRef.value.setMessage('密码至少需要4个点，请重新绘制', true);
      }
      setTimeout(() => {
        if (gesturePasswordRef.value) {
          gesturePasswordRef.value.reset();
        }
      }, 1500);
    }
  }
  else {
    // 第二次确认
    if (JSON.stringify(password) === JSON.stringify(tempPassword.value)) {
      // 密码一致，保存
      storedPassword.value = [...password];
      // 使用配置中的存储键
      uni.setStorageSync(
        config.storage.prefix + config.storage.keys.GESTURE_PASSWORD,
        JSON.stringify(password),
      );

      // 使用组件显示成功消息
      if (gesturePasswordRef.value) {
        gesturePasswordRef.value.setMessage('手势密码设置成功');
      }
      handleSuccess();
    }
    else {
      // 密码不一致
      verifyStep.value = false;
      // 使用组件显示错误消息
      if (gesturePasswordRef.value) {
        gesturePasswordRef.value.setMessage('两次手势不一致，请重新设置', true);
      }
      setTimeout(() => {
        if (gesturePasswordRef.value) {
          gesturePasswordRef.value.reset();
        }
      }, 1500);
    }
  }
}

// 手势完成回调
function handleComplete(password: number[]) {
  if (password.length < 4) {
    // 直接使用组件显示错误消息
    if (gesturePasswordRef.value) {
      gesturePasswordRef.value.setMessage('手势密码至少需要连接4个点', true);
    }
    setTimeout(() => {
      if (gesturePasswordRef.value) {
        gesturePasswordRef.value.reset();
      }
    }, 1500);
  }
}

// 验证结果回调
function handleVerified(success: boolean) {
  if (success) {
    // 使用组件显示成功消息
    if (gesturePasswordRef.value) {
      gesturePasswordRef.value.setMessage('验证成功');
    }
    handleSuccess();
  }
  else {
    // 使用组件显示错误消息
    if (gesturePasswordRef.value) {
      gesturePasswordRef.value.setMessage('手势密码错误，请重试', true);
    }
    setTimeout(() => {
      if (gesturePasswordRef.value) {
        gesturePasswordRef.value.reset();
      }
    }, 1500);
  }
}

// 错误回调
function handleError(errorMsg: string) {
  // 直接使用组件显示错误消息
  if (gesturePasswordRef.value) {
    gesturePasswordRef.value.setMessage(errorMsg, true);
  }
  setTimeout(() => {
    if (gesturePasswordRef.value) {
      gesturePasswordRef.value.reset();
    }
  }, 1500);
}

// 失败回调
function handleFail(data: any) {
  // 直接使用组件显示错误消息
  if (gesturePasswordRef.value) {
    gesturePasswordRef.value.setMessage(data.message || '手势错误', true);
  }
  setTimeout(() => {
    if (gesturePasswordRef.value) {
      gesturePasswordRef.value.reset();
    }
  }, 1500);
}

// 跳过设置手势密码
function skipGesture(force = false) {
  if (mode.value === 'set') {
    if (force) {
      // 强制跳过，不显示确认弹窗
      // 首先判断是否是新用户
      const isNewUser = userStore.userName?.includes('新用户') || 
        (userStore.userInfo && !userStore.userInfo.isProfileCompleted);
      
      if (isNewUser) {
        // 新用户跳转到个人信息设置页面
        uni.redirectTo({
          url: '/pages/profile/basic-setup',
          success: () => console.log('跳转到个人信息设置页面成功'),
          fail: (err) => {
            console.error('跳转到个人信息设置页面失败:', err);
            // 失败后作为降级处理跳转到主页
            navigateHome();
          },
        });
      }
      else {
        // 老用户补充设置，跳转到首页
        navigateHome();
      }
      return;
    }

    uni.showModal({
      title: '安全提示',
      content: '不设置手势密码可能降低账户安全性，您确定要跳过吗？',
      success: (res) => {
        if (res.confirm) {
          // 判断是新用户首次设置还是老用户补充设置
          const isNewUser = userStore.userName?.includes('新用户') || 
            (userStore.userInfo && !userStore.userInfo.isProfileCompleted);

          if (isNewUser) {
            // 使用 redirectTo 替换当前页面，避免用户返回到手势设置
            uni.redirectTo({
              url: '/pages/profile/basic-setup',
              success: () => console.log('跳转到个人信息设置页面成功'),
              fail: (err) => {
                console.error('跳转到个人信息设置页面失败:', err);
                // 失败后作为降级处理跳转到主页
                navigateHome();
              },
            });
          }
          else {
            // 老用户补充设置完成，或从其他入口进入设置
            navigateHome();
          }
        }
      },
    });
  }
}

// 重置手势按钮
function resetGesture() {
  verifyStep.value = false;

  // 重置手势组件
  if (gesturePasswordRef.value) {
    gesturePasswordRef.value.reset();
    // 重置组件内的提示消息
    gesturePasswordRef.value.setMessage(mode.value === 'set' ? '请绘制解锁图案' : '请输入手势密码');
  }
}

// 成功处理
function handleSuccess() {
  if (mode.value === 'set') {
    // 更新用户状态
    userStore.setGestureStatus(true);
    
    // 存储手势密码时使用用户ID作为标识，保证每个用户有独立的手势密码
    if (userStore.userInfo && userStore.userInfo.userId) {
      // 使用配置中的存储键，并添加用户ID后缀
      const gestureStorageKey = `${config.storage.prefix}${config.storage.keys.GESTURE_PASSWORD}_${userStore.userInfo.userId}`;
      uni.setStorageSync(
        gestureStorageKey,
        JSON.stringify(storedPassword.value),
      );
      
      // 同时设置全局标识，方便启动时检查
      uni.setStorageSync(
        `${config.storage.prefix}${config.storage.keys.GESTURE_PASSWORD}`,
        JSON.stringify(storedPassword.value),
      );
    }

    // 判断是新用户首次设置还是老用户补充设置
    const isNewUser = userStore.userName?.includes('新用户') || 
      (userStore.userInfo && !userStore.userInfo.isProfileCompleted);

    if (isNewUser) {
      // 使用 redirectTo 替换当前页面，避免用户返回到手势设置
      uni.redirectTo({
        url: '/pages/profile/basic-setup',
        success: () => console.log('跳转到个人信息设置页面成功'),
        fail: (err) => {
          console.error('跳转到个人信息设置页面失败:', err);
          // 失败后作为降级处理跳转到主页
          navigateHome();
        },
      });
    }
    else {
      // 老用户补充设置完成，或从其他入口进入设置
      navigateHome();
    }
  }
  else {
    // verify mode - 验证通过后直接跳转首页
    navigateHome();
  }
}
</script>

<template>
  <view class="gesture-page">
    <!-- 顶部通用导航栏，完全替换自定义导航栏 -->
    <AppNavBar :title="navTitle" theme="primary" :showBack="true" />
    <!-- 内容区 -->
    <view class="gesture-page__content">
      <!-- 用户信息区 -->
      <view class="gesture-page__user-info">
        <view class="gesture-page__avatar">
          <image
            :src="userAvatar || '/static/icons/default-avatar.png'"
            mode="aspectFill"
            class="gesture-page__avatar-image"
          />
        </view>
        <text class="gesture-page__username">
          {{ userName }}
        </text>
      </view>
      <!-- 手势密码区域 -->
      <view class="gesture-page__pattern-container">
        <GesturePassword
          ref="gesturePasswordRef"
          :size="patternSize"
          :container-size="patternSize"
          :mode="mode"
          :stored-password="storedPassword"
          :min-points="4"
          normal-color="var(--color-secondary, #CCCCCC)"
          active-color="var(--color-primary, #FF6B35)"
          error-color="var(--color-error, #F44336)"
          :point-radius="30"
          :inner-point-radius="8"
          :line-width="2"
          :show-message="true"
          :initial-message="mode === 'set' ? '请绘制解锁图案' : '请输入手势密码'"
          @complete="handleComplete"
          @set="handleSet"
          @verified="handleVerified"
          @error="handleError"
          @fail="handleFail"
        />
      </view>
      <!-- 底部操作区 -->
      <view class="gesture-page__actions-container">
        <view class="gesture-page__actions">
          <view v-if="mode === 'set'" class="gesture-page__action-btn">
            <AppButton type="default" block class="gesture-page__skip-btn" @click="skipGesture">
              跳过
            </AppButton>
          </view>
          <view class="gesture-page__action-btn">
            <AppButton type="primary" block class="gesture-page__reset-btn" @click="resetGesture">
              重置
            </AppButton>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .gesture-page {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
    background-color: var(--bg-primary, #fff);
  }

  .gesture-page__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-start; // 修改为顶部对齐
    padding: var(--space-md, 16px); // 恢复标准内边距
  }

  .gesture-page__user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: var(--space-xl-plus, 40px); // 使用预定义变量
    margin-bottom: var(--space-xl-plus, 40px); // 使用预定义变量
  }

  .gesture-page__avatar {
    width: 80px;
    height: 80px;
    border-radius: var(--radius-circle, 50%);
    overflow: hidden;
    margin-bottom: var(--space-md, 16px);
    border: 2px solid var(--color-primary, #FF6B35);
  }

  .gesture-page__avatar-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .gesture-page__username {
    font-size: var(--font-size-subtitle, 18px);
    font-weight: 500;
    color: var(--text-primary, #333);
    margin-bottom: var(--space-sm, 8px);
  }

  .gesture-page__pattern-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 0 auto; // 改为不使用flex:1，用固定高度
    height: auto; // 自适应高度
  }

  .gesture-page__actions-container {
    width: 100%;
    margin-top: auto; // 自动占据底部空间
    padding-bottom: var(--space-lg, 24px);
  }

  /* iOS安全区域适配 */

  /* #ifdef APP-PLUS-IOS */
  .gesture-page__actions-container {
    padding-bottom: var(--space-lg-safe, calc(var(--space-lg, 24px) + env(safe-area-inset-bottom, 0)));
  }

  /* #endif */

  /* 微信小程序胶囊按钮适配 */

  /* #ifdef MP-WEIXIN */
  .gesture-page__nav-bar {
    padding-right: 90px;
  }

  /* #endif */

  .gesture-page__actions {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm, 8px); // 按钮之间8px间距，符合UI规范
  }
  
  /* 添加按钮样式 */
  .gesture-page__action-btn {
    width: 100%;
  }

  .gesture-page__skip-btn, 
  .gesture-page__reset-btn {
    height: 44px;
    font-size: var(--font-size-medium, 16px);
    font-weight: 500;
  }
</style>
