<script setup lang="ts">
import AppButton from '@/components/common/AppButton.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import AppInput from '@/components/common/AppInput.vue';
import AppNavBar from '@/components/common/AppNavBar.vue';
import { useAuth } from '@/hooks/useAuth';
import { useErrorHandler } from '@/hooks/useErrorHandler';
import { useKeyboardAdapter } from '@/hooks/useKeyboardAdapter';
import { useValidation } from '@/hooks/useValidation';
import { useUserStore } from '@/stores/user.store';
import { computed, onMounted, onUnmounted, reactive, ref } from 'vue';

/**
 * Hooks 初始化
 * 使用 Pinia store 管理用户状态和组合式API处理业务逻辑
 */
const auth = useAuth();
const userStore = useUserStore();
const { validatePhone: validatePhoneFormat, validateSmsCode } = useValidation();
const { errorState, showError, handleError, clearError } = useErrorHandler();
const { keyboardState, handleKeyboardHeightChange } = useKeyboardAdapter({
  extraPadding: 20,
  keyboardVisibleClass: 'login-page--keyboard-open',
});

/**
 * 状态管理
 * 使用 reactive 统一管理表单状态，便于维护
 */
const formState = reactive({
  phone: '', // 手机号
  code: '', // 验证码
  isLoading: false, // 登录按钮加载状态
});

/**
 * 验证码获取状态管理
 */
const codeState = reactive({
  isGettingCode: false, // 是否正在获取验证码
  countdown: 0, // 验证码倒计时
  countdownTimer: null as number | null, // 倒计时定时器
});

/**
 * 计算属性：表单是否有效
 * 当手机号格式正确且验证码长度为6位时表单有效
 */
const isFormValid = computed(() => validatePhoneFormat(formState.phone).valid && formState.code.length === 6);

/**
 * 容器引用
 * 用于键盘适配
 */
const containerRef = ref<HTMLElement | null>(null);

/**
 * 验证手机号输入
 * 在输入框失焦时触发验证
 * @returns 是否验证通过
 */
function validatePhone() {
  const result = validatePhoneFormat(formState.phone);
  if (formState.phone && !result.valid) {
    showError(result.message);
    return false;
  }

  clearError();
  return true;
}

/**
 * 获取短信验证码
 * 验证手机号并调用API发送验证码
 */
async function getVerificationCode() {
  if (codeState.isGettingCode) {
    return;
  }

  if (!validatePhone()) {
    return;
  }

  codeState.isGettingCode = true;
  codeState.countdown = 60;

  try {
    // 获取验证码API调用
    const params = { phone: formState.phone, type: 'login' };
    console.log('[Login] 获取验证码:', params);

    // 如果是测试账号，只需要模拟等待，不实际发送请求
    if (formState.phone === '***********' || formState.phone === '***********') {
      console.log('[Login] 检测到测试账号，不实际发送验证码');
      // 开始倒计时
      startCountdown();

      // 模拟API调用成功
      uni.showToast({
        title: '验证码已发送',
        icon: 'success',
        duration: 1500,
      });

      return;
    }

    // 真实API调用
    await auth.sendSmsCode(formState.phone, 'login');

    // 开始倒计时
    startCountdown();

    uni.showToast({
      title: '验证码已发送',
      icon: 'success',
      duration: 1500,
    });
  }
  catch (error) {
    codeState.isGettingCode = false;
    handleError(error);
  }
}

/**
 * 开始验证码倒计时
 */
function startCountdown() {
  // 清除可能存在的倒计时
  if (codeState.countdownTimer) {
    clearInterval(codeState.countdownTimer);
  }

  // 设置新的倒计时
  codeState.countdownTimer = window.setInterval(() => {
    if (codeState.countdown > 0) {
      codeState.countdown--;
    }
    else {
      codeState.isGettingCode = false;
      clearInterval(codeState.countdownTimer!);
      codeState.countdownTimer = null;
    }
  }, 1000);
}

/**
 * 手机号+验证码登录
 * 验证表单并调用登录API
 */
async function handleLogin() {
  // 表单验证
  const phoneValidation = validatePhoneFormat(formState.phone);
  if (!phoneValidation.valid) {
    showError(phoneValidation.message);
    return;
  }

  // 测试账号特殊处理
  const isTestAccount = formState.phone === '***********' || formState.phone === '***********';

  // 验证码验证
  if (!isTestAccount) {
    const codeValidation = validateSmsCode(formState.code);
    if (!codeValidation.valid) {
      showError(codeValidation.message);
      return;
    }
  }
  else if (formState.code !== '1234') {
    // 测试账号使用固定验证码1234
    showError('验证码错误，请重新输入');
    return;
  }

  // 设置加载状态
  formState.isLoading = true;

  // 构建登录凭证
  const credentials = {
    phone: formState.phone,
    smsCode: formState.code,
  };

  try {
    console.log('[Login Page] 开始登录:', credentials);

    // 使用 useAuth 的 handleLogin 方法进行登录
    const success = await auth.handleLogin(credentials);

    if (!success) {
      // 修复 auth.error?.message 访问，先确认 auth.error 存在
      throw new Error(auth.error ? auth.error.message : '登录失败，请重试');
    }

    // 登录成功，获取用户数据
    console.log('[Login Page] 登录成功，获取用户数据');

    // 根据needSetupSecurity确定后续流程
    if (!userStore.hasSetGesture) {
      console.log('[Login Page] 检测到新用户或未设置手势密码，引导设置手势密码');
      // 新用户，需要设置手势密码 - 使用redirectTo避免用户返回登录页
      uni.redirectTo({
        url: '/pages/auth/gesture?mode=set',
        success: () => console.log('[Login Page] 成功跳转到手势设置页面'),
        fail: err => console.error('[Login Page] 跳转到手势设置页面失败:', err),
      });
    }
    else {
      console.log('[Login Page] 检测到老用户且已设置手势密码，需要验证手势密码');
      // 老用户需要先验证手势密码
      uni.redirectTo({
        url: '/pages/auth/gesture?mode=verify',
        success: () => console.log('[Login Page] 成功跳转到手势验证页面'),
        fail: err => console.error('[Login Page] 跳转到手势验证页面失败:', err),
      });
    }
  }
  catch (error) {
    console.error('[Login Page] 登录失败:', error);
    handleError(error);
  }
  finally {
    // 无论成功失败，都重置加载状态
    formState.isLoading = false;
  }
}

/**
 * 微信登录
 * 处理多端微信登录逻辑
 */
function handleWechatLogin() {
  console.log('发起微信登录');

  // 平台特定的微信登录逻辑
  // #ifdef MP-WEIXIN
  uni.login({
    provider: 'weixin',
    success: (loginRes) => {
      console.log('微信登录成功:', loginRes);
      // 真实环境中应该将code发送给后端
      // 模拟中跳转到下一步
      uni.navigateTo({ url: '/pages/auth/wechat-auth' });
    },
    fail: (err) => {
      console.error('微信登录失败:', err);
      handleError('微信登录失败，请重试');
    },
  });
  // #endif

  // 非微信平台，跳转到微信授权页
  // #ifndef MP-WEIXIN
  uni.navigateTo({ url: '/pages/auth/wechat-auth' });
  // #endif
}

/**
 * 手动关闭错误提示
 */
function dismissError() {
  clearError();
}

/**
 * 打开用户协议
 */
function openTerms() {
  uni.navigateTo({ url: '/pages/webview/index?url=https://example.com/terms&title=用户协议' });
}

/**
 * 打开隐私政策
 */
function openPrivacy() {
  uni.navigateTo({ url: '/pages/webview/index?url=https://example.com/privacy&title=隐私政策' });
}

/**
 * 生命周期钩子 - 组件挂载
 * 初始化平台特定功能
 */
onMounted(() => {
  // 获取容器元素
  if (containerRef.value) {
    // 设置键盘适配器的容器元素
    // 注意：由于此时是在template中使用ref，我们不能直接传递
  }

  // #ifdef APP-PLUS
  // 处理iOS和Android键盘弹出问题
  if (currentPlatform === 'ios' || currentPlatform === 'android') {
    console.log(`[Login] 初始化${currentPlatform}平台特定键盘行为`);

    // 监听键盘高度变化
    uni.onKeyboardHeightChange((res) => {
      handleKeyboardHeightChange(res.height);
    });
  }
  // #endif

  // #ifdef MP-WEIXIN
  console.log('[Login] 微信小程序平台特定初始化');
  // 小程序特殊API调用或初始化
  // #endif

  // #ifdef H5
  console.log('[Login] H5平台初始化');
  // H5特有的触摸反馈设置
  document.querySelectorAll('.login-page__btn').forEach((btn) => {
    btn.addEventListener('touchstart', () => {
      // 添加触摸反馈效果
      (btn as HTMLElement).style.transform = 'scale(0.98)';
    });

    btn.addEventListener('touchend', () => {
      // 恢复原始状态
      (btn as HTMLElement).style.transform = 'scale(1)';
    });
  });
  // #endif
});

/**
 * 生命周期钩子 - 组件卸载
 * 清理资源和事件监听
 */
onUnmounted(() => {
  // 清理定时器
  if (codeState.countdownTimer) {
    clearInterval(codeState.countdownTimer);
  }

  // 移除键盘高度变化监听
  // #ifdef APP-PLUS
  uni.offKeyboardHeightChange();
  // #endif

  // #ifdef H5
  // 清理 H5 特有的事件监听器
  document.querySelectorAll('.login-page__btn').forEach((btn) => {
    btn.removeEventListener('touchstart', () => {});
    btn.removeEventListener('touchend', () => {});
  });
  // #endif
});
</script>

<template>
  <!-- 导航栏始终顶格显示，不受圆角影响 -->
  <AppNavBar title="登录账号" theme="primary" />
  <view
    ref="containerRef"
    class="login-page"
    :class="{ 'login-page--keyboard-open': keyboardState.visible }"
  >
    <!-- 内容区域 - 仅内容区加圆角，顶部为0，左右下为16px -->
    <view
      class="login-page__content"
      :style="{ paddingBottom: `${keyboardState.contentPadding}px` }"
    >
      <!-- Logo 和欢迎语 -->
      <view class="login-page__header">
        <view class="login-page__logo">
          <image src="@/assets/images/LOGO.png" mode="aspectFit" />
        </view>
        <text class="login-page__welcome-subtitle">
          登录账无忌，轻松管理您的财务
        </text>
      </view>

      <!-- 登录表单 -->
      <view class="login-page__form">
        <view class="login-page__input-group">
          <text class="login-page__input-label">
            手机号码
          </text>
          <AppInput
            v-model="formState.phone"
            type="tel"
            placeholder="请输入手机号码"
            prefix-text="+86"
            :maxlength="11"
            @blur="validatePhone"
          />
        </view>

        <view class="login-page__input-header">
          <text class="login-page__input-label">
            验证码
          </text>
          <text
            class="login-page__input-action"
            :class="{ disabled: codeState.isGettingCode }"
            @click="getVerificationCode"
          >
            {{ codeState.isGettingCode ? `${codeState.countdown}秒后重试` : '获取验证码' }}
          </text>
        </view>
        <AppInput
          v-model="formState.code"
          type="number"
          placeholder="请输入短信验证码"
          :maxlength="6"
        />
      </view>

      <!-- 错误信息显示区域 - 添加可关闭按钮 -->
      <view v-if="errorState.visible" class="login-page__error-message">
        <AppIcon icon="circle-exclamation" class="login-page__error-icon" />
        <text class="login-page__error-text">
          {{ errorState.message }}
        </text>
        <view class="login-page__error-close" @click="dismissError">
          <AppIcon icon="times" size="sm" />
        </view>
      </view>

      <view class="login-page__form-submit">
        <AppButton
          class="login-page__btn"
          type="primary"
          size="large"
          :loading="formState.isLoading"
          @click="handleLogin"
        >
          手机号登录
        </AppButton>
      </view>

      <!-- 分隔线 -->
      <view class="login-page__divider">
        <view class="login-page__divider-line" />
        <text class="login-page__divider-text">
          或
        </text>
        <view class="login-page__divider-line" />
      </view>

      <!-- 修改微信登录按钮，保持与手机号登录按钮一致的尺寸 -->
      <view class="login-page__action-buttons">
        <AppButton
          size="large"
          class="login-page__btn login-page__btn--wechat"
          @click="handleWechatLogin"
        >
          <view class="login-page__button-content">
            <AppIcon icon="weixin" class="login-page__btn-icon" />
            <text>微信登录</text>
          </view>
        </AppButton>
      </view>

      <!-- 隐私协议 -->
      <view class="login-page__privacy-terms">
        登录即表示您同意
        <text class="login-page__privacy-link" @click="openTerms">
          《用户协议》
        </text>
        和
        <text class="login-page__privacy-link" @click="openPrivacy">
          《隐私政策》
        </text>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  // 优化：导航栏顶格显示，内容区圆角只作用于下方和左右
  .login-page {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: var(--bg-secondary, #f5f5f5);
    /* 不加圆角，避免影响导航栏 */
  }

  .login-page__content {
    flex: 1;
    padding: var(--space-lg, 24px) var(--space-md, 16px);
    background-color: var(--bg-primary, #fff);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: padding-bottom 0.25s ease;
    /* 只给内容区加圆角，顶部为0，左右下为16px */
    border-radius: 0 0 16px 16px;
    box-shadow: 0 2px 8px rgb(0 0 0 / 6%);
  }

  .login-page__header {
    text-align: center;
    width: 100%;
    padding-top: 0;
    margin-bottom: var(--space-xl, 32px);

    .login-page__logo {
      width: 100px;
      height: 100px;
      margin: 0 auto var(--space-md, 16px);

      image {
        width: 100%;
        height: 100%;
      }
    }

    .login-page__welcome-subtitle {
      font-size: 15px;
      color: var(--text-secondary, #666);
    }
  }

  .login-page__form {
    padding: 0 var(--space-sm, 8px);
    padding-bottom: var(--space-lg, 24px);
    width: 90%;
    max-width: 360px;

    .login-page__input-group {
      margin-bottom: var(--space-md, 16px);
    }

    .login-page__input-label {
      display: block;
      font-size: 14px;
      color: var(--text-secondary, #666);
      margin-bottom: var(--space-sm, 8px);
    }

    .login-page__input-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--space-sm, 8px);
    }

    .login-page__input-action {
      font-size: 14px;
      color: var(--color-primary, #ff6b35);
      font-weight: 500;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }

      &--disabled {
        color: var(--text-hint, #999);
        cursor: not-allowed;
      }
    }
  }

  .login-page__form-submit {
    margin-top: var(--space-xl, 32px);
    margin-bottom: var(--space-md, 16px);
    width: 90%;
    max-width: 360px;
  }

  .login-page__divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--space-md, 16px) auto;
    width: 90%;
    max-width: 360px;

    .login-page__divider-line {
      flex: 1;
      height: 1px;
      background-color: var(--divider-color, #eee);
    }

    .login-page__divider-text {
      margin: 0 var(--space-md, 16px);
      font-size: 14px;
      color: var(--text-hint, #999);
      white-space: nowrap;
      text-align: center;
    }
  }

  .login-page__action-buttons {
    display: flex;
    flex-direction: column;
    gap: var(--space-sm, 8px);
    width: 90%;
    max-width: 360px;
  }

  /* 统一按钮样式 */
  .login-page__btn {
    width: 100%;
    height: var(--button-height-large, 48px);
    border-radius: var(--radius-button, 8px);

    /* 确保内容居中显示 */
    :deep(.app-button__content) {
      display: flex !important;
      align-items: center;
      justify-content: center;
    }
  }

  /* 微信登录按钮样式 */
  .login-page__btn--wechat {
    background-color: var(--color-wechat, #07c160) !important;
    color: var(--text-inverse, #fff) !important;
    border-color: var(--color-wechat, #07c160) !important;

    &:active {
      background-color: var(--color-wechat-active, #06ad56) !important;
    }

    :deep(svg) {
      color: var(--text-inverse, #fff);
      fill: var(--text-inverse, #fff);
    }
  }

  .login-page__button-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-sm, 8px);

    .login-page__btn-icon {
      font-size: 20px;
    }
  }

  .login-page__privacy-terms {
    text-align: center;
    font-size: 12px;
    color: var(--text-hint, #999);
    line-height: 1.5;
    padding-bottom: var(--space-md, 16px);
    margin-top: var(--space-xl, 32px);
    width: 100%;

    .login-page__privacy-link {
      color: var(--color-primary, #ff6b35);
      text-decoration: none;

      &:active {
        opacity: 0.8;
      }
    }
  }

  /* 错误信息增强样式 */
  .login-page__error-message {
    width: 100%;
    color: var(--color-error, #f44336);
    font-size: 14px;
    margin-top: 8px;
    margin-bottom: 16px;
    background-color: var(--color-error-bg, rgb(244 67 54 / 10%));
    border-radius: 4px;
    padding: 8px 12px;
    text-align: left;
    font-weight: 500;
    display: flex;
    align-items: center;
    position: relative;

    .login-page__error-icon {
      margin-right: 8px;
      flex-shrink: 0;
    }

    .login-page__error-text {
      flex: 1;
    }

    .login-page__error-close {
      padding: 4px;
      margin-left: 8px;
      cursor: pointer;
      color: var(--color-text-hint, #999);

      &:active {
        opacity: 0.7;
      }
    }
  }

  /* 键盘适配相关样式 */
  .login-page__content {
    flex: 1;
    padding: var(--space-lg, 24px) var(--space-md, 16px);
    background-color: var(--bg-primary, #fff);
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    transition: padding-bottom 0.25s ease; /* 平滑过渡键盘适配 */
  }

  /* 键盘弹出时的样式 */
  .login-page--keyboard-open {
    .login-page__logo {
      height: 60px; /* 键盘打开时缩小logo */
      width: 60px;
      transition: all 0.3s ease;
    }

    .login-page__welcome-subtitle {
      display: none; /* 键盘打开时隐藏欢迎语 */
    }

    .login-page__header {
      margin-bottom: var(--space-md, 16px);
    }
  }

  /* 防止内容溢出 */
  .login-page__form,
  .login-page__action-buttons,
  .login-page__form-submit {
    width: 90%;
    max-width: 360px;
    flex-shrink: 0; /* 防止在空间不足时压缩 */
  }

  /* #ifdef APP-PLUS-IOS */
  .login-page__form {
    padding-bottom: calc(var(--space-lg, 24px) + env(safe-area-inset-bottom, 0px));
  }

  /* #endif */

  /* #ifdef MP-WEIXIN */
  .login-page__nav-bar {
    padding-right: 90px; /* 为小程序胶囊按钮留出空间 */
  }

  /* #endif */

  /* #ifdef H5 */
  .login-page__btn {
    transition: transform 0.2s;

    &:active {
      transform: scale(0.98);
    }
  }

  /* #endif */

  .login-page__icon {
    font-size: 18px;
    color: var(--text-secondary, #666);
    margin-right: 10px;
    width: 20px;
    text-align: center;
  }
</style>
