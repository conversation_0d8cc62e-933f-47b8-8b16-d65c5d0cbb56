<script setup lang="ts">
import { ref, computed } from 'vue';

// 选项卡定义 - 使用 uView Plus 图标分类
const tabs = [
  {
    id: 'common',
    label: '常用图标',
  },
  {
    id: 'direction',
    label: '方向图标',
  },
  {
    id: 'file',
    label: '文件图标',
  }
];

// 定义常用的 uView Plus 图标分组
const iconGroups = {
  common: [
    'home', 'plus', 'minus', 'check', 'close', 'error', 'info', 'help',
    'heart', 'star', 'share', 'settings', 'user', 'phone', 'email',
    'search', 'calendar', 'bell', 'camera', 'edit', 'trash'
  ],
  direction: [
    'arrow-left', 'arrow-right', 'arrow-up', 'arrow-down', 
    'chevron-left', 'chevron-right', 'chevron-up', 'chevron-down',
    'arrow-leftward', 'arrow-rightward', 'arrow-upward', 'arrow-downward'
  ],
  file: [
    'file', 'file-text', 'file-image', 'file-pdf', 'file-word', 'file-excel',
    'folder', 'folder-open', 'folder-add', 'download', 'upload'
  ]
};

// 当前选中的标签页
const currentTab = ref('common');
// 当前选中的图标
const selectedIcon = ref('home');
// 搜索关键词
const searchKeyword = ref('');

// 根据当前标签和搜索关键词过滤图标
const filteredIcons = computed(() => {
  const icons = iconGroups[currentTab.value as keyof typeof iconGroups] || [];
  if (!searchKeyword.value) {
    return icons;
  }
  return icons.filter(icon => icon.includes(searchKeyword.value.toLowerCase()));
});

// 生成示例代码
const codeExample = computed(() => {
  return `<AppIcon icon="${selectedIcon.value}" />`;
});

// 处理图标点击
function handleIconClick(icon: string) {
  selectedIcon.value = icon;
}

// 处理标签页切换
function handleTabChange(tabId: string) {
  currentTab.value = tabId;
  if (iconGroups[tabId as keyof typeof iconGroups]?.length > 0) {
    selectedIcon.value = iconGroups[tabId as keyof typeof iconGroups][0];
  }
}
</script>

<template>
  <view class="icon-preview-page">
    <view class="icon-preview-page__header">
      <view class="icon-preview-page__title">图标预览</view>
      <view class="icon-preview-page__subtitle">uView Plus 图标库</view>
      </view>

    <!-- 搜索栏 -->
    <view class="icon-preview-page__search">
      <view class="icon-preview-page__search-input">
        <AppIcon icon="search" class="search-icon" />
        <input 
          v-model="searchKeyword" 
          type="text" 
          placeholder="搜索图标..." 
          class="search-input"
        />
      </view>
    </view>

    <!-- 标签页 -->
    <view class="icon-preview-page__tabs">
      <view
        v-for="tab in tabs"
        :key="tab.id"
        class="icon-preview-page__tab"
        :class="{ 'icon-preview-page__tab--active': currentTab === tab.id }"
        @click="handleTabChange(tab.id)"
      >
        {{ tab.label }}
      </view>
    </view>

    <!-- 图标网格 -->
    <scroll-view scroll-y class="icon-preview-page__icon-grid">
      <view
        v-for="icon in filteredIcons"
        :key="icon"
        class="icon-preview-page__icon-item"
        :class="{ 'icon-preview-page__icon-item--active': selectedIcon === icon }"
        @click="handleIconClick(icon)"
      >
        <AppIcon :icon="icon" />
        <text class="icon-name">{{ icon }}</text>
      </view>
    </scroll-view>

    <!-- 选中图标预览 -->
    <view class="icon-preview-page__selected-preview">
      <view class="icon-preview-page__selected-info">
        <view class="selected-icon-display">
          <AppIcon :icon="selectedIcon" size="48" />
      </view>
        <view class="selected-icon-name">{{ selectedIcon }}</view>
      </view>
      <view class="icon-preview-page__code-example">
        <text class="code-title">使用示例：</text>
        <view class="code-block">{{ codeExample }}</view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.icon-preview-page {
  padding: 20px;
  height: 100%;
  display: flex;
  flex-direction: column;

  &__header {
    margin-bottom: 20px;
  }

  &__title {
    font-size: 24px;
    font-weight: bold;
    color: var(--text-primary, #333);
    margin-bottom: 4px;
  }

  &__subtitle {
    font-size: 14px;
    color: var(--text-secondary, #666);
  }

  &__search {
    margin-bottom: 20px;

    &-input {
      position: relative;
      display: flex;
      align-items: center;
      background-color: var(--bg-secondary, #f5f5f5);
      border-radius: var(--radius-input, 8px);
      padding: 8px 12px;

      .search-icon {
        margin-right: 8px;
        color: var(--text-hint, #999);
      }

      .search-input {
        flex: 1;
        border: none;
        background: transparent;
        color: var(--text-primary, #333);
        font-size: 15px;
      }
    }
  }

  &__tabs {
    display: flex;
    margin-bottom: 15px;
    border-bottom: 1px solid var(--border-color, #e5e5e5);
  }

  &__tab {
    padding: 10px 15px;
    font-size: 14px;
    color: var(--text-secondary, #666);
    cursor: pointer;
    position: relative;

    &--active {
      color: var(--color-primary, #ff6b35);
      font-weight: 500;

      &::after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        right: 0;
        height: 2px;
        background-color: var(--color-primary, #ff6b35);
    }
    }
  }

  &__icon-grid {
      flex: 1;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    overflow-y: auto;
    margin-bottom: 20px;
  }

  &__icon-item {
    width: calc(25% - 8px);
    aspect-ratio: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
      background-color: var(--bg-secondary, #f5f5f5);
    border-radius: var(--radius-sm, 4px);
    padding: 10px 5px;
    gap: 5px;

    &--active {
      background-color: var(--color-primary, #ff6b35);
      color: var(--text-inverse, #fff);

      :deep(.u-icon) {
        color: var(--text-inverse, #fff) !important;
      }

      .icon-name {
        color: var(--text-inverse, #fff);
      }
    }

    .icon-name {
      font-size: 12px;
      color: var(--text-secondary, #666);
      text-align: center;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 100%;
    }
  }

  &__selected-preview {
    border-top: 1px solid var(--border-color, #e5e5e5);
    padding-top: 15px;
  }

  &__selected-info {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .selected-icon-display {
      width: 60px;
      height: 60px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: var(--bg-secondary, #f5f5f5);
      border-radius: var(--radius-md, 8px);
      margin-right: 15px;
    }

    .selected-icon-name {
      font-size: 18px;
      font-weight: 500;
      color: var(--text-primary, #333);
    }
    }

  &__code-example {
    .code-title {
      font-size: 14px;
      color: var(--text-secondary, #666);
      margin-bottom: 5px;
      display: block;
    }

    .code-block {
      background-color: var(--bg-secondary, #f5f5f5);
      padding: 10px;
      border-radius: var(--radius-sm, 4px);
      font-family: monospace;
      font-size: 14px;
      color: var(--text-primary, #333);
      overflow-x: auto;
    }
    }
  }
</style>
