<template>
  <div class="chart-test-page">
    <h2 class="page-title">图表测试页面</h2>
    
    <div class="chart-container">
      <h3>柱状图测试 (静态数据)</h3>
      <QiunDataCharts
        :canvas-id="'test-column-chart'"
        type="column"
        :chartData="columnChartData"
        :opts="columnChartOpts"
        height="300px"
        @complete="onChartComplete"
        @error="onChartError"
      />
    </div>
    
    <div class="chart-container">
      <h3>环形图测试 (静态数据)</h3>
      <QiunDataCharts
        :canvas-id="'test-ring-chart'"
        type="ring"
        :chartData="ringChartData"
        :opts="ringChartOpts"
        height="300px"
        @complete="onChartComplete"
        @error="onChartError"
      />
    </div>
    
    <div class="chart-container">
      <h3>预设测试数据</h3>
      <QiunDataCharts
        :canvas-id="'test-preset-chart'"
        type="column"
        :chartData="{}"
        :opts="{}"
        height="300px"
        :useStaticData="true"
        @complete="onChartComplete"
        @error="onChartError"
      />
    </div>
    
    <div class="log-container">
      <h3>日志信息</h3>
      <div class="log-box">
        <div v-for="(log, index) in logs" :key="index" :class="`log-item ${log.type}`">
          {{log.message}}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue';
import QiunDataCharts from '@/components/u-charts/qiun-data-charts.vue';

// 日志记录
const logs = ref<Array<{type: string, message: string}>>([]);

// 添加日志
function addLog(message: string, type: 'info' | 'error' | 'success' = 'info') {
  logs.value.unshift({ type, message: `[${new Date().toLocaleTimeString()}] ${message}` });
  
  // 最多保留20条日志
  if (logs.value.length > 20) {
    logs.value = logs.value.slice(0, 20);
  }
}

// 柱状图数据
const columnChartData = reactive({
  categories: ['1月', '2月', '3月', '4月', '5月'],
  series: [
    { name: '收入', data: [1500, 2000, 1800, 2500, 3000] },
    { name: '支出', data: [1200, 1800, 1600, 2200, 2500] }
  ]
});

// 柱状图配置
const columnChartOpts = {
  type: 'column',
  padding: [15, 15, 15, 15],
  background: '#FFFFFF',
  enableScroll: false,
  xAxis: {
    disableGrid: true
  },
  yAxis: {
    data: [{ min: 0 }],
    gridType: 'dash'
  },
  extra: {
    column: {
      width: 20
    }
  }
};

// 环形图数据
const ringChartData = reactive({
  series: [{
    data: [
      { name: '餐饮美食', value: 1500 },
      { name: '交通出行', value: 800 },
      { name: '购物消费', value: 600 },
      { name: '休闲娱乐', value: 400 },
      { name: '其他', value: 200 }
    ]
  }]
});

// 环形图配置
const ringChartOpts = {
  type: 'ring',
  padding: [15, 15, 15, 15],
  background: '#FFFFFF',
  enableScroll: false,
  title: {
    name: '70%',
    fontSize: 20
  },
  subtitle: {
    name: '占比',
    fontSize: 14
  },
  extra: {
    ring: {
      ringWidth: 30
    }
  }
};

// 图表加载完成回调
function onChartComplete(data: any) {
  addLog(`图表加载完成: ${data.canvasId}`, 'success');
}

// 图表加载错误回调
function onChartError(error: any) {
  addLog(`图表加载错误: ${error.message || JSON.stringify(error)}`, 'error');
}
</script>

<style lang="scss" scoped>
.chart-test-page {
  padding: 20px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.page-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 16px;
}

.chart-container {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  
  h3 {
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 16px;
  }
}

.log-container {
  background-color: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  
  h3 {
    font-size: 16px;
    margin-top: 0;
    margin-bottom: 8px;
  }
}

.log-box {
  max-height: 300px;
  overflow-y: auto;
  background-color: #1e1e1e;
  border-radius: 4px;
  padding: 8px;
}

.log-item {
  font-family: monospace;
  font-size: 12px;
  padding: 4px;
  margin-bottom: 4px;
  white-space: pre-wrap;
  word-break: break-all;
  
  &.info {
    color: #ffffff;
  }
  
  &.success {
    color: #4caf50;
  }
  
  &.error {
    color: #f44336;
  }
}
</style> 