<script setup lang="ts">
import { ref } from 'vue';
// AppIcon组件已全局注册或通过easycom自动引入
// 直接导入u-icon组件，解决无法自动解析的问题
import UIcon from 'uview-plus/components/u-icon/u-icon.vue';

// 欢迎页使用的图标测试 (根据提供的uView Plus图标集截图进行核对和修正)
const welcomeIcons = ref([
  'mic',
  'file-text',
  'integral',
  'info',
  'camera',
  'minus' // 代表 "line"
]);

// 其他测试图标 (从uView Plus图标集截图中选取确认有效的名称)
const testIcons = ref([
  'home',
  'photo',
  'star',
  'setting',
  'search',
  'question-circle',
  'map',
  'camera',
  'plus',
  'trash',
  'email-fill',
  'bell-fill',
  'lock-fill',
  'heart-fill',
  'thumb-up-fill'
]);
</script>

<template>
  <view class="icon-test-page">
    <view class="page__header">
      <text class="page__title">uView Plus 图标测试</text>
      <text class="page__subtitle">测试样式正确引入后图标显示情况</text>
    </view>
    
    <view class="page__body">
      <!-- 测试部分1: 直接使用u-icon组件 -->
      <view class="section">
        <view class="section__title">
          <text>1. 直接使用u-icon组件测试</text>
        </view>
        <view class="section__content">
          <view class="icon-grid">
            <view 
              class="icon-item" 
              v-for="(icon, index) in welcomeIcons" 
              :key="'welcome-' + index"
            >
              <UIcon 
                :name="icon" 
                size="28" 
                color="#FF6B35"
                class="icon-demo"
              ></UIcon>
              <text class="icon-name">{{icon}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 测试部分2: 使用AppIcon组件 -->
      <view class="section">
        <view class="section__title">
          <text>2. 使用AppIcon组件测试 (请确保列表中的图标名为uView Plus有效名称)</text>
        </view>
        <view class="section__content">
          <view class="icon-grid">
            <view 
              class="icon-item" 
              v-for="(icon, index) in testIcons.slice(0, 8)" 
              :key="'appicon-' + index"
            >
              <AppIcon 
                :icon="icon" 
                size="28" 
                color="var(--color-primary, #FF6B35)"
              ></AppIcon>
              <text class="icon-name">{{icon}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 测试部分4: 尺寸和颜色测试 -->
      <view class="section">
        <view class="section__title">
          <text>4. 尺寸和颜色测试</text>
        </view>
        <view class="section__content">
          <view class="icon-row">
            <UIcon name="home" size="20" color="#3c9cff"></UIcon>
            <UIcon name="home" size="30" color="#19be6b"></UIcon>
            <UIcon name="home" size="40" color="#ff9900"></UIcon>
            <UIcon name="home" size="50" color="#fa3534"></UIcon>
          </view>
        </view>
      </view>
      
      <!-- 测试部分5: 旋转动画测试 -->
      <view class="section">
        <view class="section__title">
          <text>5. 旋转动画测试</text>
        </view>
        <view class="section__content">
          <view class="icon-row">
            <UIcon name="reload" size="40" color="#3c9cff" class="u-icon--spin"></UIcon>
            <AppIcon icon="reload" size="40" color="#19be6b" :spin="true"></AppIcon>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.icon-test-page {
  padding: 30rpx;
  
  .page__header {
    margin-bottom: 30rpx;
    
    .page__title {
      font-size: 36rpx;
      font-weight: bold;
      color: var(--color-text-primary, #333);
    }
    
    .page__subtitle {
      font-size: 24rpx;
      color: var(--color-text-secondary, #666);
      margin-top: 10rpx;
      display: block;
    }
  }
  
  .section {
    margin-bottom: 40rpx;
    
    .section__title {
      font-size: 28rpx;
      color: var(--color-text-secondary, #666);
      margin-bottom: 20rpx;
    }
    
    .section__content {
      background-color: #fff;
      border-radius: 8rpx;
      padding: 20rpx;
    }
  }
  
  .icon-grid {
    display: flex;
    flex-wrap: wrap;
    
    .icon-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 20rpx 0;
      
      .icon-demo {
        margin-bottom: 10rpx;
      }

      .icon-name {
        font-size: 20rpx;
        color: #666;
        text-align: center;
      }
    }
  }
  
  .icon-row {
    display: flex;
    justify-content: space-around;
    align-items: center;
  }
}
</style>