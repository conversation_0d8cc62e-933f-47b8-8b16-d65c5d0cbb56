<script setup lang="ts">
import type { TransactionCategory } from '@/types/transaction';
import { getAllCategories } from '@/api/transaction';
import CategorySelector from '@/components/business/CategorySelector.vue';
import AppCard from '@/components/common/AppCard.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import { computed, onMounted, ref } from 'vue';

// 状态
const categories = ref<TransactionCategory[]>([]);
const isLoading = ref(true);
const selectedExpenseCategoryId = ref<string | number>('');
const selectedIncomeCategoryId = ref<string | number>('');
const selectedCategory = ref<TransactionCategory | null>(null);

// 常用图标列表 (测试用)
const commonIcons = [
  'utensils',
  'shopping-bag',
  'car',
  'home',
  'mobile-alt',
  'money-bill-wave',
  'gift',
  'chart-line',
  'briefcase-medical',
  'film',
  'plane',
  'coffee',
  'dumbbell',
  'gamepad',
  'calculator',
];

// 计算属性
const expenseCategories = computed(() => categories.value.filter(cat => cat.type === 'expense'));

const incomeCategories = computed(() => categories.value.filter(cat => cat.type === 'income'));

// 方法
async function loadCategories() {
  isLoading.value = true;
  try {
    const data = await getAllCategories();
    categories.value = data;
  }
  catch (error) {
    console.error('Failed to load categories:', error);
    uni.showToast({
      title: '加载分类失败',
      icon: 'none',
    });
  }
  finally {
    isLoading.value = false;
  }
}

function handleCategorySelected(category: TransactionCategory) {
  selectedCategory.value = category;
  console.log('Selected category:', category);

  // 根据类型同步选中状态
  if (category.type === 'expense') {
    selectedIncomeCategoryId.value = '';
  }
  else {
    selectedExpenseCategoryId.value = '';
  }
}

function getCategoryColor(category: TransactionCategory): string {
  const colorMap: Record<string, string> = {
    // 支出类别
    餐饮: '#FF6B35',
    购物: '#0EA5E9',
    交通: '#F59E0B',
    娱乐: '#8B5CF6',
    住房: '#4F46E5',
    通讯: '#0891B2',
    医疗健康: '#EF4444',
    // 收入类别
    工资: '#10B981',
    奖金: '#059669',
    理财收入: '#84CC16',
    // 默认颜色
    default: '#888888',
  };

  return colorMap[category.name] || colorMap.default;
}

// 生命周期
onMounted(() => {
  loadCategories();
});
</script>

<template>
  <view class="category-test-page">
    <view class="nav-bar">
      <view class="nav-title">
        分类图标测试
      </view>
    </view>

    <view class="content">
      <AppCard>
        <template #header>
          <view class="card-title">
            当前所有分类
          </view>
        </template>
        <view class="category-list">
          <view v-if="isLoading" class="loading">
            加载中...
          </view>
          <view v-else-if="categories.length === 0" class="no-data">
            暂无分类数据
          </view>
          <view v-else>
            <view class="type-section">
              <view class="type-title">
                支出分类
              </view>
              <CategorySelector
                v-model="selectedExpenseCategoryId"
                :categories="expenseCategories"
                title="选择支出分类"
                @select="handleCategorySelected"
              />
            </view>

            <view class="type-section">
              <view class="type-title">
                收入分类
              </view>
              <CategorySelector
                v-model="selectedIncomeCategoryId"
                :categories="incomeCategories"
                title="选择收入分类"
                @select="handleCategorySelected"
              />
            </view>
          </view>
        </view>
      </AppCard>

      <AppCard custom-class="mt-4">
        <template #header>
          <view class="card-title">
            选中的分类
          </view>
        </template>
        <view v-if="selectedCategory" class="selected-category">
          <view
            class="category-icon-wrapper"
            :style="{ backgroundColor: getCategoryColor(selectedCategory) }"
          >
            <AppIcon :icon="selectedCategory.icon" color="#FFFFFF" />
          </view>
          <view class="category-details">
            <view class="category-name">
              {{ selectedCategory.name }}
            </view>
            <view class="category-info">
              ID: {{ selectedCategory.id }} | 类型:
              {{ selectedCategory.type === 'income' ? '收入' : '支出' }}
            </view>
            <view class="category-info">
              图标: {{ selectedCategory.icon }}
            </view>
          </view>
        </view>
        <view v-else class="no-selection">
          请从上方选择一个分类
        </view>
      </AppCard>

      <AppCard custom-class="mt-4">
        <template #header>
          <view class="card-title">
            图标预览
          </view>
        </template>
        <view class="icons-preview">
          <view v-for="icon in commonIcons" :key="icon" class="icon-item">
            <AppIcon :icon="icon" size="lg" />
            <text class="icon-name">
              {{ icon }}
            </text>
          </view>
        </view>
      </AppCard>
    </view>
  </view>
</template>

<style lang="scss" scoped>
  .category-test-page {
    min-height: 100vh;
    background-color: var(--bg-secondary, #f5f5f5);
    padding-bottom: 20px;
  }

  .nav-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 60px;
    background-color: var(--color-primary, #ff6b35);

    .nav-title {
      color: white;
      font-size: 18px;
      font-weight: 500;
    }
  }

  .content {
    padding: 16px;
  }

  .card-title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-primary, #333);
  }

  .type-section {
    margin-bottom: 20px;

    .type-title {
      font-size: 14px;
      color: var(--text-secondary, #666);
      margin-bottom: 10px;
    }
  }

  .loading,
  .no-data {
    display: flex;
    justify-content: center;
    padding: 20px 0;
    color: var(--text-hint, #999);
  }

  .selected-category {
    display: flex;
    align-items: center;
    padding: 16px;

    .category-icon-wrapper {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
    }

    .category-details {
      flex: 1;
    }

    .category-name {
      font-size: 18px;
      font-weight: 500;
      color: var(--text-primary, #333);
      margin-bottom: 4px;
    }

    .category-info {
      font-size: 14px;
      color: var(--text-secondary, #666);
      margin-bottom: 2px;
    }
  }

  .no-selection {
    padding: 20px;
    text-align: center;
    color: var(--text-hint, #999);
  }

  .icons-preview {
    display: flex;
    flex-wrap: wrap;

    .icon-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 10px;

      .icon-name {
        font-size: 12px;
        color: var(--text-secondary, #666);
        margin-top: 5px;
        text-align: center;
      }
    }
  }

  // 工具类
  .mt-4 {
    margin-top: 16px;
  }
</style>
