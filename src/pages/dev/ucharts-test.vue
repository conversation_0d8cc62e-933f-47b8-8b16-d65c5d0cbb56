<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
// 直接引入项目中已存在的uCharts库
import uCharts from '@/components/u-charts/u-charts.js';

// 状态管理
const chartRendered = ref(false);
const loadingStatus = ref('等待渲染');
const errorMessage = ref('');
let chartInstance = null;

// 渲染简单柱状图的函数
const renderBarChart = () => {
  try {
    loadingStatus.value = '尝试渲染...';
    
    // 获取canvas上下文
    const canvasId = 'myTestCanvas';
    const ctx = uni.createCanvasContext(canvasId);
    
    // 最简单的柱状图配置
    const chartData = {
      categories: ['A', 'B', 'C', 'D', 'E'],
      series: [
        {
          name: '数据',
          data: [35, 20, 25, 37, 45]
        }
      ]
    };
    
    // 创建图表实例 (使用最简配置)
    chartInstance = new uCharts({
      type: 'column',
      context: ctx,
      width: 300,
      height: 200,
      categories: chartData.categories,
      series: chartData.series,
      animation: true,
      background: '#FFFFFF',
      padding: [15, 15, 0, 15],
      enableScroll: false,
      legend: {
        show: true,
        position: 'top',
        float: 'right',
        fontSize: 13
      },
      xAxis: {
        disableGrid: true,
        gridType: 'dash',
        itemCount: 5,
        scrollShow: false,
        scrollAlign: 'left'
      },
      yAxis: {
        gridType: 'dash',
        splitNumber: 5,
        min: 0,
        max: 50,
        format: (val) => val.toFixed(0)
      },
      extra: {
        column: {
          type: 'group',
          width: 30,
          seriesGap: 2,
          barBorderRadius: [5, 5, 0, 0]
        }
      }
    });
    
    // 绘制图表
    if (chartInstance && typeof chartInstance.draw === 'function') {
      chartInstance.draw();
      loadingStatus.value = '已渲染';
      chartRendered.value = true;
    } else {
      throw new Error('chart.draw is not a function');
    }
  } catch (error) {
    console.error('渲染图表失败:', error);
    loadingStatus.value = '渲染失败';
    errorMessage.value = error.message || '未知错误';
  }
};

// 组件挂载时渲染图表
onMounted(() => {
  // 延迟渲染，确保canvas已准备好
  setTimeout(() => {
    renderBarChart();
  }, 300);
});

// 组件卸载时清理资源
onUnmounted(() => {
  chartInstance = null;
});
</script>

<template>
  <view class="ucharts-test-page">
    <view class="page-header">
      <view class="page-title">uCharts测试页面</view>
      <view class="page-subtitle">直接调用原生API</view>
    </view>

    <!-- 状态显示区域 -->
    <view class="status-area" :style="{ backgroundColor: chartRendered ? '#E8F5E9' : '#FFEBEE' }">
      <text class="status-text">状态: {{ loadingStatus }}</text>
      <text v-if="errorMessage" class="error-message">错误: {{ errorMessage }}</text>
    </view>

    <!-- 柱状图 -->
    <view class="chart-container">
      <canvas 
        canvas-id="myTestCanvas" 
        id="myTestCanvas" 
        style="width: 100%; height: 300px; background-color: #f9f9f9; border-radius: 8px;"
      ></canvas>
    </view>

    <view class="tip-block">
      <view class="tip-title">这是一个极简测试环境，使用原生uCharts API。</view>
      <view class="tip-content">如果图表能正常渲染，则表明uCharts库本身功能正常；如果仍有问题，则可能是引用路径或API参数错误。</view>
    </view>
  </view>
</template>

<style scoped>
.ucharts-test-page {
  padding: 16px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
  text-align: center;
}

.page-title {
  font-size: 20px;
  font-weight: bold;
  color: #333;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.status-area {
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 16px;
  transition: background-color 0.3s;
}

.status-text {
  font-size: 16px;
  font-weight: 500;
  display: block;
}

.error-message {
  font-size: 14px;
  color: #d32f2f;
  margin-top: 8px;
  display: block;
}

.chart-container {
  margin-bottom: 24px;
}

.tip-block {
  background-color: #e3f2fd;
  padding: 16px;
  border-radius: 8px;
  border-left: 4px solid #2196f3;
}

.tip-title {
  font-weight: bold;
  margin-bottom: 8px;
  font-size: 16px;
}

.tip-content {
  font-size: 14px;
  line-height: 1.5;
  color: #555;
}
</style> 