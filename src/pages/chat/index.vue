<template>
  <view class="chat-page">
    <!-- 顶部导航栏 -->
    <view class="chat-page__nav">
      <view class="chat-page__nav-back" @tap="handleBack">
        <AppIcon icon="arrow-left" />
      </view>
      <text class="chat-page__nav-title">账无忌</text>
      <view class="chat-page__nav-right">
        <AppIcon icon="cog" />
      </view>
    </view>
    
    <!-- 聊天内容区域 -->
    <scroll-view 
      class="chat-page__content" 
      scroll-y 
      :scroll-top="scrollTop"
      @scroll="handleScroll"
      :scroll-into-view="latestMessageId"
      scroll-with-animation
      enhanced
      show-scrollbar
      :scroll-anchoring="true"
    >
      <!-- 欢迎消息 -->
      <view class="chat-page__system-message">
        <view class="chat-page__system-bubble">
          欢迎使用AI记账助手，我可以帮您记录收支、查询账单和分析财务状况。
        </view>
      </view>
      
      <!-- 消息列表 -->
      <view class="chat-page__messages">
        <template v-for="(message, index) in messages" :key="message.id">
          <!-- 普通消息气泡 -->
          <ChatBubble 
            :id="`msg-${message.id}`"
            :messageId="message.id"
            :sender="message.sender"
            :timestamp="message.timestamp"
            :isLoading="message.isLoading"
            @imageTap="handleImageTap"
            @imageLoaded="handleImageLoaded"
          >
            <!-- 交易识别结果 -->
            <template v-if="message.type === 'transaction'">
              <ChatRecognitionResult 
                :transaction="message.transaction" 
                :is-confirmed="confirmedTransactionIds.includes(message.transaction?.id)"
                @confirm-transaction="handleConfirmTransaction"
                @edit-transaction="handleEditTransaction"
                @delete-transaction="handleDeleteTransaction"
              >
                <!-- 操作按钮在组件内部已定义，无需通过slot传入 -->
              </ChatRecognitionResult>
            </template>
            
            <!-- 文本消息 -->
            <template v-else-if="message.type === 'text'">
              <text>{{ message.content }}</text>
            </template>
            
            <!-- 语音消息 -->
            <template v-else-if="message.type === 'voice'">
              <view class="voice-message">
                <view class="voice-message__wrapper" @tap="playVoice(message.voiceUrl)">
                  <AppIcon icon="play" size="sm" color="var(--color-primary, #4CAF50)" />
                  <view class="voice-message__duration">{{ message.voiceDuration || 0 }}″</view>
                  <view class="voice-message__waves">
                    <view class="voice-message__wave"></view>
                    <view class="voice-message__wave"></view>
                    <view class="voice-message__wave"></view>
                  </view>
                </view>
              </view>
            </template>

            <!-- 图片消息 -->
            <template v-else-if="message.type === 'image'">
              <view class="image-message">
                <image 
                  class="image-message__img" 
                  :src="message.imageUrl" 
                  mode="widthFix" 
                  @tap="handleImageTap(message.imageUrl)"
                  @load="handleImageLoaded({messageId: message.id, id: `msg-${message.id}`})"
                />
              </view>
            </template>
          </ChatBubble>
        </template>
        
        <!-- AI正在输入提示 -->
        <ChatBubble 
          v-if="isAITyping" 
          sender="assistant" 
          :timestamp="Date.now()"
          :isLoading="true"
        >
          <text>对方正在输入...</text>
        </ChatBubble>
      </view>
    </scroll-view>
    
    <!-- 底部输入区域 -->
    <view class="chat-page__input-container">
      <ChatInputBar 
        :placeholder="inputPlaceholder" 
        :disabled="isInputDisabled"
        @send="handleSendMessage"
        @startVoice="handleVoiceStart"
        @endVoice="handleVoiceEnd"
        @functionClick="toggleFunctionsPanel"
        @focus="handleInputFocus"
      />
    </view>
    
    <!-- 语音输入动画区域 -->
    <view v-if="isVoiceInputActive" class="chat-page__voice-container">
      <view class="chat-page__voice-wave">
        <view 
          class="chat-page__voice-bar" 
          v-for="i in 5" 
          :key="i"
          :style="{ height: getVoiceBarHeight(i) + 'px' }"
        ></view>
      </view>
      <text class="chat-page__voice-tip">{{ isVoiceCancelling ? '松开手指，取消录音' : '松开结束' }}</text>
      <text class="chat-page__voice-cancel">上滑取消</text>
    </view>
    
    <!-- 图片预览组件 -->
    <ImageViewer
      :visible="isImageViewerVisible"
      :image-url="currentImageUrl"
      @close="handleImageViewerClose"
    />
    
    <!-- 功能面板 -->
    <view 
      class="chat-page__functions-panel" 
      :class="{ 'chat-page__functions-panel--active': isFunctionsPanelVisible }"
    >
      <view class="chat-page__functions-grid">
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('album')">
          <view class="chat-page__function-icon" style="color: var(--color-success, #4CAF50);">
            <AppIcon icon="image" />
          </view>
          <text class="chat-page__function-label">相册</text>
        </view>
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('camera')">
          <view class="chat-page__function-icon" style="color: var(--color-info, #2196F3);">
            <AppIcon icon="camera" />
          </view>
          <text class="chat-page__function-label">拍照</text>
        </view>
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('query')">
          <view class="chat-page__function-icon" style="color: var(--color-error, #E91E63);">
            <AppIcon icon="calendar-alt" />
          </view>
          <text class="chat-page__function-label">查询账单</text>
        </view>
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('analysis')">
          <view class="chat-page__function-icon" style="color: var(--color-warning, #9C27B0);">
            <AppIcon icon="chart-pie" />
          </view>
          <text class="chat-page__function-label">消费分析</text>
        </view>
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('budget')">
          <view class="chat-page__function-icon" style="color: var(--text-secondary, #607D8B);">
            <AppIcon icon="piggy-bank" />
          </view>
          <text class="chat-page__function-label">预算管理</text>
        </view>
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('receipt')">
          <view class="chat-page__function-icon" style="color: var(--text-primary, #795548);">
            <AppIcon icon="receipt" />
          </view>
          <text class="chat-page__function-label">报销单据</text>
        </view>
        <view class="chat-page__function-item" @tap="handleFunctionItemClick('advice')">
          <view class="chat-page__function-icon" style="color: var(--color-primary, #FF6B35);">
            <AppIcon icon="lightbulb" />
          </view>
          <text class="chat-page__function-label">理财建议</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
// 1. Vue核心库
import { ref, onMounted, computed, nextTick, onUnmounted, watch } from 'vue';
import { onLoad, onShow } from '@dcloudio/uni-app';
import dayjs from 'dayjs';

// 2. Pinia状态
import { useCategoryStore } from '@/stores/category.store';
import { useTransactionStore } from '@/stores/transaction.store';

// 3. 组件导入
import AppButton from '@/components/common/AppButton.vue';
import AppIcon from '@/components/common/AppIcon.vue';
import ChatBubble from '@/components/business/ChatBubble.vue';
import ImageViewer from '@/components/business/ImageViewer.vue';
import ChatInputBar from './components/ChatInputBar.vue';
import ChatRecognitionResult from './components/ChatRecognitionResult.vue';

// 4. 工具/平台适配
import { getPlatformStyle } from '@/utils/platform';

// 5. API
import { sendMessage, recognizeVoice, getChatHistory } from '@/api/chat';

// 消息类型
/**
 * Message 聊天消息对象类型
 * @property id - 消息唯一ID
 * @property sender - 发送者（user/assistant/system）
 * @property content - 文本内容
 * @property timestamp - 时间戳
 * @property type - 消息类型
 * @property isLoading - 是否为加载中
 * @property voiceUrl - 语音消息URL
 * @property voiceDuration - 语音时长
 * @property imageUrl - 图片消息URL
 * @property transaction - 识别出的交易对象
 */
interface Message {
  id: string;
  sender: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: number;
  type: 'text' | 'voice' | 'image' | 'transaction';
  isLoading?: boolean;
  voiceUrl?: string;
  voiceDuration?: number;
  imageUrl?: string;
  transaction?: TransactionRecognized;
}

// 识别出的交易信息类型
/**
 * TransactionRecognized 识别出的交易对象类型
 * @property id - 交易唯一ID
 * @property type - 类型（income/expense）
 * @property amount - 金额
 * @property categoryId - 分类ID
 * @property date - 日期
 * @property description - 描述
 */
interface TransactionRecognized {
  id: string;
  type: 'income' | 'expense';
  amount: number;
  categoryId: string;
  date: Date | string;
  description: string;
}

// API响应接口
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

interface ChatHistoryResponse {
  messages: Message[];
  total: number;
}

// 消息存储相关常量
const CHAT_STORAGE_KEY = 'localLedgerDB:chat_messages';
const CONFIRMED_TRANSACTIONS_KEY = 'localLedgerDB:confirmed_transactions';

// 页面状态
const scrollTop = ref(0);
const inputPlaceholder = ref('请输入您的问题或记账指令');
const isAITyping = ref(false);
const isInputDisabled = ref(false);
const isVoiceInputActive = ref(false);
const isVoiceCancelling = ref(false);
const isImageViewerVisible = ref(false);
const currentImageUrl = ref('');
// 🔑 聊天消息列表
const messages = ref<Message[]>([]);
const latestMessageId = ref('');
const voiceAmplitudes = ref<number[]>([0, 0, 0, 0, 0]);
const isFunctionsPanelVisible = ref(false);
const confirmedTransactionIds = ref<string[]>([]);
const scrollTimeoutId = ref<number | null>(null);

// 获取录音管理器
const recorderManager = uni.getRecorderManager();

// 语音动画相关
let voiceAnimationTimer: number | null = null;

// 存储消息到本地
const saveMessages = () => {
  try {
    uni.setStorageSync(CHAT_STORAGE_KEY, JSON.stringify(messages.value));
  } catch (error) {
    console.error('保存聊天消息失败:', error);
  }
};

// 从本地存储加载聊天历史
const loadMessages = () => {
  try {
    const storedMessages = uni.getStorageSync(CHAT_STORAGE_KEY);
    if (storedMessages) {
      const parsedMessages = typeof storedMessages === 'string' ? 
        JSON.parse(storedMessages) : storedMessages;
      
      if (Array.isArray(parsedMessages)) {
        console.log(`从本地存储加载了${parsedMessages.length}条聊天记录`);
        return parsedMessages;
      }
    }
    return null;
  } catch (error) {
    console.error('加载聊天消息失败:', error);
    return null; // 加载失败返回null
  }
};

// 分类仓库
const categoryStore = useCategoryStore();

// 初始化页面
onMounted(async () => {
  console.log('聊天页面初始化');
  
  // 加载分类数据
  if (!categoryStore.isInitialized) {
    try {
      await categoryStore.fetchCategories();
      console.log('分类数据加载完成');
    } catch (error) {
      console.error('加载分类数据失败:', error);
      uni.showToast({
        title: '加载分类数据失败',
        icon: 'none'
      });
    }
  }
  
  // 优先从本地存储加载聊天历史
  const storedMessages = loadMessages();
  if (storedMessages && storedMessages.length > 0) {
    messages.value = storedMessages;
    if (messages.value.length > 0) {
      latestMessageId.value = messages.value[messages.value.length - 1].id;
    }
    console.log('从本地存储加载聊天历史完成，共', messages.value.length, '条消息');
  } else {
    // 如果本地没有，尝试从API加载
    try {
      const response = await getChatHistory({
        pageSize: 20,
        pageNum: 1
      });
      
      if (response.code === 0) {
        // 确保按时间顺序排序
        const sortedMessages = response.data.messages.sort((a, b) => a.timestamp - b.timestamp);
        messages.value = sortedMessages;
        
        if (messages.value.length > 0) {
          latestMessageId.value = messages.value[messages.value.length - 1].id;
        }
        
        // 保存到本地
        saveMessages();
        console.log('聊天历史加载完成，共加载', messages.value.length, '条消息');
      } else {
        console.error('加载聊天历史失败:', response.message);
      }
    } catch (error) {
      console.error('加载聊天历史失败:', error);
    }
  }
  
  // 如果没有历史消息，添加欢迎消息
  if (messages.value.length === 0) {
    nextTick(() => {
      const welcomeMessage = {
        id: 'welcome-msg',
        sender: 'assistant',
        content: '您好，我是账无忌AI助手，很高兴为您服务。我可以帮您记录消费、查询账单、分析财务状况等。试着对我说："今天买菜花了50元"。',
        timestamp: Date.now(),
        type: 'text'
      };
      
      messages.value.push(welcomeMessage);
      saveMessages(); // 保存到本地存储
      
      // 设置最新消息ID并直接滚动到底部
      latestMessageId.value = `msg-${welcomeMessage.id}`;
      // 直接设置scrollTop到最大值，避免动画效果
      scrollTop.value = 999999;
    });
  } else {
    // 有历史消息则滚动到最后一条，直接设置scrollTop，避免动画
    nextTick(() => {
      scrollTop.value = 999999;
    });
  }
  
  // 加载已确认的交易ID
  try {
    const savedConfirmedIds = uni.getStorageSync(CONFIRMED_TRANSACTIONS_KEY);
    if (savedConfirmedIds) {
      confirmedTransactionIds.value = Array.isArray(savedConfirmedIds) ? 
        savedConfirmedIds : 
        JSON.parse(savedConfirmedIds);
      console.log('已加载确认的交易ID:', confirmedTransactionIds.value);
    }
  } catch (error) {
    console.error('加载已确认交易ID失败:', error);
  }
  
  // 初始化录音管理器事件监听
  if (recorderManager) {
    // 录音完成时触发
    recorderManager.onStop((res) => {
      console.log('录音完成事件触发', res);
      if (!isVoiceCancelling.value) {
        handleRecordResult(res);
      } else {
        console.log('录音已取消');
      }
    });
    
    // 录音错误时触发
    recorderManager.onError((err) => {
      console.error('录音错误', err);
      uni.showToast({
        title: '录音失败，请检查麦克风权限',
        icon: 'none',
        duration: 2000
      });
      isVoiceInputActive.value = false;
      stopVoiceAnimation();
    });
    
    // 录音被打断时触发
    if (typeof recorderManager.onInterruptionBegin === 'function') {
      recorderManager.onInterruptionBegin(() => {
        console.log('录音被打断');
        if (isVoiceInputActive.value) {
          // 停止录音但不处理结果
          isVoiceCancelling.value = true;
          recorderManager.stop();
        }
      });
    }
  } else {
    console.error('录音管理器初始化失败');
  }
});

// 在页面显示时滚动到最新消息
onShow(() => {
  if (messages.value.length > 0) {
    nextTick(() => {
      // 直接滚动到底部，不使用动画
      scrollTop.value = 999999;
    });
  }
});

// 在页面销毁前清理
onUnmounted(() => {
  // 停止录音动画定时器
  if (voiceAnimationTimer) {
    clearInterval(voiceAnimationTimer);
    voiceAnimationTimer = null;
  }
  
  // 清理滚动尝试定时器
  if (scrollTimeoutId.value) {
    clearTimeout(scrollTimeoutId.value);
    scrollTimeoutId.value = null;
  }
});

// 处理返回按钮点击
const handleBack = () => {
  uni.navigateBack();
};

// 处理滚动事件
const handleScroll = (e) => {
  // 保存滚动位置
  scrollTop.value = e.detail.scrollTop;
};

// 优化的滚动到最新消息方法
const attemptScrollToLatest = (forceToBottom = false, isInitialAttempt = true) => {
  // 清理之前的滚动尝试
  if (scrollTimeoutId.value) {
    clearTimeout(scrollTimeoutId.value);
    scrollTimeoutId.value = null;
  }
  
  if (messages.value.length === 0) return;
  
  // 找到最后一条消息的ID
  const lastMessage = messages.value[messages.value.length - 1];
  const msgId = `msg-${lastMessage.id}`;
  
  // 更新最新消息ID（用于scroll-into-view）
  latestMessageId.value = msgId;
  
  // 对于初始页面加载或确认交易后的滚动，直接滚动到底部而无需动画
  if (forceToBottom) {
    nextTick(() => {
      // 禁用滚动动画，直接定位到底部
      const scrollView = uni.createSelectorQuery().select('.chat-page__content');
      if (scrollView) {
        scrollTop.value = 999999;
      }
    });
    return;
  }
  
  // 对于普通消息，使用scroll-into-view平滑滚动到对应消息
  nextTick(() => {
    // 直接滚动到对应元素，依赖scroll-into-view
    // 这里不设置scrollTop，依靠scroll-into-view和scroll-anchoring实现平滑滚动
  });
};

// 原有的滚动方法重定向到新方法
const scrollToLatest = (forceToBottom = false) => {
  attemptScrollToLatest(forceToBottom);
};

// 添加用户消息
/**
 * addUserMessage - 添加用户文本消息到消息列表
 */
const addUserMessage = (content: string): Message => {
  const message: Message = {
    id: Date.now().toString(),
    type: 'text',
    sender: 'user',
    content,
    timestamp: Date.now()
  };
  
  messages.value.push(message);
  saveMessages(); // 保存到本地存储
  
  // 设置最新消息ID并自动滚动
  latestMessageId.value = `msg-${message.id}`;
  attemptScrollToLatest();
  
  return message;
};

// 添加AI消息
/**
 * addAIMessage - 添加AI助手文本消息到消息列表
 */
const addAIMessage = (content: string): Message => {
  const message: Message = {
    id: Date.now().toString(),
    type: 'text',
    sender: 'assistant',
    content,
    timestamp: Date.now()
  };
  
  messages.value.push(message);
  saveMessages(); // 保存到本地存储
  
  // 设置最新消息ID并自动滚动
  latestMessageId.value = `msg-${message.id}`;
  attemptScrollToLatest();
  
  return message;
};

// 添加交易消息
/**
 * addTransactionMessage - 添加AI助手识别的交易卡片到消息列表
 */
const addTransactionMessage = (transaction: TransactionRecognized): Message => {
  const message: Message = {
    id: Date.now().toString(),
    type: 'transaction',
    sender: 'assistant',
    content: '',
    timestamp: Date.now(),
    transaction
  };
  
  messages.value.push(message);
  saveMessages(); // 保存到本地存储
  
  // 设置最新消息ID
  latestMessageId.value = `msg-${message.id}`;
  // 对于交易卡片，使用强制滚动并多次尝试
  attemptScrollToLatest(true);
  
  return message;
};

// 显示AI正在输入状态
const showAITyping = () => {
  isAITyping.value = true;
  
  // 自动滚动到底部，让用户看到AI正在输入的状态
  nextTick(() => {
    scrollToLatest(true);
  });
};

// 隐藏AI正在输入状态
const hideAITyping = () => {
  isAITyping.value = false;
};

// 处理AI回复
const handleAIResponse = async (userText: string) => {
  // 显示AI正在输入
  isAITyping.value = true;
  
  try {
    // 等待500ms模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 添加AI正在输入的消息
    const loadingMessage: Message = {
      id: `msg-${messages.value.length + 1}`,
      sender: 'assistant',
      content: '...',
      timestamp: Date.now(),
      type: 'text',
      isLoading: true
    };
    
    messages.value.push(loadingMessage);
    
    // 滚动到最新消息
    scrollToLatest();
    
    // 调用AI处理接口
    let response;
    try {
      // 这里应该调用真实的API
      // response = await sendMessage({ content: userText });
      
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // 检测是否包含记账关键词
      const hasExpenseKeywords = ['花了', '支出', '消费', '买了', '付款'].some(keyword => 
        userText.includes(keyword)
      );
      
      const hasIncomeKeywords = ['收入', '赚了', '发工资', '报销'].some(keyword => 
        userText.includes(keyword)
      );
      
      // 简单的金额提取正则
      const amountMatch = userText.match(/(\d+(\.\d+)?)元/);
      const amount = amountMatch ? parseFloat(amountMatch[1]) : 0;
      
      if ((hasExpenseKeywords || hasIncomeKeywords) && amount > 0) {
        // 模拟交易识别响应
        response = {
          code: 0,
          data: {
            type: 'transaction',
            transaction: {
              type: hasIncomeKeywords ? 'income' : 'expense',
              amount: hasIncomeKeywords ? amount : -amount,
              categoryId: hasExpenseKeywords ? 'cat-1' : 'cat-income-1', // 默认饮食或工资
              date: new Date(),
              description: userText
            }
          }
        };
      } else {
        // 模拟普通回复
        response = {
          code: 0,
          data: {
            type: 'text',
            content: `您好，我是账无忌AI助手，很高兴为您服务。请问有什么我可以帮助您的吗？例如记录消费、查询账单、制定预算等。`
          }
        };
      }
    } catch (error) {
      console.error('API调用失败:', error);
      response = {
        code: -1,
        message: '网络请求失败'
      };
    }
    
    // 删除加载中的消息
    const loadingIndex = messages.value.findIndex(msg => msg.id === loadingMessage.id);
    if (loadingIndex !== -1) {
      messages.value.splice(loadingIndex, 1);
    }
    
    // 处理响应
    if (response.code === 0) {
      const data = response.data;
      
      if (data.type === 'transaction') {
        // 如果识别到交易消息
        const transaction = data.transaction;
        
        // 先回复一个确认消息
        const aiMessage: Message = {
          id: `msg-${messages.value.length + 1}`,
          sender: 'assistant',
          content: '我已经帮您识别出以下交易信息，请确认：',
          timestamp: Date.now(),
          type: 'text'
        };
        
        messages.value.push(aiMessage);
        
        // 然后添加交易卡片
        const transactionMessage: Message = {
          id: `msg-${messages.value.length + 1}`,
          sender: 'system',
          content: '',
          timestamp: Date.now(),
          type: 'transaction',
          transaction: {
            id: `temp-${Date.now()}`,
            type: transaction.type,
            amount: transaction.amount,
            categoryId: transaction.categoryId,
            date: transaction.date instanceof Date ? 
              transaction.date.toISOString().split('T')[0] : 
              new Date(transaction.date).toISOString().split('T')[0],
            description: transaction.description || ''
          }
        };
        
        messages.value.push(transactionMessage);
        
        // 滚动到最新消息
        scrollToLatest(true);
      } else {
        // 普通文本回复
        const aiMessage: Message = {
          id: `msg-${messages.value.length + 1}`,
          sender: 'assistant',
          content: data.content,
          timestamp: Date.now(),
          type: 'text'
        };
        
        messages.value.push(aiMessage);
        
        // 滚动到最新消息
        scrollToLatest();
      }
    } else {
      // 添加错误消息
      const errorMessage: Message = {
        id: `msg-${messages.value.length + 1}`,
        sender: 'assistant',
        content: '抱歉，我遇到了一些问题，请稍后再试。',
        timestamp: Date.now(),
        type: 'text'
      };
      
      messages.value.push(errorMessage);
      
      // 滚动到最新消息
      scrollToLatest();
      console.error('API错误:', response.message);
    }
  } catch (error) {
    console.error('处理AI回复失败:', error);
    
    // 添加错误消息
    const errorMessage: Message = {
      id: `msg-${messages.value.length + 1}`,
      sender: 'assistant',
      content: '抱歉，处理消息时发生错误，请检查网络连接后重试。',
      timestamp: Date.now(),
      type: 'text'
    };
    
    messages.value.push(errorMessage);
    
    // 滚动到最新消息
    scrollToLatest();
  } finally {
    // 关闭AI正在输入状态
    isAITyping.value = false;
  }
};

// 发送文本消息
const handleSendMessage = async (text: string) => {
  if (!text.trim()) {
    return;
  }
  
  console.log('发送消息:', text);
  
  // 禁用输入，防止重复发送
  isInputDisabled.value = true;
  
  // 添加用户消息
  const userMessage: Message = {
    id: `msg-${Date.now()}`,
    sender: 'user',
    content: text,
    timestamp: Date.now(),
    type: 'text'
  };
  
  // 添加到消息列表
  messages.value.push(userMessage);
  saveMessages(); // 保存到本地
  
  // 直接滚动到底部无动画
  nextTick(() => {
    scrollTop.value = 999999;
  });
  
  // 显示AI正在输入状态
  isAITyping.value = true;
  
  try {
    // 调用API处理消息
    const response = await sendMessage({ content: text });
    
    // 隐藏AI正在输入状态
    isAITyping.value = false;
    
    // 处理响应
    if (response && response.code === 0) {
      const data = response.data;
      
      if (data.type === 'transaction' && data.transaction) {
        // 如果识别到交易信息
        console.log('识别到交易信息:', data.transaction);
        
        // 先回复一个确认消息
        const aiMessage = {
          id: `msg-${Date.now()}`,
          sender: 'assistant',
          content: '我已经帮您识别出以下交易信息，请确认：',
          timestamp: Date.now(),
          type: 'text'
        };
        
        messages.value.push(aiMessage);
        
        // 然后添加交易卡片
        const transactionMessage = {
          id: `msg-${Date.now()}`,
          sender: 'assistant',
          content: '',
          timestamp: Date.now(),
          type: 'transaction',
          transaction: data.transaction
        };
        
        console.log('添加交易消息:', transactionMessage);
        messages.value.push(transactionMessage);
        
        // 保存消息
        saveMessages();
        
        // 直接滚动到底部无动画
        nextTick(() => {
          scrollTop.value = 999999;
        });
      } else {
        // 普通文本回复
        const aiMessage = {
          id: `msg-${Date.now()}`,
          sender: 'assistant',
          content: data.content || '我明白了',
          timestamp: Date.now(),
          type: 'text'
        };
        
        messages.value.push(aiMessage);
        saveMessages();
        
        // 直接滚动到底部无动画
        nextTick(() => {
          scrollTop.value = 999999;
        });
      }
    } else {
      // 错误响应
      const errorMessage = {
        id: `msg-${Date.now()}`,
        sender: 'assistant',
        content: '抱歉，处理您的消息时遇到了问题，请稍后重试。',
        timestamp: Date.now(),
        type: 'text'
      };
      
      messages.value.push(errorMessage);
      saveMessages();
      
      // 直接滚动到底部无动画
      nextTick(() => {
        scrollTop.value = 999999;
      });
      
      console.error('API错误响应:', response);
    }
  } catch (error) {
    console.error('发送消息异常:', error);
    
    // 添加错误消息
    const errorMessage = {
      id: `msg-${Date.now()}`,
      sender: 'assistant',
      content: '抱歉，网络连接异常，请检查网络后重试。',
      timestamp: Date.now(),
      type: 'text'
    };
    
    messages.value.push(errorMessage);
    saveMessages();
    
    // 直接滚动到底部无动画
    nextTick(() => {
      scrollTop.value = 999999;
    });
    
    // 隐藏AI正在输入状态
    isAITyping.value = false;
  } finally {
    // 重新启用输入
    isInputDisabled.value = false;
  }
};

// 开始语音输入
const handleVoiceStart = () => {
  console.log('开始语音输入');
  isVoiceInputActive.value = true;
  voiceAmplitudes.value = [0, 0, 0, 0, 0];
  
  // 启动录音动画
  startVoiceAnimation();
  
  // 开始录音
  if (recorderManager) {
    recorderManager.start({
      duration: 60000, // 最长录音时间60秒
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 64000,
      format: 'mp3'
    });
  }
};

// 结束语音输入
const handleVoiceEnd = (isCancelled: boolean) => {
  console.log('结束语音输入, 取消:', isCancelled);
  isVoiceInputActive.value = false;
  isVoiceCancelling.value = false;
  
  // 停止录音动画
  stopVoiceAnimation();
  
  // 停止录音
  if (recorderManager) {
    if (isCancelled) {
      recorderManager.stop(); // 仍然需要停止，但不处理结果
    } else {
      recorderManager.stop(); // 正常停止并处理结果
    }
  }
};

// 处理录音完成
const handleRecordResult = async (res) => {
  console.log('录音结果:', res);
  
  if (!res || !res.tempFilePath) {
    console.error('录音结果异常');
    uni.showToast({
      title: '录音失败，请重试',
      icon: 'none'
    });
    return;
  }
  
  // 语音录制结束，关闭输入状态
  isVoiceInputActive.value = false;
  stopVoiceAnimation();
  
  // 防止重复发送
  isInputDisabled.value = true;
  
  // 添加语音消息
  const voiceMessage: Message = {
    id: `msg-${Date.now()}`,
    sender: 'user',
    content: '[语音消息]',
    timestamp: Date.now(),
    type: 'voice',
    voiceUrl: res.tempFilePath,
    voiceDuration: Math.round(res.duration / 1000)
  };
  
  messages.value.push(voiceMessage);
  saveMessages();
  
  // 直接滚动到底部，无动画
  nextTick(() => {
    scrollTop.value = 999999;
  });
  
  // 添加AI正在分析的提示
  const loadingMessage: Message = {
    id: `loading-${Date.now()}`,
    sender: 'assistant',
    content: '正在分析您的语音...',
    timestamp: Date.now(),
    type: 'text',
    isLoading: true
  };
  
  messages.value.push(loadingMessage);
  
  // 直接滚动到底部，无动画
  nextTick(() => {
    scrollTop.value = 999999;
  });
  
  // API调用
  let response;
  try {
    // 模拟API调用
    if (process.env.NODE_ENV === 'development') {
      // 本地开发环境模拟响应
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟
      
      // 随机决定返回交易或普通回复
      const isTransaction = Math.random() > 0.5;
      
      if (isTransaction) {
        // 模拟交易消息
        const transactionTypes = ['income', 'expense'];
        const categoryIds = ['cat-food', 'cat-shopping', 'cat-transport', 'cat-salary'];
        const descriptions = ['买菜', '购物', '打车', '工资'];
        
        const randomTypeIndex = Math.floor(Math.random() * transactionTypes.length);
        const randomCategoryIndex = Math.floor(Math.random() * categoryIds.length);
        
        response = {
          code: 0,
          data: {
            type: 'transaction',
            transaction: {
              id: `temp-${Date.now()}`,
              type: transactionTypes[randomTypeIndex],
              amount: Math.floor(Math.random() * 500) + 10,
              categoryId: categoryIds[randomCategoryIndex],
              date: new Date().toISOString().split('T')[0],
              description: descriptions[randomCategoryIndex]
            }
          }
        };
      } else {
        // 模拟普通回复
        response = {
          code: 0,
          data: {
            type: 'text',
            content: `您好，我是账无忌AI助手，很高兴为您服务。请问有什么我可以帮助您的吗？例如记录消费、查询账单、制定预算等。`
          }
        };
      }
    } else {
      // 生产环境调用真实API
      response = await processVoice({ filePath: res.tempFilePath, duration: res.duration });
    }
  } catch (error) {
    console.error('API调用失败:', error);
    response = {
      code: -1,
      message: '网络请求失败'
    };
  }
  
  // 删除加载中的消息
  const loadingIndex = messages.value.findIndex(msg => msg.id === loadingMessage.id);
  if (loadingIndex !== -1) {
    messages.value.splice(loadingIndex, 1);
  }
  
  // 处理响应
  if (response.code === 0) {
    const data = response.data;
    
    if (data.type === 'transaction') {
      // 如果识别到交易消息
      const transaction = data.transaction;
      
      // 先回复一个确认消息
      const aiMessage: Message = {
        id: `msg-${Date.now()}`,
        sender: 'assistant',
        content: '我已经帮您识别出以下交易信息，请确认：',
        timestamp: Date.now(),
        type: 'text'
      };
      
      messages.value.push(aiMessage);
      
      // 然后添加交易卡片
      const transactionMessage: Message = {
        id: `msg-${Date.now()}`,
        sender: 'system',
        content: '',
        timestamp: Date.now(),
        type: 'transaction',
        transaction: {
          id: `temp-${Date.now()}`,
          type: transaction.type,
          amount: transaction.amount,
          categoryId: transaction.categoryId,
          date: transaction.date instanceof Date ? 
            transaction.date.toISOString().split('T')[0] : 
            new Date(transaction.date).toISOString().split('T')[0],
          description: transaction.description || ''
        }
      };
      
      messages.value.push(transactionMessage);
      
      // 保存消息到本地
      saveMessages();
      
      // 直接滚动到底部，无动画
      nextTick(() => {
        scrollTop.value = 999999;
      });
    } else {
      // 普通文本回复
      const aiMessage: Message = {
        id: `msg-${Date.now()}`,
        sender: 'assistant',
        content: data.content || '我无法理解您的语音，请尝试说得更清楚一些',
        timestamp: Date.now(),
        type: 'text'
      };
      
      messages.value.push(aiMessage);
      
      // 保存消息到本地
      saveMessages();
      
      // 直接滚动到底部，无动画
      nextTick(() => {
        scrollTop.value = 999999;
      });
    }
  } else {
    // 添加错误消息
    const errorMessage: Message = {
      id: `msg-${Date.now()}`,
      sender: 'assistant',
      content: '抱歉，我遇到了一些问题，无法识别您的语音，请稍后再试。',
      timestamp: Date.now(),
      type: 'text'
    };
    
    messages.value.push(errorMessage);
    
    // 保存消息到本地
    saveMessages();
    
    // 直接滚动到底部，无动画
    nextTick(() => {
      scrollTop.value = 999999;
    });
    
    console.error('API错误:', response?.message || '未知错误');
  }
  
  // 重新启用输入
  isInputDisabled.value = false;
};

// 处理语音文件（模拟实现，实际项目中应该用真实语音识别）
const handleProcessVoiceFile = async (filePath: string) => {
  // 在实际项目中，这里应该将tempFilePath转换为File对象或直接上传filePath
  console.log('处理语音文件:', filePath);
  
  // 模拟延迟
  await new Promise(resolve => setTimeout(resolve, 1000));
  
  // 模拟语音识别结果，假设用户说"今天午餐花了38元"
  const simulatedText = "今天午餐花了38元";
  
  // 添加用户消息（实际应显示语音组件和识别文本）
  addUserMessage(simulatedText);
  
  // 显示AI正在输入
  showAITyping();
  
  // 模拟调用普通文本处理
  setTimeout(() => {
    hideAITyping();
    
    // 添加AI回复
    addAIMessage('好的，我来帮您记录这笔支出：');
    
    // 添加交易卡片
    const transactionMsg = addTransactionMessage({
      id: `temp-${Date.now()}`,
      type: 'expense',
      amount: -38,
      categoryId: 'cat-1', // 餐饮分类
      date: new Date(),
      description: '午餐'
    });
    
    // 确保滚动到最新消息（交易卡片）
    // 使用延迟确保DOM完全渲染
    setTimeout(() => {
      scrollToLatest(true);
    }, 400);
  }, 1500);
};

// 处理语音开始录制
const handleVoiceRecordingStart = () => {
  isVoiceInputActive.value = true;
  isVoiceCancelling.value = false;
  
  // 开始录音
  recorderManager.start({
    duration: 60000, // 最长录音时间，单位ms
    sampleRate: 16000, // 采样率
    numberOfChannels: 1, // 录音通道数
    encodeBitRate: 48000, // 编码码率
    format: 'mp3' // 音频格式
  });
};

// 处理语音录制结束
const handleVoiceRecordingEnd = (cancelled: boolean) => {
  isVoiceInputActive.value = false;
  
  if (cancelled) {
    // 取消录音
    recorderManager.stop();
    console.log('取消录音');
  } else {
    // 停止录音并处理结果
    recorderManager.stop();
    console.log('结束录音，准备处理');
  }
};

// 处理普通文本处理结果
const handleProcessTextResult = async (text: string) => {
  // 显示AI正在输入
  showAITyping();
  
  try {
    // 调用API处理文本
    const response = await sendMessage({ content: text });
    
    // 隐藏AI正在输入
    hideAITyping();
    
    // 处理响应
    if (response.code === 0) {
      // 添加AI回复
      addAIMessage(response.data.content);
    } else {
      // 添加错误消息
      addAIMessage('抱歉，我遇到了一些问题，请稍后再试。');
      console.error('API错误:', response.message);
    }
  } catch (error) {
    console.error('处理文本失败:', error);
    hideAITyping();
    addAIMessage('抱歉，消息处理失败，请稍后再试。');
  }
};

// 处理交易确认
const handleConfirmTransaction = (transaction) => {
  console.log('确认交易:', transaction);
  
  if (!transaction || !transaction.id) {
    console.error('交易ID不存在，无法确认');
    return;
  }
  
  // 已确认的交易不重复处理
  if (confirmedTransactionIds.value.includes(transaction.id)) {
    console.log('交易已确认，跳过处理');
    return;
  }
  
  const transactionStore = useTransactionStore();
  
  // 准备交易数据
  const transactionData = {
    type: transaction.type,
    amount: transaction.type === 'income' ? 
      Math.abs(transaction.amount) : 
      -Math.abs(transaction.amount),
    categoryId: transaction.categoryId,
    date: transaction.date instanceof Date ? 
      dayjs(transaction.date).format('YYYY-MM-DDTHH:mm:ss') : 
      (typeof transaction.date === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(transaction.date)
        ? dayjs().format('YYYY-MM-DDTHH:mm:ss')
        : dayjs(transaction.date).format('YYYY-MM-DDTHH:mm:ss')),
    description: transaction.description || '来自AI助手的记账'
  };
  
  // 添加到交易存储
  transactionStore.addTransaction(transactionData).then(() => {
    // 添加到确认列表
    confirmedTransactionIds.value.push(transaction.id);
    
    // 保存确认的交易ID到本地存储
    try {
      uni.setStorageSync(CONFIRMED_TRANSACTIONS_KEY, confirmedTransactionIds.value);
    } catch (error) {
      console.error('保存已确认交易ID失败:', error);
    }
    
    // 添加成功消息
    const successMessage: Message = {
      id: `msg-${Date.now()}`,
      sender: 'assistant',
      content: `✅ 交易已确认并添加到您的账单中。`,
      timestamp: Date.now(),
      type: 'text'
    };
    
    messages.value.push(successMessage);
    
    // 保存消息
    saveMessages();
    
    // 设置最新消息ID并直接滚动到底部，无动画效果
    latestMessageId.value = `msg-${successMessage.id}`;
    nextTick(() => {
      // 强制直接滚动到底部，避免多次滚动尝试
      scrollTop.value = 999999;
    });
    
    // 显示成功提示
    uni.showToast({
      title: '记账成功',
      icon: 'success'
    });
  });
};

// 处理交易编辑
const handleEditTransaction = (transaction) => {
  console.log('编辑交易:', transaction);
  
  // 在实际应用中，这里应该打开编辑页面
  // 这里简单模拟编辑后直接更新
  uni.showToast({
    title: '暂未实现编辑功能',
    icon: 'none'
  });
};

// 处理交易删除
const handleDeleteTransaction = (transaction) => {
  console.log('删除交易:', transaction);
  
  // 从消息列表中删除该交易卡片
  const transactionMsgIndex = messages.value.findIndex(msg => 
    msg.type === 'transaction' && 
    msg.transaction?.id === transaction.id
  );
  
  if (transactionMsgIndex !== -1) {
    // 删除交易卡片
    messages.value.splice(transactionMsgIndex, 1);
    
    // 添加删除成功消息
    const successMessage = {
      id: `msg-${Date.now()}`,
      sender: 'assistant',
      content: `已取消该条交易记录。`,
      timestamp: Date.now(),
      type: 'text'
    };
    
    messages.value.push(successMessage);
    
    // 保存消息
    saveMessages();
    
    // 设置最新消息ID并滚动
    latestMessageId.value = `msg-${successMessage.id}`;
    attemptScrollToLatest(true);
    
    // 显示成功提示
    uni.showToast({
      title: '已取消记账',
      icon: 'none'
    });
  }
};

// 处理图片预览
const handleImagePreview = (imageUrl: string) => {
  currentImageUrl.value = imageUrl;
  isImageViewerVisible.value = true;
};

// 关闭图片预览
const handleImageViewerClose = () => {
  isImageViewerVisible.value = false;
  currentImageUrl.value = '';
};

// 获取语音条高度（用于动画效果）
const getVoiceBarHeight = (index: number) => {
  if (isVoiceCancelling.value) {
    return 10; // 取消录音时固定高度
  }
  return voiceAmplitudes.value[index - 1];
};

// 处理功能面板显示/隐藏
const toggleFunctionsPanel = () => {
  isFunctionsPanelVisible.value = !isFunctionsPanelVisible.value;
};

// 处理功能项点击
const handleFunctionItemClick = (functionType: string) => {
  console.log(`功能项点击: ${functionType}`);
  
  switch (functionType) {
    case 'album':
      // 从相册选择图片
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          // 这里可以处理选择的图片，例如上传或显示
          console.log('选择的图片路径:', tempFilePaths);
          
          // 关闭功能面板
          isFunctionsPanelVisible.value = false;
        }
      });
      break;
      
    case 'camera':
      // 拍照
      uni.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['camera'],
        success: (res) => {
          const tempFilePaths = res.tempFilePaths;
          // 这里可以处理拍摄的照片
          console.log('拍摄的照片路径:', tempFilePaths);
          
          // 关闭功能面板
          isFunctionsPanelVisible.value = false;
        }
      });
      break;
      
    default:
      // 其他功能暂未实现，可以展示提示
      uni.showToast({
        title: '该功能正在开发中',
        icon: 'none'
      });
      
      // 关闭功能面板
      isFunctionsPanelVisible.value = false;
  }
};

// 开始语音动画
const startVoiceAnimation = () => {
  // 清理可能存在的定时器
  if (voiceAnimationTimer) {
    clearInterval(voiceAnimationTimer);
  }
  
  // 每150ms更新波形
  voiceAnimationTimer = setInterval(() => {
    if (isVoiceInputActive.value && !isVoiceCancelling.value) {
      // 生成随机振幅值
      voiceAmplitudes.value = voiceAmplitudes.value.map(() => Math.random() * 35 + 5);
    }
  }, 150);
};

// 停止语音动画
const stopVoiceAnimation = () => {
  if (voiceAnimationTimer) {
    clearInterval(voiceAnimationTimer);
    voiceAnimationTimer = null;
  }
  // 重置振幅值
  voiceAmplitudes.value = [0, 0, 0, 0, 0];
};

// 获取语音振幅
const getVoiceAmplitude = (index: number) => {
  if (index < 1 || index > voiceAmplitudes.value.length) {
    return 0;
  }
  return voiceAmplitudes.value[index - 1];
};

// 监听消息列表变化，确保渲染和滚动
watch(() => messages.value.length, (newLen, oldLen) => {
  // 如果消息列表长度增加，说明有新消息
  if (newLen > oldLen) {
    const lastMessage = messages.value[messages.value.length - 1];
    latestMessageId.value = `msg-${lastMessage.id}`;
    
    // 特别处理交易类型消息和图片类型消息，这类消息需要更多渲染时间
    if (lastMessage && (lastMessage.type === 'transaction' || lastMessage.type === 'image')) {
      // 使用多次尝试滚动确保复杂组件完全可见
      attemptScrollToLatest(true);
    } else {
      // 普通消息使用单次尝试
      attemptScrollToLatest();
    }
  }
});

// 处理图片点击
const handleImageTap = (imageUrl) => {
  if (typeof imageUrl === 'string' && imageUrl.trim() !== '') {
    currentImageUrl.value = imageUrl;
    isImageViewerVisible.value = true;
  }
};

// 处理图片加载完成事件
const handleImageLoaded = (data) => {
  console.log('图片加载完成:', data);
  
  // 如果是最新消息的图片加载完成，触发滚动更新
  if (messages.value.length > 0) {
    const lastMessage = messages.value[messages.value.length - 1];
    if (lastMessage.id === data.messageId) {
      console.log('最新消息的图片加载完成，更新滚动位置');
      // 使用短延迟确保图片已完全渲染并撑开容器
      setTimeout(() => {
        attemptScrollToLatest(true);
      }, 100);
    }
  }
};

// 处理输入框获得焦点
const handleInputFocus = () => {
  // 当输入框获得焦点时，自动滚动到底部
  nextTick(() => {
    scrollTop.value = 999999;
  });
};
</script>

<style lang="scss" scoped>
.chat-page {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--bg-page, #f8f8f8);
  
  // 导航栏
  &__nav {
    display: flex;
    align-items: center;
    height: v-bind("getPlatformStyle('nav-height')");
    padding: 0 var(--spacing-md, 16px);
    background-color: var(--bg-primary, #ffffff);
    border-bottom: 1px solid var(--border-color, #e5e5e5);
    
    &-back {
      width: var(--spacing-xl, 32px);
      height: var(--spacing-xl, 32px);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-primary, #333333);
    }
    
    &-title {
      flex: 1;
      text-align: center;
      font-size: var(--font-size-lg, 18px);
      font-weight: 500;
      color: var(--text-primary, #333333);
      position: relative;
      
      &::after {
        content: "";
        display: block;
        width: 30px;
        height: 3px;
        background: var(--color-primary, #FF6B35);
        position: absolute;
        bottom: -5px;
        left: 50%;
        transform: translateX(-50%);
        border-radius: var(--radius-sm, 2px);
      }
    }
    
    &-right {
      width: var(--spacing-xl, 32px);
      height: var(--spacing-xl, 32px);
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--text-primary, #333333);
    }
  }
  
  // 内容区域
  &__content {
    flex: 1;
    padding: var(--spacing-md, 16px);
    overflow-y: auto;
  }
  
  // 系统消息
  &__system-message {
    display: flex;
    justify-content: center;
    margin-bottom: var(--spacing-md, 16px);
  }
  
  &__system-bubble {
    max-width: 90%;
    padding: var(--spacing-xs, 4px) var(--spacing-md, 16px);
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: var(--radius-lg, 16px);
    font-size: var(--font-size-sm, 14px);
    color: var(--text-secondary, #666666);
    text-align: center;
  }
  
  // 消息列表
  &__messages {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md, 16px);
  }
  
  // 输入区域
  &__input-container {
    padding: 0;
    background-color: var(--bg-primary, #ffffff);
    border-top: 1px solid var(--border-color, #e5e5e5);
  }
  
  // 语音输入动画
  &__voice-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
  
  &__voice-wave {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 120px;
    height: 120px;
    background-color: var(--bg-primary, #ffffff);
    border-radius: var(--radius-circle, 50%);
    margin-bottom: var(--spacing-lg, 20px);
  }
  
  &__voice-bar {
    width: 4px;
    height: 20px;
    background-color: var(--color-primary, #FF6B35);
    margin: 0 2px;
    border-radius: var(--radius-sm, 2px);
    transition: height 0.15s ease-in-out;
  }
  
  &__voice-tip {
    color: var(--text-inverse, #ffffff);
    font-size: var(--font-size-md, 16px);
    margin-bottom: var(--spacing-sm, 10px);
  }
  
  &__voice-cancel {
    color: var(--color-primary, #FF6B35);
    font-size: var(--font-size-sm, 14px);
  }
  
  // 功能面板
  &__functions-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    background-color: var(--bg-primary, #ffffff);
    border-top: 1px solid var(--border-color, #e5e5e5);
    z-index: 100;
    transform: translateY(100%);
    transition: transform 0.3s ease-in-out;
    padding-bottom: env(safe-area-inset-bottom, 0);
    
    &--active {
      transform: translateY(0);
    }
  }
  
  &__functions-grid {
    display: flex;
    flex-wrap: wrap;
    padding: var(--spacing-md, 16px);
  }
  
  &__function-item {
    width: 25%;
    padding: var(--spacing-md, 16px);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  
  &__function-icon {
    width: 50px;
    height: 50px;
    border-radius: var(--radius-lg, 16px);
    background-color: var(--bg-secondary, #f5f5f5);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: var(--spacing-sm, 10px);
    font-size: var(--font-size-xl, 24px);
  }
  
  &__function-label {
    font-size: var(--font-size-sm, 14px);
    color: var(--text-primary, #333333);
  }
}

// 语音消息样式
.voice-message {
  display: flex;
  align-items: center;
  min-width: 120rpx;
  margin: var(--spacing-xs, 5px) 0;
  
  &__wrapper {
    display: flex;
    align-items: center;
    background-color: var(--bg-tertiary, #f0f0f0);
    padding: var(--spacing-xs, 10rpx) var(--spacing-sm, 20rpx);
    border-radius: var(--radius-md, 8px);
    min-width: 120rpx;
  }
  
  &__duration {
    margin: 0 var(--spacing-xs, 10rpx);
    color: var(--text-secondary, #666666);
    font-size: var(--font-size-sm, 28rpx);
  }
  
  &__waves {
    display: flex;
    align-items: center;
    height: 20rpx;
  }
  
  &__wave {
    width: 4rpx;
    height: 16rpx;
    background-color: var(--color-primary, #4CA7FF);
    margin: 0 2rpx;
    border-radius: var(--radius-xs, 2px);
    
    &:nth-child(2) {
      height: 20rpx;
    }
    
    &:nth-child(3) {
      height: 12rpx;
    }
  }
}

// 图片消息样式
.image-message {
  margin: var(--spacing-xs, 5px) 0;
  
  &__img {
    max-width: 400rpx;
    border-radius: var(--radius-md, 8px);
  }
}

/* 多端适配 */
/* #ifdef H5 */
.chat-page {
  &__content {
    height: calc(100vh - 110px);
  }
}
/* #endif */

/* #ifdef MP-WEIXIN */
.chat-page {
  &__content {
    height: calc(100vh - 200rpx);
  }
}
/* #endif */

/* #ifdef APP-PLUS */
.chat-page {
  &__content {
    height: calc(100vh - 110px);
  }
}
/* #endif */
</style>