<template>
  <view class="chat-input-bar">
    <!-- 左侧工具区：语音按钮和功能按钮 -->
    <view class="chat-input-bar__tools">
      <!-- 语音/键盘切换按钮 -->
      <view class="chat-input-bar__tool-button" @tap="toggleVoiceMode">
        <AppIcon :icon="isVoiceMode ? 'mobile-alt' : 'microphone-alt'" />
      </view>
      
      <!-- 更多功能按钮 -->
      <view class="chat-input-bar__tool-button" @tap="handleFunctionClick">
        <AppIcon icon="circle-plus" />
      </view>
    </view>
    
    <!-- 中间输入框 -->
    <view class="chat-input-bar__input-box">
      <input 
        v-if="!isVoiceMode"
        type="text"
        class="chat-input-bar__message-input"
        v-model="inputText"
        :placeholder="placeholder"
        confirm-type="send"
        @confirm="handleSend"
        @focus="handleFocus"
      />
      
      <!-- 语音输入提示 -->
      <view 
        v-else 
        class="chat-input-bar__voice-input" 
        @touchstart="startVoiceInput" 
        @touchend="stopVoiceInput"
        @touchmove="handleTouchMove"
      >
        <text>{{ voiceButtonText }}</text>
      </view>
    </view>
    
    <!-- 右侧发送按钮 -->
    <view 
      class="chat-input-bar__send-button" 
      :class="{ 'chat-input-bar__send-button--active': inputText.trim() }"
      @tap="handleSend"
    >
      <AppIcon icon="paper-plane" />
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import AppIcon from '@/components/common/AppIcon.vue';

// 定义组件属性
interface Props {
  /** 是否禁用输入 */
  disabled?: boolean
  /** 输入框占位文本 */
  placeholder?: string
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  placeholder: '请输入您的问题或记账指令'
});

// 定义组件事件
const emit = defineEmits<{
  /** 发送消息事件 */
  (e: 'send', text: string): void
  /** 开始录音事件 */
  (e: 'startVoice'): void
  /** 结束录音事件 */
  (e: 'endVoice', isCancelled: boolean): void
  /** 功能按钮点击事件 */
  (e: 'functionClick'): void
  /** 输入框获得焦点事件 */
  (e: 'focus'): void
}>();

// 组件状态
const inputText = ref('');
const isVoiceMode = ref(false);
const isRecording = ref(false);
const isCancelling = ref(false);
const startY = ref(0);

// 计算属性
const voiceButtonText = computed(() => {
  if (isRecording.value) {
    return isCancelling.value ? '松开手指，取消录音' : '松开发送';
  }
  return '按住说话';
});

// 切换语音/键盘输入模式
const toggleVoiceMode = (): void => {
  isVoiceMode.value = !isVoiceMode.value;
};

// 开始语音输入
const startVoiceInput = (e: TouchEvent): void => {
  if (props.disabled) return;
  
  isRecording.value = true;
  isCancelling.value = false;
  startY.value = e.touches[0]?.clientY ?? 0;
  
  // 通知父组件开始录音
  emit('startVoice');
  
  // 在实际实现中，这里应该调用录音API
  console.log('开始录音');
};

// 处理手指移动
const handleTouchMove = (e: TouchEvent): void => {
  if (!isRecording.value) return;
  
  // 计算手指垂直移动距离
  const moveY = e.touches[0]?.clientY ?? 0;
  const deltaY = startY.value - moveY;
  
  // 如果上滑超过50px，认为是取消录音的手势
  isCancelling.value = deltaY > 50;
};

// 结束语音输入
const stopVoiceInput = (): void => {
  if (!isRecording.value) return;
  
  const cancelled = isCancelling.value;
  isRecording.value = false;
  isCancelling.value = false;
  
  // 通知父组件结束录音，并传递是否取消的状态
  emit('endVoice', cancelled);
  
  if (!cancelled) {
    // 模拟发送语音文件
    // 在实际实现中，这里应该处理并发送真实的语音文件
    console.log('发送语音文件');
  } else {
    console.log('取消录音');
  }
};

// 处理发送按钮点击
const handleSend = (): void => {
  if (isVoiceMode.value) {
    return; // 语音模式下不处理发送
  }
  
  const text = inputText.value.trim();
  if (text) {
    emit('send', text);
    inputText.value = ''; // 清空输入框
  } else {
    // 如果输入为空，则触发功能面板
    emit('functionClick');
  }
};

// 处理"+"按钮点击
const handleFunctionClick = (): void => {
  emit('functionClick');
};

// 处理输入框获得焦点
const handleFocus = (): void => {
  emit('focus');
};
</script>

<style lang="scss" scoped>
.chat-input-bar {
  display: flex;
  align-items: center;
  padding: var(--spacing-xs, 8px);
  background-color: var(--bg-primary, #ffffff);
  border-top: 1px solid var(--border-color, #e5e5e5);
  width: 100%;
  box-sizing: border-box;
  
  &__tools {
    display: flex;
    align-items: center;
  }
  
  &__tool-button {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-circle, 50%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary, #666666);
    font-size: var(--font-size-md, 18px);
    margin-right: var(--spacing-xs, 5px);
    background-color: var(--bg-secondary, #f5f5f5);
    
    &:active {
      background-color: rgba(0,0,0,0.05);
    }
  }
  
  &__input-box {
    flex: 1;
    display: flex;
    align-items: center;
    background-color: var(--bg-secondary, #f5f5f5);
    border-radius: var(--radius-lg, 20px);
    padding: var(--spacing-xs, 8px) var(--spacing-sm, 15px);
    margin: 0 var(--spacing-xs, 8px);
  }
  
  &__message-input {
    width: 100%;
    border: none;
    background: transparent;
    font-size: var(--font-size-md, 16px);
    outline: none;
    color: var(--text-primary, #333333);
    
    &::placeholder {
      color: var(--text-tertiary, #999999);
    }
  }
  
  &__voice-input {
    width: 100%;
    text-align: center;
    color: var(--text-secondary, #666666);
    font-size: var(--font-size-sm, 14px);
  }
  
  &__send-button {
    width: 36px;
    height: 36px;
    border-radius: var(--radius-circle, 50%);
    background-color: var(--bg-secondary, #f5f5f5);
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-secondary, #666666);
    transition: all 0.2s ease;
    
    /* 当输入框有内容时的样式 */
    &--active {
      background-color: var(--color-primary, #FF6B35);
      color: var(--text-inverse, white);
      box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3);
    }
    
    &:active {
      opacity: 0.8;
    }
  }
}

/* 多端适配 */
/* #ifdef MP-WEIXIN */
.chat-input-bar {
  &__tool-button {
    width: 32px;
    height: 32px;
  }
  
  &__send-button {
    width: 32px;
    height: 32px;
  }
  
  &__input-box {
    padding: var(--spacing-xs, 6px) var(--spacing-sm, 12px);
  }
}
/* #endif */

/* #ifdef H5 */
.chat-input-bar {
  padding-bottom: calc(var(--spacing-xs, 8px) + env(safe-area-inset-bottom, 0));
}
/* #endif */
</style> 