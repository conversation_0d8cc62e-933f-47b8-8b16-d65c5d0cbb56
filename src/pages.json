{
  "pages": [ // pages数组中第一项表示应用启动页，参考：https://uniapp.dcloud.io/collocation/pages
    {
      "path": "pages/welcome/index",
      "style": {
        "navigationBarTitleText": "欢迎使用",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/home/<USER>",
      "style": {
        "navigationBarTitleText": "首页",
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/auth/login",
      "style": {
        "navigationBarTitleText": "登录",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/auth/gesture",
      "style": {
        "navigationBarTitleText": "手势密码",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/profile/basic-setup",
      "style": {
        "navigationBarTitleText": "完善个人资料",
        "navigationStyle": "custom" // 与原型一致，自定义导航
      }
    },
    // {
    // 	"path": "pages/profile/ai-setup",
    // 	"style": {
    // 		"navigationBarTitleText": "AI个性化设置",
    // 		"navigationStyle": "custom"
    // 	}
    // },
    {
      "path": "pages/ai-personality/index",
      "style": {
        "navigationBarTitleText": "定制你的助手",
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/guide/index",
      "style": {
        "navigationBarTitleText": "使用指南",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/auth/wechat-auth",
      "style": {
        "navigationBarTitleText": "微信授权"
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationBarTitleText": "uni-app"
      }
    },
    {
      "path": "pages/transaction/list",
      "style": {
        "navigationBarTitleText": "账单",
        "navigationStyle": "custom" // 账单页通常也自定义导航栏
      }
    },
    {
      "path": "pages/transaction/record",
      "style": {
        "navigationBarTitleText": "记一笔",
        "navigationStyle": "custom" // 使用自定义导航栏
      }
    },
    {
      "path": "pages/analysis/index",
      "style": {
        "navigationBarTitleText": "统计",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/profile/index",
      "style": {
        "navigationBarTitleText": "我的",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/chat/index",
      "style": {
        "navigationBarTitleText": "AI助手",
        "navigationStyle": "custom",
        "enablePullDownRefresh": false
      }
    },
    {
      "path": "pages/dev/ucharts-test",
      "style": {
        "navigationBarTitleText": "uCharts测试页面",
        "navigationStyle": "default" // 使用默认导航栏，避免自定义导航栏干扰测试
      }
    },
    {
      "path": "pages/dev/chart-test",
      "style": {
        "navigationBarTitleText": "图表测试页面",
        "navigationStyle": "default" // 使用默认导航栏，避免自定义导航栏干扰测试
      }
    },
    {
      "path": "pages/dev/icon-test",
      "style": {
        "navigationBarTitleText": "图标测试页面",
        "navigationStyle": "default" // 使用默认导航栏，避免自定义导航栏干扰测试
      }
    }
  ],
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "AI记账",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#F5F5F5",
    "app-plus": {
      "bounce": "none",
      "optimization": {
        "subNVue": "none"
      },
      "softinputNavBar": "none",
      "softinputMode": "adjustPan"
    }
  },
  "tabBar": {
    "color": "#999999",
    "selectedColor": "#FF6B35",
    "backgroundColor": "#FFFFFF",
    "borderStyle": "black",
    "list": [
      {
        "pagePath": "pages/home/<USER>",
        "text": "首页",
        "iconPath": "static/icons/tabbar/home.png",
        "selectedIconPath": "static/icons/tabbar/home-active.png"
      },
      {
        "pagePath": "pages/analysis/index",
        "text": "统计",
        "iconPath": "static/icons/tabbar/analysis.png",
        "selectedIconPath": "static/icons/tabbar/analysis-active.png"
      },
      {
        "pagePath": "pages/transaction/add",
        "text": "记账",
        "iconPath": "static/icons/tabbar/record.png",
        "selectedIconPath": "static/icons/tabbar/record-active.png"
      },
      {
        "pagePath": "pages/chat/index",
        "text": "AI助手",
        "iconPath": "static/icons/tabbar/chat.png",
        "selectedIconPath": "static/icons/tabbar/chat-active.png"
      },
      {
        "pagePath": "pages/profile/index",
        "text": "我的",
        "iconPath": "static/icons/tabbar/profile.png",
        "selectedIconPath": "static/icons/tabbar/profile-active.png"
      }
    ]
  },
  "easycom": {
    "autoscan": true,
    "custom": {
      "^App(.*)": "@/components/common/App$1.vue",
      "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue",
      "^u-icon$": "uview-plus/components/u-icon/u-icon.vue"
    }
  },
  "condition": {
    "current": 0,
    "list": [
      {
        "name": "welcome",
        "path": "pages/welcome/index"
      },
      {
        "name": "guide",
        "path": "pages/guide/index"
      },
      {
        "name": "login",
        "path": "pages/auth/login"
      },
      {
        "name": "register",
        "path": "pages/auth/register"
      },
      {
        "name": "home",
        "path": "pages/home/<USER>"
      }
    ]
  }
}