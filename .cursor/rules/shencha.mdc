---
description: 
globs: 
alwaysApply: false
---
# 项目代码审查规则（AI 记账应用 - 优化版）

> **版本：** 1.1
> **审查目标：**
> 在每次 `git commit` 之前，对本次开发或修改的代码进行全面审查，确保代码遵循项目规范，保持高质量的代码库。

## 一、审查前提

| 角色       | 说明                                   |
| ---------- | -------------------------------------- |
| 审查者     | <PERSON> (AI Agent)                      |
| 监督者     | 叶同学（项目负责人，0 编程经验）        |
| 文档依据   | `架构设计文档.md`<br>`开发规范与指南-基础篇.md`<br>`开发规范与指南-高级篇.md`<br>`UI界面规则文档.md`<br>相关页面的 UI 原型文件 |

## 二、审查流程

1. 叶同学告知本次提交涉及的页面、组件或功能
2. Claude 仔细阅读审查规则
3. Claude 审查涉及的所有代码文件
4. <PERSON> 生成审查报告，指出不符合规范处，并提出**不影响现有 UI 和功能**的优化建议
5. 叶同学确认优化建议
6. Claude 应用优化建议到代码中
7. 优化后的代码再次验证确保 UI 和功能无损

## 三、核心原则

- **功能优先与 UI 稳定：** 优化建议**不得**破坏现有功能或导致 UI 效果与原型/当前稳定版产生负面偏差
- **规范至上：** 所有代码**必须**严格遵守项目文档规范
- **简洁易懂：** 代码应简洁、清晰、易于理解，避免不必要的复杂度
- **可维护性：** 结构合理，模块划分清晰，便于未来修改和扩展
- **性能意识：** 避免明显的性能陷阱（如不必要的循环、过度响应式依赖等）

## 四、审查清单与优化要求

### 1. Vue 组件（`.vue` 文件）

#### 1.1 `<template>` 部分

| 检查项         | 说明/优化建议 |
| -------------- | ------------- |
| 结构清晰与语义化 | HTML 结构层次分明，语义化标签，嵌套合理 |
| 指令使用        | `v-if`/`v-else-if`/`v-else`/`v-show` 用法得当，`v-for` 必须绑定唯一且稳定的 `:key`，`v-bind`/`v-on` 简写统一 |
| Props 传递      | 子组件 props 名称和值正确，布尔类型 props 传递规范 |
| 事件监听        | 事件名准确，处理函数存在 |
| 插槽（Slots）   | 父组件传递内容符合预期 |
| 图标组件使用     | **必须使用 `<AppIcon>` 组件**，传递正确的 uView Plus 图标名、大小和颜色（必须用CSS变量） |
| 分类图标使用     | 分类图标必须使用 `<CategorySelector>` 组件 |
| 代码格式        | 符合项目格式化要求（缩进、换行等） |

#### 1.2 `<script setup lang="ts">` 部分

| 检查项         | 说明/优化建议 |
| -------------- | ------------- |
| 导入（Imports） | 导入顺序规范（Vue相关 -> 组件 -> API/工具），无未使用导入，路径用 `@/` |
| Props 定义      | 明确 TypeScript 类型，可选 props 提供 `default`（对象/数组用工厂函数），名称用 `camelCase` |
| Emits 定义      | 所有事件声明，事件名用统一风格，事件载荷类型校验 |
| 响应式变量      | 命名清晰，`camelCase`，`ref`/`reactive` 用法正确，`computed` 有实际依赖 |
| 函数/方法       | 命名清晰，职责单一，参数/返回值有类型，可提取公共逻辑 |
| 生命周期钩子     | 钩子用法与阶段匹配，`onUnmounted` 清理副作用 |
| watch/watchEffect | 侦听数据源正确，回调高效，`deep`/`immediate` 用法正确 |
| 类型使用        | 恰当用项目类型，避免 `any` |
| 注释           | 复杂逻辑有注释，内容准确 |
| 代码风格        | 遵循项目标准，魔法数字/硬编码字符串提取为常量 |
| 废弃代码        | 删除注释掉的大段代码或未用变量/函数 |

#### 1.3 `<style lang="scss" scoped>` 部分

| 检查项         | 说明/优化建议 |
| -------------- | ------------- |
| scoped 属性     | 所有组件样式都用 `scoped`（特殊全局样式除外） |
| CSS 变量       | **必须使用 `var(--variable-name, fallbackValue)` 引用变量**，禁止硬编码颜色/像素值（1px 边框等原子值除外） |
| 图表组件特殊规则 | 对于无法直接识别CSS变量的第三方组件（如QiunDataCharts），保留通过JS工具函数获取CSS变量实际值的代码，不要改为直接使用CSS变量 |
| 命名规范（BEM） | 类名用 BEM 或项目约定规范 |
| 样式简洁性     | 无冗余/重复样式，合理用 SCSS 特性，避免过度嵌套 |
| !important     | 原则上禁止，除非覆盖第三方库样式且无其他办法，并需注释说明，优先用 `u-*` 工具类 |
| 浏览器前缀      | 一般无需手写，uni-app 编译器自动处理 |
| 魔法数字        | 避免未解释的魔法数字 |

### 2. TypeScript/JavaScript 逻辑文件（`.ts`, `.js`）

| 检查项         | 说明/优化建议 |
| -------------- | ------------- |
| 模块化         | 文件/函数职责单一，合理用 ES 模块导入导出 |
| 命名规范       | 变量/函数用 `camelCase`，类/接口/类型用 `PascalCase` |
| 类型安全       | 函数参数/返回值/变量有类型，充分用 TypeScript 特性，避免 `any` |
| 错误处理       | 可能出错操作有 `try...catch` 或 `.catch()`，错误处理友好 |
| 异步处理       | `async/await` 或 `Promise` 用法正确，并发/竞态条件处理得当 |
| Pinia Store    | 必须使用 Composition API，State 扁平，Getters 派生状态，Actions 处理异步和修改 state |
| API 调用       | API 函数封装良好，处理 VITE_USE_MOCK_DATA 环境变量判断，Mock API 结构与真实 API 一致 |
| 本地存储       | 使用 `src/utils/storage.ts` 中的工具函数，遵循键名规范，禁止直接使用 `localStorage` 或原生 API |
| 工具函数       | 纯函数，无副作用，通用、可复用 |
| 注释和文档     | 复杂算法/核心业务/公共函数有 JSDoc 或注释 |
| 废弃代码       | 删除不再使用的函数、变量、导入 |

### 3. 图标使用专项检查

| 检查项         | 说明/优化建议 |
| -------------- | ------------- |
| 使用统一组件    | **必须使用全局注册的 `<AppIcon>` 组件**渲染图标，该组件内部封装了 uView Plus 的 `<u-icon>` |
| 图标名称        | 使用 uView Plus 图标库中有效的图标名称 |
| 图标颜色        | 必须使用 CSS 变量控制颜色，如 `color="var(--color-primary, #FF6B35)"` |
| 图标尺寸        | 按UI规范使用统一的图标尺寸 |
| 类别图标        | 业务类别图标必须使用 `<CategorySelector>` 组件 |
| 禁止其他方式    | 禁止使用 `<i>` 标签、CDN 图标、直接使用 `<u-icon>` 或其他第三方图标库 |
| easycom 配置    | 确认 `pages.json` 中 easycom 规则正确配置了 uView Plus |
| 样式引入        | 确认 `App.vue` 中已正确引入 uView Plus 样式 |

### 4. 配置文件和项目结构

| 检查项         | 说明/优化建议 |
| -------------- | ------------- |
| pages.json     | 页面路径、导航栏、tabBar 配置正确，条件编译按需使用 |
| manifest.json  | 权限、图标等配置正确 |
| 目录结构       | 新文件放在规范指定目录，如位置不当，提出移动建议 |
| 依赖管理       | 无不必要/重复依赖，依赖版本无冲突风险 |

## 五、审查报告格式

---

### 审查范围
> [列出本次审查涉及的主要文件或功能模块]

### 审查日期
> [YYYY-MM-DD]

### 总体评价
> [对本次代码的整体质量和规范性给出简要评价]

---

### 1. 存在的问题与优化建议

| 文件路径 | 问题描述 | 规范依据 | 风险 | 优化建议 |
| -------- | -------- | -------- | ---- | -------- |
| [文件路径] | [问题描述] | [规范出处] | [可能风险] | [代码修改建议] |

---

### 2. 确认已符合规范的亮点（可选）

> - 例如：组件的 Props 定义清晰，类型准确
> - 例如：所有 API 调用都正确使用了 try...catch 错误处理

---

### 3. 需要叶同学确认或讨论的事项

> - 例如：某功能逻辑较复杂，是否需要进一步拆分
> - 例如：某文件不再使用，请确认是否可以移除

---
