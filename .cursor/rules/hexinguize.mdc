---
description: 
globs: 
alwaysApply: true
---
核心开发规则 (Core Development Rules - MUST ALWAYS BE FOLLOWED)
文档层级：
架构设计文档.md -> 开发规范与指南-基础篇.md/高级篇.md -> UI界面规则文档.md

## 1. 文件存放 (File Placement)
- **页面 (`.vue`)**: `src/pages/[模块名]/[页面名].vue`
- **通用组件 (`App*`)**: `src/components/common/`
- **业务组件**: `src/components/business/` 或页面下的 `components/`
- **API 调用**: `src/api/[模块名].ts` (推荐TypeScript)
- **API Mocks**: `src/api/mocks/[模块名].mock.ts` (必须 (MUST) 与API文件对应)
- **Hooks**: `src/hooks/useXxx.ts` (必须使用use前缀)
- **状态 (Pinia)**: `src/stores/[模块名].store.ts` (必须 (MUST) 使用.store后缀)
- **样式变量**: `src/assets/styles/variables.scss` (全局CSS变量必须在此定义)
- **样式工具类**: `tools/style/scripts/_utilities.scss` (所有!important工具类必须在此定义)
- **工具函数**: `src/utils/` (通用功能模块)
- **多端适配**: `src/utils/platform.ts` (处理平台差异的核心文件)

## 2. 命名规范 (Naming Conventions)
- **组件文件/名称**:
  - **通用组件**: 必须 (MUST) 使用 PascalCase 格式，并以 App 前缀开头 (如 `AppButton.vue`, `AppIcon.vue`)。唯一例外是 `CategorySelector.vue`
  - **业务组件**: 必须 (MUST) 使用 PascalCase 格式，无需 App 前缀 (如 `TransactionItem.vue`)
  - **页面私有组件**: 必须 (MUST) 使用 PascalCase 并加页面前缀 (如 `BudgetSetupChart.vue`)
- **API/JS/TS文件**: 必须 (MUST) 使用 camelCase (如 `apiService.ts`, `dateUtils.ts`)
- **Store文件**: 必须 (MUST) 使用 camelCase 并添加.store后缀 (如 `user.store.ts`, `transaction.store.ts`)
- **CSS类名**: 必须 (MUST) 使用以下之一:
  - BEM命名法: `block__element--modifier` (如 `transaction-card__amount--positive`)
  - 页面前缀: `page-name__element--modifier` (如 `budget-setup__header`)
  - 禁止 (FORBID) 使用通用类名 (如 `.container`, `.title`, `.wrapper`, `.content`)
- **Hooks文件**: 必须 (MUST) 使用 `useXxx.ts` 命名，遵循Vue3 Composition API惯例

## 3. 组件使用 (Component Usage)
- **优先使用通用组件**: 必须 (MUST) 优先使用 `src/components/common/` 下的 `App*` 通用组件
- **保持一致性**: 必须 (MUST) 确保在不同页面中对同一通用组件的使用方式和传入的props保持一致
- **禁止重复创建**: 禁止 (FORBID) 创建与通用组件功能重复的业务或私有组件，除非通用组件无法满足需求
- **核心通用组件列表**:
  - `AppButton`: 按钮组件，提供 size, type, disabled, loading 等属性
  - `AppCard`: 卡片容器，具有统一的圆角、阴影和内边距
  - `AppIcon`: 图标组件，封装 uView Plus 的 u-icon
  - `AppInput`: 输入框组件，支持各种类型输入
  - `AppModal`: 模态框组件
  - 其他 `App*` 前缀组件

## 4. 图标使用 (Icon Usage)
- **通用图标**: 必须 (MUST) 通过全局注册的 `<AppIcon>` 组件使用 uView Plus 图标。该组件内部封装了 uView Plus 的 `<u-icon>`。
- **类别图标**: 对于业务类别图标，必须 (MUST) 使用 `<CategorySelector>` 组件，确保类别图标的一致性和交互体验统一。其内部图标也应遵循 uView Plus 规范。
- **来源与加载**: uView Plus 图标库通常通过 `uni_modules` 引入或通过 NPM 安装 `uview-plus` 包。其组件（包括图标）一般通过 uni-app 的 `easycom` 机制实现按需编译和自动引入，无需在每个页面或组件中手动导入和注册。
- **多端适配**: `<AppIcon>` 的多端平台适配能力主要依赖其内部封装的 uView Plus `<u-icon>` 组件自身的能力。
- **禁止其他方式**: 禁止 (FORBID) 使用 `<i>` 标签、CDN、其他第三方图标库，或直接在 `<AppIcon>` 外部使用 `<u-icon>`（除非有特殊、合理且经过确认的理由）。
- **图标属性标准** (以 `<AppIcon>` 封装的 `<u-icon>` 为准):
  - `icon` (或 `name`): uView Plus 图标名称 (如 `photo`, `setting`, `weixin-fill`)。
  - `size`: 支持具体数字 (单位px) 或 uView Plus 预设的字符串值 (如 `'xl'`, `'lg'`, `'22px'`)。
  - `color`: 必须 (MUST) 使用CSS变量，如 `"var(--color-primary)"`。
  - 其他 `<u-icon>` 支持的属性如 `bold`, `label`, `label-pos`, `label-size`, `label-color` 等可通过 `<AppIcon>` 透传。

**标准用法示例**:
```vue
<template>
  <!-- 显示 uView Plus 的 "photo" 图标，大号尺寸，使用 CSS 变量 -->
  <AppIcon icon="photo" size="lg" color="var(--color-primary)" />

  <!-- 显示 uView Plus 的 "setting" 图标，使用默认尺寸和颜色 -->
  <AppIcon icon="setting" />

  <!-- 显示 uView Plus 的 "weixin-fill" 图标 (通常表示填充样式)，添加自定义 class -->
  <AppIcon icon="weixin-fill" custom-class="social-icon" />
  
  <!-- 类别选择器组件 (其内部图标数据源应为 uView Plus 图标名) -->
  <CategorySelector 
    :modelValue="selectedCategoryId" 
    @update:modelValue="handleSelect" 
  />

  <!-- 在账单项中根据 categoryId 显示类别图标 (假设 categoryInfo.icon 是 uView Plus 图标名) -->
  <div class="transaction-item">
    <AppIcon 
      :icon="categoryInfo.icon" 
      :style="{ color: categoryInfo.color }" 
    />
    <span>{{ categoryInfo.name }}</span>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue';
import { useCategoryStore } from '@/stores/category.store';
// AppIcon 和 CategorySelector 通常已全局注册或通过 easycom 自动引入
// 无需显式导入
// import AppIcon from '@/components/common/AppIcon.vue';
// import CategorySelector from '@/components/business/CategorySelector.vue';

const selectedCategoryId = ref('cat-1');
const transactionCategoryId = ref('cat-food');

const categoryStore = useCategoryStore();
// 获取分类信息用于显示
const categoryInfo = computed(() => {
  // 假设 getCategoryById 返回的对象中 icon 属性已是 uView Plus 图标名
  return categoryStore.getCategoryById(transactionCategoryId.value) || 
    { icon: 'question-circle-fill', color: 'gray', name: '未知' }; // uView Plus 风格的默认图标
});

function handleSelect(categoryId: string) {
  selectedCategoryId.value = categoryId;
}
</script>
```

## 5. CSS样式 (Styling)
- **作用域**: 必须 (MUST) 使用 `<style lang="scss" scoped>`
- **CSS变量**: 必须 (MUST) 使用 `var(--variable-name, fallbackValue)` 引用全局CSS变量
- **提供后备值**: 使用 `var()` 时，必须 (MUST) 提供一个有效的后备值，例如 `color: var(--color-primary, #FF6B35);`
- **禁止硬编码**: 禁止 (FORBID) 直接写入具体像素值或颜色码，应使用CSS变量
- **禁止修改全局变量**: 禁止 (FORBID) 在组件内重定义全局CSS变量
- **样式权重控制**: 选择器嵌套不超过3层
- **BEM命名示例**:
  ```scss
  .transaction-card {
    /* 块(Block) */
    &__header {
      /* 元素(Element) */
    }
    
    &__amount {
      /* 元素(Element) */
      &--positive {
        /* 修饰符(Modifier) */
        color: var(--color-success, #4CAF50);
      }
      
      &--negative {
        /* 修饰符(Modifier) */
        color: var(--color-error, #F44336);
      }
    }
  }
  ```
- **!important规则**: 原则上禁止 (FORBID) 使用 `!important`，但有以下例外情况:
  - 处理第三方组件样式覆盖
  - 处理原生组件样式
  - 处理跨组件样式冲突的紧急修复
  - 必须 (MUST) 优先使用 `tools/style/scripts/_utilities.scss` 中定义的工具类（如 `.u-hide`、`.u-show` 等）
- **暗黑模式适配**:
  - 颜色变量必须支持暗黑模式切换
  - 暗黑模式变量必须在 `variables.scss` 中定义
  - 组件必须使用CSS变量，以支持自动适配暗黑模式
- ✅ **字体**: 字体大小必须 (MUST) 使用CSS变量，遵循规范中定义的字号系统

## 6. 代码风格 (Code Style)
- **脚本设置**: 必须 (MUST) 使用 `<script setup lang="ts">`
- **TypeScript**: 必须 (MUST) 使用TypeScript，为组件props和函数参数提供类型声明
- **导入顺序**: 必须 (MUST) 遵循如下顺序:
  ```javascript
  // 1. Vue核心库
  import { ref, computed, onMounted } from 'vue';
  
  // 2. Pinia状态
  import { useUserStore } from '@/stores/user.store';
  
  // 3. 组件导入
  import AppButton from '@/components/common/AppButton.vue';
  import TransactionList from '@/components/business/TransactionList.vue';
  
  // 4. Hooks
  import { useAuth } from '@/hooks/useAuth';
  
  // 5. API和工具
  import { getTransactions } from '@/api/transaction';
  import { formatDate } from '@/utils/date';
  ```
- **Props定义**: 使用TypeScript接口或类型定义明确props类型:
  ```typescript
  interface Props {
    category: string;
    amount: number;
    date?: string; // 可选prop
  }
  
  const props = defineProps<Props>();
  ```

## 7. 防御性编程 (Defensive Programming)
- **可选链**: 必须 (MUST) 使用 `?.` 安全访问可能不存在的属性
- **空值合并**: 必须 (MUST) 使用 `??` 提供默认值（优于 `||`），正确处理 `0` 和空字符串
- **数组安全操作**: 必须 (MUST) 在操作数组前检查其存在性和长度，或使用可选链
- **异步错误处理**: 必须 (MUST) 使用 `try...catch` 或 `.catch()` 处理异步错误，并提供用户友好的错误反馈
- **禁止不安全渲染**: 禁止 (FORBID) 使用 `v-html` 渲染不可信内容
- **异步清理**: 必须 (MUST) 在组件卸载时清理异步操作和定时器

**防御性编程示例**:
```javascript
// 安全访问嵌套属性
const userName = user?.profile?.name ?? '未知用户';

// 数组安全操作
const items = list?.filter(item => item?.isActive) ?? [];

// 异步错误处理
const fetchData = async () => {
  try {
    const data = await api.getData();
    return data;
  } catch (error) {
    console.error('获取数据失败', error);
    return []; // 提供合理的默认值
  }
};

// 组件卸载时清理
onMounted(() => {
  const timer = setInterval(() => {
    // 定时操作
  }, 1000);
  
  // 确保组件卸载时清理定时器
  onUnmounted(() => {
    clearInterval(timer);
  });
});
```

## 8. 状态管理 (State Management)
- **Pinia**: 必须 (MUST) 使用Pinia，禁止 (FORBID) 使用Vuex
- **Store命名**: 必须 (MUST) 使用 `[模块名].store.ts` 格式命名Store文件
- **模块化**: 必须 (MUST) 按业务模块拆分store，清晰分离状态、getters和actions
- **状态隔离**: 页面级状态应使用局部状态或独立的状态模块，避免滥用全局状态
- **Store结构**: 必须 (MUST) 遵循以下结构:
  ```typescript
  // src/stores/user.store.ts
  import { defineStore } from 'pinia';
  
  export const useUserStore = defineStore('user', {
    // 状态定义
    state: () => ({
      userInfo: null,
      isLoggedIn: false
    }),
    
    // 计算属性
    getters: {
      userName: (state) => state.userInfo?.name ?? '未登录用户'
    },
    
    // 异步操作和修改状态的方法
    actions: {
      async login(credentials) {
        try {
          const result = await api.login(credentials);
          this.userInfo = result.user;
          this.isLoggedIn = true;
          return true;
        } catch (error) {
          console.error('登录失败', error);
          return false;
        }
      }
    }
  });
  ```

## 9. 多端适配 (Multi-platform Adaptation)
- **条件编译**: 使用 `#ifdef` / `#endif` 处理平台特有代码
- **样式适配**: 使用CSS变量和响应式单位(rpx)，结合平台特定媒体查询
- **端适配器**: 必须 (MUST) 使用 `utils/platform.ts` 中的平台适配函数获取特定样式或行为
- **统一入口**: 所有平台差异处理必须 (MUST) 通过 `platform.ts` 中的适配器函数进行，禁止在业务代码中直接判断平台
- **图标适配**: 确保 `AppIcon` 组件正确处理平台差异 (Web/App使用SVG，小程序自动降级为图片)
- **高度标准**: 遵循UI规范中的平台特定高度:
  - iOS: 88px (包含状态栏)
  - Android: 56dp
  - 小程序: 90px
  - H5: 60px

## 10. API与Mock数据与本地持久化数据库 (API, Mock Data & Local LedgerDB)
- **环境变量检查**: 在 `src/api/*.ts` 文件中的每个API函数开头，必须 (MUST) 检查环境变量 `import.meta.env.VITE_USE_MOCK_DATA`
- **条件返回**:
  - 若 `VITE_USE_MOCK_DATA === 'true'`，必须 (MUST) 导入并返回对应的 `src/api/mocks/*.mock.ts` 中的模拟数据
  - 否则执行真实的 `utils/request.ts` 网络请求
- **Mock结构**: Mock数据结构必须 (MUST) 严格符合API接口文档定义的响应体格式
- **路径规范**: 所有API路径必须包含版本号: `/api/v1/endpoint`
- **RESTful风格**: 使用适当的HTTP方法表达资源操作 (GET查询、POST创建、PUT更新、DELETE删除)
- **本地持久化数据库（localLedgerDB）协同**:
  - 推荐Mock实现内部使用`uni.setStorageSync/uni.getStorageSync`进行本地数据持久化，适配H5/小程序/APP多端。
  - 本地存储key命名规范：`transactions`（交易）、`categories`（分类）、`assets`（账户）等。
  - Mock模式下（`VITE_USE_MOCK_DATA=true`），API优先走Mock实现，Mock可用本地存储作为数据底座；关闭Mock时API全部走真实后端接口，本地存储不参与主数据流。
- **API与Mock命名规范**：API文件、Mock文件一一对应，命名、导出方式保持一致，便于切换和维护。
- **注意事项**：本地存储仅用于开发、测试和小型应用，不建议存放敏感信息或大数据量，数据结构需与接口文档保持一致。

**示例**:
```typescript
// src/api/auth.ts
import { request } from '@/utils/request';
import authMock from './mocks/auth.mock'; // 导入Mock文件
import type { LoginCredentials, LoginResponseData } from '@/types/api';

const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // 读取环境变量

/**
 * 用户登录
 * @param credentials - 登录凭证
 * @returns Promise<LoginResponseData>
 */
export function login(credentials: LoginCredentials): Promise<LoginResponseData> {
  if (useMock) {
    // 如果启用Mock，返回Mock数据（Mock内部可用本地存储持久化数据）
    return authMock.login(credentials);
  }
  // 否则，发起真实API请求
  return request.post('/api/v1/auth/login', credentials);
}
```

## UI检查项
- ✅ **分类数据源 (Category Data Source)**:
  - 页面或组件中所有需要**显示或选择**收支分类的地方，其分类数据（名称、图标、颜色等）**必须 (MUST)** 从 **Pinia 的 Category Store (`src/stores/category.store.ts`)** 中获取
  - 必须 (MUST) 使用Store提供的Getters (如 `expenseCategories`, `incomeCategories`, `getCategoryById`) 来获取数据
  - 禁止 (FORBID) 在组件或页面内部硬编码分类列表或从非Store的静态文件中导入分类数据
  - 交易记录相关的模拟数据 (`src/api/mocks/`) 中的 `categoryId` 或 `categoryName` 必须 (MUST) 使用Category Store中定义的有效值
- ✅ **标题栏 (Nav Bar)**: 高度必须符合平台标准:
  - iOS: 88px (包含状态栏)
  - Android: 56dp
  - 小程序: 90px
  - H5: 60px
- ✅ **颜色**: 所有颜色值必须 (MUST) 使用CSS变量，如 `var(--color-primary, #FF6B35)`
- ✅ **卡片 (Card)**: 必须 (MUST) 使用 `<AppCard>` 组件，确保一致的外观和行为
- ✅ **按钮 (Button)**: 必须 (MUST) 使用 `<AppButton>` 组件，遵循尺寸和样式规范
- ✅ **间距 (Spacing)**: 页面边距、元素间距必须 (MUST) 使用CSS变量和统一的间距系统
- ✅ **图标 (Icon)**:
  - 通用图标必须 (MUST) 全部通过 `<AppIcon>` 组件渲染
  - 业务类别图标的显示/选择必须 (MUST) 通过 `<CategorySelector>` 组件处理
  - `<AppIcon>` 的props (icon/name, size, color等) 必须 (MUST) 正确传递，颜色必须 (MUST) 使用CSS变量
- ✅ **字体**: 字体大小必须 (MUST) 使用CSS变量，遵循规范中定义的字号系统
- ✅ **文件放置与命名**: 新文件必须 (MUST) 按照规定的目录结构和命名规范放置
- ✅ **暗黑模式**: 必须 (MUST) 使用CSS变量以支持暗黑模式，遵循规范中的暗黑模式适配指南
