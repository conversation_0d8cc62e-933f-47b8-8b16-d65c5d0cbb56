# AI记账应用开发步骤指南 (无编程经验用户专用)

+----------------------------------+
|       项目文档关系图              |
+----------------------------------+
|                                  |
|  架构设计文档.md                  |
|  (整体架构、技术选型、目录结构)    |
|          |                       |
|          v                       |
|  开发规范与指南-基础篇.md          |
|  (开发规范、编码标准、命名约定)    |
|          |                       |
|          v                       |
|  开发规范与指南-高级篇.md          |
|  (高级模式、优化策略、最佳实践)    |
|          |                       |
|          v                       |
|  UI界面规则文档.md                |
|  (UI设计、组件样式、布局规范)      |
|                                  |
+----------------------------------+

文档主要职责:
- 架构设计文档: 定义系统架构和技术栈选择，是最高层次的设计指导
- 开发规范与指南-基础篇: 规定具体的开发实践和编码标准，是开发过程的实施指南
- 开发规范与指南-高级篇: 提供高级模式、优化策略和最佳实践
- UI界面规则文档: 详细说明UI组件和视觉规范，是界面实现的参考标准

## 使用指南

本文档专为没有编程经验的用户设计，通过在Cursor中与AI大模型配合完成AI记账应用的开发。您**无需**编写代码，只需按照以下步骤操作，让AI为您生成所需的全部代码。

### 🚀 关键准备工作

1. **确保四个核心文档在Cursor中可访问**：
   - 架构设计文档.md
   - 开发规范与指南-基础篇.md
   - 开发规范与指南-高级篇.md 
   - UI界面规则文档.md

2. **使用Cursor的AI聊天功能**：
   - 在指令中包含相关文档：使用`@文档名`语法让AI查看文档内容
   - 例如：`@UI界面规则文档.md 请帮我创建...`

3. **参考《Claude开发指令模板.md》**：
   - 使用标准化指令，按需修改尖括号内的参数
   - 确保引用正确的文档章节（如第二节、第五节等）

4. **理解项目结构**：
   - 所有代码保持相对路径引用（例如：`src/pages/xxx`）
   - 所有页面位于`src/pages/`目录下
   - 所有组件位于`src/components/`目录下
   - API调用位于`src/api/`目录下

## 💎 Cursor中高效使用AI的技巧

### 1. 高效交流方式

- **使用@引用文档**：`@架构设计文档.md 请根据这个文档...`
- **分步骤提问**：每次只给AI一个小任务，而不是一次请求多个功能
- **提供明确的文件路径**：始终使用准确的相对路径（如`src/components/common/AppButton.vue`）
- **@文件名.扩展名**：使用此语法让AI直接查看现有文件
- **清晰的需求描述**：简短但包含所有必要信息，明确指出要参考哪个UI原型页面（如 `prototype/login.html`）。

### 2. 处理长代码生成

当需要创建较大文件时：
- **分段请求**：先请求文件结构和基础布局，再逐步添加内容和逻辑。
- **分组件开发**：先开发基础组件，再组合使用。
- **使用"继续"提示**：如果AI回复被截断，输入"请继续"让AI完成。

### 3. 避免常见问题

- **路径始终使用相对路径**：如`src/xxx`而非绝对路径。
- **确保文档引用正确**：使用准确的文档名称和章节编号。
- **确保UI原型引用正确**：使用准确的文件名，如 `prototype/home.html`。
- **遇到错误立即修复**：不要累积多个问题后再解决。

## 开发阶段规划 (优化版 - "组件优先，多端同步")

### 阶段一：项目初始化与基础环境搭建

🎯 **目标**：创建项目骨架，配置基础工具和规范，并能运行基础项目。

#### 步骤 1.1: 创建项目（已完成）
您已经完成了项目模板的下载和基础依赖安装。

#### 步骤 1.2: 配置基础工具（使用Cursor AI）
1.  在Cursor中打开聊天，输入:
    ```
    @架构设计文档.md @开发规范与指南-基础篇.md 
    请帮我设置项目基础工具配置，包括：
    1. 创建.eslintrc.js, .prettierrc.js (如果需要), .stylelintrc.json配置文件。
    2. 安装并配置husky和lint-staged，用于代码提交前检查。
    3. 确保这些配置与《开发规范与指南-基础篇》第六节(代码风格)和《UI界面规则文档》工具链部分保持一致。
    ```
    > 💡 **新手提示**：Prettierrc用于代码格式化，如果ESLint配置已包含格式化规则，则可能不需要单独创建。AI会根据规范判断。
    > 🔍 **验证方法**：查看根目录下是否生成了对应的配置文件，内容是否包含ESLint、Stylelint规则。

#### 步骤 1.3: 配置 Mock 数据开关
在Cursor中提问:
```
@开发规范与指南-基础篇.md
请根据《开发规范与指南-基础篇》中关于模拟数据规范的要求：
1. 在项目根目录创建 .env.development 文件。
2. 在该文件中添加环境变量 VITE_USE_MOCK_DATA=true，用于在开发环境默认启用 Mock 数据。
3. （可选）创建 .env.production 文件，并设置 VITE_USE_MOCK_DATA=false 或不设置。
```
> ⚙️ **验证方法**：检查项目根目录下是否生成了 `.env.development` 文件，并包含正确的环境变量设置。

#### 步骤 1.4: 设置全局样式变量（使用Cursor AI）
在Cursor中提问:
```
@UI界面规则文档.md
请根据《UI界面规则文档》第三节(颜色与样式系统)和第五节第6点(CSS变量规范)，创建或更新以下文件：
1. 在`src/assets/styles/`目录下创建`variables.scss`文件。
2. 在`variables.scss`中包含所有必要的CSS变量，如主题色、文字色、背景色、间距、圆角等，确保提供后备值。
3. 确保支持暗黑模式（参考《UI界面规则文档》第八节第8点）。
4. 在`App.vue`文件的`<style lang="scss">`部分导入`variables.scss`，使其全局可用。
5. 明确定义移动端安全区域相关变量，如--safe-area-bottom。
6. 统一分类变量前缀，如cat-。
```
> 🎨 **验证方法**：检查`src/assets/styles/variables.scss`文件内容是否完整，是否包含`:root`和`@media (prefers-color-scheme: dark)`或`.theme-dark`下的变量定义。检查`App.vue`是否导入了该文件。

#### 步骤 1.5: 配置图标组件（使用Cursor AI）
在Cursor中提问:
```
@开发规范与指南-基础篇.md @UI界面规则文档.md
请根据《开发规范与指南-基础篇》第4.4节(图标使用)和《UI界面规则文档》第四节(图标使用规范)：
1. 确认Font Awesome相关依赖包 (`@fortawesome/...`) 已安装。
2. 创建`src/components/common/AppIcon.vue`通用图标组件。
3. 在`main.ts`中按需导入必要的Font Awesome图标 (例如先导入 'wallet', 'user', 'weixin') 并配置全局图标库。
4. 确保`AppIcon.vue`组件的实现能接收`icon`, `size`, `color`, `provider`等参数，并能正确渲染SVG图标。
```
> 🔍 **验证要点**：检查`AppIcon.vue`文件是否存在且代码结构合理。检查`main.ts`文件是否有导入和配置`library.add()`的代码。

#### 步骤 1.6: 配置路由和页面结构（使用Cursor AI）
在Cursor中提问:
```
@开发规范与指南-基础篇.md @UI界面规则文档.md
请根据《开发规范与指南-基础篇》第4.1节、第4.3节：
1. 修改项目根目录下的`pages.json`文件，添加`easycom`配置，使其能自动识别`src/components/common/`目录下以`App`开头的组件。
2. 在`pages.json`中配置基本页面路由结构，包括欢迎页、登录页、首页等，遵循架构文档中的目录组织。
3. 确保在`main.ts`中完成必要的应用配置。
```
> 📱 **重要提示**：确认`pages.json`中添加了正确的`easycom`规则和页面路由配置。

#### 步骤 1.7: 初始化Pinia状态管理（使用Cursor AI）
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-高级篇.md
请根据文档中关于状态管理的要求（《基础篇》第10节，《高级篇》第3节）：
1. 确认Pinia (`pinia@^2`) 已安装。
2. 创建`src/stores/index.ts`文件，用于创建和导出Pinia实例。
3. 修改`main.ts`文件，导入Pinia实例并将其注册到Vue应用中。
4. 创建一个基础的用户状态模块`src/stores/user.store.ts`作为示例，包含state, getters, actions。
5. API 风格: 统一采用 Composition API 风格，提升开发体验和代码一致性。
6. 采用 pinia-plugin-persistedstate 插件，减少重复代码，增强功能完整性。
```
> 📝 **要点检查**：检查`main.ts`是否正确`use(pinia)`。检查`user.store.ts`文件结构是否清晰。

### 阶段二：通用组件开发

🎯 **目标**：在开始页面开发前，先创建所有项目需要的通用组件，确保UI一致性和代码复用。

#### 步骤 2.1: 创建基础UI组件套件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请根据《UI界面规则文档》和《开发规范与指南-基础篇》第四节(组件使用)，创建以下基础通用组件：
1. `src/components/common/AppButton.vue`：按钮组件，支持不同尺寸、类型、状态
2. `src/components/common/AppCard.vue`：卡片容器组件
3. `src/components/common/AppInput.vue`：输入框组件，支持不同类型的输入
4. `src/components/common/AppModal.vue`：模态框组件
5. `tools/style/scripts/_utilities.scss`：集中管理 !important 的工具类文件

确保每个组件：
- 使用`<script setup lang="ts">`和`<style lang="scss" scoped>`
- 严格使用`variables.scss`中的CSS变量
- 适当添加TypeScript类型定义
- 实现适当的props接口和事件
- 考虑多端适配
```
> 🧩 **验证方法**：检查每个组件是否创建完成，代码结构是否符合规范，可以创建一个临时页面引入并测试这些组件。

#### 步骤 2.2: 创建表单相关组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请根据《UI界面规则文档》创建以下表单相关组件：
1. `src/components/common/AppSwitch.vue`：开关组件
2. `src/components/common/AppRadio.vue`：单选框组件
3. `src/components/common/AppCheckbox.vue`：复选框组件
4. `src/components/common/AppSelect.vue`：下拉选择组件
5. `src/components/common/AppDatePicker.vue`：日期选择器组件（滚轮型）
6. `src/components/common/AppCalendar.vue`：日历选择器组件（日历表型）

确保每个组件：
- 遵循与之前创建的AppButton等组件相同的设计语言和样式规范
- 处理好各平台兼容性
- 添加适当的状态反馈（如选中、禁用、错误等）
```
> 🧩 **验证方法**：检查每个组件是否创建完成，是否能在不同状态下正确工作。

#### 步骤 2.3: 创建布局组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请创建以下布局和导航相关组件：
1. `src/components/common/AppNavBar.vue`：顶部导航栏，支持返回按钮、标题和右侧操作区
2. `src/components/common/AppTabBar.vue`：底部选项卡导航，支持图标和文字
3. `src/components/common/AppDivider.vue`：分隔线组件
4. `src/components/common/AppEmpty.vue`：空状态提示组件
5. `src/components/common/AppLoading.vue`：加载状态组件

重点关注：
- 多端安全区域适配(特别是TabBar的iOS底部安全区域)
- 小程序特殊UI处理(如胶囊按钮区域)
- 暗黑模式支持
```
> 🧩 **验证方法**：在不同平台测试这些组件，特别是TabBar和NavBar的适配情况。

#### 步骤 2.4: 创建业务组件基础结构
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请创建以下记账应用特有的业务组件基础结构：
1. `src/components/business/CategorySelector.vue`：交易分类选择器组件
2. `src/components/business/TransactionItem.vue`：交易记录列表项组件
3. `src/components/business/AmountInput.vue`：金额输入组件（包含数字键盘）
4. `src/components/business/BudgetProgress.vue`：预算进度条组件
5. `src/components/business/GesturePassword.vue`：手势密码组件

现阶段只需创建基本结构和样式，具体的数据和交互逻辑将在稍后的阶段连接。
```
> 🧩 **验证方法**：检查这些业务组件的基础结构是否创建完成，确保它们能接收必要的props。

#### 步骤 2.5: 测试组件与样式指南
在Cursor中提问:
```
@UI界面规则文档.md
请创建一个简单的组件测试页面：
1. 创建`src/pages/dev/components.vue`页面
2. 在此页面中展示我们创建的各种通用组件，包括不同状态和属性配置
3. 为每个组件添加简短的使用说明和示例代码
4. 更新`pages.json`添加这个开发页面到路由
```
> 🧩 **验证方法**：访问组件测试页面，确认所有组件正常显示和工作。

#### 步骤 2.6: 组件集成与一致性检查
在Cursor中提问:
```
@开发规范与指南-基础篇.md @UI界面规则文档.md
请帮我检查所有组件的一致性：
1. 确保所有组件使用统一的命名约定（AppXxx）
2. 验证所有组件样式都使用了CSS变量而非硬编码值
3. 检查组件事件名称一致性（如click vs tap, change vs update）
4. 确保多端适配代码一致
5. 检查组件文档和类型定义完整性
```
> 🧩 **验证方法**：运行项目，访问组件测试页面，验证组件工作和表现的一致性。

#### 步骤 2.7: 运行项目（H5预览）
现在可以尝试运行项目，看看组件库是否正常。
在Cursor中提问:
```
请帮我运行项目，以便在浏览器中预览H5版本，并检查组件测试页面。
```
AI应执行类似 `npm run dev:h5` 的命令。
> 🚀 **验证方法**：命令执行后，终端会显示一个本地访问地址（如 `http://localhost:xxxx`）。在浏览器中打开该地址，导航到组件测试页面，检查组件是否正常显示和工作。

### 阶段三：核心认证流程开发与多端适配

🎯 **目标**：完成用户登录和认证相关页面，确保在所有目标平台上的良好表现。

#### 步骤 3.1: 创建请求工具和认证API（后端逻辑准备）
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md @API接口文档样例.md
请根据架构设计文档、开发规范（包括新增的模拟数据规范）和API接口文档样例，创建：
1. `src/utils/request.ts`请求工具，包含错误处理、超时重试、请求拦截等功能。
2. `src/api/auth.ts`认证API模块，定义注册、登录、获取验证码等函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
3. 创建对应的模拟数据文件 `src/api/mocks/auth.mock.ts`，导出模拟函数，其返回的数据结构 **必须 (MUST)** 符合《API接口文档样例.md》中相关接口的响应体格式。
4. 更新 `src/api/index.ts` 统一导出文件。
5. 确保遵循防御性编程原则。
```
> 💡 **新手提示**：这一步是准备工作，暂时不需要连接真实后端。
> 🔍 **验证方法**：检查`request.ts`和`auth.ts`文件是否存在，代码结构是否符合规范。

#### 步骤 3.2: 创建认证相关Hook（逻辑准备）
在Cursor中提问:
```
@开发规范与指南-高级篇.md
请根据《开发规范与指南-高级篇》中的组合式函数规范，创建：
1. `src/hooks/useAuth.ts`组合式函数。
2. 包含管理登录状态、存储/清除Token（例如使用`uni.setStorageSync`）、处理登录/注册逻辑的函数。
3. 确保包含必要的响应式状态（如`isLoggedIn`）和方法。
```
> 🔍 **验证方法**：检查`useAuth.ts`文件是否存在，是否导出了预期的状态和函数。

#### 步骤 3.3: 创建登录页面UI (`prototype/login.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/login.html
请根据UI原型`prototype/login.html`和相关文档规范，创建登录页面并确保多端适配：
1. 创建文件`src/pages/auth/login.vue`。
2. 严格按照`login.html`的视觉布局来构建页面骨架。
3. 使用通用组件`<AppInput>`作为手机号和验证码输入框，使用`<AppButton>`作为获取验证码和登录按钮。
4. 应用`variables.scss`中的CSS变量来设置颜色、间距等样式。
5. 页面最外层容器使用`.login-page`类名，内部元素使用BEM命名法（如`.login-form__input`）。
6. **多端适配特别注意**:
   - 使用`utils/platform.js`中的适配函数处理不同平台样式差异
   - 针对iOS：处理安全区域和键盘弹起
   - 针对Android：适配不同屏幕尺寸和密度
   - 针对小程序：处理胶囊按钮区域
   - 使用条件编译(`#ifdef`/`#endif`)处理特殊平台代码
7. 暂时不需要实现交互逻辑，先关注静态页面效果和多端适配。
```
> 🎨 **验证方法**：
> - **H5**：在浏览器中访问登录页面，对比`prototype/login.html`的截图
> - **iOS/Android**：在模拟器或真机上检查页面布局适配情况
> - **小程序**：在微信开发者工具中检查页面是否考虑了胶囊按钮区域

#### 步骤 3.4: 连接登录页面逻辑并测试多端表现
在Cursor中提问:
```
@src/pages/auth/login.vue @src/hooks/useAuth.ts @src/api/auth.ts
请为登录页面`src/pages/auth/login.vue`添加交互逻辑并确保多端良好表现：
1. 引入并使用`useAuth` Hook。
2. 将输入框与响应式状态绑定（使用`v-model`）。
3. 为"获取验证码"按钮添加点击事件，调用API发送验证码，并添加倒计时显示。
4. 为"登录"按钮添加点击事件，调用`useAuth`中的登录函数，并处理登录成功/失败的反馈（如提示信息、页面跳转）。
5. 添加简单的表单验证（如手机号格式）。
6. **多端交互优化**:
   - **iOS/Android**: 处理软键盘弹出时的页面调整
   - **小程序**: 特殊API调用兼容
   - **H5**: 添加适当的触摸反馈
```
> ⚙️ **验证方法**：
> - **所有平台**：尝试输入、点击按钮，观察是否有预期的交互反馈
> - **iOS/Android**：检查键盘弹出时页面是否正常调整
> - **小程序**：验证获取验证码和登录功能是否正常
> - **H5**：测试触摸反馈和响应性

#### 步骤 3.5: 创建手势密码页面 UI 并支持多端适配
在Cursor中提问:
```
@UI界面规则文档.md @prototype/pattern.html
请根据UI原型`prototype/pattern.html`创建手势密码页面并确保多端适配：
1. 创建文件`src/pages/auth/gesture.vue`。
2. 参照`pattern.html`布局，包含用户头像、用户名、手势绘制区域和提示文字。
3. 在手势绘制区域暂时放置一个占位符或基础`<canvas>`元素。
4. 添加"忘记手势密码?"或"跳过"的链接。
5. 页面最外层容器使用`.gesture-page`类名。
6. **多端适配注意点**:
   - 确保Canvas在不同像素密度设备上正常显示
   - 处理不同尺寸屏幕的绘制区域自适应
   - 小程序Canvas特殊处理（可能需要使用<canvas type="2d">）
```
> 🎨 **验证方法**：
> - **所有平台**：对比`prototype/pattern.html`截图，检查布局和静态元素
> - **iOS/Android**：检查不同屏幕尺寸下的显示效果
> - **小程序**：确认Canvas元素能够正确渲染

#### 步骤 3.6: 实现手势密码组件并处理多端差异
在Cursor中提问:
```
@开发规范与指南-高级篇.md @UI界面规则文档.md
请根据《开发规范与指南-高级篇》第八节(手势密码实现指南)，创建手势密码组件并确保多端兼容：
1. 创建`src/components/business/GesturePassword.vue`文件。
2. 完整实现该指南中提供的组件代码，包括Canvas绘制逻辑、触摸事件处理、设置/验证模式切换等。
3. 创建或更新`src/utils/gesture.ts`工具函数文件。
4. 确保组件样式符合UI规范。
5. **多端兼容处理**:
   - 统一触摸/鼠标事件处理
   - 针对iOS/Android设备的高DPI屏幕优化Canvas渲染
   - 小程序Canvas API兼容层（使用条件编译）
   - H5环境下的性能优化
```
> 🔍 **验证方法**：
> - **所有平台**：在`gesture.vue`页面测试手势绘制是否流畅，点是否能被选中，线条是否能绘制
> - **iOS/Android**：测试触摸灵敏度和绘制质量
> - **小程序**：确认Canvas交互正常工作
> - **H5**：检查鼠标和触摸事件是否都能正确响应

#### 步骤 3.7: 连接手势密码页面逻辑并测试多端表现
在Cursor中提问:
```
@src/pages/auth/gesture.vue @src/components/business/GesturePassword.vue @src/hooks/useAuth.ts
请将手势密码逻辑集成到`src/pages/auth/gesture.vue`并优化多端表现：
1. 在页面中引入并使用`<GesturePassword>`组件。
2. 根据页面加载参数（判断是设置模式还是验证模式）设置组件的`mode`属性。
3. 如果是验证模式，从本地存储（如`uni.getStorageSync('gesture-pattern')`）读取已存储的图案，传递给组件的`storedPattern`属性。
4. 处理组件发出的`setup-complete`, `verify-success`, `verify-fail`事件，实现对应的逻辑（如保存图案、跳转页面、显示错误提示）。
5. 连接"忘记密码"和"跳过"链接的跳转逻辑。
6. **多端优化**:
   - 统一的存储接口（处理不同平台的存储限制）
   - 设备旋转处理（主要针对H5和部分Android设备）
   - 平台特定的动画和过渡效果
   - 应用退出/恢复状态处理
```
> ⚙️ **验证方法**：
> - **所有平台**：完整测试手势密码的设置流程和验证流程
> - **iOS/Android**：测试应用切换到后台再恢复时的状态
> - **小程序**：测试小程序关闭和重新打开后的状态恢复
> - **H5**：测试浏览器刷新和历史导航对状态的影响

#### 步骤 3.8: 创建欢迎页面并支持多端展示
在Cursor中提问:
```
@UI界面规则文档.md @prototype/welcome.html
请根据UI原型`prototype/welcome.html`创建欢迎页面并优化多端显示：
1. 创建文件`src/pages/welcome/index.vue`。
2. 实现轮播图或静态引导页面的布局。
3. 添加"立即体验"或"登录/注册"按钮。
4. 页面最外层容器使用`.welcome-page`类名。
5. **多端适配优化**:
   - 响应式图片资源（针对不同屏幕尺寸和像素密度）
   - 不同端的动画效果优化
   - 统一的滑动/点击交互
   - 安全区域适配（特别是全面屏设备）
```
> 🎨 **验证方法**：
> - **所有平台**：对比`prototype/welcome.html`截图，检查布局和静态元素
> - **iOS/Android**：测试不同尺寸和方向下的显示效果
> - **小程序**：检查启动体验和转场动画
> - **H5**：测试响应式布局在不同视口大小下的表现

#### 步骤 3.9: 配置路由与启动页并优化多端导航体验
在Cursor中提问:
```
@src/pages/welcome/index.vue @src/pages/auth/login.vue @src/pages/auth/gesture.vue
请更新`pages.json`文件并优化多端导航体验：
1. 添加欢迎页、登录页、手势密码页的路由配置。
2. 将欢迎页(`src/pages/welcome/index`)设置为应用的启动页面（入口页）。
3. 配置从欢迎页跳转到登录页或首页的逻辑。
4. 配置登录成功后跳转到首页或手势密码验证页的逻辑。
5. **多端导航优化**:
   - 统一页面转场动画
   - 处理各平台返回按钮行为
   - 设置页面标题和导航栏样式
   - 针对iOS/Android/小程序的特定导航体验优化
```
> 🗺️ **验证方法**：
> - **所有平台**：测试完整的导航流程，从欢迎页→登录→手势密码→首页
> - **iOS/Android**：检查手势返回和硬件返回键处理
> - **小程序**：验证页面栈管理和返回体验
> - **H5**：测试浏览器前进/后退按钮的工作方式

### 阶段四：通用业务组件开发与完善

🎯 **目标**：完成和优化业务组件，准备它们与数据存储的连接，确保多端一致表现。

#### 步骤 4.1: 完善分类相关组件和存储
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md
请完善分类选择器组件和状态管理：
1. 创建`src/stores/category.store.ts`分类状态管理模块，定义分类数据结构、状态、getter和action。
2. 创建`src/api/category.ts`和对应的mock数据。
3. 完善`src/components/business/CategorySelector.vue`组件，实现:
   - 从category.store获取分类数据
   - 网格布局显示分类图标和名称
   - 支持选择和反选
   - 支持按收入/支出类型筛选
   - 多端自适应布局和交互
```
> 🧩 **验证方法**：在测试页面引入`CategorySelector`组件，验证它能正确显示分类并处理选择事件。

#### 步骤 4.2: 完善交易列表项组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请完善交易列表项组件：
1. 更新`src/components/business/TransactionItem.vue`组件：
   - 完善UI布局，包括左侧图标、中间交易信息、右侧金额
   - 实现不同交易类型的样式区分（收入/支出）
   - 添加日期格式化和金额显示格式化
   - 支持可选的交互事件（如点击、滑动删除）
   - 优化多端表现，特别是触摸反馈
2. 创建一个简单的测试页面验证组件显示效果
```
> 🧩 **验证方法**：在测试页面中使用模拟数据验证`TransactionItem`组件的显示效果和交互。

#### 步骤 4.3: 完善金额输入组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请完善金额输入组件：
1. 更新`src/components/business/AmountInput.vue`组件：
   - 实现自定义数字键盘UI
   - 添加金额格式化显示
   - 支持小数点和退格键
   - 实现金额限制和验证
   - 处理不同平台的键盘弹出和交互差异
2. 创建测试页面验证金额输入功能
```
> 🧩 **验证方法**：在测试页面中测试金额输入组件，验证数字键盘交互、格式化显示和验证功能。

#### 步骤 4.4: 完善预算进度组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请完善预算进度组件：
1. 更新`src/components/business/BudgetProgress.vue`组件：
   - 实现视觉美观的进度条
   - 添加不同状态的颜色变化（正常、接近预算、超出预算）
   - 支持显示百分比和剩余金额
   - 添加简单的动画效果
   - 确保在各平台上的一致表现
2. 创建测试页面验证组件在不同状态下的显示
```
> 🧩 **验证方法**：在测试页面中使用不同进度值测试组件显示效果。

#### 步骤 4.5: 创建并完善图表组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-高级篇.md
请创建记账分析图表组件：
1. 创建`src/components/business/AppChart.vue`组件：
   - 集成适合uni-app的图表库（如uCharts或echarts-for-weixin）
   - 支持常见图表类型（折线图、饼图、柱状图）
   - 封装统一的数据格式和配置接口
   - 优化不同平台的渲染性能
   - 处理触摸交互和缩放
2. 创建测试页面展示不同类型的图表
```
> 🧩 **验证方法**：在测试页面中展示不同类型的图表，检查渲染质量和交互体验。

#### 步骤 4.6: 创建消息对话组件
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-基础篇.md
请创建聊天对话业务组件：
1. 创建`src/components/business/ChatBubble.vue`气泡组件：
   - 支持用户和AI两种角色的消息样式
   - 处理文本、图片等不同内容类型
   - 实现时间戳和状态指示
   - 优化长文本和溢出处理
2. 创建`src/components/business/RecognitionResult.vue`识别结果组件：
   - 展示结构化的交易识别结果
   - 提供编辑和确认界面
   - 支持字段错误修正
3. 创建测试页面验证组件效果
```
> 🧩 **验证方法**：在测试页面中使用模拟数据测试对话气泡和识别结果组件的显示效果。

### 阶段五：核心模块 - 首页与交易 (与数据连接)

🎯 **目标**：构建首页、账单列表、记账确认等核心页面，并连接到Pinia状态管理。

#### 步骤 5.1: 创建交易API模块及其Mock数据
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md @API接口文档样例.md
请根据架构设计文档、开发规范（包括新增的模拟数据规范）和API接口文档样例，创建：
1. `src/api/transaction.ts`交易API模块，定义创建、查询、修改、删除等函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
2. 创建对应的模拟数据文件 `src/api/mocks/transaction.mock.ts`，导出模拟函数，其返回的数据结构 **必须 (MUST)** 符合《API接口文档样例.md》中相关接口的响应体格式。
3. 更新 `src/api/index.ts` 统一导出文件。
4. 确保遵循防御性编程原则。
5. 模拟数据必须包含各种类型的交易记录，支持不同的交易分类、时间范围和金额，以便测试筛选和统计功能。
6. 确保每条交易记录都有正确的 categoryId 字段，与 Category Store 中的分类保持一致。
```
> 💡 **新手提示**：模拟数据应尽量真实多样，包含各种交易场景，有助于后续开发统计分析功能。
> 🔍 **验证方法**：检查API模块和Mock文件是否创建完成，文件结构是否符合规范。使用 `console.log` 测试导入并调用API函数，确认能返回预期的模拟数据。

#### 步骤 5.1.1: 创建交易状态管理模块
在Cursor中提问:
```
@开发规范与指南-基础篇.md @开发规范与指南-高级篇.md
请创建交易状态管理模块：
1. 创建文件 `src/stores/transaction.store.ts`。
2. 使用 defineStore 定义交易状态模块，包含以下核心部分：
   - state：包含 transactions 数组、loading 状态、分页信息、筛选条件等
   - getters：提供按日期分组、按分类筛选、按时间范围筛选等功能
   - actions：包含 fetchTransactions（获取交易列表）、addTransaction（添加交易）、updateTransaction（更新交易）、deleteTransaction（删除交易）等方法
3. 确保 actions 中的方法调用 `src/api/transaction.ts` 中的相应函数。
4. 实现与 categoryStore 的连接，确保能通过 categoryId 获取完整的分类信息。
5. 添加必要的类型定义 (使用 TypeScript)。
6. 使用 pinia-plugin-persistedstate 实现数据持久化（选择性持久化，避免存储过大数据）。
```
> 🔍 **验证方法**：检查 transaction.store.ts 文件结构，确保包含所有必要的状态、getter 和 action。尝试在控制台中导入并初始化 store，测试基本操作如 fetchTransactions。

#### 步骤 5.2: 创建首页 UI (`prototype/home.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/home.html
请根据UI原型`prototype/home.html`创建首页：
1. 创建文件`src/pages/home/<USER>
2. 严格按照原型布局，包含顶部背景、资产总览卡片(`AppCard`包裹)、快捷功能按钮区、近期账单列表标题。
3. 快捷功能按钮使用`<AppButton>`或自定义图标按钮。
4. 近期账单列表暂时使用静态数据或占位符。
5. 页面底部需要放置底部导航栏（TabBar）的占位符。
6. 页面最外层容器使用`.home-page`类名。
7. 遵循UI界面规则文档中的颜色、字体、间距等规范。
```
> 🎨 **验证方法**：在H5预览中访问首页（可能需要先完成登录或跳过），对比`prototype/home.html`截图，检查布局、颜色、卡片样式。

#### 步骤 5.3: 创建底部导航栏 TabBar 组件
在Cursor中提问:
```
@UI界面规则文档.md @prototype/home.html
请创建一个通用的底部导航栏组件：
1. 创建文件`src/components/common/AppTabBar.vue`。
2. 根据原型（如`home.html`底部所示），包含"首页"、"账单"、"统计"、"我的"四个选项，每个选项包含图标(<AppIcon>)和文字。
3. 实现选中状态的高亮效果。
4. 组件能接收当前激活索引作为prop，并能发出切换事件。
5. 样式遵循UI规范，注意处理底部安全区域（参考《UI界面规则文档》多端适配部分）。
6. 确保在iOS全面屏设备上正确处理安全区域。
```
> 🔍 **验证方法**：可以在一个临时页面或`App.vue`中引入并测试该组件的外观和选中效果。在不同设备上检查底部安全区域处理是否正确。

#### 步骤 5.4: 集成 TabBar 到主页面
在Cursor中提问:
```
@src/pages/home/<USER>/components/common/AppTabBar.vue
请将底部导航栏`<AppTabBar>`集成到应用的主页面布局中：
1. 优先使用 `pages.json` 配置方法：在 `pages.json` 中添加 `tabBar` 节点，定义列表、颜色、图标路径等。确保图标路径正确，并且与设计保持一致。
2. 如果需要自定义TabBar（如中间凸起按钮），则修改 `App.vue` 的模板，添加自定义TabBar组件，并处理页面切换逻辑。
3. 确保在首页(`src/pages/home/<USER>
4. 实现页面切换逻辑，可使用 uni.switchTab 或自定义导航方法。
```
> 🗺️ **验证方法**：重新运行项目，检查首页等主页面底部是否正确显示了TabBar，点击不同选项是否能（在配置好路由后）切换页面。

#### 步骤 5.5: 连接首页数据
在Cursor中提问:
```
@src/pages/home/<USER>/stores/transaction.store.ts
请将首页`src/pages/home/<USER>
1. 在首页组件中引入`useTransactionStore`和（如果有的话）`useAssetStore`。
2. 添加 onLoad 或 onShow 生命周期钩子，在页面加载时调用 transactionStore.fetchTransactions() 获取交易数据。
3. 使用 computed 属性从 Store 获取最近的交易记录（如 transactionStore.recentTransactions）。
4. 从 Store 中获取总资产/收支统计数据，显示在资产总览卡片中。
5. 为快捷功能按钮添加跳转事件（如"记一笔"按钮跳转到交易录入页）。
6. 确保列表项显示的分类信息（图标、名称、颜色）从 categoryStore 获取，而不是硬编码。
```
> 📊 **验证方法**：刷新首页，检查资产总览和近期账单是否显示了来自 Store 的（模拟）数据。点击"记一笔"等按钮，确认能跳转到正确页面。

#### 步骤 5.6: 创建交易项组件 (`TransactionItem`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/transactions.html @src/stores/category.store.ts
请创建一个用于在列表中显示单条交易记录的业务组件：
1. 创建文件`src/components/business/TransactionItem.vue`。
2. 根据`transactions.html`原型中列表项的样式设计组件，包含分类图标、类别名称、备注（可选）、金额、交易时间等。
3. 金额根据收支类型显示不同颜色（支出使用 var(--color-expense, #F44336)，收入使用 var(--color-income, #4CAF50)）。
4. 组件接收一个交易对象作为prop（包含id, categoryId, amount, type, date, note等字段）。
5. **关键**：确保组件内部使用 `useCategoryStore().getCategoryById(props.transaction.categoryId)` 获取分类信息，而不是直接从交易对象中读取分类名称和图标。
6. 添加点击事件，在点击时发出包含交易ID的事件，以便导航到详情页。
7. 可选：支持滑动操作（如向左滑动显示删除/编辑按钮）。
```
> 🎨 **验证方法**：在首页或账单列表页中引入并使用这个组件，传入模拟数据，检查显示效果。确认分类图标和名称是否正确显示。

#### 步骤 5.7: 创建交易列表页面 UI (`prototype/transactions.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/transactions.html
请根据UI原型`prototype/transactions.html`创建交易列表页面：
1. 创建文件`src/pages/transaction/list.vue`。
2. 页面包含顶部筛选区域（日期范围、类型、分类等）和下方的交易列表。
3. 列表区域使用`<TransactionItem>`组件渲染交易数据。
4. 实现按日期分组显示（如"今天"、"昨天"、"本月"等），每个日期组显示小计。
5. 添加下拉刷新和上拉加载更多的基本结构（逻辑后续添加）。
6. 页面最外层容器使用`.transaction-list-page`类名。
7. 筛选区域应包含日期选择器、收入/支出切换、分类筛选等。
```
> 🎨 **验证方法**：在H5预览中访问该页面，对比`prototype/transactions.html`截图，检查布局和静态元素。

#### 步骤 5.8: 连接交易列表页面逻辑
在Cursor中提问:
```
@src/pages/transaction/list.vue @src/stores/transaction.store.ts @src/components/business/TransactionItem.vue
请为交易列表页面`src/pages/transaction/list.vue`添加数据和交互逻辑：
1. 引入`useTransactionStore`和`useCategoryStore`。
2. 添加响应式状态记录当前筛选条件（如时间范围、交易类型、分类ID等）。
3. 添加 onLoad 或 onShow 生命周期钩子，在页面加载时根据筛选条件调用 transactionStore.fetchTransactions(filters) 获取交易数据。
4. 使用 computed 属性从 Store 获取并按日期分组的交易记录（可利用 transactionStore 中的 getter 如 groupedByDate）。
5. 实现下拉刷新逻辑（重新获取数据）和上拉加载更多逻辑（分页获取更多数据）。
6. 连接筛选区域的交互，当筛选条件变化时更新本地状态并重新获取数据。
7. 处理列表为空时的显示状态（空状态提示）。
8. 实现点击交易项导航到交易详情页的逻辑。
9. 确保使用 v-for 渲染 TransactionItem 组件时，传递完整的交易对象（包含 categoryId）。
```
> ⚙️ **验证方法**：访问交易列表页，检查数据是否加载，下拉刷新和上拉加载是否有效，筛选功能是否能按预期工作。确认交易记录的分类信息显示正确。

#### 步骤 5.9: 创建记账确认页面 UI (`prototype/transaction-confirm.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/transaction-confirm.html
请根据UI原型`prototype/transaction-confirm.html`创建记账确认页面：
1. 创建文件`src/pages/transaction/record.vue`。
2. 布局应包含以下关键部分：
   - 顶部的收入/支出类型切换
   - 金额输入区域（显示金额和数字键盘）
   - 分类选择区域（后续集成 CategorySelector 组件）
   - 日期选择区域（使用 DatePicker 组件）
   - 备注输入区域
   - 位置信息（可选）和图片凭证（可选）区域
3. 使用`<AppInput>`, `<AppButton>`, `<AppDatePicker>`等通用组件。
4. 底部添加一个醒目的"保存"按钮。
5. 页面最外层容器使用`.transaction-record-page`类名。
6. 为数字键盘创建基本布局（后续添加交互逻辑）。
```
> 🎨 **验证方法**：访问页面，对比`prototype/transaction-confirm.html`截图，确认布局和样式符合设计。

#### 步骤 5.10: 创建分类选择器组件
在Cursor中提问:
```
@UI界面规则文档.md @src/stores/category.store.ts
请创建一个可复用的分类选择器业务组件：
1. 创建文件 `src/components/business/CategorySelector.vue`。
2. **关键**：组件的数据来源**必须**是 `src/stores/category.store.ts`。在组件内部导入 `useCategoryStore`。
3. 组件应接收 `type` 属性（值为 'expense' 或 'income'），并根据类型调用 `categoryStore.expenseCategories` 或 `categoryStore.incomeCategories` Getter 获取对应分类列表。
4. 以网格形式展示分类，每个分类项显示：
   - 使用 `<AppIcon>` 显示分类图标
   - 显示分类名称
   - 使用分类对应的颜色作为图标背景或边框
5. 组件应支持 v-model，接收当前选中的分类 ID 作为 `modelValue` prop，并在选择变化时发出 `update:modelValue` 事件。
6. 添加适当的选中状态样式。
7. 可选：支持搜索/筛选功能。
```
> 🧩 **验证方法**：创建一个测试页面，引入该组件并测试分类选择功能。确认组件能正确显示分类图标和名称，并能正确处理选择事件。

#### 步骤 5.11: 创建数字键盘组件
在Cursor中提问:
```
@UI界面规则文档.md @prototype/transaction-confirm.html
请创建一个数字键盘业务组件：
1. 创建文件 `src/components/business/NumberKeyboard.vue`。
2. 布局包含 0-9 数字按键、小数点按键和删除按键。
3. 组件接收当前输入值作为 prop（`value`），并在按键点击时发出更新事件（`update:value` 或自定义事件）。
4. 实现基本的输入逻辑：
   - 数字按键：追加数字到当前值
   - 小数点：添加小数点（如果尚未存在）
   - 删除按键：删除最后一位字符
5. 添加适当的按键点击反馈效果。
6. 可选：添加金额格式化功能（如添加千位分隔符）。
```
> 🧩 **验证方法**：在记账页面引入该组件，测试数字输入功能，确认按键点击能正确修改金额值。

#### 步骤 5.12: 连接记账确认页面逻辑
在Cursor中提问:
```
@src/pages/transaction/record.vue @src/components/business/CategorySelector.vue @src/components/business/NumberKeyboard.vue @src/stores/transaction.store.ts @src/stores/category.store.ts
请为记账确认页面 `src/pages/transaction/record.vue` 添加交互逻辑：
1. 导入并使用必要的组件：`CategorySelector`、`NumberKeyboard`（如果已创建）等。
2. 添加页面的响应式状态，包括：
   - 交易类型（收入/支出，默认为支出）
   - 金额值（默认为 0）
   - 选中的分类 ID（默认为第一个分类）
   - 日期（默认为当前日期）
   - 备注文本
3. 连接收入/支出切换按钮，切换时更新 `type` 状态并重置选中的分类。
4. 集成 `<CategorySelector>` 组件：
   - 传入当前的 `type` 和选中的分类 ID
   - 监听分类选择事件，更新选中的分类 ID
5. 集成 `<NumberKeyboard>` 组件（或实现内联的键盘逻辑）：
   - 连接金额输入逻辑
   - 确保金额格式正确（如小数位不超过2位）
6. 连接日期选择和备注输入。
7. 为"保存"按钮添加点击事件处理函数：
   - 验证必填字段（金额、分类）
   - 构建交易对象（包含类型、金额、分类ID、日期、备注等）
   - 调用 `transactionStore.addTransaction(transaction)`
   - 成功后显示提示并返回上一页或首页
```
> ⚙️ **验证方法**：完整测试记账流程，输入金额、选择分类、日期、备注，点击保存，看是否能成功添加交易记录，并在列表页或首页看到新记录。

#### 步骤 5.13: 创建交易详情页面
在Cursor中提问:
```
@UI界面规则文档.md @src/stores/transaction.store.ts @src/stores/category.store.ts
请创建交易详情页面：
1. 创建文件 `src/pages/transaction/detail.vue`。
2. 页面应显示单笔交易的完整信息，包括：
   - 交易类型（收入/支出）和金额
   - 分类信息（图标、名称、颜色）
   - 交易日期和时间
   - 备注信息
   - 位置信息（如有）
   - 图片凭证（如有）
3. 提供"编辑"和"删除"按钮。
4. 页面加载时，根据路由参数中的交易 ID 从 transactionStore 获取交易详情。
5. 分类信息必须通过 `categoryStore.getCategoryById(transaction.categoryId)` 获取。
6. "编辑"按钮应跳转到记账页面，并传递当前交易信息。
7. "删除"按钮应显示确认对话框，确认后调用 `transactionStore.deleteTransaction(id)` 并返回上一页。
```
> 🎨 **验证方法**：从交易列表页点击交易项，导航到详情页，确认详情信息显示正确。测试编辑和删除功能。

#### 步骤 5.14: 创建分类管理页面 UI (`prototype/categories.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/categories.html
请根据UI原型`prototype/categories.html`创建分类管理页面：
1. 创建文件`src/pages/category/index.vue`。
2. 页面应包含收入分类和支出分类两个Tab标签页。
3. 每个标签页内显示对应类型的分类列表。
4. 列表项显示分类图标、名称，并提供编辑和删除按钮。
5. 页面底部提供添加新分类的入口按钮。
6. 页面最外层容器使用`.category-management-page`类名。
7. 根据UI规范设置颜色、字体、间距等样式。
```
> 🎨 **验证方法**：访问页面，对比`prototype/categories.html`截图，确认布局和样式符合设计。

#### 步骤 5.15: 创建分类编辑模态框组件
在Cursor中提问:
```
@UI界面规则文档.md @src/stores/category.store.ts
请创建分类编辑模态框组件：
1. 创建文件 `src/components/business/CategoryEditModal.vue`。
2. 组件应包含以下表单元素：
   - 分类名称输入框
   - 图标选择器（可显示预设图标列表供选择）
   - 颜色选择器（可显示预设颜色列表供选择）
   - 类型选择（收入/支出）
3. 组件应支持两种模式：添加新分类和编辑现有分类。
4. 在编辑模式下，应预填充现有分类信息。
5. 添加"保存"和"取消"按钮。
6. 使用 props 接收初始数据和模式，并通过事件发送结果。
```
> 🧩 **验证方法**：在分类管理页面引入该组件，测试添加和编辑分类功能。

#### 步骤 5.16: 连接分类管理页面逻辑
在Cursor中提问:
```
@src/pages/category/index.vue @src/components/business/CategoryEditModal.vue @src/stores/category.store.ts
请为分类管理页面 `src/pages/category/index.vue` 添加数据和交互逻辑：
1. 引入 `useCategoryStore` 和 `CategoryEditModal` 组件。
2. 添加页面响应式状态：
   - 当前选中的Tab（收入/支出）
   - 是否显示编辑模态框
   - 当前编辑的分类对象（用于传递给模态框）
   - 编辑模式（'add' 或 'edit'）
3. 在 onLoad 或 onShow 生命周期钩子中，调用 `categoryStore.fetchCategories()` 获取分类数据。
4. 根据当前Tab，使用 `categoryStore.incomeCategories` 或 `categoryStore.expenseCategories` getter 获取并显示对应类型的分类列表。
5. 实现"添加分类"功能：
   - 点击添加按钮时，设置编辑模式为 'add'，当前编辑分类为空对象，并显示编辑模态框
   - 模态框保存时，调用 `categoryStore.addCategory(newCategory)`
6. 实现"编辑分类"功能：
   - 点击编辑按钮时，设置编辑模式为 'edit'，当前编辑分类为选中的分类对象，并显示编辑模态框
   - 模态框保存时，调用 `categoryStore.updateCategory(updatedCategory)`
7. 实现"删除分类"功能：
   - 点击删除按钮时，显示确认对话框
   - 确认后调用 `categoryStore.deleteCategory(categoryId)`
```
> ⚙️ **验证方法**：完整测试分类的添加、编辑和删除功能。确认操作后分类列表能正确更新。

### 阶段六：账单提醒与通知 (UI优先)

🎯 **目标**：实现账单提醒和通知中心页面的UI和基础功能。

#### 步骤 6.1: 创建提醒与通知API及其Mock数据
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md @API接口文档样例.md
请根据架构设计文档、开发规范（包括新增的模拟数据规范）和API接口文档样例，创建：
1. `src/api/reminder.ts`提醒API模块，定义创建、查询、修改、删除提醒的函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
2. `src/api/notification.ts`通知API模块，定义获取、标记已读等函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
3. 创建对应的模拟数据文件：
   - `src/api/mocks/reminder.mock.ts`：模拟提醒数据
   - `src/api/mocks/notification.mock.ts`：模拟通知数据
   其返回的数据结构 **必须 (MUST)** 符合《API接口文档样例.md》中相关接口的响应体格式。
4. 更新 `src/api/index.ts` 统一导出文件。
5. 确保遵循防御性编程原则。
```
> 💡 **新手提示**：模拟数据应包含各种类型的提醒（如信用卡还款、房租等）和通知（如预算超支提醒、账单到期提醒等）。

#### 步骤 6.2: 创建账单提醒页面 UI (`prototype/bill-reminders.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/bill-reminders.html
请根据UI原型`prototype/bill-reminders.html`创建账单提醒页面：
1. 创建文件`src/pages/reminder/index.vue`。
2. 页面应能列表显示已设置的账单提醒（如信用卡还款、房租等）。
3. 提供添加新提醒的入口。
4. 列表项应显示提醒名称、金额、到期日、状态等。
5. 页面最外层容器使用`.reminder-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/bill-reminders.html`截图。

#### 步骤 6.3: 连接账单提醒页面逻辑
在Cursor中提问:
```
@src/pages/reminder/index.vue @src/stores/reminder.store.ts
请为账单提醒页面添加数据和交互逻辑：
1. 引入`useReminderStore`。
2. 获取并显示提醒列表。
3. 实现添加、编辑、删除提醒的功能（可能需要新页面或弹出框）。
4. 实现标记提醒为已处理的功能。
```
> ⚙️ **验证方法**：测试提醒的增删改查和状态变更功能。

#### 步骤 6.4: 创建通知中心页面（如果原型中有）
如果原型中有单独的通知中心页面：
在Cursor中提问:
```
@UI界面规则文档.md
请创建通知中心页面：
1. 创建文件`src/pages/notification/index.vue`。
2. 页面用于显示系统消息和用户相关的通知（如预算超支警告、账单到期提醒聚合等）。
3. 实现通知列表展示、标记已读、删除通知等功能。
4. 连接`notification.store`获取数据。
```
> 📢 **验证方法**：测试通知的接收、阅读和管理功能。

### 阶段七：数据分析和预算管理 (UI优先)

🎯 **目标**：构建数据分析和预算管理页面的UI，并集成图表和逻辑。

#### 步骤 7.1: 创建分析与预算API及其Mock数据
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md @API接口文档样例.md
请根据架构设计文档、开发规范（包括新增的模拟数据规范）和API接口文档样例，创建：
1. `src/api/analysis.ts`分析API模块，定义获取各类统计数据的函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
2. `src/api/budget.ts`预算API模块，定义预算设置、查询等函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
3. 创建对应的模拟数据文件：
   - `src/api/mocks/analysis.mock.ts`：模拟统计数据
   - `src/api/mocks/budget.mock.ts`：模拟预算数据
   其返回的数据结构 **必须 (MUST)** 符合《API接口文档样例.md》中相关接口的响应体格式。
4. 更新 `src/api/index.ts` 统一导出文件。
5. 确保遵循防御性编程原则。
```
> 💡 **新手提示**：模拟数据应包含各种时间维度（日、周、月、年）的统计数据，以及不同分类的预算数据，便于测试图表展示。

#### 步骤 7.2: 创建数据分析页面 UI (`prototype/analysis.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/analysis.html
请根据UI原型`prototype/analysis.html`创建数据分析页面：
1. 创建文件`src/pages/analysis/index.vue`。
2. 布局包含时间范围选择器、收支总览卡片、收支趋势图（折线图）、分类支出排行（饼图或条形图）、月度收支对比等模块。
3. 图表区域暂时使用占位符。
4. 页面最外层容器使用`.analysis-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/analysis.html`截图，检查布局和静态卡片。

#### 步骤 7.3: 集成图表并连接数据分析逻辑
在Cursor中提问:
```
@src/pages/analysis/index.vue @src/hooks/useCharts.ts @src/stores/analysis.store.ts
请为数据分析页面`src/pages/analysis/index.vue`集成图表并连接逻辑：
1. 引入图表库（例如，如果选择uCharts，需要安装对应uni-app插件）。
2. 使用`useCharts` Hook 初始化和渲染收支趋势图、分类占比图等。
3. 连接`analysis.store`获取图表所需的数据。
4. 实现时间范围选择器的交互，选择后重新获取数据并更新图表。
5. 优化图表性能，特别是在大数据量时。
6. 在按分类统计时，需要使用 Category Store 中的分类信息（名称、颜色）来展示图表。
```
> 📊 **验证方法**：访问数据分析页，检查图表是否能根据（模拟）数据显示，时间范围选择是否能更新图表。

#### 步骤 7.4: 创建预算设置页面 UI (`prototype/budget.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/budget.html
请根据UI原型`prototype/budget.html`创建预算设置页面：
1. 创建文件`src/pages/budget/setup.vue`。
2. 页面包含设置总预算的输入框和按分类设置预算的列表。
3. 每个分类预算项旁边应有进度条（使用`BudgetProgress`组件占位符）。
4. 提供编辑和保存预算的功能。
5. 页面最外层容器使用`.budget-setup-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/budget.html`截图。

#### 步骤 7.5: 创建预算进度条组件
在Cursor中提问:
```
@UI界面规则文档.md
请创建预算进度条业务组件：
1. 创建文件`src/components/business/BudgetProgress.vue`。
2. 组件能接收预算金额和当前已用金额作为prop。
3. 以进度条形式可视化显示预算使用比例。
4. 当支出接近或超过预算时，进度条颜色应变化（如变为黄色或红色）以示预警。
5. 样式符合UI规范。
```
> 🧩 **验证方法**：在预算设置页或其他页面引入并测试该组件。

#### 步骤 7.6: 连接预算管理页面逻辑
在Cursor中提问:
```
@src/pages/budget/setup.vue @src/stores/budget.store.ts @src/components/business/BudgetProgress.vue
请为预算设置页面`src/pages/budget/setup.vue`添加数据和交互逻辑：
1. 引入`useBudgetStore`。
2. 获取并显示当前的总预算和各分类预算。
3. 将预算金额与输入框绑定。
4. 为分类预算列表项集成`BudgetProgress`组件并传入正确的预算和支出数据。
5. 实现保存预算设置的功能，调用Store中的Action更新预算。
6. 预算列表需要基于 Category Store 中的分类来展示和设置。
```
> ⚙️ **验证方法**：测试预算设置页的数据显示、编辑和保存功能，检查进度条是否正确反映预算使用情况。

### 阶段八：个人中心与系统设置 (UI优先)

🎯 **目标**：构建个人中心、设置等页面的UI，并实现相关功能。

#### 步骤 8.1: 创建个人中心页面 UI (`prototype/profile-new.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/profile-new.html
请根据UI原型`prototype/profile-new.html`创建个人中心页面：
1. 创建文件`src/pages/profile/index.vue`。
2. 布局包含顶部的用户信息区域（头像、昵称）、数据统计（如记账天数、总笔数），以及下方的功能入口列表（如账本管理、数据同步、预算设置、分类管理、设置等）。
3. 功能入口通常使用列表项或卡片样式，包含图标和文字。
4. 页面最外层容器使用`.profile-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/profile-new.html`截图。

#### 步骤 8.2: 连接个人中心页面逻辑
在Cursor中提问:
```
@src/pages/profile/index.vue @src/stores/user.store.ts
请为个人中心页面`src/pages/profile/index.vue`连接数据和导航逻辑：
1. 引入`useUserStore`获取并显示用户头像和昵称。
2. 获取并显示相关统计数据（可能需要新的API或Store支持）。
3. 为各个功能入口添加点击事件，跳转到对应的页面（如设置页、预算页、分类页等）。
```
> ⚙️ **验证方法**：检查用户信息是否正确显示，点击各入口是否能跳转到相应页面。

#### 步骤 8.3: 创建个人信息页面 UI (`prototype/profile.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/profile.html
请根据UI原型`prototype/profile.html`创建个人信息页面：
1. 创建文件`src/pages/profile/info.vue`。
2. 页面用于展示和编辑用户的详细信息，如头像、昵称、手机号（可能只读）、邮箱等。
3. 头像部分应支持点击更换。
4. 使用表单形式布局，输入框使用`<AppInput>`。
5. 页面最外层容器使用`.profile-info-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/profile.html`截图。

#### 步骤 8.4: 连接个人信息页面逻辑
在Cursor中提问:
```
@src/pages/profile/info.vue @src/stores/user.store.ts @src/api/user.ts
请为个人信息页面`src/pages/profile/info.vue`添加数据和交互逻辑：
1. （如果需要）创建`src/api/user.ts`用于更新用户信息。
2. 引入`useUserStore`获取并填充当前用户信息到表单。
3. 实现头像上传功能（调用`uni.chooseImage`和`uni.uploadFile`）。
4. 实现保存修改的功能，调用API更新用户信息，并同步更新`user.store`。
5. 添加必要的表单验证。
```
> ⚙️ **验证方法**：测试用户信息是否正确加载，修改信息和头像后能否成功保存。

#### 步骤 8.5: 创建系统设置页面 UI (`prototype/settings.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/settings.html
请根据UI原型`prototype/settings.html`创建系统设置页面：
1. 创建文件`src/pages/profile/settings.vue`。
2. 页面包含多个设置项，如账户与安全（修改密码、手势密码开关）、通知设置、主题设置（明/暗/自动）、数据管理（备份/恢复/清除缓存）、关于我们、退出登录等。
3. 使用列表项或分组卡片形式展示设置项，开关类设置使用`<switch>`组件。
4. 页面最外层容器使用`.settings-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/settings.html`截图。

#### 步骤 8.6: 实现主题切换功能
在Cursor中提问:
```
@src/pages/profile/settings.vue @UI界面规则文档.md
请在系统设置页面`src/pages/profile/settings.vue`中实现主题切换功能：
1. 参考《UI界面规则文档》第八节第8点关于暗黑模式适配和手动切换的说明。
2. 可能需要创建或使用一个`src/utils/theme.ts`工具函数来处理主题切换逻辑（应用CSS类到`<html>`或`:root`，保存用户偏好到本地存储）。
3. 在设置页面添加切换选项（如"浅色"、"深色"、"跟随系统"），并调用主题切换函数。
```
> 🌓 **验证方法**：在设置页面切换主题选项，观察整个应用的UI（背景、文字、组件颜色）是否按预期在明暗模式间切换。

#### 步骤 8.7: 实现其他设置项逻辑
根据需要，逐步为设置页面的其他选项添加逻辑：
在Cursor中提问:
```
@src/pages/profile/settings.vue
请为系统设置页面添加[具体设置项，如"清除缓存"或"退出登录"]的功能逻辑：
1. [描述具体逻辑，如调用`uni.clearStorageSync`或调用`useAuth`的退出登录方法]。
2. [可能需要添加确认提示框]。
```
> ⚙️ **验证方法**：逐个测试设置项的功能是否符合预期。

### 阶段九：资产管理 (UI优先)

🎯 **目标**：构建资产管理相关页面的UI，并实现资产的增删改查。

#### 步骤 9.1: 创建资产管理API及其Mock数据
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md @API接口文档样例.md
请根据架构设计文档、开发规范（包括新增的模拟数据规范）和API接口文档样例，创建：
1. `src/api/asset.ts`资产API模块，定义资产账户的增删改查、获取列表等函数接口。**在此文件中实现检查 `import.meta.env.VITE_USE_MOCK_DATA` 的逻辑。**
2. 创建对应的模拟数据文件 `src/api/mocks/asset.mock.ts`，导出模拟函数，其返回的数据结构 **必须 (MUST)** 符合《API接口文档样例.md》中相关接口的响应体格式。
3. 更新 `src/api/index.ts` 统一导出文件。
4. 确保遵循防御性编程原则。
```
> 💡 **新手提示**：模拟数据应包含各种类型的资产账户（如现金、储蓄卡、信用卡、支付宝、微信等），以及不同的余额和交易记录。

#### 步骤 9.2: 创建资产总览页面 UI (`prototype/assets.html`)
在Cursor中提问:
```
@UI界面规则文档.md @prototype/assets.html
请根据UI原型`prototype/assets.html`创建资产总览页面：
1. 创建文件`src/pages/assets/index.vue`。
2. 页面顶部显示总资产、总负债、净资产等统计信息。
3. 下方使用卡片列表展示不同的资产账户（如现金、储蓄卡、信用卡、支付宝、微信等）。
4. 每个资产卡片显示账户名称、余额、类型图标。
5. 提供添加新资产账户的入口按钮。
6. 页面最外层容器使用`.assets-overview-page`类名。
```
> 🎨 **验证方法**：访问页面，对比`prototype/assets.html`截图。

#### 步骤 9.3: 连接资产总览页面逻辑
在Cursor中提问:
```
@src/pages/assets/index.vue @src/stores/asset.store.ts
请为资产总览页面`src/pages/assets/index.vue`连接数据和交互逻辑：
1. 引入`useAssetStore`。
2. 获取并显示总资产、负债、净资产统计。
3. 获取并渲染资产账户列表。
4. 实现点击"添加资产"按钮跳转到添加/编辑页面的逻辑。
5. 实现点击某个资产卡片跳转到资产详情页面的逻辑。
```
> ⚙️ **验证方法**：检查页面数据是否正确加载，点击按钮是否能正确导航。

#### 步骤 9.4: 创建资产添加/编辑页面 UI
在Cursor中提问:
```
@UI界面规则文档.md
请创建资产添加/编辑页面：
1. 创建文件`src/pages/assets/edit.vue`（可以复用）。
2. 页面包含选择资产类型（现金、储蓄卡、信用卡等）、输入账户名称、输入初始余额、选择币种（如果需要）、备注等表单项。
3. 使用`<AppInput>`、选择器等通用组件。
4. 提供保存和删除（编辑模式下）按钮。
5. 页面最外层容器使用`.asset-edit-page`类名。
```
> 🎨 **验证方法**：访问页面，检查表单布局和组件。

#### 步骤 9.5: 连接资产添加/编辑页面逻辑
在Cursor中提问:
```
@src/pages/assets/edit.vue @src/stores/asset.store.ts
请为资产添加/编辑页面`src/pages/assets/edit.vue`添加逻辑：
1. 引入`useAssetStore`。
2. 如果是编辑模式，根据传入的账户ID获取并填充现有数据。
3. 实现表单数据绑定和验证。
4. 实现保存功能，调用Store中的添加或更新资产Action。
5. 实现删除功能（编辑模式下），调用Store中的删除资产Action。
```
> ⚙️ **验证方法**：测试添加新资产和编辑现有资产的功能。

#### 步骤 9.6: 创建资产详情页面 UI (如果需要)
如果原型中有单独的资产详情页：
在Cursor中提问:
```
@UI界面规则文档.md
请创建资产详情页面：
1. 创建文件`src/pages/assets/detail.vue`。
2. 页面显示特定资产账户的详细信息（名称、余额、类型等）。
3. 显示与该资产账户相关的交易记录列表（可筛选）。
4. 可能包含该账户的收支统计图表。
5. 提供编辑入口。
```
> 🎨 **验证方法**：访问页面，检查布局和元素。

#### 步骤 9.7: 连接资产详情页面逻辑
在Cursor中提问:
```
@src/pages/assets/detail.vue @src/stores/asset.store.ts @src/stores/transaction.store.ts
请为资产详情页面`src/pages/assets/detail.vue`连接逻辑：
1. 根据传入的账户ID从`asset.store`获取资产详情。
2. 从`transaction.store`获取并筛选与该资产相关的交易记录。
3. （如果需要）获取并渲染该账户的统计图表。
```
> ⚙️ **验证方法**：检查页面数据是否正确加载和筛选。

以下步骤内容需要完善在使用！！
步骤 X.1: 创建真实的 API 模块： 指导 Claude 创建（或完善）@/api/category.ts, @/api/transaction.ts 等，确保它们包含调用真实后端接口的函数。
步骤 X.2: 关闭 Mock 开关： 指导 Claude 修改 .env.development 文件，将 VITE_USE_MOCK_DATA 设置为 false。
步骤 X.3: 实现 Store 从 API 获取数据：
修改 Pinia Store（如 category.store.ts, transaction.store.ts）中的 Actions (例如 fetchCategories, fetchTransactions)。让这些 Action 在 Mock 关闭时，调用真实的 API 函数（从 @/api/... 导入）来获取数据，并将获取到的数据更新到 Store 的 state 中。
通常需要在应用启动或用户登录后，自动调用一次 fetchCategories 和 fetchTransactions 等 Action 来加载初始数据。这可以在 App.vue 的 onLaunch 或首页的 onLoad/onShow 中完成。
步骤 X.4: 实现 Store 向 API 发送数据： 修改 Store 中的 add, update, delete 等 Action，让它们在 Mock 关闭时，调用对应的真实 API 函数将更改发送到后端，并在成功后更新本地 Store 的 state。

### 阶段十：全面优化与最终测试

🎯 **目标**：进行全面的应用优化，包括性能、体验和跨平台兼容性，确保应用在所有目标平台上表现出色。

#### 步骤 10.1: 多端兼容性全面检查与优化
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-高级篇.md
请对所有页面进行全面的多端兼容性检查，并解决发现的问题：
1. 使用`utils/platform.js`中的工具函数确保所有页面都正确处理了设备差异
2. 检查所有页面在不同平台上的样式、布局和交互一致性
3. 验证所有表单组件在各平台的输入体验
4. 确保全局样式变量在各平台上正确应用
5. 检查动画和过渡效果在各平台的表现
```
> 📱 **验证方法**：在所有目标平台上完整测试应用的各个页面和功能。

#### 步骤 10.2: 应用性能全面优化
在Cursor中提问:
```
@开发规范与指南-高级篇.md
请对应用性能进行全面优化：
1. 对长列表或大数据组件进行虚拟列表优化
2. 实现图片懒加载和图片资源优化
3. 检查不必要的计算或重渲染，改用缓存或计算属性
4. 优化启动时间和页面加载时间
5. 检查并减少不必要的网络请求
6. 添加适当的加载状态和骨架屏
```
> ⚡ **验证方法**：使用性能分析工具测量优化前后的性能差异。

#### 步骤 10.3: 用户体验完善与细节打磨
在Cursor中提问:
```
@UI界面规则文档.md @开发规范与指南-高级篇.md
请对应用用户体验进行完善和细节打磨：
1. 添加合适的过渡动画和交互反馈
2. 完善空状态和错误状态的用户提示
3. 优化触摸区域大小和交互便捷性
4. 加强表单验证和用户引导
5. 改进主题切换流畅度和视觉一致性
6. 增强应用的辅助功能，提高可访问性
```
> 🎯 **验证方法**：进行全面的用户体验测试，确保应用使用流程顺畅自然。

#### 步骤 10.4: 数据与状态管理优化
在Cursor中提问:
```
@开发规范与指南-高级篇.md
请优化应用的数据和状态管理：
1. 检查并优化Pinia store的结构和性能
2. 完善全局错误处理和网络状态监听
3. 增强数据缓存策略和离线支持
4. 优化状态持久化和恢复机制
5. 确保数据变更合理触发UI更新
```
> 📊 **验证方法**：测试离线场景、应用关闭重开、状态恢复等情况下的数据稳定性。

#### 步骤 10.5: 多语言与本地化支持（可选）
在Cursor中提问:
```
@开发规范与指南-高级篇.md
请为应用添加多语言和本地化支持：
1. 配置i18n框架支持
2. 提取所有界面文本到语言文件
3. 实现语言切换功能
4. 处理不同语言环境下的日期、货币和数字格式
```
> 🌍 **验证方法**：切换不同语言，确认所有界面文本和格式正确显示。

#### 步骤 10.6: 手势与快捷操作优化
在Cursor中提问:
```
@开发规范与指南-高级篇.md
请优化应用的手势和快捷操作：
1. 为列表项添加滑动删除/编辑功能
2. 优化双指缩放或其他多指手势（如适用）
3. 改进滚动和拖动体验
4. 添加合适的快捷方式和快捷键（如适用）
```
> 👆 **验证方法**：测试各种手势操作的流畅度和准确性。

#### 步骤 10.7: 最终多端测试与问题修复
在Cursor中提问:
```
@开发规范与指南-高级篇.md
请进行最终的多端测试并修复发现的问题：
1. 在iOS、Android、小程序和H5四个平台上进行全面测试
2. 记录并分类所有发现的问题
3. 优先修复高优先级问题（如崩溃、数据丢失等）
4. 解决跨平台一致性问题
5. 进行最终的用户流程走查
```
> 🧪 **验证方法**：确保所有记录的问题都已解决，应用在所有目标平台上稳定运行。

### 阶段十一：部署上线准备

🎯 **目标**：从模拟数据迁移到真实后端API，确保所有功能可以与真实服务器交互，并为最终上线做准备。

#### 步骤 11.1: 创建真实的API模块
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md @API接口文档样例.md
请完善以下API模块，确保它们包含调用真实后端接口的函数：
1. 完善`src/api/category.ts`，实现调用真实后端的增删改查函数。
2. 完善`src/api/transaction.ts`，实现真实交易数据的获取和操作函数。
3. 检查其他API模块，确保它们也具备与真实后端通信的能力。
4. 确保所有API函数都有适当的错误处理和请求超时处理。
```
> 🔌 **验证方法**：检查API模块代码，确保所有函数都能正确处理真实后端通信。

#### 步骤 11.2: 关闭Mock数据开关
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md
请修改环境配置文件，禁用模拟数据：
1. 修改`.env.development`文件，将`VITE_USE_MOCK_DATA`设置为`false`。
2. 检查所有API模块是否正确处理了模拟数据开关。
3. 确保在没有后端服务的情况下，应用仍然可以运行（可能需要临时切换回模拟数据）。
```
> 🔄 **验证方法**：确认应用启动后不再使用模拟数据，而是尝试与真实后端通信。

#### 步骤 11.3: 实现Store从API获取数据
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md
请修改Pinia Store模块，实现从API获取数据：
1. 修改`src/stores/category.store.ts`中的`fetchCategories` Action，使其在模拟数据关闭时从真实API获取数据。
2. 修改`src/stores/transaction.store.ts`中的`fetchTransactions` Action，使其调用真实API函数。
3. 在应用启动或用户登录后，自动调用这些Action加载初始数据。
4. 确保所有Store都能处理API请求失败的情况。
```
> 🔄 **验证方法**：登录应用后，观察Store是否能成功从API获取并显示数据。

#### 步骤 11.4: 实现Store向API发送数据
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md
请修改Pinia Store模块，实现向API发送数据：
1. 修改所有Store中的`add`、`update`、`delete`等Action，使其在模拟数据关闭时调用真实API函数。
2. 确保所有数据变更操作都能正确发送到后端，并在成功后更新本地Store状态。
3. 实现乐观更新模式，在API请求成功前预先更新UI，提高用户体验。
4. 添加请求错误处理和回滚机制。
```
> 🔄 **验证方法**：测试添加、修改、删除等操作，确认数据能成功发送到后端并持久化。

#### 步骤 11.5: 最终API集成测试
在Cursor中提问:
```
@架构设计文档.md @开发规范与指南-基础篇.md
请进行全面的API集成测试：
1. 针对所有主要功能进行端到端测试，确保前后端数据交互正常。
2. 测试API错误处理和恢复机制。
3. 检查数据同步和冲突解决机制。
4. 验证离线模式和再连接逻辑。
5. 测试并发请求和请求队列机制。
```
> 🧪 **验证方法**：完整测试所有与后端交互的功能，确保数据流转正确、错误处理得当。

## 🔍 项目验证清单 (优化版)

使用此清单确认您的项目符合要求：

### 基础环境与配置
- [ ] 项目结构符合规范
- [ ] 全局样式变量已配置并应用
- [ ] 图标组件(<AppIcon>)正常工作
- [ ] 通用组件(<AppButton>, <AppCard>等)可用
- [ ] Pinia状态管理已配置
- [ ] easycom配置生效
- [ ] ESLint/Stylelint/Husky配置生效
- [ ] 项目能在H5环境正常运行 (`npm run dev:h5`)

### 通用组件库完整性
- [ ] 所有通用UI组件已创建并可用
- [ ] 所有业务组件基础结构已实现
- [ ] 组件样式使用CSS变量而非硬编码值
- [ ] 组件都有适当的TypeScript类型定义
- [ ] 组件测试页面可访问并显示所有组件

### 核心流程 - 认证
- [ ] 欢迎页正常显示和跳转
- [ ] 登录页面UI与原型一致
- [ ] 登录功能正常（API模拟或真实）
- [ ] 手势密码页面UI与原型一致
- [ ] 手势密码设置和验证功能正常
- [ ] 各平台上的登录体验一致流畅

### 核心流程 - 首页与交易
- [ ] 首页UI与原型一致
- [ ] 底部导航栏(TabBar)正常显示和切换
- [ ] 首页能正确显示资产和近期交易（模拟或真实数据）
- [ ] 交易列表页面UI与原型一致
- [ ] 交易列表能加载、刷新、加载更多、筛选
- [ ] 记账确认页面UI与原型一致
- [ ] 记账确认流程（输入、选择、保存）正常
- [ ] 分类管理页面UI与原型一致
- [ ] 分类管理功能（增删改）正常
- [ ] 列表性能优化（虚拟列表/懒加载）已实现

### 核心流程 - AI特性
- [ ] 语音记账页面UI与原型一致
- [ ] 语音录制和识别功能正常（权限处理）
- [ ] AI对话交互基本流程正常
- [ ] AI识别结果确认组件能正确显示和交互
- [ ] （可选）票据识别页面UI和基本流程
- [ ] 各平台上语音和相机权限管理一致

### 其他核心模块
- [ ] 账单提醒页面UI和基本功能
- [ ] 数据分析页面UI与原型一致
- [ ] 数据分析图表能正确显示和交互
- [ ] 预算设置页面UI与原型一致
- [ ] 预算设置和进度显示正常
- [ ] 个人中心页面UI与原型一致
- [ ] 个人信息页面UI和编辑功能
- [ ] 系统设置页面UI与原型一致
- [ ] 主题切换功能正常工作
- [ ] 资产总览页面UI与原型一致
- [ ] 资产账户列表和统计显示正常
- [ ] 资产添加/编辑功能正常

### 多端兼容性与适配
- [ ] iOS安全区域适配（刘海屏/全面屏）已实现
- [ ] Android不同分辨率适配良好
- [ ] 小程序胶囊按钮区域处理合理
- [ ] 键盘弹出逻辑在各平台表现一致
- [ ] 触摸反馈和动画在各平台流畅
- [ ] 条件编译代码正确处理平台差异
- [ ] 多端导航体验统一
- [ ] 字体和图标在各平台显示正常

### 性能与用户体验
- [ ] 应用启动时间合理(<3秒)
- [ ] 页面切换流畅无卡顿
- [ ] 列表滚动无明显掉帧
- [ ] 大型列表已实现虚拟滚动优化
- [ ] 骨架屏/加载状态已实现
- [ ] 过渡动画自然流畅
- [ ] 内存占用合理
- [ ] 电池消耗合理

### 数据与状态管理
- [ ] 本地存储策略恰当
- [ ] 数据同步机制可靠
- [ ] 离线使用支持
- [ ] 状态变更正确触发UI更新
- [ ] Pinia store结构合理清晰
- [ ] API错误处理完善

### 自动化测试覆盖率
- [ ] 单元测试框架配置完成
- [ ] 核心工具函数有测试覆盖
- [ ] 关键组件有测试覆盖
- [ ] 主要业务逻辑有测试覆盖
- [ ] （可选）主要流程有E2E测试覆盖
- [ ] CI/CD流程配置完成（如适用）

### 权限与安全
- [ ] 权限请求流程用户友好
- [ ] 权限被拒绝时有合理提示和替代方案
- [ ] 敏感数据存储安全
- [ ] 网络通信加密（HTTPS）
- [ ] Token管理和刷新逻辑合理
- [ ] 数据操作有适当的确认流程

### 质量保证
- [ ] 所有关键页面在iOS真机/模拟器上显示和交互正常
- [ ] 所有关键页面在Android真机/模拟器上显示和交互正常
- [ ] 所有关键页面在微信小程序开发者工具/真机上显示和交互正常
- [ ] 主要列表滚动流畅
- [ ] 页面切换无明显卡顿
- [ ] 错误处理机制健全，有友好提示
- [ ] 所有已知bug已修复或记录