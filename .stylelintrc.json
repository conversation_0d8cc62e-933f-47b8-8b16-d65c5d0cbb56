{"extends": ["stylelint-config-standard-scss", "stylelint-config-recommended-vue"], "rules": {"selector-max-id": 0, "selector-max-universal": 0, "selector-max-compound-selectors": 3, "selector-no-qualifying-type": true, "selector-class-pattern": null, "declaration-no-important": [true, {"severity": "warning", "message": "避免直接使用!important，请使用u-前缀的工具类（在tools/style/scripts/_utilities.scss中）或第三方样式覆盖场景。"}], "no-invalid-position-at-import-rule": null, "scss/load-partial-extension": null, "scss/load-no-partial-leading-underscore": null, "unit-no-unknown": [true, {"ignoreUnits": ["rpx"]}], "value-keyword-case": null, "no-descending-specificity": null, "declaration-property-value-no-unknown": null, "property-no-vendor-prefix": null, "no-duplicate-selectors": null, "function-url-quotes": null, "function-url-no-scheme-relative": null, "string-no-newline": null, "function-no-unknown": null, "max-line-length": null, "font-family-no-missing-generic-family-keyword": null, "scss/at-import-partial-extension": null, "scss/at-import-no-partial-leading-underscore": null, "scss/at-rule-no-unknown": null, "scss/no-global-function-names": null, "scss/at-extend-no-missing-placeholder": null, "scss/dollar-variable-colon-space-after": null, "scss/dollar-variable-no-missing-interpolation": null, "declaration-block-no-duplicate-properties": null, "declaration-block-no-shorthand-property-overrides": null, "selector-pseudo-class-no-unknown": null, "selector-type-no-unknown": null, "property-no-unknown": null, "scss/at-import-partial-extension-blacklist": null, "custom-property-empty-line-before": null, "scss/double-slash-comment-empty-line-before": null, "keyframes-name-pattern": null, "no-empty-source": null, "number-max-precision": null, "import-notation": null, "at-rule-no-unknown": null, "scss/comment-no-empty": null, "declaration-empty-line-before": null, "value-no-vendor-prefix": null, "media-feature-name-no-vendor-prefix": null, "scss/at-mixin-pattern": null, "color-function-notation": null}, "overrides": [{"files": ["**/*.vue"], "customSyntax": "postcss-html"}, {"files": ["**/*.scss"], "customSyntax": "postcss-scss"}], "ignoreFiles": ["dist/**", "node_modules/**", "**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx", "vendor/**", "**/*.min.css"]}