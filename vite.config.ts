import { defineConfig } from 'vite';
import uni from '@dcloudio/vite-plugin-uni';
import path from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  resolve: {
    alias: {
      '@': path.resolve(__dirname, 'src'),
      '@tools': path.resolve(__dirname, 'tools'),
      'uview-plus': path.resolve(__dirname, 'node_modules/uview-plus'),
      '/static': path.resolve(__dirname, 'static')
    }
  },
  css: {
    preprocessorOptions: {
      scss: {
        includePaths: [
          path.resolve(__dirname), 
          path.resolve(__dirname, 'node_modules'),
          path.resolve(__dirname, 'node_modules/uview-plus'),
          path.resolve(__dirname, 'src/assets/styles'),
          path.resolve(__dirname, 'src'),
          path.resolve(__dirname, 'static')
        ],
        importer: [(url) => {
          if (url.startsWith('uview-plus/')) {
            return { file: path.resolve(__dirname, 'node_modules', url) };
          }
          return null;
        }]
      }
    },
    devSourcemap: true // 开发环境下启用sourceMap方便调试
  },
  server: {
    host: '0.0.0.0',
    port: 8081,
    strictPort: false,
    open: true,
    hmr: {
      overlay: true
    },
    fs: {
      strict: false,
      allow: [
        path.resolve(__dirname, 'node_modules/uview-plus')
      ]
    }
  },
  build: {
    minify: false,
    sourcemap: true,
    terserOptions: {
      compress: {
        drop_console: false,
        drop_debugger: true
      }
    },
    reportCompressedSize: false,
    assetsInlineLimit: 4096,
    rollupOptions: {
      output: {
        assetFileNames: 'static/[ext]/[name]-[hash].[ext]',
      }
    }
  },
  optimizeDeps: {
    include: [
      'uview-plus'
    ],
    exclude: []
  }
});