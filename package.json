{"name": "uni-preset-vue", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-jd": "uni -p mp-jd", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-jd": "uni build -p mp-jd", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "type-check": "vue-tsc --noEmit", "lint": "eslint --ext .js,.ts,.vue src", "lint:fix": "eslint --ext .js,.ts,.vue src --fix", "lint:style": "stylelint \"src/**/*.{vue,scss,css}\"", "lint:style:fix": "stylelint \"src/**/*.{vue,scss,css}\" --fix --allow-empty-input", "lint:style:safe": "stylelint \"src/**/*.{vue,scss,css}\" --custom-syntax postcss-scss", "lint:style:check-only": "stylelint \"src/**/*.{vue,scss,css}\" --quiet --no-fix", "lint:style:fix-careful": "stylelint \"src/components/common/**/*.vue\" \"src/components/business/**/*.vue\" --fix --allow-empty-input --quiet", "lint:svg-safe": "stylelint \"src/**/*.{scss,css}\" --fix --quiet && stylelint \"src/**/*.vue\" --fix --ignore-path .stylelintignore --quiet", "lint:check-only": "eslint --ext .js,.ts,.vue src --no-fix", "prepare": "husky install", "postinstall": "cp -r patches/uview-plus/components/u-icon/u-icon.vue node_modules/uview-plus/components/u-icon/u-icon.vue"}, "dependencies": {"@dcloudio/uni-app": "3.0.0-4030620241128001", "@dcloudio/uni-app-harmony": "3.0.0-4030620241128001", "@dcloudio/uni-app-plus": "3.0.0-4030620241128001", "@dcloudio/uni-components": "3.0.0-4030620241128001", "@dcloudio/uni-h5": "3.0.0-4030620241128001", "@dcloudio/uni-mp-alipay": "3.0.0-4030620241128001", "@dcloudio/uni-mp-baidu": "3.0.0-4030620241128001", "@dcloudio/uni-mp-jd": "3.0.0-4030620241128001", "@dcloudio/uni-mp-kuaishou": "3.0.0-4030620241128001", "@dcloudio/uni-mp-lark": "3.0.0-4030620241128001", "@dcloudio/uni-mp-qq": "3.0.0-4030620241128001", "@dcloudio/uni-mp-toutiao": "3.0.0-4030620241128001", "@dcloudio/uni-mp-weixin": "3.0.0-4030620241128001", "@dcloudio/uni-mp-xhs": "3.0.0-4030620241128001", "@dcloudio/uni-quickapp-webview": "3.0.0-4030620241128001", "@dcloudio/uni-ui": "^1.4.28", "@qiun/ucharts": "2.5.0-20230101", "dayjs": "^1.11.13", "pinia": "2.2.1", "pinia-plugin-persistedstate": "^2.3.0", "uqrcodejs": "^4.0.7", "uuid": "^9.0.1", "uview-plus": "^3.1.37", "vue": "3.4.21", "vue-i18n": "^9.1.9"}, "devDependencies": {"@antfu/eslint-config": "^4.12.0", "@dcloudio/types": "^3.4.8", "@dcloudio/uni-automator": "3.0.0-4030620241128001", "@dcloudio/uni-cli-shared": "^3.0.0-4030620241128001", "@dcloudio/uni-stacktracey": "3.0.0-4030620241128001", "@dcloudio/vite-plugin-uni": "3.0.0-4030620241128001", "@eslint/js": "^9.24.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.31.0", "@typescript-eslint/parser": "^8.31.0", "@uni-helper/eslint-config": "^0.4.0", "@vitejs/plugin-vue": "^5.1.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/runtime-core": "^3.4.21", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.1.3", "eslint": "^9.25.1", "eslint-config-prettier": "^10.1.2", "eslint-define-config": "^2.1.0", "eslint-plugin-jsonc": "^2.20.0", "eslint-plugin-markdown": "^5.1.0", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-vue": "^9.30.0", "globals": "^16.0.0", "happy-dom": "^17.4.4", "husky": "^9.1.7", "lint-staged": "^15.5.0", "patch-package": "^8.0.0", "postcss-html": "^1.8.0", "prettier": "^3.5.3", "sass": "^1.89.0", "sass-loader": "^10.5.2", "sass-migrator": "^2.3.2", "stylelint": "^16.18.0", "stylelint-config-recommended-vue": "^1.6.0", "stylelint-config-standard-scss": "^14.0.0", "stylelint-scss": "^6.11.1", "transform-to-unocss": "^0.1.10", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1", "vite": "^5.2.8", "vitest": "^3.1.1", "vue-eslint-parser": "^10.1.3", "vue-tsc": "^1.0.24"}, "lint-staged": {"*.{js,ts,vue}": ["prettier --write", "eslint --fix --config eslint.config.mjs --max-warnings=0", "git add"], "*.{css,scss}": ["prettier --write", "stylelint --fix --allow-empty-input --quiet", "git add"], "src/**/*.vue": ["prettier --write --ignore-path .<PERSON><PERSON><PERSON><PERSON>", "eslint --fix --config eslint.config.mjs --max-warnings=0", "stylelint --fix --allow-empty-input --quiet --ignore-path .stylelint<PERSON>ore", "git add"]}}