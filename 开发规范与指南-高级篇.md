# 开发规范与指南-高级篇

+----------------------------------+
|       项目文档关系图              |
+----------------------------------+
|                                  |
|  架构设计文档.md                  |
|  (整体架构、技术选型、目录结构)    |
|          |                       |
|          v                       |
|  开发文档和规范.md                |
|  (开发规范、编码标准、命名约定)    |
|          |                       |
|          v                       |
|  UI界面规则文档.md                |
|  (UI设计、组件样式、布局规范)      |
|                                  |
+----------------------------------+

> 本文档是《开发规范与指南-基础篇》的补充，主要面向进阶开发需求和最佳实践。

## 高级代码组织与模式

### 1. 组合式函数 (Composables)
- **定义**: 组合式函数是对可复用逻辑的封装，以 `useXxx.js` 命名，放置在 `src/hooks/` 目录下
- **返回值**: 组合式函数应当返回一个对象，包含响应式状态和方法
- **生命周期**: 组合式函数内部的副作用 **必须 (MUST)** 使用适当的生命周期钩子包裹

**标准示例:**
```javascript
// src/hooks/useCounter.js
import { ref, computed, onMounted, onUnmounted } from 'vue';

export function useCounter(initialValue = 0, options = {}) {
  // 参数验证
  if (typeof initialValue !== 'number') {
    console.warn('useCounter: initialValue 应该是一个数字');
    initialValue = 0;
  }
  
  // 响应式状态
  const count = ref(initialValue);
  
  // 计算属性
  const isPositive = computed(() => count.value > 0);
  
  // 方法
  const increment = () => { count.value++ };
  const decrement = () => { count.value-- };
  const reset = () => { count.value = initialValue };
  
  // 副作用处理
  let timer = null;
  
  if (options.autoIncrement) {
    onMounted(() => {
      timer = setInterval(() => {
        increment();
      }, options.interval || 1000);
    });
    
    // 清理副作用！
    onUnmounted(() => {
      if (timer) clearInterval(timer);
    });
  }
  
  return {
    count,     // 响应式状态
    isPositive, // 计算属性
    increment,  // 方法
    decrement,
    reset
  };
}
```

### 2. 依赖注入模式
- **提供数据**: 使用 `provide` 在上层组件提供数据
- **注入数据**: 使用 `inject` 在下层组件接收数据
- **默认值**: 使用 `inject` 时 **必须 (MUST)** 提供默认值

**依赖注入示例:**
```javascript
// 父组件提供数据
import { provide } from 'vue';

provide('themeColor', ref('light'));

// 子组件注入数据
import { inject } from 'vue';

const themeColor = inject('themeColor', ref('light')); // 提供默认值
```

### 3. 项目级状态管理

#### Pinia 最佳实践
- **模块化**: 按业务领域划分 store，每个 store 保持独立。
- **TypeScript**: **必须 (MUST)** 使用 TypeScript 定义 store 的类型，便于代码补全和类型检查。
- **选择API**: **必须 (MUST)** 使用 `composition API` 风格的 Pinia store (即 `setup` 函数风格)。
- **持久化**: 需要持久化的状态应谨慎选择，并可使用 `pinia-plugin-persistedstate` 插件。
- **共享数据**: 应用级别的共享数据（如用户信息、**分类列表**、全局设置等）**必须 (MUST)** 通过 Pinia Store 管理，确保数据唯一性和一致性。

**用户状态示例 (`user.store.ts`):**
```typescript
// src/stores/user.store.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue'; // 确保导入 ref 和 computed
import type { UserInfo, LoginCredentials } from '@/types'; // 假设类型在 @/types
import { login, logout } from '@/api/auth'; // 假设 API 在 @/api/auth

// 使用 Setup 函数风格定义 Store
export const useUserStore = defineStore('user', () => {
  // State
  const userInfo = ref<UserInfo | null>(null);
  const token = ref<string | null>(null); // 添加 token 状态

  // Getters (使用 computed)
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value); // 更可靠的登录状态判断
  const userName = computed(() => userInfo.value?.name ?? '未登录用户');
  const userRole = computed(() => userInfo.value?.role ?? 'guest');

  // Actions (普通函数)
  async function loginUser(credentials: LoginCredentials): Promise<boolean> {
    try {
      // 调用 API (假设 api.login 返回 { user: UserInfo, token: string })
      const result = await login(credentials);
      userInfo.value = result.user;
      token.value = result.token;
      // 可以在这里处理 token 持久化，或者使用持久化插件
      uni.setStorageSync('access_token', result.token); // 示例：使用 uni-app API 存储
      return true;
    } catch (error) {
      console.error('登录失败:', error);
      // 清理状态
      userInfo.value = null;
      token.value = null;
      uni.removeStorageSync('access_token');
      return false;
    }
  }

  async function logoutUser(): Promise<boolean> {
    try {
      // 可选：调用后端 API 使 token 失效
      if (token.value) {
        await logout(token.value); // 假设有 logout API
      }
    } catch (error) {
       console.error('调用退出登录API失败 (但仍会继续前端退出):', error);
    } finally {
      // 清理前端状态和存储
      userInfo.value = null;
      token.value = null;
      uni.removeStorageSync('access_token');
    }
    return true; // 通常前端退出操作总是"成功"
  }

  // 可选：用于应用启动时检查本地存储的 token
  function checkLoginStatus() {
     const storedToken = uni.getStorageSync('access_token');
     if (storedToken) {
         token.value = storedToken;
         // 这里通常还需要一个 API 请求来获取用户信息
         // fetchUserInfo(); // 假设有此 action
     }
  }


  // 必须返回 state, getters, actions
  return {
    // State
    userInfo,
    token,
    // Getters
    isLoggedIn,
    userName,
    userRole,
    // Actions
    loginUser,
    logoutUser,
    checkLoginStatus,
    // resetState // setup store 中可以直接 userInfo.value = null 等操作
  };
}, {
  // 持久化插件配置 (如果使用)
  // persist: {
  //   key: 'user-store',
  //   storage: { // 适配 uni-app 的 storage
  //     getItem: uni.getStorageSync,
  //     setItem: uni.setStorageSync,
  //     removeItem: uni.removeStorageSync,
  //   },
  //   paths: ['token', 'userInfo'] // 选择要持久化的状态
  // }
});
**共享列表数据示例 (category.store.ts 片段):
// src/stores/category.store.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import type { Category } from '@/types'; // 假设定义了 Category 类型
import { fetchCategoriesApi, addCategoryApi } from '@/api/category'; // 假设的 API

// 初始默认分类数据 (可以放在这里或单独文件导入)
const defaultCategories: Category[] = [
  { id: 'cat-food', name: '餐饮', icon: 'integral', color: '#FF6B35', type: 'expense' }, // 例如: integral (uView Plus)
  { id: 'cat-shopping', name: '购物', icon: 'shopping-cart', color: '#4CAF50', type: 'expense' }, // 例如: shopping-cart (uView Plus)
  { id: 'cat-salary', name: '工资', icon: 'rmb-circle', color: '#4CAF50', type: 'income' }, // 例如: rmb-circle (uView Plus)
  // ... 其他默认分类
];

export const useCategoryStore = defineStore('category', () => {
  // State
  const categories = ref<Category[]>([]); // 初始为空，将从API或默认值加载
  const isLoading = ref(false);

  // Getters
  const getCategoryById = computed(() => {
    return (id: string) => categories.value.find(cat => cat.id === id);
  });
  const expenseCategories = computed(() => categories.value.filter(cat => cat.type === 'expense'));
  const incomeCategories = computed(() => categories.value.filter(cat => cat.type === 'income'));

  // Actions
  async function fetchCategories() {
    if (categories.value.length > 0) return; // 避免重复加载
    isLoading.value = true;
    try {
      // 优先从 API 加载，如果失败或无数据，使用默认值
      const dataFromApi = await fetchCategoriesApi(); // 假设的 API 调用
      if (dataFromApi && dataFromApi.length > 0) {
           categories.value = dataFromApi;
      } else {
           categories.value = defaultCategories;
      }
    } catch (error) {
      console.error('获取分类失败, 使用默认分类:', error);
      categories.value = defaultCategories; // 出错时使用默认值
    } finally {
      isLoading.value = false;
    }
  }

  async function addCategory(newCategoryData: Omit<Category, 'id'>) {
     // 调用 API 添加，成功后更新本地 state 或重新 fetch
     try {
         const addedCategory = await addCategoryApi(newCategoryData); // 假设 API 返回添加后的对象
         categories.value.push(addedCategory);
     } catch (error) {
         console.error("添加分类失败:", error);
         // 可能需要显示错误提示给用户
     }
  }

  // ... 其他 Actions 如 updateCategory, deleteCategory

  return {
    categories, // 直接暴露 state 也是常见的做法
    isLoading,
    getCategoryById,
    expenseCategories,
    incomeCategories,
    fetchCategories,
    addCategory,
  };
});

## 性能优化策略

### 1. 组件性能优化
- **懒加载**: 路由和大型组件应使用懒加载
- **虚拟列表**: 长列表渲染应使用虚拟滚动
- **避免不必要的计算**: 使用缓存计算，避免重复执行昂贵计算

**路由懒加载示例:**
```javascript
// src/router/index.js
const routes = [
  {
    path: '/dashboard',
    component: () => import('@/pages/dashboard/index.vue') // 懒加载
  },
  {
    path: '/analysis',
    component: () => import(/* webpackChunkName: "analysis" */ '@/pages/analysis/index.vue')
  }
];
```

**虚拟列表示例:**
```vue
<template>
  <RecycleScroller
    class="transaction-list"
    :items="transactions"
    :item-size="80"
    key-field="id"
  >
    <template #item="{ item }">
      <TransactionCard :transaction="item" />
    </template>
  </RecycleScroller>
</template>

<script setup>
import { RecycleScroller } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';
import TransactionCard from '@/components/business/TransactionCard.vue';
import { ref } from 'vue';

const transactions = ref([/* 大量数据 */]);
</script>
```

### 2. 资源优化
- **图片优化**: 
  - 使用 WebP 格式
  - 根据设备 DPR 提供不同分辨率图片
  - 懒加载非关键图片
- **字体优化**:
  - 使用字体子集
  - 预加载关键字体
  - 考虑使用系统字体降级

**图片懒加载示例:**
```vue
<template>
  <img
    v-for="img in images"
    :key="img.id"
    :data-src="img.src"
    class="lazy-image"
    loading="lazy" 
  />
</template>

<script setup>
import { onMounted } from 'vue';

onMounted(() => {
  // 使用 Intersection Observer 实现懒加载
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        const img = entry.target;
        img.src = img.dataset.src;
        observer.unobserve(img);
      }
    });
  });
  
  document.querySelectorAll('.lazy-image').forEach(img => {
    observer.observe(img);
  });
});
</script>
```

### 3. 网络优化
- **预加载**: 预加载可能即将使用的资源
- **缓存策略**: 合理利用 HTTP 缓存、Local Storage 和 indexedDB
- **请求合并**: 减少请求次数，避免请求瀑布

**预加载示例:**
```javascript
// 预加载下一个可能访问的页面
const prefetchNextPage = () => {
  const prefetchLink = document.createElement('link');
  prefetchLink.rel = 'prefetch';
  prefetchLink.href = '/next-page';
  document.head.appendChild(prefetchLink);
};

// 在用户悬停在链接上或即将完成当前页面任务时调用
```

**API 缓存策略:**
```javascript
// src/utils/request.js 中增加缓存机制
const cache = new Map();

export const request = {
  // 带缓存的 GET 请求
  async getWithCache(url, params = {}, cacheOptions = {}) {
    const { ttl = 60000, forceRefresh = false } = cacheOptions;
    const cacheKey = `${url}?${JSON.stringify(params)}`;
    
    // 检查缓存是否有效
    const cachedData = cache.get(cacheKey);
    const isCacheValid = cachedData && (Date.now() - cachedData.timestamp < ttl);
    
    if (isCacheValid && !forceRefresh) {
      return cachedData.data;
    }
    
    // 缓存无效或强制刷新，发起请求
    const data = await this.get(url, params);
    
    // 更新缓存
    cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
    
    return data;
  }
};
```

## 前端安全最佳实践

### 1. XSS 防护
- **输入验证**: 对用户输入进行验证和过滤
- **输出编码**: 对展示的内容进行适当编码
- **CSP**: 配置 Content-Security-Policy 防护跨站脚本攻击

**安全渲染示例:**
```javascript
// 安全的内容渲染函数
import { escapeHtml } from '@/utils/security';

function renderUserContent(content) {
  // 净化并转义 HTML
  return escapeHtml(content);
}

// utils/security.js
export function escapeHtml(unsafeText) {
  return unsafeText
    .replace(/&/g, '&amp;')
    .replace(/</g, '&lt;')
    .replace(/>/g, '&gt;')
    .replace(/"/g, '&quot;')
    .replace(/'/g, '&#039;');
}
```

### 2. 身份验证和授权
- **JWT 处理**: 安全存储和传输 JWT
- **令牌刷新**: 实现无感知令牌刷新机制
- **权限检查**: 前端权限验证与后端权限验证结合

**JWT 令牌管理示例:**
```javascript
// src/utils/token.js
export const tokenManager = {
  getToken() {
    return localStorage.getItem('access_token');
  },
  
  setToken(token) {
    localStorage.setItem('access_token', token);
  },
  
  removeToken() {
    localStorage.removeItem('access_token');
  },
  
  // 解析 JWT 获取过期时间
  getExpirationTime(token) {
    if (!token) return 0;
    
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      return payload.exp * 1000; // 转换为毫秒
    } catch (e) {
      console.error('解析 JWT 失败', e);
      return 0;
    }
  },
  
  // 判断令牌是否即将过期（10分钟内）
  isTokenExpiringSoon(token) {
    const expirationTime = this.getExpirationTime(token);
    return expirationTime - Date.now() < 10 * 60 * 1000;
  }
};
```

### 3. 敏感数据处理
- **数据脱敏**: 敏感信息展示时进行脱敏处理
- **安全存储**: 不要在前端存储敏感数据
- **传输加密**: 确保所有 API 请求使用 HTTPS

**数据脱敏示例:**
```javascript
// src/utils/formatter.js
export const dataFormatter = {
  // 手机号脱敏：显示前3位和后4位，中间用星号代替
  maskPhone(phone) {
    if (!phone || phone.length < 7) return phone;
    return `${phone.substring(0, 3)}****${phone.substring(phone.length - 4)}`;
  },
  
  // 银行卡号脱敏：只显示后4位
  maskCardNumber(cardNumber) {
    if (!cardNumber || cardNumber.length < 4) return cardNumber;
    return `**** **** **** ${cardNumber.substring(cardNumber.length - 4)}`;
  },
  
  // 姓名脱敏：显示姓，名用星号代替
  maskName(name) {
    if (!name || name.length < 2) return name;
    return `${name.substring(0, 1)}${'*'.repeat(name.length - 1)}`;
  }
};
```

## 自动化测试策略

### 1. 单元测试
- **测试范围**: 重点测试工具函数、复杂业务逻辑和组合式函数
- **测试框架**: 使用 Vitest 配合 Vue Test Utils
- **Mock 策略**: 使用 vi.mock() 模拟外部依赖

**单元测试示例:**
```javascript
// src/utils/__tests__/formatter.spec.js
import { describe, it, expect } from 'vitest';
import { dataFormatter } from '../formatter';

describe('dataFormatter', () => {
  describe('maskPhone', () => {
    it('应该正确脱敏手机号', () => {
      expect(dataFormatter.maskPhone('***********')).toBe('138****5678');
    });
    
    it('处理无效输入时应返回原值', () => {
      expect(dataFormatter.maskPhone('')).toBe('');
      expect(dataFormatter.maskPhone(null)).toBe(null);
      expect(dataFormatter.maskPhone('123')).toBe('123');
    });
  });
  
  // 其他测试...
});
```

### 2. 组件测试
- **测试策略**:
  - **通用组件 (`src/components/common/`)**: 争取高覆盖率，测试各种 props 和 slots 的组合情况。
  - **业务组件 (`src/components/business/`)**: 重点测试核心业务逻辑、关键交互流程、与 Store 的交互以及边缘情况。
  - **页面组件 (`src/pages/`)**: 通常进行集成度更高的测试或 E2E 测试，单元测试主要覆盖页面内独立的复杂逻辑。
- **测试框架**: 使用 Vitest 配合 `@vue/test-utils`。
- **Store Mocking**: 测试依赖 Pinia Store 的组件时，**必须 (MUST)** 使用 `@pinia/testing` 提供的 `createTestingPinia` 来创建 Mock Store 实例并注入。

**基础组件测试示例 (`AppButton.vue`):**
```javascript
// src/components/common/__tests__/AppButton.spec.js
import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import AppButton from '../AppButton.vue';

describe('AppButton', () => {
  it('应该正确渲染按钮文本', () => {
    const wrapper = mount(AppButton, {
      slots: {
        default: '点击我'
      }
    });
    expect(wrapper.text()).toBe('点击我');
  });

  it('点击时应该触发 click 事件', async () => {
    const onClick = vi.fn(); // 使用 vi.fn() 创建 mock 函数
    const wrapper = mount(AppButton, {
      // 在 props 中传递 mock 函数
      props: { onClick } // 假设 AppButton 通过 props 接收 onClick
      // 或者如果 AppButton 是通过 emit('click') 发出事件
      // 则不需要 props，直接检查 wrapper.emitted().click
    });

    await wrapper.trigger('click');

    // 如果是通过 props 传递
    // expect(onClick).toHaveBeenCalled();

    // 如果是通过 emit 发出
     expect(wrapper.emitted().click).toBeTruthy(); // 检查是否发出了 click 事件
     expect(wrapper.emitted().click.length).toBe(1); // 确保只发出一次
  });

  it('禁用时不应触发 click 事件', async () => {
    const wrapper = mount(AppButton, {
      props: {
        disabled: true
      }
    });

    await wrapper.trigger('click');
    expect(wrapper.emitted().click).toBeFalsy(); // 确认没有发出 click 事件
    // 检查是否包含禁用相关的 class
    expect(wrapper.classes()).toContain('app-button--disabled'); // 假设禁用类名是 .app-button--disabled
  });

  // ... 可以添加更多测试，如不同 size, type 的 props 测试
});

**依赖 Store 的业务组件测试示例 (TransactionItem.vue):// src/components/business/__tests__/TransactionItem.spec.ts
import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { createTestingPinia } from '@pinia/testing'; // 导入测试工具

import TransactionItem from '../TransactionItem.vue'; // 被测试组件
import { useCategoryStore } from '@/stores/category.store'; // 依赖的 Store
import type { Transaction, Category } from '@/types'; // 相关类型

// 模拟的交易数据
const mockTransaction: Transaction = {
  id: 'tx-123',
  type: 'expense',
  amount: 99.50,
  categoryId: 'cat-shopping', // 关联 Category ID
  date: '2024-07-10T10:00:00Z',
  description: '买了一些零食',
  accountId: 'acc-cash',
};

// 模拟的分类数据
const mockCategory: Category = {
  id: 'cat-shopping',
  name: '购物',
  icon: 'shopping-cart', // uView Plus icon name, e.g., 'shopping-cart' or 'bag'
  color: '#4CAF50',      // Background color
  type: 'expense'
};

describe('TransactionItem.vue', () => {
  let pinia: ReturnType<typeof createTestingPinia>;

  beforeEach(() => {
    // 在每个测试前创建新的 Pinia 测试实例
    pinia = createTestingPinia({
      // 可以创建 spy 来监视 action 调用
      createSpy: vi.fn,
      // 设置初始状态
      initialState: {
        // 这里的 key 必须是 store 的 id ('category')
        category: {
          categories: [mockCategory], // 提供测试所需的分类数据
        },
      },
      // 如果需要 stub 掉 action
      // stubActions: false,
    });
  });

  it('应该根据 Store 数据正确显示分类图标和名称', () => {
    // 获取 mock store 实例 (可选，如果需要直接操作 store)
    const categoryStore = useCategoryStore(pinia);
    // 可以断言 store 的初始状态
    expect(categoryStore.categories).toHaveLength(1);

    // 挂载组件，传入 props 和 mock pinia 实例
    const wrapper = mount(TransactionItem, {
      props: {
        transaction: mockTransaction,
      },
      global: {
        plugins: [pinia], // **关键：注入 mock Pinia**
         // 如果 AppIcon 是全局注册的或用了 easycom，这里通常不需要 stub
         // stubs: { AppIcon: true } // 如果需要 stub 掉子组件
      },
    });

    // 断言组件渲染内容是否正确
    // 1. 检查分类名称是否显示
    expect(wrapper.text()).toContain(mockCategory.name); // "购物"

    // 2. 检查是否渲染了 AppIcon (或者其内部的 <i> 标签)
    const appIconWrapper = wrapper.findComponent({ name: 'AppIcon' }); // 假设 AppIcon 有 name 选项
    // 或者查找渲染出的 i 标签
    // const iconElement = wrapper.find('i');

    expect(appIconWrapper.exists()).toBe(true); // 确认 AppIcon 组件存在

    // 3. 检查 AppIcon 是否接收了正确的 props (来自 Store 的数据)
    expect(appIconWrapper.props('icon')).toBe(mockCategory.icon); // e.g., 'shopping-cart'
    // 注意：颜色可能是通过 style 绑定的，需要检查渲染出的 style
    // expect(appIconWrapper.attributes('style')).toContain(`color: ${mockCategory.color}`); // 假设颜色是 style

    // 不再检查 Font Awesome 的 class
    // expect(iconElement.classes()).toContain(`fa-${mockCategory.icon}`);
  });

  it('应该根据交易类型显示正确的金额样式', () => {
     const wrapper = mount(TransactionItem, {
      props: {
        transaction: mockTransaction, // 支出类型
      },
      global: { plugins: [pinia] },
    });

     // 假设金额元素有特定类名
     const amountElement = wrapper.find('.transaction-item__amount'); // 假设金额的类名
     expect(amountElement.exists()).toBe(true);
     expect(amountElement.classes()).toContain('transaction-item__amount--expense'); // 假设支出类名
     expect(wrapper.text()).toContain(`-${mockTransaction.amount.toFixed(2)}`); // 检查金额格式
  });

  // ... 可以添加更多测试，如收入类型、无备注、长备注等情况
});
```

### 3. 端到端测试
- **范围**: 覆盖关键业务流程和用户旅程
- **框架**: 使用 Cypress 或 Playwright 进行端到端测试
- **环境**: 在类生产环境中进行测试，使用测试数据

**端到端测试示例:**
```javascript
// cypress/e2e/login.cy.js
describe('登录流程', () => {
  beforeEach(() => {
    cy.visit('/login');
  });
  
  it('应该能成功登录', () => {
    cy.get('[data-cy=username]').type('testuser');
    cy.get('[data-cy=password]').type('password123');
    cy.get('[data-cy=login-button]').click();
    
    // 验证登录成功后跳转
    cy.url().should('include', '/dashboard');
    cy.get('[data-cy=user-greeting]').should('contain', 'testuser');
  });
  
  it('输入错误凭据时应显示错误消息', () => {
    cy.get('[data-cy=username]').type('wronguser');
    cy.get('[data-cy=password]').type('wrongpass');
    cy.get('[data-cy=login-button]').click();
    
    cy.get('[data-cy=error-message]').should('be.visible');
    cy.url().should('include', '/login');
  });
});
```

## 多端适配高级实践

### 1. 响应式设计系统
- **断点系统**: 定义统一的断点体系，适配原型UI显示的多端设计
- **适配策略**: 采用移动优先的设计策略，优先适配iOS和Android设备
- **响应式容器**: 使用flex和grid布局创建自适应容器，更好支持原型UI的卡片式布局

**断点定义示例:**
```scss
// src/assets/styles/_breakpoints.scss
$breakpoints: (
  xs: 0,      // 小型手机
  sm: 375px,  // iPhone SE / 小型安卓
  md: 414px,  // iPhone Pro Max / 主流安卓
  lg: 768px,  // iPad Mini / 平板
  xl: 1024px  // iPad Pro / 大型平板
);

@mixin respond-to($breakpoint) {
  $value: map-get($breakpoints, $breakpoint);
  
  @if $value != null {
    @media (min-width: $value) {
      @content;
    }
  } @else {
    @error "Unknown breakpoint: #{$breakpoint}";
  }
}

// 使用示例 - 针对原型UI中的卡片组件
.card {
  width: calc(100% - 32px);
  margin: 16px;
  
  @include respond-to(md) {
    // 在大屏设备上优化卡片布局
    max-width: 380px;
    margin: 16px auto;
  }
  
  @include respond-to(lg) {
    // 平板设备上的优化
    max-width: 420px;
    margin: 20px auto;
  }
}

// 响应式网格布局 - 适用于原型UI中的分析页面
.analysis-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 16px;
  
  @include respond-to(md) {
    grid-template-columns: 1fr 1fr;
  }
  
  @include respond-to(lg) {
    grid-template-columns: 1fr 2fr 1fr;
  }
}
```

### 2. 平台特定功能适配
- **功能检测**: 使用特性检测而非设备检测，确保应用在不同平台上的稳定性
- **平台降级**: 提供优雅的功能降级方案，与原型UI保持一致的视觉体验
- **条件编译**: 针对特定平台，使用条件编译提供专属实现

**功能检测示例:**
```javascript
// src/utils/platform.js 增强版
/**
 * 平台能力检测与适配
 * 扩展基础版platform.js中的功能
 */
export const platformCapabilities = {
  // 检测是否支持指纹/面部识别 (用于手势密码页面的生物识别替代)
  hasBiometrics() {
    // #ifdef APP-PLUS
    return new Promise((resolve) => {
      plus.fingerprint.isSupport((type) => {
        resolve(type !== null);
      });
    });
    // #endif
    
    // 其他平台默认不支持
    return Promise.resolve(false);
  },
  
  // 检测是否支持录音功能 (用于语音记账页面)
  hasVoiceCapability() {
    // #ifdef APP-PLUS || MP-WEIXIN
    try {
      const rm = uni.getRecorderManager();
      return !!rm;
    } catch (err) {
      console.error('录音功能检测失败', err);
      return false;
    }
    // #endif
    
    // #ifdef H5
    return navigator.mediaDevices && 
           navigator.mediaDevices.getUserMedia && 
           window.MediaRecorder;
    // #endif
    
    return false;
  },
  
  // 检测是否支持图表功能 (用于数据分析页面)
  hasChartCapability() {
    // 所有平台基本都支持Canvas
    return true;
  },
  
  // 获取安全区域信息 (适配不同机型的安全区域)
  getSafeAreaInsets() {
    try {
      const systemInfo = uni.getSystemInfoSync();
      
      // #ifdef APP-PLUS || MP-WEIXIN
      if (systemInfo.safeArea) {
        return {
          top: systemInfo.safeArea.top,
          right: systemInfo.screenWidth - systemInfo.safeArea.right,
          bottom: systemInfo.screenHeight - systemInfo.safeArea.bottom,
          left: systemInfo.safeArea.left
        };
      }
      // #endif
      
      // 默认安全区域
      return { top: 0, right: 0, bottom: 0, left: 0 };
    } catch (err) {
      console.error('获取安全区域失败', err);
      return { top: 0, right: 0, bottom: 0, left: 0 };
    }
  },
  
  // 获取设备性能等级 (用于决定是否启用高级动画效果)
  getDevicePerformanceLevel() {
    try {
      const systemInfo = uni.getSystemInfoSync();
      
      // #ifdef APP-PLUS
      // 根据设备型号、内存、CPU核心数评估性能
      const deviceModel = systemInfo.model;
      const isHighEndDevice = (
        deviceModel.includes('iPhone') && parseInt(deviceModel.replace(/[^0-9]/g, '')) >= 11 || 
        systemInfo.platform === 'android' && systemInfo.system.includes('Android 10')
      );
      
      return isHighEndDevice ? 'high' : 'medium';
      // #endif
      
      // #ifdef H5
      // 简单检测是否为移动设备
      const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      return isMobile ? 'medium' : 'high';
      // #endif
      
      // #ifdef MP-WEIXIN
      // 小程序环境默认为中等性能
      return 'medium';
      // #endif
      
      return 'medium'; // 默认中等性能级别
    } catch (err) {
      console.error('获取设备性能级别失败', err);
      return 'low'; // 出错时返回低性能级别，确保应用稳定运行
    }
  }
};
```

**平台降级示例:**
```javascript
// src/utils/voiceInput.js - 语音输入功能 (原型UI中的语音记账页面)
import { platformCapabilities } from './platform';

export const voiceInputService = {
  // 初始化语音录制
  async init() {
    if (await platformCapabilities.hasVoiceCapability()) {
      // 支持原生录音
      this.recorder = uni.getRecorderManager();
      this.setupRecorderListeners();
      return true;
    } else {
      // 降级方案：使用文本输入 + AI解析
      console.log('当前平台不支持语音录制，已切换到文本输入模式');
      return false;
    }
  },
  
  // 开始录音
  startRecording() {
    if (!this.recorder) {
      uni.showToast({
        title: '录音功能不可用，请使用文本输入',
        icon: 'none'
      });
      return false;
    }
    
    // 不同平台使用不同的录音参数
    const options = {
      duration: 60000, // 最长录音时间
      sampleRate: 16000,
      numberOfChannels: 1,
      encodeBitRate: 48000,
      format: 'mp3',
      frameSize: 50
    };
    
    // #ifdef MP-WEIXIN
    options.frameSize = 1; // 小程序需要调整此参数
    // #endif
    
    this.recorder.start(options);
    return true;
  },
  
  // 停止录音
  stopRecording() {
    if (this.recorder) {
      this.recorder.stop();
    }
  },
  
  // 设置录音器监听
  setupRecorderListeners() {
    if (!this.recorder) return;
    
    this.recorder.onStart(() => {
      this.isRecording = true;
      if (this.onStart) this.onStart();
    });
    
    this.recorder.onStop((res) => {
      this.isRecording = false;
      if (this.onStop) this.onStop(res);
    });
    
    this.recorder.onError((err) => {
      this.isRecording = false;
      if (this.onError) this.onError(err);
      
      // 自动降级处理
      uni.showToast({
        title: '录音出错，已切换到文本输入',
        icon: 'none'
      });
    });
  },
  
  // 设置事件处理器
  setHandlers({ onStart, onStop, onError }) {
    this.onStart = onStart;
    this.onStop = onStop;
    this.onError = onError;
  }
};
```

### 3. 性能优化策略

#### 针对原型UI首页和数据分析页面的性能优化

**首页虚拟列表优化:**
```vue
<template>
  <view class="home-container">
    <!-- 顶部资产卡片 - 固定显示 -->
    <AssetOverviewCard :total="totalAsset" />
    
    <!-- 快捷功能区 - 固定显示 -->
    <QuickActionBar />
    
    <!-- 交易记录列表 - 使用虚拟滚动优化 -->
    <view class="transaction-list-container">
      <text class="section-title">近期账单</text>
      
      <RecycleScroller
        v-if="devicePerformance !== 'low'" 
        class="scroller"
        :items="transactions"
        :item-size="80"
        key-field="id"
        page-mode
      >
        <template #item="{ item }">
          <TransactionItem :transaction="item" />
        </template>
      </RecycleScroller>
      
      <!-- 低性能设备降级方案 -->
      <view v-else class="fallback-list">
        <TransactionItem 
          v-for="(item, index) in limitedTransactions" 
          :key="item.id"
          :transaction="item"
        />
        
        <view v-if="transactions.length > limitCount" class="view-more" @tap="navigateToList">
          查看更多
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { platformCapabilities } from '@/utils/platform';
import { RecycleScroller } from 'vue-virtual-scroller';
import 'vue-virtual-scroller/dist/vue-virtual-scroller.css';

import AssetOverviewCard from '@/components/business/AssetOverviewCard.vue';
import QuickActionBar from '@/components/business/QuickActionBar.vue';
import TransactionItem from '@/components/business/TransactionItem.vue';

// 设备性能检测
const devicePerformance = ref('medium');
onMounted(async () => {
  devicePerformance.value = platformCapabilities.getDevicePerformanceLevel();
});

// 最大显示条数（用于低性能设备）
const limitCount = 10;

// 模拟数据
const transactions = ref([/* 数据... */]);
const totalAsset = ref(5000);

// 限制条数的交易列表
const limitedTransactions = computed(() => {
  return transactions.value.slice(0, limitCount);
});

// 跳转到完整列表
const navigateToList = () => {
  uni.navigateTo({
    url: '/pages/transaction/list'
  });
};
</script>
```

**数据分析页图表懒加载与按需渲染:**
```vue
<template>
  <view class="analysis-container">
    <!-- 选项卡 - 切换不同的分析视图 -->
    <view class="tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab"
        :class="{ active: activeTabIndex === index }"
        @tap="switchTab(index)"
      >
        {{ tab.name }}
      </view>
    </view>
    
    <!-- 懒加载的图表内容 -->
    <view class="chart-container">
      <!-- 使用v-show而不是v-if，保持已渲染组件的状态 -->
      <view v-show="activeTabIndex === 0" class="chart-panel">
        <IncomeExpenseChart v-if="tabsRendered[0]" :data="incomeExpenseData" />
      </view>
      
      <view v-show="activeTabIndex === 1" class="chart-panel">
        <CategoryChart v-if="tabsRendered[1]" :data="categoryData" />
      </view>
      
      <view v-show="activeTabIndex === 2" class="chart-panel">
        <TrendChart v-if="tabsRendered[2]" :data="trendData" />
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';
import IncomeExpenseChart from '@/components/business/charts/IncomeExpenseChart.vue';
import CategoryChart from '@/components/business/charts/CategoryChart.vue';
import TrendChart from '@/components/business/charts/TrendChart.vue';

// 选项卡定义
const tabs = [
  { name: '收支对比' },
  { name: '分类占比' },
  { name: '月度趋势' }
];

// 激活的选项卡索引
const activeTabIndex = ref(0);
// 已渲染的选项卡记录
const tabsRendered = ref([true, false, false]);

// 切换选项卡
const switchTab = (index) => {
  activeTabIndex.value = index;
  tabsRendered.value[index] = true;
};

// 模拟数据
const incomeExpenseData = ref({});
const categoryData = ref({});
const trendData = ref({});

// 选项卡切换时按需加载数据
watch(activeTabIndex, (newIndex) => {
  if (!tabsRendered.value[newIndex]) {
    tabsRendered.value[newIndex] = true;
  }
});
</script>
```

## 应用主题管理

### 1. 主题化系统设计
为支持原型UI中的多主题切换需求（包括明暗模式切换），需要实现主题管理系统：

```typescript
// src/utils/theme.ts
import { ref, watchEffect } from 'vue';

// 主题类型定义
export type ThemeMode = 'light' | 'dark' | 'system';

// 主题状态
export const currentTheme = ref<ThemeMode>('system');
export const effectiveTheme = ref<'light' | 'dark'>('light');

/**
 * 监听系统主题变化
 */
const setupSystemThemeListener = () => {
  // 仅在浏览器环境执行
  if (typeof window === 'undefined') return;
  
  const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
  
  // 初始化
  if (currentTheme.value === 'system') {
    effectiveTheme.value = mediaQuery.matches ? 'dark' : 'light';
  }
  
  // 监听变化
  const handleChange = (e: MediaQueryListEvent) => {
    if (currentTheme.value === 'system') {
      effectiveTheme.value = e.matches ? 'dark' : 'light';
    }
  };
  
  mediaQuery.addEventListener('change', handleChange);
  
  // 返回清理函数
  return () => mediaQuery.removeEventListener('change', handleChange);
};

/**
 * 初始化主题
 */
export const initTheme = () => {
  // 尝试从存储中读取
  try {
    // #ifndef H5
    const saved = uni.getStorageSync('theme-mode');
    if (saved && ['light', 'dark', 'system'].includes(saved)) {
      currentTheme.value = saved as ThemeMode;
    }
    // #endif
    
    // #ifdef H5
    const saved = localStorage.getItem('theme-mode');
    if (saved && ['light', 'dark', 'system'].includes(saved)) {
      currentTheme.value = saved as ThemeMode;
    }
    // #endif
  } catch (err) {
    console.error('读取主题设置失败', err);
  }
  
  // 设置系统主题监听
  const cleanup = setupSystemThemeListener();
  
  // 监听主题变化并应用
  watchEffect(() => {
    if (currentTheme.value === 'system') {
      const isDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      effectiveTheme.value = isDark ? 'dark' : 'light';
    } else {
      effectiveTheme.value = currentTheme.value as 'light' | 'dark';
    }
    
    // 应用主题到HTML元素
    applyTheme(effectiveTheme.value);
  });
  
  return cleanup;
};

/**
 * 切换主题
 */
export const setTheme = (mode: ThemeMode) => {
  currentTheme.value = mode;
  
  // 保存设置
  try {
    // #ifndef H5
    uni.setStorageSync('theme-mode', mode);
    // #endif
    
    // #ifdef H5
    localStorage.setItem('theme-mode', mode);
    // #endif
  } catch (err) {
    console.error('保存主题设置失败', err);
  }
};

/**
 * 应用主题到DOM
 */
const applyTheme = (theme: 'light' | 'dark') => {
  // 移除现有主题类
  document.documentElement.classList.remove('theme-light', 'theme-dark');
  // 添加新主题类
  document.documentElement.classList.add(`theme-${theme}`);
};
```

### 2. 全局状态管理最佳实践
针对原型UI中需要共享的数据（如资产信息、交易记录等），推荐以下Pinia设计模式：

```typescript
// src/stores/transaction.store.ts
import { defineStore } from 'pinia';
import { ref, computed } from 'vue';
import { getTransactions, createTransaction, updateTransaction, deleteTransaction } from '@/api/transaction';
import type { Transaction, TransactionFilter } from '@/types';

export const useTransactionStore = defineStore('transaction', () => {
  // 状态
  const transactions = ref<Transaction[]>([]);
  const isLoading = ref(false);
  const error = ref<string | null>(null);
  const currentFilter = ref<TransactionFilter>({
    startDate: null,
    endDate: null,
    category: null,
    type: null, // 'income' | 'expense' | null
    sortBy: 'date',
    sortOrder: 'desc'
  });
  
  // Getters
  const filteredTransactions = computed(() => {
    return transactions.value.filter(tx => {
      // 日期过滤
      if (currentFilter.value.startDate && new Date(tx.date) < new Date(currentFilter.value.startDate)) {
        return false;
      }
      if (currentFilter.value.endDate && new Date(tx.date) > new Date(currentFilter.value.endDate)) {
        return false;
      }
      
      // 类别过滤
      if (currentFilter.value.category && tx.category !== currentFilter.value.category) {
        return false;
      }
      
      // 类型过滤
      if (currentFilter.value.type && tx.type !== currentFilter.value.type) {
        return false;
      }
      
      return true;
    }).sort((a, b) => {
      // 排序
      const factor = currentFilter.value.sortOrder === 'asc' ? 1 : -1;
      
      if (currentFilter.value.sortBy === 'date') {
        return factor * (new Date(a.date).getTime() - new Date(b.date).getTime());
      }
      
      if (currentFilter.value.sortBy === 'amount') {
        return factor * (a.amount - b.amount);
      }
      
      return 0;
    });
  });
  
  const totalIncome = computed(() => {
    return transactions.value
      .filter(tx => tx.type === 'income')
      .reduce((sum, tx) => sum + tx.amount, 0);
  });
  
  const totalExpense = computed(() => {
    return transactions.value
      .filter(tx => tx.type === 'expense')
      .reduce((sum, tx) => sum + tx.amount, 0);
  });
  
  const balance = computed(() => {
    return totalIncome.value - totalExpense.value;
  });
  
  // Actions
  const fetchTransactions = async () => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const data = await getTransactions();
      transactions.value = data;
    } catch (err) {
      console.error('获取交易记录失败', err);
      error.value = '获取数据失败，请重试';
    } finally {
      isLoading.value = false;
    }
  };
  
  const addTransaction = async (tx: Omit<Transaction, 'id'>) => {
    isLoading.value = true;
    error.value = null;
    
    try {
      const newTx = await createTransaction(tx);
      transactions.value.unshift(newTx);
      return newTx;
    } catch (err) {
      console.error('添加交易记录失败', err);
      error.value = '添加失败，请重试';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };
  
  const removeTransaction = async (id: string) => {
    isLoading.value = true;
    error.value = null;
    
    try {
      await deleteTransaction(id);
      transactions.value = transactions.value.filter(tx => tx.id !== id);
    } catch (err) {
      console.error('删除交易记录失败', err);
      error.value = '删除失败，请重试';
      throw err;
    } finally {
      isLoading.value = false;
    }
  };
  
  const setFilter = (filter: Partial<TransactionFilter>) => {
    currentFilter.value = { ...currentFilter.value, ...filter };
  };
  
  const resetFilter = () => {
    currentFilter.value = {
      startDate: null,
      endDate: null,
      category: null,
      type: null,
      sortBy: 'date',
      sortOrder: 'desc'
    };
  };
  
  return {
    // 状态
    transactions,
    isLoading,
    error,
    currentFilter,
    
    // Getters
    filteredTransactions,
    totalIncome,
    totalExpense,
    balance,
    
    // Actions
    fetchTransactions,
    addTransaction,
    removeTransaction,
    setFilter,
    resetFilter
  };
}, {
  // 持久化配置
  persist: {
    key: 'transaction-store',
    paths: ['currentFilter'] // 只持久化过滤器设置
  }
});
```

### 3. AI交互模型 (针对原型UI中的语音记账)

为实现原型UI中的AI语音交互功能，建议采用以下设计模式：

```typescript
// src/services/ai.service.ts
import { ref, readonly } from 'vue';
import { recognizeVoice, chatWithAI } from '@/api/ai';

// 聊天消息类型
export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: number;
  recognized?: boolean; // 是否已被识别为交易
}

// 交易识别结果类型
export interface RecognitionResult {
  type: 'income' | 'expense';
  amount: number;
  category: string;
  date: string;
  description: string;
  confidence: number; // 0-1之间的置信度
}

export const useAIService = () => {
  // 状态
  const messages = ref<Message[]>([]);
  const isProcessing = ref(false);
  const recognitionResult = ref<RecognitionResult | null>(null);
  const error = ref<string | null>(null);
  
  // 添加用户消息
  const addUserMessage = (content: string) => {
    const message: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: Date.now()
    };
    
    messages.value.push(message);
    return message;
  };
  
  // 添加AI消息
  const addAIMessage = (content: string) => {
    const message: Message = {
      id: Date.now().toString(),
      role: 'assistant',
      content,
      timestamp: Date.now()
    };
    
    messages.value.push(message);
    return message;
  };
  
  // 语音识别
  const processVoice = async (audioFile: string) => {
    isProcessing.value = true;
    error.value = null;
    
    try {
      // 调用语音识别API
      const result = await recognizeVoice(audioFile);
      
      // 添加用户消息
      const message = addUserMessage(result.text);
      
      // 处理识别结果
      await processUserInput(message);
      
      return result;
    } catch (err) {
      console.error('语音识别失败', err);
      error.value = '语音识别失败，请重试或改用文字输入';
      
      // 添加错误提示为AI消息
      addAIMessage('抱歉，我没能听清您的语音，请重试或直接输入文字。');
      
      throw err;
    } finally {
      isProcessing.value = false;
    }
  };
  
  // 处理用户输入
  const processUserInput = async (message: Message) => {
    if (message.recognized) return;
    
    isProcessing.value = true;
    
    try {
      // 调用AI处理
      const response = await chatWithAI({
        messages: messages.value.map(m => ({
          role: m.role,
          content: m.content
        })),
        features: ['transaction_recognition']
      });
      
      // 添加AI回复
      addAIMessage(response.reply);
      
      // 设置识别结果
      if (response.recognition && response.recognition.success) {
        recognitionResult.value = response.recognition.result;
        
        // 标记消息已被识别
        message.recognized = true;
      }
      
      return response;
    } catch (err) {
      console.error('AI处理失败', err);
      error.value = 'AI处理失败，请重试';
      
      // 添加错误提示为AI消息
      addAIMessage('抱歉，我遇到了一些问题。请重新描述您的记账需求。');
      
      throw err;
    } finally {
      isProcessing.value = false;
    }
  };
  
  // 发送文本消息
  const sendTextMessage = async (text: string) => {
    const message = addUserMessage(text);
    return processUserInput(message);
  };
  
  // 清空对话
  const clearConversation = () => {
    messages.value = [];
    recognitionResult.value = null;
    error.value = null;
  };
  
  return {
    // 状态
    messages: readonly(messages),
    isProcessing: readonly(isProcessing),
    recognitionResult: readonly(recognitionResult),
    error: readonly(error),
    
    // 方法
    sendTextMessage,
    processVoice,
    addUserMessage,
    addAIMessage,
    clearConversation
  };
};
```

## 持续集成与部署

### 1. CI/CD 流程
- **提交检查**: 提交前运行 lint 和单元测试
- **构建流程**: 自动化构建流程，区分开发、测试和生产环境
- **多端打包**: 配置不同平台的打包策略

**package.json 构建脚本示例:**
```json
{
  "scripts": {
    "dev": "uni -p h5",
    "dev:mp-weixin": "uni -p mp-weixin",
    "dev:app": "uni -p app-plus",
    "build": "npm run build:h5 && npm run build:mp-weixin && npm run build:app",
    "build:h5": "uni build -p h5",
    "build:mp-weixin": "uni build -p mp-weixin",
    "build:app": "uni build -p app-plus",
    "test": "vitest run",
    "test:coverage": "vitest run --coverage",
    "lint": "eslint src",
    "lint:fix": "eslint src --fix",
    "lint:style": "stylelint **/*.{vue,scss,css} --fix"
  }
}
```

### 2. 环境配置与管理
- **环境变量**: 使用环境变量管理配置
- **构建变量**: 使用构建时变量进行环境区分
- **特性开关**: 实现特性开关功能，支持灰度发布

**环境配置示例:**
```javascript
// src/config/env.config.js
const ENV = process.env.NODE_ENV || 'development';
const APP_ENV = process.env.VUE_APP_ENV || 'dev';

const envConfig = {
  development: {
    apiBaseUrl: 'http://localhost:3000/api',
    enableLog: true,
    mockData: true
  },
  test: {
    apiBaseUrl: 'https://test-api.example.com/api',
    enableLog: true,
    mockData: false
  },
  production: {
    apiBaseUrl: 'https://api.example.com/api',
    enableLog: false,
    mockData: false
  }
};

// 根据构建环境选择配置
export const config = {
  ...envConfig[ENV],
  appVersion: process.env.VUE_APP_VERSION || '0.0.0',
  buildTime: process.env.VUE_APP_BUILD_TIME || new Date().toISOString()
};

// 特性开关
export const features = {
  enableNewUI: APP_ENV === 'prod' ? false : true, // 新UI仅在非生产环境启用
  enableVoiceInput: true, // 语音输入功能已就绪
  enableDataExport: APP_ENV !== 'prod' // 数据导出功能仅在非生产环境启用
};
```

## 团队协作与开发流程

### 1. Git 工作流
- **分支策略**: 
  - `main`: 生产环境代码
  - `develop`: 开发环境代码
  - `feature/*`: 功能分支
  - `bugfix/*`: 修复分支
  - `release/*`: 发布分支
- **提交规范**: 
  - 遵循 Conventional Commits 规范
  - 使用 commitlint 和 husky 强制执行

**提交规范示例:**
```
# 功能新增
feat: 添加语音录入功能

# 修复缺陷
fix: 修复iOS端按钮点击无效的问题

# 文档更新
docs: 更新API文档

# 代码重构
refactor: 重构数据加载逻辑

# 性能优化
perf: 优化列表渲染性能

# 测试相关
test: 添加用户注册流程测试

# 构建相关
build: 更新webpack配置
```

### 2. 代码评审标准
- **评审清单**:
  - 代码风格是否符合规范
  - 是否包含足够的测试
  - 是否考虑了性能和安全
  - 是否有适当的文档和注释
- **评审流程**:
  - 提交代码前进行自我评审
  - 提交拉取请求后由指定评审员进行评审
  - 解决评审意见后再进行合并

**评审评论示例:**
```
- [ ] 代码风格: 请检查第45行是否符合项目命名规范
- [ ] 防御性编程: 建议在调用API前添加参数验证
- [ ] 性能考虑: 长列表渲染可能导致性能问题，考虑使用虚拟滚动
- [ ] 测试覆盖: 缺少错误处理情况的测试
```

### 3. 文档与知识管理
- **内联文档**: 关键函数和组件必须有JSDoc注释
- **设计文档**: 复杂功能需要编写设计文档
- **知识库**: 重要技术决策和解决方案记录在团队知识库中

**JSDoc示例:**
```javascript
/**
 * 格式化金额为带货币符号的字符串
 * @param {number} amount - 金额数值
 * @param {string} [currency='CNY'] - 货币代码 (ISO 4217)
 * @param {string} [locale='zh-CN'] - 区域设置
 * @returns {string} 格式化后的金额字符串
 * @example
 * // 返回 "¥1,234.56"
 * formatCurrency(1234.56);
 * 
 * // 返回 "$1,234.56"
 * formatCurrency(1234.56, 'USD');
 */
export function formatCurrency(amount, currency = 'CNY', locale = 'zh-CN') {
  return new Intl.NumberFormat(locale, {
    style: 'currency',
    currency
  }).format(amount);
}
```

## 手势密码实现指南

为支持原型UI中的手势密码功能，以下提供完整的实现指南：

### 1. 核心绘制逻辑

```typescript
// src/utils/gesture.ts
export type Point = { x: number; y: number };
export type Dot = { position: Point; radius: number };

/**
 * 计算两点之间的距离
 */
export const getDistance = (p1: Point, p2: Point): number => {
  return Math.sqrt(Math.pow(p2.x - p1.x, 2) + Math.pow(p2.y - p1.y, 2));
};

/**
 * 检查点是否在圆内
 */
export const isPointInCircle = (point: Point, circle: Dot): boolean => {
  return getDistance(point, circle.position) <= circle.radius;
};

/**
 * 生成9点坐标
 */
export const generateDots = (width: number, height: number, dotRadius: number = 8): Dot[] => {
  const dots: Dot[] = [];
  const padding = 40; // 边距
  const spacing = (width - padding * 2) / 2; // 点之间的间距
  
  for (let i = 0; i < 3; i++) {
    for (let j = 0; j < 3; j++) {
      dots.push({
        position: {
          x: padding + j * spacing,
          y: padding + i * spacing
        },
        radius: dotRadius
      });
    }
  }
  
  return dots;
};

/**
 * 计算两点之间的角度（弧度）
 */
export const getAngle = (p1: Point, p2: Point): number => {
  return Math.atan2(p2.y - p1.y, p2.x - p1.x);
};

/**
 * 绘制手势路径
 */
export const drawGesturePath = (
  context: CanvasRenderingContext2D,
  pattern: number[],
  dots: Dot[],
  currentPoint: Point | null,
  options?: {
    lineWidth?: number;
    strokeStyle?: string;
    dotRadius?: number;
    dotFillStyle?: string;
    dotStrokeStyle?: string;
    activeDotFillStyle?: string;
    error?: boolean;
  }
) => {
  // 配置默认值
  const config = {
    lineWidth: 2,
    strokeStyle: '#FF6B35', // 原型UI中的主题色
    dotRadius: 8,
    dotFillStyle: '#FFFFFF',
    dotStrokeStyle: '#CCCCCC',
    activeDotFillStyle: '#FF6B35',
    ...options
  };
  
  // 清除画布
  context.clearRect(0, 0, context.canvas.width, context.canvas.height);
  
  // 绘制所有点
  dots.forEach((dot, index) => {
    context.beginPath();
    context.arc(dot.position.x, dot.position.y, config.dotRadius, 0, Math.PI * 2);
    
    // 设置填充样式
    if (pattern.includes(index)) {
      context.fillStyle = config.activeDotFillStyle;
    } else {
      context.fillStyle = config.dotFillStyle;
    }
    
    // 设置描边样式
    context.strokeStyle = config.dotStrokeStyle;
    context.lineWidth = 1;
    
    context.fill();
    context.stroke();
  });
  
  // 绘制已连接的点
  if (pattern.length > 0) {
    context.beginPath();
    
    // 设置线条样式
    context.lineWidth = config.lineWidth;
    context.strokeStyle = config.error ? '#F44336' : config.strokeStyle;
    
    // 移动到第一个点
    const firstDot = dots[pattern[0]];
    context.moveTo(firstDot.position.x, firstDot.position.y);
    
    // 连接其他点
    for (let i = 1; i < pattern.length; i++) {
      const dot = dots[pattern[i]];
      context.lineTo(dot.position.x, dot.position.y);
    }
    
    // 连接到当前点（如果存在）
    if (currentPoint && pattern.length > 0) {
      context.lineTo(currentPoint.x, currentPoint.y);
    }
    
    context.stroke();
  }
};

/**
 * 密码加密处理
 */
export const encryptPattern = (pattern: number[]): string => {
  // 简单加密，实际项目中应使用更强的加密算法
  return btoa(pattern.join(','));
};

/**
 * 验证手势密码
 */
export const verifyPattern = (input: number[], stored: string): boolean => {
  try {
    const decrypted = atob(stored).split(',').map(Number);
    
    if (input.length !== decrypted.length) {
      return false;
    }
    
    for (let i = 0; i < input.length; i++) {
      if (input[i] !== decrypted[i]) {
        return false;
      }
    }
    
    return true;
  } catch (err) {
    console.error('手势密码验证失败', err);
    return false;
  }
};
```

### 2. 手势密码组件实现

基于上述工具函数，实现符合原型UI的手势密码组件：

```vue
<!-- src/components/business/GesturePassword.vue -->
<template>
  <view class="gesture-password">
    <canvas
      id="patternCanvas"
      class="gesture-canvas"
      @touchstart="handleTouchStart"
      @touchmove="handleTouchMove"
      @touchend="handleTouchEnd"
    ></canvas>
    
    <view v-if="mode === 'setup'" class="gesture-tips">
      {{ setupStep === 1 ? '请绘制解锁图案' : '请再次绘制解锁图案确认' }}
    </view>
    
    <view v-else-if="mode === 'verify'" class="gesture-tips">
      {{ error ? errorMsg : '请绘制解锁图案' }}
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue';
import { 
  Point, Dot, isPointInCircle, generateDots, 
  drawGesturePath, encryptPattern, verifyPattern 
} from '@/utils/gesture';

// 组件属性
const props = defineProps({
  mode: {
    type: String as () => 'setup' | 'verify',
    required: true
  },
  storedPattern: {
    type: String,
    default: ''
  },
  minLength: {
    type: Number,
    default: 4
  }
});

// 组件事件
const emit = defineEmits([
  'setup-complete', 
  'verify-success', 
  'verify-fail', 
  'setup-cancel'
]);

// 组件状态
const canvasContext = ref<CanvasRenderingContext2D | null>(null);
const dots = ref<Dot[]>([]);
const pattern = ref<number[]>([]);
const isDrawing = ref(false);
const setupStep = ref(1);
const firstPattern = ref<number[]>([]);
const error = ref(false);
const errorMsg = ref('图案错误，请重试');
const canvasInfo = ref({
  width: 300,
  height: 300,
  left: 0,
  top: 0
});

// 更新画布位置信息
const updateCanvasPosition = () => {
  uni.createSelectorQuery()
    .select('#patternCanvas')
    .boundingClientRect(data => {
      if (data) {
        canvasInfo.value = {
          width: data.width || 300,
          height: data.height || 300,
          left: data.left || 0,
          top: data.top || 0
        };
        
        // 重新生成点坐标
        dots.value = generateDots(canvasInfo.value.width, canvasInfo.value.height);
        
        // 重绘
        if (canvasContext.value) {
          drawGesturePath(canvasContext.value, [], dots.value, null);
        }
      }
    })
    .exec();
};

// 初始化画布
const setupCanvas = () => {
  try {
    uni.createSelectorQuery()
      .select('#patternCanvas')
      .fields({ node: true, size: true, context: true })
      .exec((res) => {
        if (res[0]) {
          canvasContext.value = res[0].context;
          updateCanvasPosition();
        }
      });
  } catch (error) {
    console.error('初始化画布失败', error);
  }
};

// 计算触摸点相对于画布的位置
const getRelativePosition = (e: any): Point => {
  const touch = e.touches[0];
  return {
    x: touch.pageX - canvasInfo.value.left,
    y: touch.pageY - canvasInfo.value.top
  };
};

// 处理触摸开始
const handleTouchStart = (e: any) => {
  if (error.value) {
    error.value = false;
    pattern.value = [];
  }
  
  isDrawing.value = true;
  pattern.value = [];
  
  const pos = getRelativePosition(e);
  
  // 检查触摸点是否在某个圆内
  dots.value.forEach((dot, index) => {
    if (isPointInCircle(pos, dot) && !pattern.value.includes(index)) {
      pattern.value.push(index);
    }
  });
  
  // 绘制路径
  if (canvasContext.value) {
    drawGesturePath(canvasContext.value, pattern.value, dots.value, pos);
  }
};

// 处理触摸移动
const handleTouchMove = (e: any) => {
  if (!isDrawing.value || !canvasContext.value) return;
  
  const pos = getRelativePosition(e);
  
  // 检查触摸点是否在某个新的圆内
  dots.value.forEach((dot, index) => {
    if (isPointInCircle(pos, dot) && !pattern.value.includes(index)) {
      pattern.value.push(index);
    }
  });
  
  // 绘制路径
  drawGesturePath(canvasContext.value, pattern.value, dots.value, pos);
};

// 处理触摸结束
const handleTouchEnd = () => {
  isDrawing.value = false;
  
  if (pattern.value.length < props.minLength) {
    error.value = true;
    errorMsg.value = `至少连接${props.minLength}个点`;
    
    if (canvasContext.value) {
      drawGesturePath(
        canvasContext.value, 
        pattern.value, 
        dots.value, 
        null, 
        { error: true }
      );
    }
    
    setTimeout(() => {
      pattern.value = [];
      error.value = false;
      
      if (canvasContext.value) {
        drawGesturePath(canvasContext.value, [], dots.value, null);
      }
    }, 1000);
    
    return;
  }
  
  // 设置模式
  if (props.mode === 'setup') {
    if (setupStep.value === 1) {
      // 保存第一次绘制的图案
      firstPattern.value = [...pattern.value];
      setupStep.value = 2;
      
      // 清空当前图案，准备第二次绘制
      setTimeout(() => {
        pattern.value = [];
        if (canvasContext.value) {
          drawGesturePath(canvasContext.value, [], dots.value, null);
        }
      }, 500);
    } else {
      // 比较两次绘制是否一致
      const isMatch = comparePatterns(firstPattern.value, pattern.value);
      
      if (isMatch) {
        // 加密并返回
        const encryptedPattern = encryptPattern(pattern.value);
        emit('setup-complete', encryptedPattern);
      } else {
        error.value = true;
        errorMsg.value = '与首次绘制不一致，请重新开始';
        
        if (canvasContext.value) {
          drawGesturePath(
            canvasContext.value, 
            pattern.value, 
            dots.value, 
            null, 
            { error: true }
          );
        }
        
        // 重置到第一步
        setTimeout(() => {
          setupStep.value = 1;
          pattern.value = [];
          firstPattern.value = [];
          error.value = false;
          
          if (canvasContext.value) {
            drawGesturePath(canvasContext.value, [], dots.value, null);
          }
        }, 1000);
      }
    }
  }
  // 验证模式
  else if (props.mode === 'verify') {
    const isValid = verifyPattern(pattern.value, props.storedPattern);
    
    if (isValid) {
      emit('verify-success');
    } else {
      error.value = true;
      errorMsg.value = '图案错误，请重试';
      
      if (canvasContext.value) {
        drawGesturePath(
          canvasContext.value, 
          pattern.value, 
          dots.value, 
          null, 
          { error: true }
        );
      }
      
      emit('verify-fail');
      
      // 重置
      setTimeout(() => {
        pattern.value = [];
        error.value = false;
        
        if (canvasContext.value) {
          drawGesturePath(canvasContext.value, [], dots.value, null);
        }
      }, 1000);
    }
  }
};

// 比较两个图案是否相同
const comparePatterns = (p1: number[], p2: number[]): boolean => {
  if (p1.length !== p2.length) {
    return false;
  }
  
  for (let i = 0; i < p1.length; i++) {
    if (p1[i] !== p2[i]) {
      return false;
    }
  }
  
  return true;
};

// 组件挂载
onMounted(() => {
  // 延迟执行以确保DOM已渲染
  setTimeout(() => {
    setupCanvas();
    
    // 监听窗口大小变化
    uni.onWindowResize(() => {
      setTimeout(updateCanvasPosition, 300);
    });
  }, 200);
});

// 组件卸载
onUnmounted(() => {
  // 移除窗口大小变化监听
  uni.offWindowResize();
});
</script>

<style lang="scss" scoped>
.gesture-password {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  
  .gesture-canvas {
    width: 300px;
    height: 300px;
    background-color: transparent;
  }
  
  .gesture-tips {
    margin-top: 20px;
    font-size: 16px;
    color: var(--text-secondary, #666666);
    text-align: center;
    
    &--error {
      color: var(--color-error, #F44336);
    }
  }
}
</style>
```

### 3. 手势密码页面实现

```vue
<!-- src/pages/auth/gesture.vue -->
<template>
  <view class="gesture-page">
    <view class="header">
      <text class="title">{{ isVerify ? '验证手势密码' : '设置手势密码' }}</text>
    </view>
    
    <view class="user-info">
      <view class="avatar">
        <image :src="userAvatar" class="avatar-img"></image>
      </view>
      <text class="username">{{ userName }}</text>
    </view>
    
    <GesturePassword
      :mode="isVerify ? 'verify' : 'setup'"
      :stored-pattern="storedPattern"
      :min-length="4"
      @setup-complete="handleSetupComplete"
      @verify-success="handleVerifySuccess"
      @verify-fail="handleVerifyFail"
    />
    
    <view class="actions">
      <template v-if="isVerify">
        <text class="action-link" @tap="forgotPattern">忘记手势密码?</text>
      </template>
      <template v-else>
        <text class="action-link" @tap="skipSetup">跳过</text>
      </template>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import GesturePassword from '@/components/business/GesturePassword.vue';
import { useUserStore } from '@/stores/user';

// 用户信息
const userStore = useUserStore();
const userName = ref('用户名');
const userAvatar = ref('/static/default-avatar.png');

// 状态
const isVerify = ref(false);
const storedPattern = ref('');

// 处理设置完成
const handleSetupComplete = (pattern: string) => {
  console.log('手势密码设置成功');
  
  // 保存到存储
  uni.setStorageSync('gesture-pattern', pattern);
  
  // 更新用户信息
  userStore.setHasGesture(true);
  
  // 跳转到首页
  uni.switchTab({
    url: '/pages/home/<USER>'
  });
};

// 处理验证成功
const handleVerifySuccess = () => {
  console.log('手势密码验证成功');
  
  // 跳转到首页
  uni.switchTab({
    url: '/pages/home/<USER>'
  });
};

// 处理验证失败
const handleVerifyFail = () => {
  console.log('手势密码验证失败');
};

// 跳过设置
const skipSetup = () => {
  uni.switchTab({
    url: '/pages/home/<USER>'
  });
};

// 忘记密码
const forgotPattern = () => {
  uni.navigateTo({
    url: '/pages/auth/reset-gesture'
  });
};

// 组件挂载
onMounted(() => {
  // 检查是否是验证模式
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const query = currentPage.options || {};
  
  if (query.mode === 'verify') {
    isVerify.value = true;
    
    // 获取存储的手势密码
    const pattern = uni.getStorageSync('gesture-pattern');
    if (pattern) {
      storedPattern.value = pattern;
    } else {
      // 没有设置手势密码，跳转到设置页
      uni.redirectTo({
        url: '/pages/auth/gesture'
      });
    }
  }
  
  // 获取用户信息
  if (userStore.isLoggedIn) {
    userName.value = userStore.userInfo?.name || '用户名';
    userAvatar.value = userStore.userInfo?.avatar || '/static/default-avatar.png';
  }
});
</script>

<style lang="scss" scoped>
.gesture-page {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-height: 100vh;
  background-color: var(--bg-primary, #FFFFFF);
  padding: 20px;
  
  .header {
    width: 100%;
    text-align: center;
    margin-bottom: 40px;
    
    .title {
      font-size: 20px;
      font-weight: 500;
      color: var(--text-primary, #333333);
    }
  }
  
  .user-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-bottom: 40px;
    
    .avatar {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      margin-bottom: 10px;
      
      .avatar-img {
        width: 100%;
        height: 100%;
      }
    }
    
    .username {
      font-size: 18px;
      color: var(--text-primary, #333333);
    }
  }
  
  .actions {
    margin-top: 30px;
    width: 100%;
    display: flex;
    justify-content: center;
    
    .action-link {
      font-size: 16px;
      color: var(--color-primary, #FF6B35);
      padding: 10px;
    }
  }
}
</style>
```

### 4. 安全扩展建议

在实际应用中，为增强手势密码的安全性，建议采取以下措施：

1. **更强的加密**：使用更安全的加密算法（如AES）替代示例中的简单Base64加密

2. **失败限制**：增加连续验证失败的限制，如连续5次失败后需要使用密码登录

3. **结合生物识别**：在支持指纹/面部识别的设备上，提供生物识别作为备选验证方式

4. **模式变换**：定期提示用户更换手势密码，增强长期安全性

5. **动态点位**：考虑实现点位随机化功能，避免屏幕上留下使用痕迹

```typescript
// 扩展：安全性增强的加密实现
import CryptoJS from 'crypto-js';

// 安全密钥管理
const getSecretKey = () => {
  // 在生产环境中，应使用更安全的密钥存储方案
  return 'your-secret-key';
};

// 安全加密
export const secureEncryptPattern = (pattern: number[]): string => {
  const data = JSON.stringify(pattern);
  const key = getSecretKey();
  return CryptoJS.AES.encrypt(data, key).toString();
};

// 安全解密
export const secureDecryptPattern = (encryptedData: string): number[] => {
  try {
    const key = getSecretKey();
    const bytes = CryptoJS.AES.decrypt(encryptedData, key);
    const decryptedData = bytes.toString(CryptoJS.enc.Utf8);
    return JSON.parse(decryptedData);
  } catch (error) {
    console.error('解密失败', error);
    return [];
  }
};
``` 