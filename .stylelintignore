# 构建输出
dist/
node_modules/

# 特殊文件
static/
prototype/
*.js
*.ts
*.json

# 第三方库样式
src/assets/styles/vendor/

# 图标相关文件 - 避免检查破坏SVG
src/assets/images/**/*.svg
src/pages/welcome/components/WelcomeFeature.vue
src/pages/welcome/components/icons/*.vue
src/pages/welcome/components/WelcomeAnimations.vue

# 多端适配 - 避免rpx单位和特殊标签报错
**/*.nvue

# 复杂组件
src/components/common/AppIcon.vue
src/components/common/AppTabBar.vue
src/components/common/AppNavBar.vue
src/components/common/AppButton.vue
src/components/common/AppSwipeAction.vue 