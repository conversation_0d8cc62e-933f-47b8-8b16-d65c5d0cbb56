# 📱 AI记账 - 架构设计文档与开发规范

+----------------------------------+
|       项目文档关系图              |
+----------------------------------+
|                                  |
|  架构设计文档.md                  |
|  (整体架构、技术选型、目录结构)    |
|          |                       |
|          v                       |
|  开发文档和规范.md                |
|  (开发规范、编码标准、命名约定)    |
|          |                       |
|          v                       |
|  UI界面规则文档.md                |
|  (UI设计、组件样式、布局规范)      |
|                                  |
+----------------------------------+

文档主要职责:
- 架构设计文档: 定义系统架构和技术栈选择，是最高层次的设计指导
- 开发文档和规范: 规定具体的开发实践和编码标准，是开发过程的实施指南
- UI界面规则文档: 详细说明UI组件和视觉规范，是界面实现的参考标准

## 【1】需求确认

您希望我为一个基于 Vue3 + uni-app 的多端记账应用提供架构设计和开发规范，以便您作为没有开发经验的开发者能够理解和实施。该应用要求：
- 🔄 模块化开发和版本控制
- 📱 支持多端运行(iOS、Android、微信小程序)
- ☁️ 后端使用 Sealos 云平台

## 【2】实现思路

### 整体架构图

```
┌─────────────────────────────────────────┐
│              客户端应用层                 │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │  页面组件 │  │ 业务组件 │  │ 通用组件 │  │
│  └─────────┘  └─────────┘  └─────────┘  │
├─────────────────────────────────────────┤
│              业务逻辑层                   │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │ 用户认证 │  │ 语音记账 │  │ 数据分析 │  │
│  └─────────┘  └─────────┘  └─────────┘  │
├─────────────────────────────────────────┤
│              数据与服务层                 │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │ API服务 │  │ 本地存储 │  │ 状态管理 │  │
│  └─────────┘  └─────────┘  └─────────┘  │
├─────────────────────────────────────────┤
│              工具与适配层                 │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │ 工具函数 │  │ 端适配器 │  │ 加密模块 │  │
│  └─────────┘  └─────────┘  └─────────┘  │
└─────────────────────────────────────────┘
        ↓                ↓                ↓
┌─────────────┐  ┌─────────────┐  ┌─────────────┐
│   iOS/安卓   │  │  微信小程序  │  │   H5网页    │
└─────────────┘  └─────────────┘  └─────────────┘
        ↓                ↓                ↓
┌─────────────────────────────────────────────────┐
│                 Sealos云平台后端                 │
└─────────────────────────────────────────────────┘
```
### 新增：图标服务适配器
- 处理不同平台的图标渲染策略，主要依赖 uView Plus 的 `<u-icon>` 组件自身的多端兼容性。
- `<AppIcon>` 组件作为统一封装，简化 uView Plus 图标的使用。
- 统一管理 uView Plus 图标，并通过全局注册的 `<AppIcon>` 组件和业务专用的 `<CategorySelector>` 组件提供统一接口。
- 简化图标使用流程，支持颜色、尺寸等通过 props 定制。

### 图标组件职责分离
- **AppIcon组件**：负责所有通用图标渲染，封装 uView Plus 的 `<u-icon>` 组件。
  - 统一接口：`icon` (uView Plus 图标名), `size`, `color` 等属性，与 `<u-icon>` 兼容。
  - 依赖 `<u-icon>` 的多端平台适配能力。
- **CategorySelector组件**：专用于记账类别图标的展示和选择。
  - 提供交互友好的类别选择界面。
  - 集成类别颜色和图标展示（图标名使用 uView Plus 规范）。
  - 处理类别选择逻辑和事件传递。

🔑 **核心思想**：使用分层架构设计，通过模块化组织代码，确保不同端的统一体验。

### 分层架构的详细职责说明

#### 客户端应用层职责
- **页面组件**：负责整个页面的布局和组织，包含页面特定的业务逻辑
- **业务组件**：实现特定业务功能的可复用组件，如交易列表、预算进度条
- **通用组件**：与业务无关的UI组件，如按钮、卡片、图标等，确保界面一致性

#### 业务逻辑层职责
- **用户认证**：管理用户登录、注册、密码恢复和会话维护
- **语音记账**：处理语音输入、语义分析和记账数据生成
- **数据分析**：交易数据统计、预算管理和趋势分析算法

#### 数据与服务层职责
- **API服务**：统一处理网络请求、错误重试、数据缓存和请求优先级
- **本地存储**：区分临时存储(localStorage)与持久化存储(IndexedDB)，管理离线数据
- **状态管理**：负责应用状态维护和模块间通信，遵循Pinia状态管理（每个Store模块清晰分离状态、getters和actions）

#### 工具与适配层职责
- **工具函数**：提供各类通用功能，如日期处理、数据验证、格式化等
- **端适配器**：处理不同平台特性差异，确保一致的API和用户体验
- **加密模块**：保障数据安全，包括传输加密和本地数据保护

### Sealos云平台集成规范

#### API设计约束
- 所有API路径必须包含版本号：`/api/v1/endpoint`，便于未来版本迭代
- RESTful风格设计：使用适当的HTTP方法表达资源操作（GET查询、POST创建、PUT更新、DELETE删除）
- 请求与响应使用JSON格式，统一状态码和错误处理机制
- 文件上传必须支持断点续传，适应移动设备网络波动场景
- 敏感数据（密码、支付信息等）必须加密传输，使用HTTPS和附加的RSA加密

#### 权限与认证规则
- 使用JWT（JSON Web Token）进行用户认证，支持刷新机制
- 实现细粒度的权限控制，如数据访问权限、功能权限等
- 云端日志系统记录关键操作，便于审计和问题排查

#### 性能与容量规划
- API响应时间不超过200ms（不含网络延迟）
- 针对高频查询接口设置适当缓存策略
- 关键业务数据定期备份，保障数据安全

### 多端差异处理策略

#### 端适配器实现机制
- 封装平台特定API，提供统一的接口给业务层使用
- 使用条件编译（`#ifdef`/`#endif`）处理平台特有代码
- 样式适配采用CSS变量和响应式单位(rpx)，结合平台特定媒体查询

#### 关键差异处理方案
- **文件系统访问**：iOS/Android权限不同，需统一封装
- **导航与路由**：小程序页面栈限制，需特殊处理深层嵌套
- **网络请求**：各平台超时时间和并发限制不同，需统一管理
- **UI渲染**：弹窗、滚动、动画效果在不同平台表现不一致，需针对性优化

#### 多端测试策略
- 建立端差异对照表，针对性测试高风险区域
- 自动化测试覆盖核心功能在各平台的一致性
- 性能基准测试确保在低端设备上的可用性

## 【3】目录结构设计

```
ai-accounting/ (项目根目录)
├── src/ (源代码目录)
│   ├── api/ (API接口封装)
│   │   ├── mocks/ (API模拟数据)
│   │   │   ├── auth.mock.ts (认证接口模拟数据)
│   │   │   └── ... (其他模拟数据文件)
│   │   ├── index.ts (API统一导出)
│   │   ├── auth.ts (认证相关API)
│   │   ├── transaction.js (交易记录API)
│   │   ├── voice.js (语音识别API)
│   │   ├── receipt.js (票据识别API)
│   │   ├── analysis.js (数据分析API)
│   │   ├── sync.js (数据同步API)
│   │   ├── export.js (数据导出API)
│   │   ├── security.js (安全相关API)
│   │   ├── theme.js (主题皮肤API)
│   │   └── book.js (账本管理API)
│   ├── assets/ (静态资源)
│   │   ├── images/ (图片资源)
│   │   ├── icons/ (图标资源)
│   │   └── styles/ (全局样式)
│   ├── components/ (组件)
│   │   ├── common/ (通用组件)
│   │   │   ├── AppButton.vue (按钮组件)
│   │   │   ├── AppCard.vue (卡片组件)
│   │   │   ├── AppIcon.vue (图标组件 - 封装 uView Plus 的 u-icon)
│   │   │   └── ...
│   │   └── business/ (业务组件)
│   │       ├── TransactionItem.vue (交易项目组件)
│   │       ├── BudgetProgress.vue (预算进度组件)
│   │       ├── CategorySelector.vue (类别选择组件)
│   │       └── ...
│   ├── config/ (配置文件)
│   │   ├── index.js (配置入口)
│   │   ├── api.config.js (API配置)
│   │   └── platform.config.js (平台差异配置)
│   ├── hooks/ (自定义钩子)
│   │   ├── useAuth.js (认证相关钩子)
│   │   ├── useVoice.js (语音识别钩子)
│   │   └── ...
│   ├── pages/ (页面)
│   │   ├── auth/ (认证相关页面)
│   │   │   ├── login.vue (登录)
│   │   │   └── register.vue (注册)
│   │   ├── home/ (首页)
│   │   │   └── index.vue
│   │   ├── transaction/ (交易相关页面)
│   │   │   ├── list.vue (交易列表)
│   │   │   ├── detail.vue (交易详情)
│   │   │   └── voice-input.vue (语音输入)
│   │   ├── analysis/ (数据分析页面)
│   │   │   ├── budget.vue (预算分析)
│   │   │   └── trend.vue (趋势分析)
│   │   └── profile/ (个人中心相关页面)
│   ├── stores/ (状态管理 - Pinia)
│   │   ├── index.js (Pinia 实例创建)
│   │   ├── user.store.js (用户状态模块)
│   │   ├── transaction.store.js (交易状态模块)
│   │   └── ...
│   ├── utils/ (工具函数)
│   │   ├── request.js (请求封装)
│   │   ├── storage.js (存储封装)
│   │   ├── crypto.js (加密工具)
│   │   ├── date.js (日期工具)
│   │   └── ...
│   ├── App.vue (应用入口组件)
│   ├── main.js (应用入口文件)
│   ├── manifest.json (应用配置)
│   └── pages.json (页面配置)
├── tools/ (开发工具目录)
│   └── style/ (样式相关工具)
│       ├── core/ (核心工具)
│       │   ├── style-tools.js (样式工具核心文件)
│       │   └── style-fixer.js (样式修复工具)
│       ├── scripts/ (脚本工具)
│       │   ├── style-check.js (样式检查工具)
│       │   └── utilities-generator.js (工具类生成器)
│       ├── config/ (工具配置)
│       │   ├── .stylelintrc.json (Stylelint配置)
│       │   ├── utilities.config.js (工具类配置)
│       │   └── .stylelintignore (Stylelint忽略规则)
│       └── docs/ (工具文档)
│           ├── README.md (工具使用说明)
│           └── utilities-guide.md (工具类使用指南)
├── .gitignore (Git忽略文件)
├── package.json (项目依赖配置)
├── README.md (项目说明)
└── vite.config.js (构建配置)
```

### 3.1 目录职责详解

为了便于新手开发者理解各目录的用途和职责，补充以下说明：

```
/src                -> 源代码根目录，所有项目代码存放于此
  /api              -> 后端API接口调用封装，按功能模块组织
    /mocks          -> API模拟数据文件存放目录，与api目录结构对应
      /auth.mock.ts -> 认证接口的模拟数据
      /...          -> 其他模拟数据文件
    /index.ts       -> 统一导出所有API，方便集中导入
    /auth.ts        -> 认证相关API（登录、注册、密码管理）
    /transaction.js -> 交易相关API（查询、创建、修改交易）
    /voice.js       -> 语音识别相关API调用
    /receipt.js     -> 票据识别相关API调用
  
  /assets           -> 静态资源目录
    /images         -> 图片资源，如logo、占位图等
    /icons          -> 图标资源，建议使用SVG格式
    /styles         -> 全局样式文件，包括变量、混入等
  
  /components       -> 组件库，遵循单一职责原则
    /common         -> 通用UI组件，不包含业务逻辑
    /business       -> 业务组件，包含特定业务逻辑
  
  /config           -> 配置文件目录
    /index.js       -> 统一导出配置，支持环境切换
    /api.config.js  -> API相关配置，如基础URL、超时时间
    /platform.js    -> 平台特定配置，处理多端差异
  
  /hooks            -> Vue3组合式API逻辑封装，提高代码复用性
    /useAuth.js     -> 认证相关逻辑，如登录状态管理
    /useVoice.js    -> 语音识别逻辑封装
  
  /pages            -> 页面组件，按功能模块组织
    /auth           -> 认证相关页面（登录、注册）
    /home           -> 首页相关页面
    /transaction    -> 交易相关页面（列表、详情、录入）
    /analysis       -> 数据分析页面（预算、趋势）
    /profile        -> 个人中心页面
  
  /stores           -> Pinia状态管理，按业务模块拆分
    /index.js       -> Pinia 实例创建与插件配置
    /user.store.js  -> 用户相关状态模块（使用 defineStore 创建）
    /transaction.store.js -> 交易相关状态模块（使用 defineStore 创建）
  
  /utils            -> 工具函数库，提供通用功能
    /request.js     -> 网络请求工具，封装uni.request
    /storage.js     -> 存储工具，封装本地存储操作
    /crypto.js      -> 加密解密工具，提供数据安全保障
    /date.js        -> 日期处理工具，格式化、计算等
    /platform.js    -> 多端适配核心工具，处理平台差异
    /validation.js  -> 数据验证工具，表单验证等
```

🔍 **特别说明**：
- `hooks` 目录下的文件应使用 `useXxx.js` 命名，遵循 Vue3 Composition API 惯例
- `utils/platform.js` 是处理多端适配的核心，包含条件编译代码
- `pages.json` 是uni-app路由配置的核心文件，定义页面路径和窗口样式
- `tools` 目录用于存放项目开发工具，按功能模块组织，确保工具代码与业务代码分离
- `tools/style` 包含所有样式相关的工具，用于确保项目样式的一致性和可维护性
- **状态管理**: **项目状态管理必须 (MUST) 使用 Pinia。禁止使用 Vuex。** 在 `src/stores/` 目录下创建和管理状态模块（Store）。

### uView Plus图标系统集成架构

#### 配置层
- **pages.json**：通过easycom配置自动注册uView Plus组件
  ```json
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-icon$": "uview-plus/components/u-icon/u-icon.vue",
      "^u-(.*)": "uview-plus/components/u-$1/u-$1.vue"
    }
  }
  ```
- **App.vue**：引入全局样式，确保图标字体加载
  ```scss
  @import "uview-plus/index.scss";
  ```

#### 核心组件
- **AppIcon.vue**：通用图标组件封装，规范化使用方式
- **assets/styles/base/uicon.scss**：自定义图标的Unicode映射定义
- **static/fonts/uicon-iconfont.ttf**：图标字体文件，确保多端可访问

#### 最佳实践流程
1. 在页面/组件中使用 `<AppIcon>` 而非直接使用 `<u-icon>`
2. 使用CSS变量控制图标颜色，确保主题一致性：`color="var(--color-primary)"`
3. 设置适当的图标尺寸，遵循UI规范：`size="28"`
4. 交互图标通过标准事件处理：`@click="handleIconClick"`

#### 图标系统维护
- 定期检查uView Plus版本更新
- 维护图标名称的一致性使用，避免命名混乱
- 通过全局样式确保图标在不同主题下的正确显示
