/**
 * 工具类样式 - 集中管理必要的!important用法
 * 仅用于以下场景:
 * 1. 处理第三方组件/库的样式覆盖
 * 2. 处理原生组件样式
 * 3. 处理跨组件样式冲突的紧急修复
 * 
 * 使用规则:
 * - 命名规范: 所有工具类必须以 u- 前缀开头
 * - 在组件中可通过 @extend .u-类名 引用，或直接在 HTML 中使用类名
 * - 样式文件中不应直接使用 !important，而应引用这里的工具类
 */

// 所有 .u-xxx（工具类）和 %u-xxx（占位符）必须成对出现，分别用于 class 和 @extend
// 禁止在其他文件定义工具类或占位符，统一在本文件维护
// 例如：.u-hide 和 %u-hide 必须都在此处声明，且功能一致
// 用法说明：
//   - .u-xxx：直接在HTML元素class中使用
//   - %u-xxx：在SCSS中通过@extend继承

/**
 * ========================================
 * 1. 显示控制工具类
 * ========================================
 */
.u-hide {
  display: none !important; // 用于完全隐藏元素
}

.u-show {
  display: block !important; // 用于强制显示元素
}

.u-invisible {
  visibility: hidden !important; // 隐藏但保留空间
}

.u-visible {
  visibility: visible !important; // 强制可见
}

/**
 * ========================================
 * 2. 原生组件覆盖样式
 * ========================================
 */
.u-native-tabbar-hidden {
  // 隐藏原生TabBar (用于App.vue)
  // #ifdef APP-PLUS
  display: none !important;
  // #endif
}

// 覆盖第三方UI组件的常见样式问题
.u-third-party-override {
  // 第三方库覆盖样式
  // 使用时添加更详细的选择器以限制范围
}

/**
 * ========================================
 * 3. 位置与层级控制
 * ========================================
 */
.u-position-fixed {
  position: fixed !important;
}

.u-position-sticky {
  position: sticky !important;
}

.u-position-relative {
  position: relative !important;
}

.u-position-absolute {
  position: absolute !important;
}

// 层级控制
.u-z-top {
  z-index: 9999 !important; // 最高层级，谨慎使用
}

.u-z-high {
  z-index: 1000 !important; // 高层级，用于弹窗等
}

.u-z-mid {
  z-index: 100 !important; // 中层级，用于固定元素等
}

.u-z-low {
  z-index: 1 !important; // 低层级
}

.u-z-under {
  z-index: -1 !important; // 置于底层
}

/**
 * ========================================
 * 4. 组件样式覆盖
 * ========================================
 */

/**
 * 按钮紧凑型样式
 * 用于需要内嵌在卡片等组件中的小型按钮
 * 例如: <AppButton class="u-button-compact">还款</AppButton>
 */
.u-button-compact {
  padding: 4px 10px !important; // 覆盖通用组件内联样式
  font-size: 12px !important;
  height: auto !important;
  line-height: normal !important;
}

/**
 * ========================================
 * 5. 文本对齐工具类
 * ========================================
 */
.u-text-left {
  text-align: left !important;
}

.u-text-center {
  text-align: center !important;
}

.u-text-right {
  text-align: right !important;
}

/**
 * ========================================
 * 6. 字体样式工具类
 * ========================================
 */
.u-font-bold {
  font-weight: 600 !important;
}

.u-font-normal {
  font-weight: 400 !important;
}

.u-font-light {
  font-weight: 300 !important;
}

/**
 * ========================================
 * 7. 显示类型工具类
 * ========================================
 */
.u-d-flex {
  display: flex !important;
}

.u-d-block {
  display: block !important;
}

.u-d-none {
  display: none !important;
}

.u-d-inline {
  display: inline !important;
}

.u-d-inline-block {
  display: inline-block !important;
}

.u-d-grid {
  display: grid !important;
}

/**
 * ========================================
 * 8. Flex布局工具类
 * ========================================
 */
.u-flex-row {
  flex-direction: row !important;
}

.u-flex-column {
  flex-direction: column !important;
}

.u-flex-wrap {
  flex-wrap: wrap !important;
}

.u-flex-nowrap {
  flex-wrap: nowrap !important;
}

.u-justify-start {
  justify-content: flex-start !important;
}

.u-justify-center {
  justify-content: center !important;
}

.u-justify-end {
  justify-content: flex-end !important;
}

.u-justify-between {
  justify-content: space-between !important;
}

.u-justify-around {
  justify-content: space-around !important;
}

.u-align-start {
  align-items: flex-start !important;
}

.u-align-center {
  align-items: center !important;
}

.u-align-end {
  align-items: flex-end !important;
}

.u-align-stretch {
  align-items: stretch !important;
}

.u-flex-1 {
  flex: 1 !important;
}

.u-flex-grow-0 {
  flex-grow: 0 !important;
}

.u-flex-grow-1 {
  flex-grow: 1 !important;
}

/**
 * ========================================
 * 9. 溢出处理工具类
 * ========================================
 */
.u-overflow-hidden {
  overflow: hidden !important;
}

.u-overflow-auto {
  overflow: auto !important;
}

.u-overflow-scroll {
  overflow: scroll !important;
}

.u-overflow-x-auto {
  overflow-x: auto !important;
}

.u-overflow-y-auto {
  overflow-y: auto !important;
}

.u-text-truncate {
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
}

/**
 * ========================================
 * 10. 边距工具类
 * ========================================
 */
// Margin
.u-m-0 {
  margin: 0 !important;
}

.u-m-1 {
  margin: 8px !important;
}

.u-m-2 {
  margin: 16px !important;
}

.u-m-3 {
  margin: 24px !important;
}

// Margin Top
.u-mt-0 {
  margin-top: 0 !important;
}

.u-mt-1 {
  margin-top: 8px !important;
}

.u-mt-2 {
  margin-top: 16px !important;
}

.u-mt-3 {
  margin-top: 24px !important;
}

// Margin Right
.u-mr-0 {
  margin-right: 0 !important;
}

.u-mr-1 {
  margin-right: 8px !important;
}

.u-mr-2 {
  margin-right: 16px !important;
}

.u-mr-3 {
  margin-right: 24px !important;
}

// Margin Bottom
.u-mb-0 {
  margin-bottom: 0 !important;
}

.u-mb-1 {
  margin-bottom: 8px !important;
}

.u-mb-2 {
  margin-bottom: 16px !important;
}

.u-mb-3 {
  margin-bottom: 24px !important;
}

// Margin Left
.u-ml-0 {
  margin-left: 0 !important;
}

.u-ml-1 {
  margin-left: 8px !important;
}

.u-ml-2 {
  margin-left: 16px !important;
}

.u-ml-3 {
  margin-left: 24px !important;
}

// Padding
.u-p-0 {
  padding: 0 !important;
}

.u-p-1 {
  padding: 8px !important;
}

.u-p-2 {
  padding: 16px !important;
}

.u-p-3 {
  padding: 24px !important;
}

// Padding Top
.u-pt-0 {
  padding-top: 0 !important;
}

.u-pt-1 {
  padding-top: 8px !important;
}

.u-pt-2 {
  padding-top: 16px !important;
}

// Padding Bottom
.u-pb-0 {
  padding-bottom: 0 !important;
}

.u-pb-1 {
  padding-bottom: 8px !important;
}

.u-pb-2 {
  padding-bottom: 16px !important;
}

/**
 * ========================================
 * 11. 尺寸控制工具类
 * ========================================
 */
.u-w-100 {
  width: 100% !important;
}

.u-h-100 {
  height: 100% !important;
}

.u-mw-100 {
  max-width: 100% !important;
}

.u-mh-100 {
  max-height: 100% !important;
}

/**
 * ========================================
 * 12. 边框控制工具类
 * ========================================
 */
.u-border-none {
  border: none !important;
}

.u-border-0 {
  border-width: 0 !important;
}

/**
 * ========================================
 * 使用指南:
 * ========================================
 * 1. 仅在处理第三方组件样式或特殊场景下使用这些类
 * 2. 在使用前，先尝试使用正确的CSS选择器权重解决问题
 * 3. 每次添加新的工具类，请添加清晰的注释说明用途
 * 4. 如果发现多处使用相同的!important规则，将其整合到这里
 * 5. 工具类应按功能分组并添加分隔注释，便于维护
 */

/**
 * ========================================
 * 13. 价格/金额样式工具类
 * ========================================
 * 用于统一处理价格/金额的显示样式
 */
.u-price {
  font-weight: 500 !important;
  font-family: -apple-system, system-ui, sans-serif !important;
  
  &--positive {
    color: var(--color-success, #4CAF50) !important;
  }
  
  &--negative {
    color: var(--color-error, #F44336) !important;
  }
  
  &--primary {
    color: var(--color-primary, #FF6B35) !important;
  }
  
  &--large {
    font-size: var(--font-size-xl, 20px) !important;
    font-weight: 600 !important;
  }
  
  &--medium {
    font-size: var(--font-size-lg, 18px) !important;
  }
  
  &--small {
    font-size: var(--font-size-md, 16px) !important;
  }
}

.u-hide-date-picker-display {
  :deep(.app-date-picker__display) {
    display: none !important;
  }
}

/**
 * 登录页、交易页等特殊按钮/表单/布局样式（由页面迁移而来）
 * 用于替换页面内的!important写法
 */
.u-login-form-error {
  color: var(--color-error, #F44336) !important;
}

.u-login-btn-fixed {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  width: 100% !important;
  z-index: 100 !important;
}

.u-list-scroll-lock {
  overflow: hidden !important;
}

// ==========Sass占位符定义，供@extend使用==========
// 用于Sass @extend，解决App.vue等全局样式继承问题
%u-border-none {
  border: none !important;
}

%u-hide {
  display: none !important;
}

%u-show {
  display: block !important;
}

%u-invisible {
  visibility: hidden !important;
}

%u-visible {
  visibility: visible !important;
}