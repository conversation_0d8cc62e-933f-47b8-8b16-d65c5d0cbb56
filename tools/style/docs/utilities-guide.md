# 工具类使用指南

## 概述

本项目提供了集中管理带有 `!important` 的工具类系统，用于处理特定样式需求。这些工具类统一放置在 `tools/style/scripts/_utilities.scss` 文件中，并被全局导入。

## 使用场景

工具类仅用于以下特定场景：

1. **处理第三方组件/库的样式覆盖**
2. **处理原生组件样式**
3. **处理跨组件样式冲突的紧急修复**

## 使用方法

有两种主要的使用方式：

### 1. 在 HTML/模板中直接使用类名

```vue
<template>
  <!-- 使用工具类隐藏元素 -->
  <view class="u-hide">这个元素会被隐藏</view>
  
  <!-- 使用多个工具类组合 -->
  <view class="u-d-flex u-justify-between u-align-center">
    弹性布局容器
  </view>
  
  <!-- 应用边距工具类 -->
  <view class="card-content u-mb-2">
    底部有边距的内容
  </view>
</template>
```

### 2. 在 SCSS 中使用 @extend 引用

```scss
.my-component {
  // 基础样式
  
  &--hidden {
    @extend .u-hide; // 引用隐藏工具类
  }
  
  &__actions {
    @extend .u-d-flex; // 弹性布局
    @extend .u-justify-end; // 对齐到末尾
  }
}
```

## 工具类分类

### 1. 显示控制

- `.u-hide` - 完全隐藏元素
- `.u-show` - 强制显示元素
- `.u-invisible` - 隐藏但保留空间
- `.u-visible` - 强制可见

### 2. 位置与层级

- `.u-position-fixed`, `.u-position-sticky`, `.u-position-relative`, `.u-position-absolute`
- `.u-z-top`, `.u-z-high`, `.u-z-mid`, `.u-z-low`, `.u-z-under`

### 3. 组件样式覆盖

- `.u-button-compact` - 用于内嵌在卡片等组件中的小型按钮

### 4. 文本对齐

- `.u-text-left`, `.u-text-center`, `.u-text-right`

### 5. 字体样式

- `.u-font-bold`, `.u-font-normal`, `.u-font-light`

### 6. 显示类型

- `.u-d-flex`, `.u-d-block`, `.u-d-none`, `.u-d-inline`, `.u-d-inline-block`, `.u-d-grid`

### 7. Flex布局

- `.u-flex-row`, `.u-flex-column`, `.u-flex-wrap`, `.u-flex-nowrap`
- `.u-justify-start`, `.u-justify-center`, `.u-justify-end`, `.u-justify-between`, `.u-justify-around`
- `.u-align-start`, `.u-align-center`, `.u-align-end`, `.u-align-stretch`
- `.u-flex-1`, `.u-flex-grow-0`, `.u-flex-grow-1`

### 8. 溢出处理

- `.u-overflow-hidden`, `.u-overflow-auto`, `.u-overflow-scroll`
- `.u-overflow-x-auto`, `.u-overflow-y-auto`
- `.u-text-truncate` - 文本截断显示省略号

### 9. 边距工具类

- `.u-m-0`, `.u-m-1`, `.u-m-2`, `.u-m-3` - 外边距
- `.u-mt-0`, `.u-mt-1`, `.u-mt-2`, `.u-mt-3` - 上边距
- `.u-mr-0`, `.u-mr-1`, `.u-mr-2`, `.u-mr-3` - 右边距
- `.u-mb-0`, `.u-mb-1`, `.u-mb-2`, `.u-mb-3` - 下边距
- `.u-ml-0`, `.u-ml-1`, `.u-ml-2`, `.u-ml-3` - 左边距
- `.u-p-0`, `.u-p-1`, `.u-p-2`, `.u-p-3` - 内边距
- `.u-pt-0`, `.u-pt-1`, `.u-pt-2` - 上内边距
- `.u-pb-0`, `.u-pb-1`, `.u-pb-2` - 下内边距

### 10. 尺寸控制

- `.u-w-100` - 宽度100%
- `.u-h-100` - 高度100%
- `.u-mw-100` - 最大宽度100%
- `.u-mh-100` - 最大高度100%

### 11. 边框控制

- `.u-border-none` - 无边框
- `.u-border-0` - 边框宽度为0

### 12. 价格/金额样式

- `.u-price` - 基础价格样式
- `.u-price--positive` - 正数价格（绿色）
- `.u-price--negative` - 负数价格（红色）
- `.u-price--primary` - 主色调价格
- `.u-price--large`, `.u-price--medium`, `.u-price--small` - 不同尺寸

## 最佳实践

1. **优先使用高特异性选择器**：在使用工具类前，先尝试使用正确的CSS选择器权重解决问题
2. **限制使用范围**：工具类只用于特定场景，避免大范围使用
3. **组合使用**：可以组合多个工具类实现复杂样式
4. **添加新工具类**：如发现多处使用相同的!important规则，应将其添加到工具类文件中

## 示例

### 按钮紧凑型样式

```vue
<template>
  <AppButton class="u-button-compact">紧凑按钮</AppButton>
</template>
```

### 价格展示

```vue
<template>
  <span class="u-price u-price--positive u-price--large">+￥199.00</span>
  <span class="u-price u-price--negative">-￥50.00</span>
</template>
```

### 弹性布局容器

```vue
<template>
  <view class="u-d-flex u-justify-between u-align-center u-p-2">
    <text>左侧内容</text>
    <AppButton>右侧按钮</AppButton>
  </view>
</template>
``` 