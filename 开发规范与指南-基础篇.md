# 开发规范与指南-基础篇

+----------------------------------+
|       项目文档关系图              |
+----------------------------------+
|                                  |
|  架构设计文档.md                  |
|  (整体架构、技术选型、目录结构)    |
|          |                       |
|          v                       |
|  开发文档和规范.md                |
|  (开发规范、编码标准、命名约定)    |
|          |                       |
|          v                       |
|  UI界面规则文档.md                |
|  (UI设计、组件样式、布局规范)      |
|                                  |
+----------------------------------+

## 核心开发规则 (MUST ALWAYS BE FOLLOWED)

### 1. 文件存放 (File Placement)
- **页面 (`.vue`)**: `src/pages/[模块名]/[页面名].vue`
- **通用组件 (`App*`)**: `src/components/common/`
- **业务组件 (`Tx*`, `Budget*` 等)**: `src/components/business/` 或页面下的 `components/`
- **API 调用**: `src/api/[模块名].js`
- **工具函数**: `src/utils/`
- **状态管理**: `src/stores/` (必须使用 Pinia，禁止使用 Vuex)
- **样式变量**: `src/assets/styles/variables.scss`

### 2. 命名规范 (Naming Conventions)
- **组件文件/名称**: `PascalCase` (如 `AppButton.vue`, `TransactionItem`)
- **通用组件**: **必须 (MUST)** 以 `App` 前缀开头，**唯一例外**是 `CategorySelector`
- **JS/TS 文件**: `camelCase` (如 `apiService.js`)
- **CSS 类名**: BEM (`block__element--modifier`) 或页面/组件前缀 (`page-name__element--modifier`)
- **禁止使用通用类名**: 如 `.container`, `.title`，避免命名冲突
- **hooks 文件**: **必须 (MUST)** 使用 `useXxx.js` 命名，遵循 Vue3 Composition API 惯例

### 3. 组件使用 (Component Usage)
- **优先使用通用组件**: **必须 (MUST)** 优先使用 `src/components/common/` 下的 `App*` 通用组件
- **通用组件列表**: `AppButton`, `AppCard`, `AppInput`, `AppModal`, `AppIcon` 等
- **禁止重复创建**: 除非通用组件无法满足需求，否则 **禁止 (FORBID)** 创建重复功能的业务或私有组件
- **使用方式一致**: 确保在不同页面中对同一通用组件的使用方式和传入的 props 保持一致

### 4. 图标使用 (Icon Usage)
- **唯一方式**: **必须 (MUST)** 通过全局注册的 `<AppIcon>` 组件使用 uView Plus 图标。该组件内部封装了 uView Plus 的 `<u-icon>`。
- **来源与加载**: uView Plus 图标库通过 NPM 安装 `uview-plus` 包，通过 uni-app 的 `easycom` 机制实现按需编译和自动引入，无需在每个页面或组件中手动导入。
- **禁止其他方式**: **禁止 (FORBID)** 使用 `<i>` 标签、CDN、其他第三方图标库，或直接使用 `<u-icon>`（除非有特殊且经过确认的理由）。
- **类别图标**: 对于业务类别图标，**必须 (MUST)** 使用 `<CategorySelector>` 组件，其内部图标也应遵循 uView Plus 规范。

### 4.1. AppIcon 组件的属性和用法
`<AppIcon>` 组件支持以下主要属性（props）：

- `icon`: (String) **必填**，uView Plus 图标名称，例如 `home`, `photo`, `setting`
- `size`: (String | Number) 图标大小，默认24，单位rpx
- `color`: (String) 图标颜色，**必须 (MUST)** 使用 CSS 变量，例如 `var(--color-primary)`
- `bold`: (Boolean) 是否加粗，默认 false
- `customPrefix`: (String) 自定义图标前缀，用于扩展自定义图标库
- `@click`: (Function) 点击事件处理函数

### 4.2. 标准用法示例
```vue
<!-- 基本用法 -->
<AppIcon icon="home" />

<!-- 指定尺寸和颜色 -->
<AppIcon icon="star" size="36" color="var(--color-warning)" />

<!-- 点击事件 -->
<AppIcon 
  icon="trash" 
  color="var(--color-error)" 
  @click="handleDelete"
/>
```

### 4.3. 图标命名规则
uView Plus 图标命名采用语义化规则：
- 基础图标使用其表意名称，如 `home`、`user`、`search`
- 填充型图标通常使用 `-fill` 后缀
- 方向型图标使用方向后缀，如 `arrow-left`、`arrow-down`

### 4.4. 多端兼容性
`<AppIcon>` 组件和 uView Plus 图标在不同平台有以下注意事项：
- **小程序**: 基础库版本建议 ≥ 2.19.2
- **H5**: 字体文件需正确加载
- **App**: 确保字体文件在原生环境可访问

### 4.5. 图标显示异常排查
若图标无法正常显示，请按顺序检查：
1. **图标名称**: 确认 `icon` 属性值是 uView Plus 库中有效的图标名
2. **easycom 配置**: 检查 `pages.json` 中 easycom 规则是否正确配置了 uView Plus
3. **样式引入**: 确认 `App.vue` 中已正确引入 uView Plus 样式
4. **字体文件**: 确认 `static/fonts/` 目录下有字体文件并可正确访问
5. **CSS 变量**: 检查使用的颜色变量是否正确定义

### 4.6. 扩展自定义图标
如需使用自定义图标，应遵循以下步骤：
1. 从 iconfont.cn 下载字体文件（.ttf、.woff等）放入 `static/fonts/` 目录
2. 在 `assets/styles/base/uicon.scss` 中添加自定义图标的Unicode映射
3. 使用 `<AppIcon>` 的 `customPrefix` 属性指定自定义图标前缀

```vue
<AppIcon 
  icon="my-icon-name" 
  customPrefix="custom-icon" 
/>
```

**更多关于图标使用的详细说明，请参考 uView Plus 官方文档。**

### 5. CSS 样式 (Styling)
- **作用域**: **必须 (MUST)** 使用 `<style lang="scss" scoped>`
- **CSS 变量**: **必须 (MUST)** 使用 `var(--variable-name, fallbackValue)` 引用全局 CSS 变量
- **禁止硬编码**: **禁止 (FORBID)** 直接写入具体像素值或颜色码
- **统一设计令牌**: 使用设计令牌（间距、颜色、字体大小、圆角等）确保 UI 一致性
- **禁止修改全局变量**: **禁止 (FORBID)** 在组件内重定义全局 CSS 变量
- **样式权重控制**: 选择器嵌套不超过 3 层
- **!important 规则**: **原则上禁止 (FORBID)** 使用 `!important`，但有以下**例外情况**:
  - 处理第三方组件样式覆盖
  - 处理原生组件样式
  - 处理跨组件样式冲突的紧急修复
  - **必须 (MUST)** 优先使用 `tools/style/scripts/_utilities.scss` 中定义的工具类（如 `.u-hide`、`.u-show` 等）

**正确用法示例:**
```scss
.card {
  padding: var(--spacing-md, 16px);
  background-color: var(--color-background, #ffffff);
  color: var(--text-primary, #333333);
  border-radius: var(--radius-md, 8px);
  box-shadow: var(--card-shadow, 0 2px 12px rgba(0, 0, 0, 0.1));
}

/* 需要使用!important的场景 */
.third-party-override {
  /* 使用工具类 */
  @extend .u-hide; // 而非直接使用 display: none !important;
}
```

### 5.1 工具类使用规范 (Utilities Usage)
- **集中管理**: 所有包含 `!important` 的样式必须在 `tools/style/scripts/_utilities.scss` 中集中定义
- **命名规范**: 工具类必须以 `u-` 前缀开头，如 `u-hide`、`u-show` 等
- **引用方式**: 在组件中通过 `@extend .u-类名` 引用，或直接在 HTML 中使用类名
- **文档完备**: 每个工具类必须有注释说明用途和使用场景
- **范围限制**: 必须使用高特异性选择器限制工具类的影响范围

### 6. 代码风格 (Code Style)
- **`<script setup>`**: **必须 (MUST)** 使用 `<script setup>` 语法
- **导入顺序**: 遵循 Vue -> 组件 -> API/Utils 的分组顺序
- **TypeScript**: 建议使用 TypeScript，为复杂 props 提供类型声明

**推荐导入顺序:**
```javascript
// 1. Vue相关
import { ref, computed, onMounted } from 'vue';
import { useStore } from 'pinia';

// 2. 组件导入
import AppButton from '@/components/common/AppButton.vue';
import TransactionList from '@/components/business/TransactionList.vue';

// 3. API和工具导入
import { getTransactions } from '@/api/transaction';
import { formatDate } from '@/utils/date';
```

### 7. 防御性编程 (Defensive Programming)
- **可选链**: **必须 (MUST)** 使用 `?.` 安全访问可能不存在的属性
- **空值合并**: 使用 `??` 提供默认值（优于 `||`），正确处理 `0` 和空字符串
- **数组安全操作**: 在操作数组前检查其存在性和长度，或使用可选链
- **异步错误处理**: **必须 (MUST)** 使用 `try...catch` 或 `.catch()` 处理异步错误
- **禁止不安全渲染**: **禁止 (FORBID)** 使用 `v-html` 渲染不可信内容

**防御性编程示例:**
```javascript
// 安全访问嵌套属性
const userName = user?.profile?.name ?? '未知用户';

// 数组安全操作
const items = list?.filter(item => item?.isActive) ?? [];

// 异步错误处理
const fetchData = async () => {
  try {
    const data = await api.getData();
    return data;
  } catch (error) {
    console.error('获取数据失败', error);
    return []; // 提供合理的默认值
  }
};
```

### 8. 页面结构规范 (Page Structure)
- **基础模板**: 使用统一的页面基础模板，确保结构一致性
- **私有组件**: 页面特有的组件应放在页面目录下的 `components/` 文件夹中
- **页面样式**: 页面最外层容器应使用唯一的、能清晰标识页面的类名

**页面基础模板:**
```vue
<template>
  <view class="[page-name]-container">
    <!-- 页面内容 -->
  </view>
</template>

<script setup lang="ts">
// 引入声明
import { ref } from 'vue'

// 组件声明
import AppButton from '@/components/common/AppButton.vue'
import AppCard from '@/components/common/AppCard.vue'

// 状态定义
// 方法定义
</script>

<style lang="scss" scoped>
.[page-name]-container {
  // 页面级样式
}
</style>
```

### 9. 多端适配 (Multi-platform Adaptation)
- **条件编译**: 使用 `#ifdef` / `#endif` 处理平台特有代码
- **样式适配**: 使用 CSS 变量和响应式单位(rpx)，结合平台特定媒体查询
- **端适配器**: 使用 `utils/platform.js` 中的平台适配函数获取特定样式或行为

#### 适配器模式 (Adapter Pattern)
为确保代码的可维护性和统一性，必须采用适配器模式处理多端差异：

```javascript
// src/utils/platform.js
/**
 * 获取当前平台类型
 * @returns {string} 'ios' | 'android' | 'h5' | 'weapp'
 */
export const getPlatformType = () => {
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  if (systemInfo.platform === 'ios') {
    return 'ios';
  }
  return 'android';
  // #endif
  
  // #ifdef H5
  return 'h5';
  // #endif
  
  // #ifdef MP-WEIXIN
  return 'weapp';
  // #endif
  
  return 'unknown';
};

/**
 * 获取平台特定样式
 * @param {string} key - 样式键名
 * @returns {string|number} 样式值
 */
export const getPlatformStyle = (key) => {
  // 共享样式变量，可根据平台返回不同值
  const styleMap = {
    'nav-height': {
      ios: '88px',       // 包含状态栏
      android: '66px',   // 标准导航栏
      h5: '60px',        // Web导航栏
      weapp: '90px'      // 小程序导航栏(含胶囊按钮区域)
    },
    'safe-bottom': {
      ios: 'env(safe-area-inset-bottom)',
      android: '0',
      h5: '0',
      weapp: '0'
    },
    'status-bar-height': {
      ios: '44px',
      android: '24px',
      h5: '0',
      weapp: '25px'
    },
    'tab-bar-height': {
      ios: '60px',
      android: '56px',
      h5: '50px',
      weapp: '56px'
    }
  };

  const platform = getPlatformType();
  return styleMap[key]?.[platform] || styleMap[key]?.['ios'] || '';
};

/**
 * 获取安全区域内边距
 * @returns {object} 包含top, right, bottom, left的对象
 */
export const getSafeAreaInsets = () => {
  // 基础安全区域
  const insets = { top: 0, right: 0, bottom: 0, left: 0 };
  
  // #ifdef APP-PLUS
  const systemInfo = uni.getSystemInfoSync();
  // iOS
  if (systemInfo.platform === 'ios') {
    const safeArea = systemInfo.safeArea;
    // 刘海屏
    if (safeArea) {
      insets.top = safeArea.top;
      insets.right = systemInfo.screenWidth - safeArea.right;
      insets.bottom = systemInfo.screenHeight - safeArea.bottom;
      insets.left = safeArea.left;
    }
  }
  // #endif
  
  // #ifdef MP-WEIXIN
  // 小程序胶囊按钮适配
  const menuButtonInfo = uni.getMenuButtonBoundingClientRect();
  insets.top = menuButtonInfo.top;
  insets.right = menuButtonInfo.right;
  // #endif
  
  return insets;
};

/**
 * 应用平台特定行为
 * @param {string} action - 行为名称
 * @param {any} params - 行为参数
 */
export const applyPlatformBehavior = (action, params) => {
  switch (action) {
    case 'hideKeyboard':
      // 隐藏键盘
      uni.hideKeyboard();
      // 处理特定平台的附加行为
      // #ifdef APP-PLUS
      plus.key.hideSoftKeybord();
      // #endif
      break;
      
    case 'setStatusBarStyle':
      // 设置状态栏样式(亮色/暗色)
      // #ifdef APP-PLUS
      plus.navigator.setStatusBarStyle(params === 'light' ? 'light' : 'dark');
      // #endif
      
      // #ifdef MP-WEIXIN
      uni.setNavigationBarColor({
        frontColor: params === 'light' ? '#ffffff' : '#000000',
        backgroundColor: '#FF6B35'
      });
      // #endif
      break;
      
    // 更多特定行为...
  }
};
```

#### 平台特定样式示例

**多端导航栏适配：**
```scss
.nav-bar {
  /* 基础样式 */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  z-index: 100;
  background-color: var(--color-primary, #FF6B35);
  color: var(--text-inverse, #FFFFFF);
  display: flex;
  align-items: center;
  justify-content: center;
  
  /* 使用适配器获取高度 */
  height: var(--nav-height, 60px);
  
  /* iOS特定样式 */
  /* #ifdef APP-PLUS-IOS */
  padding-top: 44px; /* 状态栏高度 */
  /* #endif */
  
  /* 小程序特定样式 */
  /* #ifdef MP-WEIXIN */
  padding-top: 25px; /* 胶囊按钮区域 */
  padding-right: 90px; /* 为胶囊按钮留空间 */
  /* #endif */
  
  /* H5特定样式 */
  /* #ifdef H5 */
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
  /* #endif */
}

.page-container {
  /* 考虑顶部导航栏 */
  padding-top: var(--nav-height, 60px);
  
  /* 考虑底部安全区域 - iOS底部横条 */
  /* #ifdef APP-PLUS-IOS */
  padding-bottom: calc(var(--tab-bar-height, 60px) + env(safe-area-inset-bottom, 0));
  /* #endif */
  
  /* 其他平台 */
  /* #ifndef APP-PLUS-IOS */
  padding-bottom: var(--tab-bar-height, 60px);
  /* #endif */
}
```

#### 平台特定组件示例

**底部标签栏组件：**
```vue
<template>
  <view class="tab-bar">
    <view
      v-for="(tab, index) in tabs"
      :key="index"
      class="tab-bar__item"
      :class="{'tab-bar__item--active': activeIndex === index}"
      @tap="handleTabClick(index)"
    >
      <AppIcon :icon="tab.icon" class="tab-bar__icon" />
      <text class="tab-bar__label">{{ tab.label }}</text>
    </view>
    
    <!-- 仅在iOS上添加安全区域占位 -->
    <!-- #ifdef APP-PLUS-IOS -->
    <view class="safe-area-bottom"></view>
    <!-- #endif -->
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import { getPlatformStyle } from '@/utils/platform';

const props = defineProps({
  activeIndex: {
    type: Number,
    default: 0
  },
  tabs: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['change']);

const handleTabClick = (index) => {
  if (index !== props.activeIndex) {
    emit('change', index);
  }
};

// 获取平台特定的底部高度
const tabBarHeight = computed(() => {
  return getPlatformStyle('tab-bar-height');
});
</script>

<style lang="scss" scoped>
.tab-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  display: flex;
  justify-content: space-around;
  align-items: center;
  background-color: var(--bg-primary, #FFFFFF);
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.05);
  height: v-bind(tabBarHeight);
  z-index: 100;
  
  &__item {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    height: 100%;
    padding: 6px 0;
    transition: all 0.3s;
    
    &--active {
      color: var(--color-primary, #FF6B35);
    }
  }
  
  &__icon {
    margin-bottom: 4px;
    font-size: 22px;
  }
  
  &__label {
    font-size: 12px;
  }
}

/* 安全区域适配 - iOS底部横条 */
.safe-area-bottom {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: env(safe-area-inset-bottom, 0);
  background-color: var(--bg-primary, #FFFFFF);
}

/* iOS特定样式 */
/* #ifdef APP-PLUS-IOS */
.tab-bar {
  padding-bottom: env(safe-area-inset-bottom, 0);
}
/* #endif */
</style>
```

#### 适配策略指南

1. **统一入口**：所有平台差异处理必须通过 `platform.js` 中的适配器函数进行，禁止在业务代码中直接判断平台。

2. **优先使用CSS变量**：对于样式差异，优先使用CSS变量和媒体查询解决，而非JavaScript条件编译。

3. **组件封装**：针对平台特性差异大的UI元素（如导航栏、标签栏），创建专门的适配组件。

4. **响应式设计**：优先采用响应式设计，确保在不同尺寸的设备上（包括折叠屏和平板）都有良好表现。

5. **降级处理**：对于某些平台缺少的功能，提供降级替代方案，确保核心体验一致。

### 10. 状态管理 (State Management)
- **Pinia**: **必须 (MUST)** 使用 Pinia，**禁止 (FORBID)** 使用 Vuex
- **模块化**: 按业务模块拆分 store，清晰分离状态、getters 和 actions
- **状态隔离**: 页面级状态使用局部状态或独立的状态模块，避免滥用全局状态

**Pinia store 示例:**
```javascript
// src/stores/user.store.js
import { defineStore } from 'pinia';

export const useUserStore = defineStore('user', {
  state: () => ({
    userInfo: null,
    isLoggedIn: false
  }),
  
  getters: {
    userName: (state) => state.userInfo?.name ?? '未登录用户'
  },
  
  actions: {
    async login(credentials) {
      try {
        const result = await api.login(credentials);
        this.userInfo = result.user;
        this.isLoggedIn = true;
        return true;
      } catch (error) {
        console.error('登录失败', error);
        return false;
      }
    }
  }
});
```

## 样式与视觉规范

### 1. 颜色与样式系统
- **主题色**: 使用 `var(--color-primary, #FF6B35)` 作为主色
- **文字色**: 
  - 主要文字: `var(--text-primary, #333333)`
  - 次要文字: `var(--text-secondary, #666666)`
  - 提示文字: `var(--text-hint, #999999)`
- **统一变量**: 所有全局 CSS 变量 **必须 (MUST)** 统一定义在 `src/assets/styles/variables.scss` 中

### 2. 字体与排版
- **主要字体**: PingFang SC
- **字号系统**:
  - 主标题: 20px
  - 副标题: 18px
  - 正文: 16px
  - 辅助文字: 14px

### 3. 间距与布局
- **间距系统**: 基于 8px 倍数设计间距
- **间距变量**: 使用 `--space-xs`, `--space-sm`, `--space-md`, `--space-lg`, `--space-xl` 等变量
- **圆角变量**: 使用 `--radius-sm`, `--radius-md`, `--radius-lg`, `--radius-pill` 等变量

### 4. 组件视觉规范
- **按钮尺寸标准**:

| 类型   | 高度 | 左右边距 | 字体大小 | 圆角 |
|--------|------|----------|----------|------|
| Large  | 48px | 32px     | 18px     | 8px  |
| Medium | 40px | 24px     | 16px     | 6px  |
| Small  | 32px | 16px     | 14px     | 4px  |

- **卡片间距规范**:

| 元素                | 外边距 | 内边距 |
|---------------------|--------|--------|
| 卡片容器            | 16px   | 0      |
| 卡片内容区          | 0      | 16px   |
| 卡片标题与内容间距  | -      | 12px   |
| 卡片之间的间距      | 16px   | -      |

### 5. 标题栏统一规范
- **高度标准**:
  - iOS: 88px (包含状态栏)
  - Android: 56dp
  - 小程序: 90px
  - H5: 60px

**实现模板:**
```vue
<template>
  <!-- 使用平台适配函数获取高度 -->
  <view class="nav-bar" :style="{height: navHeight}">
    <text class="title">{{ title }}</text>
  </view>
</template>

<script setup>
import { getPlatformStyle } from '@/utils/platform'

const navHeight = getPlatformStyle('nav-height') // 对应架构文档的适配器实现
</script>

<style scoped>
.nav-bar {
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary);
}

.title {
  color: white;
  font-size: 18px;
  font-weight: 500;
}
</style>
```

### 6. 暗黑模式适配
- **变量定义**: 颜色变量必须支持暗黑模式切换
- **媒体查询**: 使用 `@media (prefers-color-scheme: dark)` 定义暗黑模式样式
- **组件适配**: 组件必须使用 CSS 变量，而非硬编码颜色值

**暗黑模式变量示例:**
```scss
/* 浅色模式（默认）变量 */
:root {
  --color-primary: #FF6B35;
  --color-background: #ffffff;
  --text-primary: #333333;
  --text-secondary: #666666;
  --border-color: #e5e5e5;
  --card-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 暗黑模式变量 */
@media (prefers-color-scheme: dark) {
  :root {
    --color-primary: #FF8A65; /* 主色调更亮的版本 */
    --color-background: #121212;
    --text-primary: #f5f5f5;
    --text-secondary: #aaaaaa;
    --border-color: #333333;
    --card-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
  }
}
```

## 工具链与开发流程

### 1. 代码检查 (Linting)
- **ESLint**: 用于 JavaScript/TypeScript 代码检查
- **Stylelint**: 用于 CSS/SCSS 样式检查
- **规则配置**: 遵循项目中 `.eslintrc.js` 和 `.stylelintrc.json` 的规则配置

### 2. Git 工作流
- **Husky**: 使用 Husky 在提交前运行检查，确保代码质量
- **Lint-staged**: 只对暂存的文件运行 linter，提高效率

### 3. 自动化测试
- **Vitest**: 用于单元测试和组件测试
- **Storybook**: 用于组件可视化开发和视觉回归测试

### 4. 常见问题与解决方案

#### 样式冲突问题
- **症状**: 页面样式被其他页面影响
- **解决**: 
  - 检查类名前缀唯一性
  - 确保 `scoped` 属性正确使用
  - 避免全局样式污染

#### 图标显示异常
- **症状**: 图标无法显示或样式错误
- **解决**:
  - 检查 uView Plus 是否已正确安装和配置（例如 `main.ts` 中引入 uView Plus 主题 `uni.scss`，`pages.json` 中配置 `easycom` 规则以正确识别 `uview-plus` 组件，包括 `<u-icon>` 或封装它的 `<AppIcon>`）。
  - 确保 `<AppIcon>` 组件的 `icon` (或 `name`) prop 传递的是有效的 uView Plus 图标名称。请参考 uView Plus 官方文档查找正确的图标名称。
  - 检查 `<AppIcon>` 组件本身封装逻辑是否正确，是否能正确将 props 传递给内部的 `<u-icon>`。
  - 确认没有其他全局样式或组件的 scoped 样式意外覆盖了图标的显示 (例如 `font-size: 0`, `display: none` 等)。
  - 检查 `color` prop 是否使用了 CSS 变量，如 `color="var(--text-primary)"`，并确认该 CSS 变量已定义且有效。
  - 查看浏览器控制台或小程序开发者工具的控制台，是否有关于组件未找到或 props 错误的提示。

#### 多端适配问题
- **症状**: 不同平台显示效果不一致
- **解决**:
  - 使用平台特定条件编译
  - 应用平台特定样式
  - 使用 `utils/platform.js` 中的适配函数 

## 前端模拟数据规范 (Data Mocking)

为方便开发、测试和演示，项目采用统一的前端模拟数据（Mock）机制。

**核心原则**:
- **分离存储**: 所有前端模拟数据 **必须 (MUST)** 存放在 `src/api/mocks/` 目录下。
- **结构对应**: Mock 文件应与 `src/api/` 目录下的 API 文件一一对应（例如 `src/api/auth.ts` 对应 `src/api/mocks/auth.mock.ts`）。
- **格式一致**: Mock 文件导出的数据或函数返回的数据结构 **必须 (MUST)** 严格符合《API接口文档样例.md》中对应接口的响应体 (`data` 部分) 格式。
- **统一开关**: 使用环境变量 `VITE_USE_MOCK_DATA` (在 `.env` 或 `.env.development` 文件中设置，例如 `VITE_USE_MOCK_DATA=true`) 来全局控制是否启用 Mock 数据。
- **API 层集成**: 在实际的 API 实现文件（如 `src/api/auth.ts`）中，**必须 (MUST)** 在函数开头检查 `import.meta.env.VITE_USE_MOCK_DATA` 的值。如果为 `true`，则导入并返回对应的 Mock 数据；否则，执行真实的 API 请求。

**Mock 文件示例 (`src/api/mocks/auth.mock.ts`)**:
```typescript
import type { LoginResponseData } from '@/types/api'; // 假设定义了类型

// 模拟登录成功响应
const mockLoginSuccess: LoginResponseData = {
  userId: 'mock_u_10001',
  token: 'mock_jwt_token_string_123456',
  refreshToken: 'mock_refresh_token_string_abcdef',
  expires: Date.now() + 30 * 60 * 1000, // 模拟30分钟后过期
  refreshExpires: Date.now() + 30 * 24 * 60 * 60 * 1000, // 模拟30天后过期
  needSetupSecurity: false
};

// 模拟发送验证码响应
const mockSendSmsCodeSuccess = {
  expireIn: 300,
  requestId: `mock_sms_${Date.now()}`
};

// 导出模拟函数或数据
export default {
  // 模拟登录接口
  login: (credentials: any) => {
    console.log('[MOCK] login called with:', credentials);
    // 可以根据 credentials 模拟成功或失败
    if (credentials.phone === '13800000000' && credentials.smsCode === '1234') {
      return Promise.resolve(mockLoginSuccess);
    } else {
      return Promise.reject({ code: 1002, message: '验证码错误或已过期 (Mock)' });
    }
  },
  // 模拟发送验证码接口
  sendSmsCode: (params: any) => {
    console.log('[MOCK] sendSmsCode called with:', params);
    return Promise.resolve(mockSendSmsCodeSuccess);
  },
  // ... 其他 auth.ts 中的接口的模拟实现
};
  
  API 集成示例 (src/api/auth.ts):
import { request } from '@/utils/request';
import authMock from './mocks/auth.mock'; // 导入 Mock 文件
import type { LoginCredentials, LoginResponseData } from '@/types/api';

const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true'; // 读取环境变量

/**
 * 用户登录
 * @param credentials - 登录凭证
 * @returns Promise<LoginResponseData>
 */
export function login(credentials: LoginCredentials): Promise<LoginResponseData> {
  if (useMock) {
    // 如果启用 Mock，返回 Mock 数据
    return authMock.login(credentials);
  }
  // 否则，发起真实 API 请求
  return request.post('/api/v1/auth/login', credentials);
}

/**
 * 发送短信验证码
 * @param phone - 手机号
 * @param type - 类型
 */
export function sendSmsCode(phone: string, type: 'register' | 'login' | 'reset') {
   if (useMock) {
     return authMock.sendSmsCode({ phone, type });
   }
   return request.post('/api/v1/sms/send', { phone, type });
}

// ... 其他 API 函数类似处理 ...

### 本地持久化账本数据库（localLedgerDB）规范

为提升开发和测试的真实性，项目支持使用本地持久化数据库（localLedgerDB，意为"本地账本数据库"）来模拟真实账本数据的存储和操作。

**核心原则**：
- **统一命名**：所有本地账本相关数据统一存储在 localLedgerDB 下，采用明确的 key 进行区分。
- **主要数据 key**：
  - `transactions`：交易数据（如每一笔收支记录）
  - `categories`：分类数据（如餐饮、交通等）
  - `assets`：账户数据（如现金、银行卡等）
- **存储方式**：
  - 使用 `uni.setStorageSync(key, value)` 写入数据
  - 使用 `uni.getStorageSync(key)` 读取数据
  - 该方式跨端兼容，适用于 uni-app/H5/小程序/APP
- **适用场景**：
  - 开发、测试、演示阶段，需模拟真实账本操作和数据持久化时优先使用
  - 适合需要多端一致体验和数据可持续保存的场景
- **切换建议**：
  - Mock 适合接口联调和简单演示
  - localLedgerDB 适合功能测试和真实交互体验
  - 可通过配置或环境变量灵活切换

**代码示例**：
```js
// 写入一条交易记录
const newTransaction = { id: 't1', amount: 100, categoryId: 'cat-1', date: '2024-06-05' };
const transactions = uni.getStorageSync('transactions') || [];
transactions.push(newTransaction);
uni.setStorageSync('transactions', transactions);

// 读取所有交易记录
const allTransactions = uni.getStorageSync('transactions') || [];

// 写入分类数据
uni.setStorageSync('categories', [
  { id: 'cat-1', name: '餐饮', icon: 'utensils', color: 'var(--color-primary, #FF6B35)' },
  // ...
]);

// 读取分类数据
const categories = uni.getStorageSync('categories') || [];
```

**注意事项**：
- 本地存储仅适合开发、测试和小型应用，不建议存放敏感信息或大数据量
- 需确保 key 命名规范、数据结构与接口文档一致，便于后续与后端对接
- 多端兼容性良好，但不同平台存储容量有限，建议定期清理或分页加载
- 如需与后端同步，可在后续增加同步机制

---

**Mock 与 localLedgerDB 对比表**：
| 方案         | 数据持久性 | 适用场景         |  
   优点                   | 缺点                   |
|--------------|------------|------------------|------------------------|------------------------|
| Mock         | 否         | 接口联调、演示   | 实现简单、易切换       | 数据不持久、体验有限   |
| localLedgerDB| 是         | 功能测试、演示   | 数据持久、体验真实     | 需管理数据结构和同步   |

---

## API调用与Mock切换规范

为保证开发体验和多端一致性，项目API调用、Mock切换、本地持久化账本等需遵循以下规范：

### 1. API统一导出与调用方式
- 所有API模块通过`src/api/index.ts`统一导出，推荐如下方式调用：
  ```ts
  import { login, getTransactions, getAllCategories } from '@/api'
  ```
- API文件与Mock文件一一对应，命名规范：
  - 真实API：`src/api/transaction.ts`、`src/api/category.ts`、`src/api/asset.ts`等
  - Mock数据：`src/api/mocks/transaction.mock.ts`、`category.mock.ts`、`asset.mock.ts`等

### 2. Mock与真实接口切换机制
- 通过环境变量`VITE_USE_MOCK_DATA`控制Mock开关：
  - 在`.env`或`.env.development`文件中配置：
    ```env
    VITE_USE_MOCK_DATA=true
    ```
- 代码中判断方式：
  - 推荐统一写法：
    ```ts
    const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true'
    // 或通过配置中心
    import config from '@/config'
    const useMock = config.env.useMockData
    ```
- API实现中，优先判断useMock，决定调用Mock还是真实接口：
  ```ts
  if (useMock) {
    return transactionMock.getTransactions(params)
  }
  return request.get('/api/v1/transactions', { data: params })
  ```

### 3. 本地持久化账本（localLedgerDB）与API/Mock协同用法
- 推荐在Mock实现或本地开发阶段，结合`uni.setStorageSync/uni.getStorageSync`进行本地数据持久化：
  ```js
  // 写入账本数据
  uni.setStorageSync('transactions', transactionsArray)
  // 读取账本数据
  const transactions = uni.getStorageSync('transactions') || []
  ```
- Mock文件可用本地存储模拟真实数据的增删查改，提升开发和测试的真实性
- 本地存储key命名建议：`transactions`（交易）、`categories`（分类）、`assets`（账户）等
- 注意：本地存储仅用于开发、测试和小型应用，不建议存放敏感信息或大数据量

### 4. 环境变量配置说明
- 所有环境变量统一在`.env`、`.env.development`等文件中配置，常用变量包括：
  | 变量名                | 说明             | 示例值           |
  |----------------------|------------------|-----------------|
  | VITE_USE_MOCK_DATA   | 是否启用Mock     | true/false      |
  | VITE_APP_TITLE       | 应用名称         | 记账无忌         |
  | VITE_APP_VERSION     | 应用版本         | 1.0.0           |
- 读取方式：
  ```ts
  import.meta.env.VITE_USE_MOCK_DATA
  import.meta.env.VITE_APP_TITLE
  // 推荐通过config.env.useMockData等方式统一读取
  ```

### 5. 代码片段与注意事项
- API调用标准示例：
  ```ts
  import { getTransactions } from '@/api'
  const list = await getTransactions({ page: 1, pageSize: 20 })
  ```
- Mock切换标准示例：
  ```ts
  const useMock = import.meta.env.VITE_USE_MOCK_DATA === 'true'
  if (useMock) {
    // 调用Mock
  } else {
    // 调用真实API
  }
  ```
- 本地存储标准示例：
  ```js
  uni.setStorageSync('transactions', [{ id: 't1', ... }])
  const all = uni.getStorageSync('transactions') || []
  ```
- 注意事项：
  - API和Mock文件结构、命名、导出方式要保持一致，便于切换和维护
  - 环境变量变更后需重启开发服务生效
  - 本地存储数据结构要与接口文档保持一致，便于后续对接后端

---

## 10. API与Mock数据与本地持久化数据库 (API, Mock Data & Local LedgerDB)
- **环境变量检查**: 在 `src/api/*.ts` 文件中的每个API函数开头，必须 (MUST) 检查环境变量 `import.meta.env.VITE_USE_MOCK_DATA`
- **条件返回**:
  - 若 `VITE_USE_MOCK_DATA === 'true'`，必须 (MUST) 导入并返回对应的 `src/api/mocks/*.mock.ts` 中的模拟数据
  - 否则执行真实的 `utils/request.ts` 网络请求
- **Mock结构**: Mock数据结构必须 (MUST) 严格符合API接口文档定义的响应体格式
- **路径规范**: 所有API路径必须包含版本号: `/api/v1/endpoint`
- **RESTful风格**: 使用适当的HTTP方法表达资源操作 (GET查询、POST创建、PUT更新、DELETE删除)
- **本地持久化数据库（localLedgerDB）规范**:
  - **必须 (MUST)** 使用 `src/utils/storage.ts` 中提供的统一存储工具进行所有本地存储操作
  - **必须 (MUST)** 使用 `storage` 中定义的标准键名常量（如 `TRANSACTIONS_KEY`、`CATEGORIES_KEY` 等）
  - **禁止 (FORBID)** 在代码中直接硬编码键名或使用非标准前缀
  - **禁止 (FORBID)** 直接调用 `localStorage` 或原生 `uni.setStorageSync/getStorageSync`
  - 模块化管理数据：交易数据、分类数据、账户数据各自独立存储
  - 遵循 `storage.ts` 定义的 `ledger` 命名空间规范，所有账本相关数据使用统一前缀
- **Mock实现与本地存储协同**:
  - Mock API实现**必须 (MUST)** 使用 `storage` 工具进行本地数据持久化
  - 使用标准化的增删改查模式维护Mock数据
  - 所有涉及写操作的Mock函数必须正确更新本地存储
  - Mock读取操作也必须从本地存储加载最新数据
- **数据初始化流程**:
  - Mock模块应提供初始默认数据
  - 只有在本地存储中不存在数据时，才使用默认数据初始化
  - 提供清除和重置为默认值的功能
- **API与Mock命名规范**：
  - API文件、Mock文件一一对应，命名、导出方式保持一致，便于切换和维护
  - 所有Mock函数的输入参数和返回值类型必须与真实API完全匹配

