# Cursor AI Agent 开发规则 (AI记账项目 - 新手模式)

**版本:** 1.0
**核心目标:** 指导 AI Agent (Claude) 在 Cursor 中独立完成 AI 记账应用的开发，并同步维护 `README.md` 文档以跟踪进度，确保严格遵循项目规范。

**重要前提:**
*   **用户身份:** 我（用户）是项目负责人，但**没有编程开发经验**。你需要用简单易懂的方式解释技术选择，并**承担所有代码编写工作**。
*   **核心文档:** 你 **必须 (MUST)** 始终参考以下项目文档（我会使用 `@` 语法提供给你访问）：
    *   `架构设计文档.md` (@架构设计文档.md) - 整体蓝图和技术选型。
    *   `开发规范与指南-基础篇.md` (@开发规范与指南-基础篇.md) - **基础编码规则和必须遵守的约定**。
    *   `开发规范与指南-高级篇.md` (@开发规范与指南-高级篇.md) - 进阶实践，供你参考实现更优代码。
    *   `UI界面规则文档.md` (@UI界面规则文档.md) - **视觉和组件样式的唯一标准**。
    *   `开发步骤.md` (@开发步骤.md) - **我们遵循的主要开发流程和阶段划分**。
    *   `API接口文档样例.md` (@API接口文档样例.md) - API 和 Mock 数据结构的依据。
    *   `README.md` (@README.md) - **项目状态跟踪文档，你需要持续更新它**。
*   **UI 原型:** 我会提供 HTML 文件作为 UI 原型参考，例如 `@prototype/login.html`。你需要严格按照原型视觉效果实现，并结合 `@UI界面规则文档.md` 进行规范化。
*   **工具:** 我们使用 Cursor IDE，你可以访问和修改项目文件。

## 核心工作原则 (AI Agent 必须遵守)

1.  **文档优先原则:** 任何开发决策和代码实现，**必须 (MUST)** 以 `@架构设计文档.md`, `@开发规范与指南-基础篇.md`, `@UI界面规则文档.md` 为最高优先级依据。若原型与文档规范冲突，**优先遵循文档规范**，并向我说明差异和原因。
2.  **新手友好沟通:** 用简洁、非技术性的语言向我解释你正在做什么、为什么这么做，以及需要我确认什么。如果遇到技术术语，请简单解释。
3.  **代码生成责任:** 你负责生成**所有**项目代码（Vue 组件、TS/JS 逻辑、SCSS 样式、配置文件等）。
4.  **增量式开发与确认:**
    *   严格按照 `@开发步骤.md` 的阶段和步骤进行开发。
    *   每次只处理**一个明确的小任务**（例如，"创建登录页面的 UI 骨架"，"实现获取验证码按钮的逻辑"）。
    *   在**创建或修改任何文件之前**，必须先告诉我你的计划（要创建/修改哪个文件，实现什么功能），并**得到我的确认**。
    *   代码生成后，向我展示关键代码片段，并**请求我的确认**。
5.  **README 同步:** **每次**当我确认你完成了一个代码任务（如创建了一个组件、实现了一个功能）后，你**必须 (MUST)** 立即根据下面的【README.md 管理规范】更新 `README.md` 文件，并**再次向我展示更新后的内容以供确认**，然后才实际修改 `README.md` 文件。
6.  **文件路径:** **必须 (MUST)** 始终使用项目根目录下的相对路径（例如 `src/pages/auth/login.vue`）。
7.  **多端优先:** **必须 (MUST)** 从一开始就考虑多端兼容性（iOS, Android, 小程序, H5），遵循 `@开发规范与指南-基础篇.md` 第 9 节的多端适配策略。
8.  **Mock 数据优先:** 在与真实后端对接前，优先使用 Mock 数据进行开发和测试，遵循 `@开发规范与指南-基础篇.md` 关于 Mock 数据和 `localLedgerDB` 的规范。

## README.md 管理规范

`README.md` 是我们跟踪项目的核心。你**必须**在每次完成并经我确认的代码任务后，更新此文件。

**`README.md` 应包含以下结构 (如果不存在请创建):**

```markdown
# AI 记账应用开发进度

**当前项目状态:** [例如：阶段三：核心认证流程开发 - 进行中] (根据 @开发步骤.md 更新)

**最后更新时间:** [YYYY-MM-DD HH:MM]

## ✅ 已完成任务 (最近 5-10 项)

*   [完成日期] - [阶段.步骤] - [简要任务描述，例如：创建登录页面 UI 骨架 (`src/pages/auth/login.vue`)]
*   [完成日期] - [阶段.步骤] - [简要任务描述]
*   ...

## ⏳ 当前进行中/待办任务

*   **当前:** [阶段.步骤] - [当前正在执行的任务描述]
*   **下一步:**
    *   [阶段.步骤] - [下一个任务描述 (来自 @开发步骤.md)]
    *   [阶段.步骤] - [再下一个任务描述]
    *   ...

## 🔑 关键决策与说明

*   [日期] - [决策点说明，例如：登录页面输入框采用浮动标签样式以增强现代感，遵循 @UI界面规则文档.md]
*   [日期] - [决策点说明]
*   ...

## ⚠️ 潜在问题与风险

*   [记录遇到的技术难题、未解决的疑问、或未来可能需要注意的问题]
*   ...

## 校验清单 (由 AI 根据 @开发步骤.md 中的清单更新勾选状态)
* [ ] 基础环境与配置 - 项目结构符合规范
* [ ] ... (保持与 @开发步骤.md 中最新的校验清单一致)
更新流程:
我确认你生成的代码 OK。
你根据刚完成的任务和 @开发步骤.md，准备好要更新 README.md 的具体内容（包括修改哪个部分，增加/删除什么文字）。
你向我展示你打算如何更新 README.md。
我确认更新内容 OK。
你执行对 README.md 文件的修改。
代码生成规则 (严格执行)
你生成的代码必须严格遵守以下规范 (细节参考对应文档):

### 技术栈
- Vue 3
- uni-app
- Pinia (禁止 Vuex)
- SCSS

### 语言
- 优先使用 TypeScript (如果我没有特殊说明)
- Vue 语法: 必须 (MUST) 使用 `<script setup lang="ts">`

### 组件
- **命名**: PascalCase。通用组件必须以 App 开头 (除了 CategorySelector)
- **存放**: 
  - 通用组件在 `src/components/common/`
  - 业务组件在 `src/components/business/` 或页面 `components/` 下
- **复用**: 必须 (MUST) 优先使用 @开发规范与指南-基础篇.md 中列出的 App* 通用组件。禁止创建功能重复的组件
- **Props & Emits**: 清晰定义，建议添加类型
- **每次创建新的组件请同步到README.md**文档里并注释组件的用途

### 样式 (SCSS)
- 必须 (MUST) 使用 `<style lang="scss" scoped>`
- 必须 (MUST) 使用 CSS 变量 (var(--variable-name, fallbackValue)) 引用 @src/assets/styles/variables.scss 中定义的颜色、间距、圆角等，禁止 (FORBID) 硬编码
- **类名**: BEM 或带前缀的 BEM (page-name__element--modifier)，禁止通用类名
- **!important**: 原则上禁止。只在 @开发规范与指南-基础篇.md 第 5.1 节允许的特例下使用，且优先使用 @tools/style/scripts/_utilities.scss 中定义的 u-* 工具类
- **多端适配**: 使用条件编译和 utils/platform.js
- **图表组件特殊规则**: 对于图表组件（如QiunDataCharts）等无法直接识别CSS变量的组件，必须通过JS工具函数（如`utils/colors.ts`中的`getCssVariableValue`）读取CSS变量的实际颜色值，并通过props传递给图表组件。禁止在代码审查和优化中将此类颜色转换代码改为直接使用CSS变量。详细规范参考 `@UI界面规则文档.md` 第14章"UI组件特殊规则"。

### 图标
- 必须 (MUST) 通过全局注册的 `<AppIcon>` 组件使用 uView Plus 图标。该组件内部封装了 uView Plus 的 `<u-icon>`。
- uView Plus 图标库通过 NPM 安装 `uview-plus` 包，并通过 `easycom` 机制按需加载。`<AppIcon>` 组件自身也应遵循此机制。
- 禁止 (FORBID) 其他任何图标使用方式（如 `<i>` 标签, CDN, 直接在 `<AppIcon>` 外部使用 `<u-icon>` 等）。
- 分类图标必须使用 `<CategorySelector>` 组件，其内部图标也应遵循 uView Plus 规范（即使用 uView Plus 的图标名称）。
- 使用 `<AppIcon>` 时，应正确传递 `icon` (uView Plus图标名), `size`, 和 `color` (必须为CSS变量) 等props。
- 图标颜色必须使用CSS变量（如 `var(--color-primary)`），禁止使用直接的颜色值或颜色名称。
- 对于需要交互的图标，应通过 `@click` 事件处理，而非包装在其他元素内。
- 如需扩展自定义图标（来自iconfont等），必须通过 `customPrefix` 属性正确配置，遵循官方扩展规范。

#### 图标配置最佳实践
- **全局配置**: 在 `pages.json` 中配置 easycom 规则确保自动导入 uView Plus 图标组件
  ```json
  "easycom": {
    "autoscan": true,
    "custom": {
      "^u-icon$": "uview-plus/components/u-icon/u-icon.vue",
      "^u-(.*?)": "uview-plus/components/u-$1/u-$1.vue"
    }
  }
  ```
- **全局样式**: 在 `App.vue` 中引入 uView Plus 样式并确保图标字体加载
  ```scss
  @import "uview-plus/index.scss";
  ```
- **多端适配**: 通过全局样式和字体配置确保图标在所有平台正确显示，必要时提供平台特定样式
- **字体资源**: 将字体文件正确放置在 `static/fonts/` 目录下，确保所有平台可访问

#### 标准使用示例
```vue
<!-- 正确使用方式 -->
<AppIcon 
  icon="home" 
  size="28" 
  color="var(--color-primary)"
/>

<!-- 带事件处理 -->
<AppIcon 
  icon="plus" 
  size="36"
  color="var(--color-success)"
  @click="handleAddItem"
/>
```

#### 避免的问题
- 禁止使用第三方图标库
- 禁止直接使用CSS类名方式添加图标（除非在特定测试场景）
- 禁止在组件内部调用 `loadIconFont` 或自行加载图标字体
- 禁止使用硬编码颜色值

### API & Mocking
- API 调用放在 `src/api/`，Mock 数据在 `src/api/mocks/`
- 必须 (MUST) 实现基于 VITE_USE_MOCK_DATA 环境变量的 Mock/真实 API 切换
- Mock 数据结构必须符合 @API接口文档样例.md
- **本地持久化数据库 (localLedgerDB) 规范**:
  - **必须 (MUST)** 使用 `src/utils/storage.ts` 中提供的统一存储工具进行所有本地存储操作
  - **必须 (MUST)** 使用 `storage` 中定义的标准键名常量（如 `TRANSACTIONS_KEY`、`CATEGORIES_KEY` 等）
  - **禁止 (FORBID)** 在代码中直接硬编码键名或使用非标准前缀
  - **禁止 (FORBID)** 直接调用 `localStorage` 或原生 `uni.setStorageSync/getStorageSync`
  - 所有 Mock 数据必须使用统一存储工具实现持久化，确保多端兼容性

### 状态管理 (Pinia)
- 必须 (MUST) 使用 Pinia，模块化存放在 `src/stores/`
- 必须 (MUST) 使用 Composition API 风格 (setup 函数)
- 共享数据（用户信息、分类列表等）必须通过 Pinia 管理

### 代码风格
- 遵循 @开发规范与指南-基础篇.md 第 6 节导入顺序等规范
- 使用 ESLint 和 Prettier (如果配置了) 保证格式一致

### 防御性编程
- 遵循 @开发规范与指南-基础篇.md 第 7 节，使用 ?., ??, try...catch 等

## 交互与沟通方式

### 任务启动
- 我会基于 @开发步骤.md 给你分配任务，例如："请开始开发阶段三，步骤 3.3：创建登录页面 UI"

### 引用文档
- 我会使用 @ 引用相关文档和原型文件
- 你也应该在解释时引用依据的文档章节

### 提问
- 如果我的指令不清晰或缺少信息，必须向我提问，不要猜测

### 确认流程
1. **计划确认**: "我计划创建 src/pages/auth/login.vue 文件，包含...布局，使用 AppInput 和 AppButton 组件。是否继续？"
2. **代码确认**: "这是生成的 login.vue 关键代码片段：[代码]... 是否符合预期？"
3. **README 更新确认**: "我将更新 README.md 如下：[更新内容]... 是否确认？"
4. **解释**: 对关键代码或设计选择进行简要、易懂的解释
5. **进度反馈**: 完成任务后，明确告知任务已完成，并提示进行下一步

## 错误处理

### 代码错误
- 如果生成的代码在运行时出错，我会将错误信息反馈给你
- 你需要分析错误原因，并根据项目规范提供修复后的代码

### 理解错误
- 如果你无法理解我的指令，请直接说明，并请求我换一种方式描述或提供更多信息

### 规范冲突
- 如果我的指令与项目文档规范冲突，请明确指出冲突点，并以文档规范为准，向我解释原因

## 会话初始化
当我发送 "请根据 @.cursorrules" 时，请执行以下操作：
1. 仔细阅读并理解本 .cursorrules的所有规则
2. 检查项目根目录下的 README.md 文件
3. 根据 README.md 中的"当前项目状态"和"待办任务"，结合 @开发步骤.md，确定当前应该进行的开发步骤
4. 向我报告当前项目状态和下一步计划进行的任务，并请求确认。例如："你好！我已经阅读了开发规则。根据 README.md，我们目前处于[状态]，下一步计划是进行[任务描述]。是否开始执行？"

### 当前组件清单

> **注意：**
> - 创建新组件时，**必须优先复用已有组件**。如确实无法复用，需新建组件时，**必须告知叶同学，并同步更新本清单和@README.md**，以便团队和AI后续查阅，避免重复开发。

#### 1. 通用组件（src/components/common/）
| 组件名                | 用途简述                     | 路径                                      |
|----------------------|----------------------------|------------------------------------------|
| AppButton            | 通用按钮，支持多种样式和状态      | src/components/common/AppButton.vue      |
| AppCard              | 卡片容器，统一圆角和阴影          | src/components/common/AppCard.vue        |
| AppInput             | 通用输入框，支持多类型输入        | src/components/common/AppInput.vue       |
| AppModal             | 通用弹窗模态框                  | src/components/common/AppModal.vue       |
| AppCalendar          | 日历弹窗，选择日期               | src/components/common/AppCalendar.vue    |
| AppNavBar            | 顶部导航栏，适配多端              | src/components/common/AppNavBar.vue      |
| AppTabBar            | 底部导航栏，适配多端              | src/components/common/AppTabBar.vue      |
| AppIcon              | 图标组件，封装uView Plus的u-icon  | src/components/common/AppIcon.vue        |
| AppLoading           | 加载中动画                      | src/components/common/AppLoading.vue     |
| AppDatePicker        | 日期选择器                      | src/components/common/AppDatePicker.vue  |
| AppDivider           | 分割线                          | src/components/common/AppDivider.vue     |
| AppEmpty             | 空状态占位                      | src/components/common/AppEmpty.vue       |
| AppSwipeAction       | 滑动操作容器                    | src/components/common/AppSwipeAction.vue |
| AppSwipeActionButton | 滑动操作按钮                    | src/components/common/AppSwipeActionButton.vue |
| AppMonthNavigator    | 月份切换器                      | src/components/common/AppMonthNavigator.vue |
| AppSegmentedControl  | 分段控制器（选项卡）              | src/components/common/AppSegmentedControl.vue |

#### 2. 业务组件（src/components/business/）
| 组件名                | 用途简述                     | 路径                                      |
|----------------------|----------------------------|------------------------------------------|
| CategorySelector     | 分类选择器，选择账单类别          | src/components/business/CategorySelector.vue |
| TransactionItem      | 账单/交易列表项展示              | src/components/business/TransactionItem.vue |
| AmountInput          | 金额输入组件，带自定义键盘         | src/components/business/AmountInput.vue      |
| BudgetProgress       | 预算进度条                      | src/components/business/BudgetProgress.vue  |
| GesturePassword      | 手势密码输入与设置                | src/components/business/GesturePassword.vue |
| CategoryProgressBar  | 分类预算进度条                    | src/components/business/CategoryProgressBar.vue |
| LineChart            | 折线图组件，用于趋势展示           | src/components/business/charts/LineChart.vue |
| PieChart             | 饼图组件，用于占比分析            | src/components/business/charts/PieChart.vue |
| BarChart             | 柱状图组件，用于数值对比           | src/components/business/charts/BarChart.vue |
| ChatBubble           | 聊天气泡组件，用于展示聊天消息     | src/components/business/ChatBubble.vue |
| RecognitionResult    | 交易识别结果组件，在聊天中展示识别的交易信息 | src/components/business/RecognitionResult.vue |

#### 3. 页面私有组件
##### 3.1 欢迎页
| 组件名                | 用途简述                     | 路径                                      |
|----------------------|----------------------------|------------------------------------------|
| WelcomeSlideContent  | 欢迎页轮播内容                | src/pages/welcome/components/WelcomeSlideContent.vue |
| WelcomeFeature       | 欢迎页功能介绍卡片              | src/pages/welcome/components/WelcomeFeature.vue      |
| WelcomeSwiper        | 欢迎页轮播组件                  | src/pages/welcome/components/WelcomeSwiper.vue       |
| WelcomeCover         | 欢迎页封面图组件                | src/pages/welcome/components/WelcomeCover.vue        |

##### 3.2 统计页
| 组件名                     | 用途简述                     | 路径                                      |
|---------------------------|----------------------------|------------------------------------------|
| AnalysisCoreIndicators    | 核心财务指标组件               | src/pages/analysis/components/AnalysisCoreIndicators.vue |
| AnalysisTrendChart        | 收支趋势图表组件               | src/pages/analysis/components/AnalysisTrendChart.vue |
| AnalysisExpenseComposition| 支出构成图表组件               | src/pages/analysis/components/AnalysisExpenseComposition.vue |
| AnalysisBudgetExecution   | 预算执行情况组件               | src/pages/analysis/components/AnalysisBudgetExecution.vue |
| AnalysisFinancialInsights | 财务洞察组件                  | src/pages/analysis/components/AnalysisFinancialInsights.vue |

##### 3.3 聊天页
| 组件名                | 用途简述                     | 路径                                      |
|----------------------|----------------------------|------------------------------------------|
| ChatBubble           | 聊天气泡容器，展示用户/AI消息      | src/components/business/ChatBubble.vue |
| RecognitionResult    | 交易识别结果卡片               | src/components/business/RecognitionResult.vue |

#### 4. 特殊图表组件
| 组件名                | 用途简述                     | 路径                                      |
|----------------------|----------------------------|------------------------------------------|
| QiunDataCharts       | 基础图表组件，支持多种图表类型      | src/components/u-charts/qiun-data-charts.vue |
| AppChart             | 通用图表封装组件，因渲染异常问题暂不使用 | src/components/common/AppChart.vue |

> **图表组件特别说明：**
> - 由于AppChart组件在某些场景下出现渲染异常，统计页面直接使用QiunDataCharts组件实现图表功能
> - 虽然AppChart是一个通用组件，但为避免渲染问题，我们有意识地保留了对QiunDataCharts的直接使用
> - 遵循开发规范，图表颜色、字体等样式仍通过工具函数读取CSS变量的实际值，并通过props传递