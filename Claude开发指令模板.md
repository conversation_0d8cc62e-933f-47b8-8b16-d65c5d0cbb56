# Claude开发指令模板

## 文档目的

本文档提供标准化的指令模板，帮助没有开发经验的用户通过Claude或其他AI大模型在Cursor中进行AI记账项目的开发工作。使用这些模板可以确保与AI的沟通明确、精准，提高开发效率。

## 使用说明

1. 根据您的具体需求，从下面选择合适的指令模板
2. 复制模板内容，根据实际情况修改参数部分（用`<尖括号>`标记）
3. 将修改好的指令发送给Cursor中的AI大模型
4. 确保开发过程中始终遵循项目的四个核心文档(架构设计文档、开发文档和规范、UI界面规则文档)

## 指令模板列表

### 1. 创建新页面

```
请根据《UI界面规则文档》中的页面结构规范（第二节），创建一个名为"<页面名称>"的新页面。
这个页面应该<简述页面功能>。

页面路径为：src/pages/<目录名>/<文件名>.vue

请确保：
1. 使用<style lang="scss" scoped>隔离样式
2. 使用<script setup lang="ts">语法
3. 遵循BEM命名规范为页面最外层容器添加唯一类名（如"<页面名>-container"）
4. 所有样式值使用CSS变量（从src/assets/styles/variables.scss）
5. 优先使用AppButton、AppCard等通用组件

完整示例：
《UI界面规则文档》第二节有页面基础模板示例，请参考该模板。
```

**🔍 示例**：
```
请根据《UI界面规则文档》中的页面结构规范（第二节），创建一个名为"预算设置"的新页面。
这个页面应该允许用户设置每月预算金额和各类别的预算限制。

页面路径为：src/pages/budget/setup.vue

请确保...（其余同上）
```

### 2. 创建新组件

```
请根据《UI界面规则文档》中的组件设计规范（第五节），创建一个名为"<组件名称>"的新<通用/业务>组件。
这个组件应该<简述组件功能>。

组件路径为：src/components/<common/business>/<组件名称>.vue

请确保：
1. 如果是通用组件，名称必须以App开头（除FaIcon外）
2. 使用<style lang="scss" scoped>隔离样式
3. 使用<script setup lang="ts">语法
4. 定义明确的props接口（使用defineProps）
5. 声明emit的事件（使用defineEmits）
6. 所有样式值使用CSS变量
7. 遵循《开发规范与指南-基础篇》中的防御性编程原则

如果需要示例，请参考《UI界面规则文档》中的组件设计系统部分。
```

**🔍 示例**：
```
请根据《UI界面规则文档》中的组件设计规范（第五节），创建一个名为"AppAmountInput"的新通用组件。
这个组件应该用于金额输入，包含数字键盘和金额显示区域。

组件路径为：src/components/common/AppAmountInput.vue

请确保...（其余同上）
```

### 3. 样式修改

```
请根据《UI界面规则文档》中的样式系统规范（第三节），修改<文件路径>文件中的样式。

具体需要修改的部分：
<详细描述需要修改的样式>

请确保：
1. 保持使用<style lang="scss" scoped>隔离样式
2. 使用CSS变量而非硬编码值（如颜色、间距、字体大小等）
3. 遵循BEM命名约定（block__element--modifier）
4. 不使用!important（除非处理第三方组件样式）
5. 选择器嵌套不超过3层
6. 支持暗黑模式（所有颜色使用变量）

对于第三方组件样式覆盖，请使用:deep()选择器。
```

**🔍 示例**：
```
请根据《UI界面规则文档》中的样式系统规范（第三节），修改src/pages/transaction/list.vue文件中的样式。

具体需要修改的部分：
1. 交易列表项的间距太小，需要增加垂直间距
2. 金额文字在暗黑模式下可见度不够
3. 卡片阴影效果不符合设计规范

请确保...（其余同上）
```

### 4. 实现新功能

```
请根据《架构设计文档》中的分层架构设计（第二节），实现<功能名称>功能。
这个功能应该<简述功能需求>。

涉及的文件可能包括：
- <可能涉及的文件路径1>
- <可能涉及的文件路径2>

需要注意的架构原则：
1. 确保遵循分层架构（展示层、业务逻辑层、数据层）
2. API调用封装在api目录中的相关模块
3. 复杂业务逻辑应抽取为hooks（放在src/hooks/目录）
4. 共享状态使用Pinia管理（禁止使用Vuex）
5. 使用《开发规范与指南-基础篇》中的防御性编程原则

请完成功能实现，确保代码风格一致，并遵循项目的编码规范。
```

**🔍 示例**：
```
请根据《架构设计文档》中的分层架构设计（第二节），实现"语音记账"功能。
这个功能应该允许用户通过语音输入来记录交易，系统自动识别金额、类别和时间信息。

涉及的文件可能包括：
- src/api/voice.js
- src/hooks/useVoice.js
- src/pages/transaction/voice-input.vue
- src/stores/transaction.store.js

需要注意的架构原则：...（其余同上）
```

### 5. 多端适配修复

```
请根据《UI界面规则文档》中的多端适配详细指南（第十一节），解决<文件路径>中在<平台名称>上的显示问题。

具体问题是：<描述显示问题>

请使用以下方法处理：
1. 针对特定平台的条件编译（#ifdef / #endif）
2. 使用utils/platform.js中的平台适配函数
3. 使用CSS变量和响应式单位(rpx)

特别注意以下平台差异：
- iOS：状态栏、底部安全区域适配
- Android：设备碎片化、状态栏适配
- 小程序：胶囊按钮、页面层级限制
- H5：响应式适配
```

**🔍 示例**：
```
请根据《UI界面规则文档》中的多端适配详细指南（第十一节），解决src/pages/transaction/detail.vue中在iOS上的显示问题。

具体问题是：
页面底部按钮在iPhone X以上机型中被底部安全区域遮挡，按钮需要上移。

请使用以下方法处理：...（其余同上）
```

### 6. API集成

```
请根据《架构设计文档》中的API设计约束（Sealos云平台集成规范部分），在<文件路径>中集成<API名称>接口。

这个API的用途是<简述API功能>。

请确保：
1. API路径包含版本号（如/api/v1/endpoint）
2. 使用src/utils/request.js中的封装方法发起请求
3. 遵循RESTful风格设计（GET查询、POST创建等）
4. 正确处理错误情况（使用try/catch）
5. 实现必要的数据转换和验证
6. 敏感数据传输时使用加密功能

请参考《开发规范与指南-基础篇》中第7节的防御性编程原则，确保API调用安全可靠。
```

**🔍 示例**：
```
请根据《架构设计文档》中的API设计约束（Sealos云平台集成规范部分），在src/api/transaction.js中集成"获取交易列表"接口。

这个API的用途是获取用户的交易记录列表，支持分页和过滤条件。

请确保：...（其余同上）
```

### 7. 代码审查和优化

```
请根据《开发规范与指南-基础篇》和《开发规范与指南-高级篇》中的标准，审查以下文件的代码质量：
<文件路径>

检查以下方面：
1. 命名是否符合规范（PascalCase组件、camelCase变量等）
2. 组件结构是否合理（script setup语法、组件逻辑组织）
3. 样式是否遵循项目规范（BEM命名、CSS变量使用、scoped属性）
4. 防御性编程（可选链、空值合并、异步错误处理等）
5. 有无潜在的性能问题（参考《开发规范与指南-高级篇》性能优化策略）
6. 多端适配是否完善

请提供具体的修改建议，并直接实现优化。
```

**🔍 示例**：
```
请根据《开发规范与指南-基础篇》和《开发规范与指南-高级篇》中的标准，审查以下文件的代码质量：
src/pages/analysis/budget.vue

检查以下方面：...（其余同上）
```

### 8. 工程配置修改

```
请根据《架构设计文档》，帮我修改<配置文件路径>中的配置。

需要修改的内容是：<描述需要修改的配置>

请确保：
1. 遵循项目的模块化结构
2. 配置修改不会影响现有功能
3. 变更符合项目的整体架构设计
4. 添加必要的注释说明配置项的用途
```

**🔍 示例**：
```
请根据《架构设计文档》，帮我修改vite.config.js中的配置。

需要修改的内容是：
1. 添加路径别名配置，使@指向src目录
2. 优化构建选项，提高打包速度
3. 添加环境变量处理

请确保：...（其余同上）
```

### 9. 组件文档生成

```
请根据《UI界面规则文档》中的组件设计系统（第五节），为<组件路径>生成组件文档。

文档应包含：
1. 组件功能描述（简洁明了的说明组件用途）
2. Props接口说明（名称、类型、默认值、是否必填、说明）
3. 事件说明（名称、参数、触发条件）
4. 插槽说明（如果有）
5. 使用示例（基本用法和各种变体）
6. 注意事项（使用限制、配合组件等）

请使用Markdown格式，确保文档完整易懂。
```

**🔍 示例**：
```
请根据《UI界面规则文档》中的组件设计系统（第五节），为src/components/common/AppButton.vue生成组件文档。

文档应包含：...（其余同上）
```

### 10. 错误修复

```
当前项目中<文件路径>出现错误：
<错误信息>

请根据《开发规范与指南-基础篇》分析错误原因并修复。

可能的原因包括：
- <您对可能原因的猜测1>
- <您对可能原因的猜测2>

修复时请确保：
1. 遵循项目的编码规范
2. 使用防御性编程原则
3. 不引入新的问题
4. 添加必要的注释说明修复方法

请详细说明错误原因和修复方案，然后实施修复。
```

**🔍 示例**：
```
当前项目中src/pages/transaction/add.vue出现错误：
"TypeError: Cannot read properties of undefined (reading 'categories')"

请根据《开发规范与指南-基础篇》分析错误原因并修复。

可能的原因包括：
- 在categories数据加载完成前尝试访问
- API返回数据结构与预期不符
- 状态管理中的数据初始化问题

修复时请确保：...（其余同上）
```

## 高级组合指令

### 完整页面开发

```
请帮我开发一个完整的"<页面名称>"页面，包括：

1. 页面基础结构（根据《UI界面规则文档》的页面结构规范）
2. 必要的组件开发（遵循组件设计规范）
3. 状态管理集成（使用Pinia，根据《架构设计文档》）
4. API集成（遵循API设计约束）
5. 多端适配（根据多端适配详细指南）

页面功能描述：
<详细描述页面功能和交互>

开发要点：
1. 页面路径：src/pages/<目录>/<文件名>.vue
2. 所有样式使用CSS变量（src/assets/styles/variables.scss）
3. 优先使用现有通用组件
4. 确保页面在各平台（iOS、Android、小程序、H5）上正常显示
5. 实现防抖/节流以优化性能（参考《开发规范与指南-高级篇》）

请按照以下顺序开发：先创建页面基本结构→添加状态和方法→实现API集成→完善样式→多端适配。
```

**🔍 示例**：
```
请帮我开发一个完整的"账单详情"页面，包括：

1. 页面基础结构（根据《UI界面规则文档》的页面结构规范）
2. 必要的组件开发（遵循组件设计规范）
3. 状态管理集成（使用Pinia，根据《架构设计文档》）
4. API集成（遵循API设计约束）
5. 多端适配（根据多端适配详细指南）

页面功能描述：
此页面显示单笔交易的详细信息，包括金额、类别、时间、备注等。用户可以编辑或删除交易记录，也可以添加照片凭证。页面底部有编辑和删除按钮。

开发要点：...（其余同上）
```

### 主题和样式系统实现

```
请根据《UI界面规则文档》中的颜色与样式系统（第三节），实现：

1. 创建或更新基础的CSS变量系统（在src/assets/styles/variables.scss）
2. 实现暗黑模式支持（包括媒体查询和类名切换机制）
3. 设置响应式断点（确保在不同设备上显示正常）
4. 创建必要的主题切换功能（在utils/theme.js中）

具体要求：
<描述具体主题需求>

实现时请确保：
1. 所有颜色定义都有亮色/暗色两套方案
2. 主题变量命名符合规范（--[分类]-[功能]-[状态]）
3. 提供合理的默认值兼容不支持CSS变量的环境
4. 用户主题偏好保存到本地存储
```

**🔍 示例**：
```
请根据《UI界面规则文档》中的颜色与样式系统（第三节），实现：

1. 创建或更新基础的CSS变量系统（在src/assets/styles/variables.scss）
2. 实现暗黑模式支持（包括媒体查询和类名切换机制）
3. 设置响应式断点（确保在不同设备上显示正常）
4. 创建必要的主题切换功能（在utils/theme.js中）

具体要求：
系统需要支持默认浅色主题和暗黑主题，主色调为#FF6B35（暗色模式下使用#FF8A65）。所有组件都需要响应主题变化，包括背景色、文字颜色、边框和阴影效果。用户可以选择跟随系统设置或手动切换主题。

实现时请确保：...（其余同上）
```

### 基础通用组件开发套件

```
请根据《UI界面规则文档》中的组件设计系统（第五节）和《开发规范与指南-基础篇》，开发以下基础通用组件套件：

1. AppButton - 按钮组件（支持多种类型、尺寸和状态）
2. AppInput - 输入框组件（支持各种输入类型和验证）
3. AppCard - 卡片容器组件
4. AppList/AppListItem - 列表组件
5. AppModal - 模态对话框组件

每个组件需要：
1. 遵循组件命名规范（App前缀）
2. 使用<script setup lang="ts">语法
3. 定义TypeScript类型的props和emits
4. 实现完整的交互状态（默认、悬停、按下、禁用等）
5. 支持暗黑模式
6. 提供完整文档注释

组件应放置在src/components/common/目录下，确保互相独立且可复用。
```

## 注意事项

1. 所有开发工作必须遵循项目的四个核心文档规范：
   - 架构设计文档.md
   - 开发规范与指南-基础篇.md
   - 开发规范与指南-高级篇.md
   - UI界面规则文档.md

2. 开发顺序建议：
   - 先搭建基础框架和工具
   - 再开发通用组件
   - 然后实现核心页面
   - 最后添加功能和优化

3. 遇到问题时的处理：
   - 明确描述问题和错误信息
   - 引用相关文档中的具体章节
   - 提供您的尝试和猜测
   - 请求AI生成可执行的解决方案

4. 优先使用已有的组件和工具，避免重复开发

5. 对于复杂任务，建议分解为多个小型指令，逐步实现 