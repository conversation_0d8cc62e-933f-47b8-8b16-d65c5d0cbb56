<template>
	<view
	    class="u-icon"
	    :class="[$u.addStyle(customClass)]"
	    :style="[{
		fontFamily: fontFamily,
	    }]"
	    @tap="clickHandler"
	    :hover-class="hoverClass"
	>
		<text
		    v-if="isImg"
		    :style="[imgStyle, $u.addStyle(customStyle)]"
		    class="u-icon__img"
		    :class="$u.addStyle(customClass)"
		></text>
		<text
		    v-else
		    class="u-icon__icon"
		    :class="[name, isLabel ? 'u-icon--left' : '', iconClass]"
		    :style="[iconStyle, $u.addStyle(customStyle)]"
		>
			<template v-if="displayIcon">{{iconValue}}</template>
		</text>
		<!-- 右侧的label -->
		<text v-if="label !== ''" class="u-icon__label" :style="{
			color: $u.test.color(labelColor) ? labelColor : labelColor,
			fontSize: $u.addUnit(labelSize),
			marginLeft: $u.addUnit(labelPos == 'right' ? marginLeft : 0),
			marginTop: $u.addUnit(labelPos == 'bottom' ? marginTop : 0),
			marginRight: $u.addUnit(labelPos == 'left' ? marginRight : 0),
			marginBottom: $u.addUnit(labelPos == 'top' ? marginBottom : 0),
		}">
			{{label}}
		</text>
	</view>
</template>

<script>
	// 引入图标名称，已经对应的unicode
	import icons from './icons';
	import { props } from './props';
	import config from '../../libs/config/config';
	import { mpMixin } from '../../libs/mixin/mpMixin';
	import { mixin } from '../../libs/mixin/mixin';
	import { addUnit, addStyle } from '../../libs/function/index';
	/**
	 * icon 图标
	 * @description 基于字体的图标集，包含了大多数常见场景的图标。
	 * @tutorial https://ijry.github.io/uview-plus/components/icon.html
	 * @property {String}			name			图标名称，见示例图标集
	 * @property {String}			color			图标颜色,可接受主题色 （默认 color['u-content-color'] ）
	 * @property {String | Number}	size			图标字体大小，单位px （默认 '16px' ）
	 * @property {Boolean}			bold			是否显示粗体 （默认 false ）
	 * @property {String | Number}	index			点击图标的时候传递事件出去的index（用于区分点击了哪一个）
	 * @property {String}			hoverClass		图标按下去的样式类，用法同uni的view组件的hoverClass参数，详情见官网
	 * @property {String}			customPrefix	自定义扩展前缀，方便用户扩展自己的图标库 （默认 'uicon' ）
	 * @property {String | Number}	label			图标右侧的label文字
	 * @property {String}			labelPos		label相对于图标的位置，只能right或bottom （默认 'right' ）
	 * @property {String | Number}	labelSize		label字体大小，单位px （默认 '15px' ）
	 * @property {String}			labelColor		图标右侧的label文字颜色 （ 默认 color['u-content-color'] ）
	 * @property {String | Number}	space			label与图标的距离，单位px （默认 '3px' ）
	 * @property {String}			imgMode			图片的mode
	 * @property {String | Number}	width			显示图片小图标时的宽度
	 * @property {String | Number}	height			显示图片小图标时的高度
	 * @property {String | Number}	top				图标在垂直方向上的定位 用于解决某些情况下，让图标垂直居中的用途  （默认 0 ）
	 * @property {Boolean}			stop			是否阻止事件传播 （默认 false ）
	 * @property {Object}			customStyle		icon的样式，对象形式
	 * @event {Function} click 点击图标时触发
	 * @event {Function} touchstart 事件触摸时触发
	 * @example <u-icon name="photo" color="#2979ff" size="28"></u-icon>
	 */
	export default {
		name: 'u-icon',
		beforeCreate() {
			
			// #ifdef APP-NVUE
			// nvue通过weex的dom模块引入字体，相关文档地址如下：
			// https://weex.apache.org/zh/docs/modules/dom.html#addrule
			const domModule = weex.requireModule('dom');
			domModule.addRule('fontFace', {
				'fontFamily': "uicon-iconfont",
				'src': `url('${config.iconUrl}')`
			});
			if (config.customIcon.family) {
				domModule.addRule('fontFace', {
					'fontFamily': config.customIcon.family,
					'src': `url('${config.customIcon.url}')`
				});
			}
			// #endif
			// #ifdef APP || H5 || MP-WEIXIN || MP-ALIPAY
			uni.loadFontFace({
				family: 'uicon-iconfont',
				source: 'url("' + config.iconUrl + '")',
				success() {
					// console.log('内置字体图标加载成功');
				},
				fail() {
					console.error('内置字体图标加载出错');
				}
			});
			if (config.customIcon.family) {
				uni.loadFontFace({
					family: config.customIcon.family,
					source: 'url("' + config.customIcon.url + '")',
					success() {
						// console.log('扩展字体图标加载成功');
					},
					fail() {
						console.error('扩展字体图标加载出错');
					}
				});
			}
			// #endif
			// #ifdef APP-NVUE
			if (this.customFontFamily) {
				domModule.addRule('fontFace', {
					'fontFamily': `${this.customPrefix}-${this.customFontFamily}`,
					'src': `url('${this.customFontUrl}')`
				})
			}
        	// #endif
    	},
		data() {
			return {
				// 是否图片资源
				isImg: false,
				charCode: '',
			}
		},
		emits: ['click'],
		mixins: [mpMixin, mixin, props],
		computed: {
			iconValue() {
				// 如果设置了customPrefix值而没有设置name值，就默认使用内置的图标
				if (this.customPrefix) {
					// name存在自定义前缀与基础类名拼接，得出最终类名
					return this[this.customPrefix] || this.name
				} else if (this.name) {
					return this.name
				}
			},
			icon() {
				// 如果设置了customPrefix值而没有设置name值，就默认使用内置的图标
				if (this.customPrefix) {
					return this.name ? this.name : this.customPrefix
				} else {
					return this.name
				}
			},
			// 判断传入的name属性，是否图片路径，只要带有"/"均认为是图片形式
			isImg() {
				return this.name.indexOf('/') !== -1
			},
			// 通过图标名，查找对应的图标
			iconClass() {
				// 如果内置的图标中找不到对应的图标，就直接返回name值，因为用户可能传入的是unicode代码
				// return this.customPrefix ? `${this.customPrefix}-${this.name}` : uni.$u.type.object(this.uClasses)
				//    ? this.uClasses[this.name] : (this.name ? 'uicon-' + this.name : '')
				let iconClass = ''
				if (this.customPrefix) {
					// 如果有自定义前缀，就拼接前缀和图标名称
					iconClass = `${this.customPrefix}-${this.name}`
				} else {
					// 否则判断是否存在uClasses对象中
					iconClass = this.name ? `uicon-${this.name}` : ''
				}
				// 如果找不到对应的类名，直接返回name值
				return iconClass
			},
			iconStyle() {
				let style = {}
				style = {
					fontSize: addUnit(this.size),
					lineHeight: addUnit(this.size),
					fontWeight: this.bold ? 'bold' : 'normal',
					// 如果是显示图标，需要设置一个默认值
					color: (this.displayIcon ? config.type.includes(this.color) ? config.type[this.color] : this.color : (this.color ? this.color : config.type['u-content-color'])),
					top: addUnit(this.top),
					rotate: this.spin ? '360deg' : this.rotate + 'deg',
					// 如果设置了spin旋转，直接设置它的动画属性
					transition: this.spin ? '' : 'none',
					animation: this.spin ? 'u-icon-spin-animation 1.25s linear infinite' : ''
				}
				if (this.customPrefix !== 'uicon') {
					style.fontFamily = this.customPrefix
				}
				return style
			},
			// 图片的样式
			imgStyle() {
				let style = {}
				style.width = this.width ? addUnit(this.width) : addUnit(this.size)
				style.height = this.height ? addUnit(this.height) : addUnit(this.size)
				return style
			},
			// 是否显示图标，如果name中带有/，就是图片资源，就不显示图标
			displayIcon() {
				return !this.isImg
			},
			fontFamily() {
				let family = ''
				// 判断是否存在自定义字体
				if (this.customPrefix) {
					family = this.customPrefix
				} else {
					family = 'uicon-iconfont'
				}
				return family
			}
		},
		methods: {
			addStyle,
			addUnit,
			clickHandler(e) {
				this.$emit('click', this.index, e)
				// 是否阻止事件冒泡
				this.stop && this.preventEvent(e)
			}
		}
	}
</script>

<style lang="scss" scoped>
/* 在样式顶部使用@use导入样式，确保在其他规则之前 */
@use "src/assets/styles/variables.scss" as *;
@use "src/uni.scss" as *;
@use "uview-plus/index.scss" as *;

/* 然后是组件原有的@import导入 */
@import "../../libs/css/components.scss";

// 变量定义
$u-icon-primary: $u-primary !default;
$u-icon-success: $u-success !default;
$u-icon-info: $u-info !default;
$u-icon-warning: $u-warning !default;
$u-icon-error: $u-error !default;
$u-icon-label-line-height:1 !default;

.u-icon {
	display: inline-flex;
	align-items: center;

	&--left {
		margin-right: 5px;
	}

	&--right {
		margin-left: 5px;
	}

	&--top {
		margin-bottom: 5px;
	}

	&--bottom {
		margin-top: 5px;
	}

	&__icon {
		position: relative;

		font-family: uicon-iconfont;
		display: flex;
		align-items: center;

		&::before {
			display: flex;
			align-items: center;
		}
	}

	&__img {
		height: auto;
		will-change: transform;
	}

	&__label {
		line-height: $u-icon-label-line-height;
	}
}

/* 修复iOS模拟器字体渲染问题 */
/* #ifdef APP-PLUS */
.ios-simulator .u-icon__icon {
  font-family: uicon-iconfont !important;
}
/* #endif */

@keyframes u-icon-spin-animation {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}
</style>
